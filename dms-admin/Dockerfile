FROM 192.168.1.169:5000/java8-with-fonts:latest
ENV TZ "Asia/Shanghai"
RUN mkdir -p /dms-admin/filedata && mkdir -p /dms-admin/templates
COPY ./license.elic.xml /dms-admin/license.elic.xml
COPY ./fonts/SIMFANG.TTF /dms-admin/fonts/SIMFANG.TTF
COPY ./templates/xtcshmb.xlsx /dms-admin/templates/xtcshmb.xlsx
COPY ./templates/wlwjmb.xlsx /dms-admin/templates/wlwjmb.xlsx
COPY ./templates/lcwjxxmb.xlsx /dms-admin/templates/lcwjxxmb.xlsx
COPY ./dms-admin/target/dms-admin.jar /dms-admin/dms-admin.jar
WORKDIR /dms-admin
EXPOSE 8080 9102
CMD ["java","-Djava.awt.headless=true","-jar","dms-admin.jar"]

package com.rzdata;

import cn.hutool.system.SystemUtil;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.metrics.buffering.BufferingApplicationStartup;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import java.io.File;

/**
 * 启动程序
 *
 * <AUTHOR>
 */

@SpringBootApplication
@EnableTransactionManagement
@EnableAsync
public class AdminApplication {

    public static void main(String[] args) {
        System.setProperty("spring.devtools.restart.enabled", "false");
        SpringApplication application = new SpringApplication(AdminApplication.class);
        application.setApplicationStartup(new BufferingApplicationStartup(2048));
        application.run(args);
        //加载spire的授权文件
        com.spire.license.LicenseProvider.setLicenseFile(System.getProperty(SystemUtil.USER_DIR)+File.separator+"license.elic.xml");
        System.out.println("(♥◠‿◠)ﾉﾞ  体系文件管理系统启动成功   ლ(´ڡ`ლ)ﾞ");
    }

}

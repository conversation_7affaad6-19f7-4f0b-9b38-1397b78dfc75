package com.rzdata.asas7.service;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.rzdata.asas7.util.HttpUtils;
import com.rzdata.asas7.util.TokenManager;
import com.rzdata.framework.constant.Constants;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.utils.spring.SpringUtils;
import com.rzdata.system.service.ISysConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * 爱数文件服务类
 */
@Slf4j
@Service
public class AsFileService extends AsBaseService {

    @Autowired
    AsDirService asDirService;

    private TokenManager tokenManager;

    public AsFileService() {
        this.tokenManager = new TokenManager();
    }

    /**
     * 删除文件
     *
     * @param docid 待删除文件gns路径
     *
     * @return
     */
    public AjaxResult<JSONObject> delete(String docid) {
        String baseUrl = SpringUtils.getBean(ISysConfigService.class).selectConfigByKey(Constants.AS_BASE_URL);
        Map<String, Object> requestMap = new HashMap<>();
        requestMap.put("docid", docid);
        Map<String, String> requestHeader = new HashMap<>();
        requestHeader.put("Content-Type", "application/json");
        JSONObject jsonObjectTemp = HttpUtils.getInstance().postForObject(baseUrl.concat("/api/efast/v1/file/delete"), requestMap,requestHeader, JSONObject.class, "Bearer " + super.getToken());
        return AjaxResult.success("success",jsonObjectTemp);
    }


    /**
     * 移动文件
     *
     * @param docid 待移动文件gns路径
     * @param destparent 目标父对象的gns路径
     *
     * @return
     */
    public AjaxResult<JSONObject> move(String docid,String destparent) {
        String baseUrl = SpringUtils.getBean(ISysConfigService.class).selectConfigByKey(Constants.AS_BASE_URL);
        Map<String, Object> requestMap = new HashMap<>();
        requestMap.put("docid", docid);
        requestMap.put("destparent", destparent);
        Map<String, String> requestHeader = new HashMap<>();
        requestHeader.put("Content-Type", "application/json");
        JSONObject jsonObjectTemp = HttpUtils.getInstance().postForObject(baseUrl.concat("/api/efast/v1/file/move"), requestMap,requestHeader, JSONObject.class, "Bearer " + super.getToken());
        return AjaxResult.success("success",jsonObjectTemp);
    }

    /**
     * 复制文件
     *
     * @param docid 待复制文件gns路径
     * @param destparent 目标父对象的gns路径
     * @param ondup
     *
     * @return
     */
    public AjaxResult<JSONObject> copy(String docid,int ondup) {
        // 创建副本文件目录
        String path = docid.split("/")[0];
        AjaxResult ajaxResult =  asDirService.create("gns://" + path, IdUtil.randomUUID(), 1);
        String destparent = JSONUtil.parseObj(ajaxResult.getData()).getStr("docid");
        TokenManager tokenManager = new TokenManager();
        String baseUrl = SpringUtils.getBean(ISysConfigService.class).selectConfigByKey(Constants.AS_BASE_URL);
        Map<String, Object> requestMap = new HashMap<>();
        requestMap.put("docid", "gns://" + docid);
        requestMap.put("destparent", destparent);
        requestMap.put("ondup", ondup);
        Map<String, String> requestHeader = new HashMap<>();
        requestHeader.put("Content-Type", "application/json");
        JSONObject jsonObjectTemp = HttpUtils.getInstance().postForObject(baseUrl.concat("/api/efast/v1/file/copy"), requestMap,requestHeader, JSONObject.class, "Bearer " + tokenManager.getAppTokenId());
        return AjaxResult.success("success",jsonObjectTemp);
    }

    /**
     * 重命名
     *
     * @param docid 待重命名文件gns路径
     * @param name 重命名名称
     *
     * @return
     */
    public AjaxResult<JSONObject> rename(String docid,String name) {
        String baseUrl = SpringUtils.getBean(ISysConfigService.class).selectConfigByKey(Constants.AS_BASE_URL);
        Map<String, Object> requestMap = new HashMap<>();
        requestMap.put("docid", docid);
        requestMap.put("name", name);
        Map<String, String> requestHeader = new HashMap<>();
        requestHeader.put("Content-Type", "application/json");
        JSONObject jsonObjectTemp = HttpUtils.getInstance().postForObject(baseUrl.concat("/api/efast/v1/file/rename"), requestMap,requestHeader, JSONObject.class, "Bearer " + super.getToken());
        return AjaxResult.success("success",jsonObjectTemp);
    }

}

package com.rzdata.asas7.util;

import cn.hutool.core.util.ReUtil;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024年12月31日 17:28
 **/
public class EmailUtil {

    public static String replaceContent(String content, Map<String, String> param) {
        final String regex = "\\$\\{([\\S]+)\\}";
        return ReUtil.replaceAll(content, regex, matcher ->
                ReUtil.escape(param.getOrDefault(matcher.group(1), ""))
        );
    }
}

package com.rzdata.config;

import lombok.Data;
import lombok.Getter;
import lombok.experimental.Accessors;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 读取项目相关配置
 *
 * <AUTHOR> Li
 */

@Data
@Accessors(chain = true)
@Component
@ConfigurationProperties(prefix = "rzdata")
public class CustomConfig {

    /**
     * 项目名称
     */
    private String name;

    /**
     * 版本
     */
    private String version;
    /**
     * 流程平台下应用租户id
     */
    private String bpmTenantId;
    /**
     * 流程平台下应用租户id
     */
    private String bpmDataSource;
    /**
     * 基础信息数据库名称
     */
    private String ubaseDataSource;

    /**
     * 流程平台服务接口地址
     */
    private String workflowServiceUrl;

    /**
     * 待办地址前缀
     */
    private String workflowUniteWorkUrl;


    /**
     * 缓存懒加载
     */
    private boolean cacheLazy;

    /**
     * 获取地址开关
     */
    @Getter
    private static boolean addressEnabled;

    public void setAddressEnabled(boolean addressEnabled) {
        CustomConfig.addressEnabled = addressEnabled;
    }

    /**
     * 第三方消息通道
     */
    private String msgThirdChannel;

    /**
     * 企业微信重定向域名地址
     */
    private String qywxRedirectUri;

    /**
     * 企业微信登录后，前端页面重定向地址
     */
    private String tokenRedirectUri;

    /**
     * 复审是否开启
     */
    private boolean reviewEnable;

    /**
     * 复审流程KEY（见流程平台配置）
     */
    private String reviewFlowKey;

    /**
     * 复审提前通知的天数配置
     */
    private String reviewMsgForwardDay;

    /**
     * 复审通知人员
     * 文控company_file_manager、编制人editer、部门文控dept_file_manager
     */
    private String reviewMsgReminder;

    private String distributeRangeType;

    /**
     *  是否启用流程测试
     */
    private boolean flowTestEnable;

    /**
     *  流程测试人员，多个用逗号分隔
     */
    private String flowTestUserNames;


    /**
     *  流程提交对话框是否标记已办步骤
     */
    private boolean flowStepMarkEnable;

    /**
     *  流程提交对话框是否默认选择第一个步骤
     */
    private boolean flowStepDefSelectEnable;

    /**
     *  不需要签章的文件类型
     */
    private String noSignDocType;

    /**
     *  不需要签章的文件扩展名
     */
    private String noSignFileExdType;

    /**
     *  不需要签章的添加方式
     */
    private String noSignAddType;

    /**
     *  是否添加文件编号水印
     */
    private boolean signDocCodeEnable;


    /**
     *  流程结束参与者消息通知通道：txt站内消息、email邮件
     */
    private String msgFlowNoticeChanel;

    /**
     *  常规消息通知通道：txt站内消息、email邮件
     */
    private String msgNormalNoticeChanel;

    /**
     *  流程结束（文件新增、文件修订、作废流程触发）参与者消息通知范围：flowHandler仅限流程参与者，allUser企业所有人员
     */
    private String msgFlowNoticeScope;

    /**
     * pc前端地址
     */
    private String frontUrl;
    /**
     * 移动端前端地址
     */
    private String mobileFrontUrl;
}

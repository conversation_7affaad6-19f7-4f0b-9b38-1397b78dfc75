package com.rzdata.es;

import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.elasticsearch._types.SortOrder;
import co.elastic.clients.elasticsearch._types.mapping.Property;
import co.elastic.clients.elasticsearch._types.mapping.TypeMapping;
import co.elastic.clients.elasticsearch._types.query_dsl.*;
import co.elastic.clients.elasticsearch.core.*;
import co.elastic.clients.elasticsearch.core.bulk.BulkOperation;
import co.elastic.clients.elasticsearch.core.bulk.BulkResponseItem;
import co.elastic.clients.elasticsearch.core.search.Highlight;
import co.elastic.clients.elasticsearch.core.search.HighlightField;
import co.elastic.clients.elasticsearch.core.search.Hit;
import co.elastic.clients.json.JsonData;
import co.elastic.clients.json.jackson.JacksonJsonpMapper;
import co.elastic.clients.transport.ElasticsearchTransport;
import co.elastic.clients.transport.rest_client.RestClientTransport;
import com.rzdata.framework.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpHost;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestClientBuilder;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Elasticsearch 工具类
 * 适配 Elasticsearch 8.x 版本
 * 
 * <AUTHOR> Name
 * @date 2024-xx-xx
 */
@Slf4j
public class ElasticsearchUtil {

    private final ElasticsearchClient client;

    /**
     * 初始化 Elasticsearch 客户端
     *
     * @param host ES服务器地址
     * @param port ES服务器端口
     * @param username ES用户名(可选)
     * @param password ES密码(可选)
     */
    public ElasticsearchUtil(String host, int port, String username, String password) {
        try {
            // 创建 RestClientBuilder
            RestClientBuilder builder = RestClient.builder(
                new HttpHost(host, port, "http")
            );

            // 配置请求选项
            builder.setRequestConfigCallback(requestConfigBuilder -> 
                requestConfigBuilder
                    .setConnectTimeout(5000)
                    .setSocketTimeout(60000)
                    .setConnectionRequestTimeout(1000)
            );

            // 配置异步 HTTP 请求的默认选项
            builder.setHttpClientConfigCallback(httpClientBuilder -> {
                httpClientBuilder
                    .setMaxConnTotal(100)
                    .setMaxConnPerRoute(30);
                
                // 如果提供了用户名和密码，则配置基本认证
                if (StringUtils.isNotEmpty(username) && StringUtils.isNotEmpty(password)) {
                    CredentialsProvider credentialsProvider = new BasicCredentialsProvider();
                    credentialsProvider.setCredentials(AuthScope.ANY,
                        new UsernamePasswordCredentials(username, password));
                    httpClientBuilder.setDefaultCredentialsProvider(credentialsProvider);
                    log.info("Elasticsearch client configured with authentication");
                }
                
                return httpClientBuilder;
            });

            // 创建低级客户端
            RestClient restClient = builder.build();

            // 创建传输层
            ElasticsearchTransport transport = new RestClientTransport(
                restClient, 
                new JacksonJsonpMapper()
            );

            // 创建高级客户端
            this.client = new ElasticsearchClient(transport);
            
            log.info("Elasticsearch client initialized successfully with URL: {}:{}", host, port);
        } catch (Exception e) {
            log.error("Failed to initialize Elasticsearch client: {}", e.getMessage());
            throw new RuntimeException("Failed to initialize Elasticsearch client", e);
        }
    }

    /**
     * 创建索引
     *
     * @param index    索引名称
     * @param mappings 索引映射（字段类型）
     * @return 是否成功
     */
    public boolean createIndex(String index, Map<String, Property> mappings) {
        try {
            // 创建索引配置
            client.indices().create(c -> c
                .index(index)
                .settings(s -> s
                    .numberOfShards("1")
                    .numberOfReplicas("1")
                    .analysis(a -> a
                        .analyzer("my_analyzer", an -> an
                            .custom(ca -> ca
                                .tokenizer("standard")
                                .filter("lowercase", "word_delimiter_graph")
                            )
                        )
                    )
                )
                .mappings(TypeMapping.of(m -> m.properties(mappings)))
            );
            log.info("Successfully created index: {}", index);
            return true;
        } catch (IOException e) {
            log.error("Failed to create index: {}, error: {}", index, e.getMessage());
            return false;
        }
    }

    /**
     * 创建文本字段属性（使用IK分词器）
     *
     * @param analyzerType 分词器类型：ik_smart 或 ik_max_word
     * @return Property 对象
     */
    public Property createTextFieldProperty(String analyzerType) {
        return Property.of(p -> p
                .text(t -> t
                        .analyzer(analyzerType)      // 直接使用 ik_max_word 或 ik_smart
                        .searchAnalyzer(analyzerType)
                )
        );
    }

    /**
     * 创建关键字字段属性（不分词）
     *
     * @return Property 对象
     */
    public Property createKeywordFieldProperty() {
        return Property.of(p -> p
                .keyword(k -> k
                        .ignoreAbove(256)
                )
        );
    }

    /**
     * 索引文档
     *
     * @param index    索引名称
     * @param id       文档 ID
     * @param document 文档内容
     * @return 是否成功
     */
    public boolean indexDocument(String index, String id, Map<String, Object> document) {
        try {
            IndexResponse response = client.index(i -> i
                    .index(index)
                    .id(id)
                    .document(document));
            return response.result().name().equals("Created");
        } catch (IOException e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 更新文档
     *
     * @param index    索引名称
     * @param id       文档 ID
     * @param document 更新内容
     * @return 是否成功
     */
    public boolean updateDocument(String index, String id, Map<String, Object> document) {
        try {
            UpdateResponse<Object> response = client.update(u -> u
                    .index(index)
                    .id(id)
                    .doc(document), Object.class);
            return response.result().name().equals("Updated");
        } catch (IOException e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 删除文档
     *
     * @param index 索引名称
     * @param id    文档 ID
     * @return 是否成功
     */
    public boolean deleteDocument(String index, String id) {
        try {
            DeleteResponse response = client.delete(d -> d
                    .index(index)
                    .id(id));
            return response.result().name().equals("Deleted");
        } catch (IOException e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 多字段搜索
     *
     * @param index  索引名称
     * @param fields 搜索字段
     * @param query  搜索关键字
     * @param from   分页起始位置
     * @param size   每页大小
     * @return 搜索结果
     */
    public List<Map<String, Object>> multiFieldSearch(String index, List<String> fields, String query, int from, int size) {
        try {
            // 构建多字段查询
            Query multiMatchQuery = Query.of(q -> q
                    .multiMatch(mm -> mm
                            .fields(fields)
                            .query(query)));

            SearchResponse<Object> response = client.search(s -> s
                    .index(index)
                    .query(multiMatchQuery)
                    .from(from)
                    .size(size), Object.class);

            return response.hits().hits().stream()
                    .map(hit -> (Map<String, Object>) hit.source())
                    .collect(Collectors.toList());
        } catch (IOException e) {
            e.printStackTrace();
            return Collections.emptyList();
        }
    }

    /**
     * 分页多字段关键字查询，并高亮显示关键字
     * @param index 索引名称
     * @param query 搜索关键字
     * @param fields 搜索字段列表
     * @param fixedFields 固定字段查询条件
     * @param pageNum 当前页码（从1开始）
     * @param pageSize 每页大小
     */
    public Map<String, Object> searchWithPagingAndMultiField(String index, String query, List<String> fields, 
            Map<String, String> fixedFields, int pageNum, int pageSize) throws IOException {
        
        // 将页码转换为from参数（页码从1开始，from从0开始）
        int from = (pageNum - 1) * pageSize;
        
        // 确保from不为负数
        from = Math.max(0, from);
        
        // 确保pageSize合理
        pageSize = Math.min(Math.max(1, pageSize), 100); // 限制每页最大100条
        
        BoolQuery.Builder boolQueryBuilder = new BoolQuery.Builder();

        if(StringUtils.isNotEmpty(query)){
            List<Query> queries = new ArrayList<>();
            
            // 1. 添加短语匹配查询（支持部分匹配）
            for (String field : fields) {
                // 完整短语匹配
                queries.add(MatchPhraseQuery.of(m -> m
                    .field(field)
                    .query(query)
                    .slop(0)           // 要求词必须完全相邻
                    .boost(10.0f)      // 给予最高权重
                )._toQuery());

                // 分词匹配（要求连续字符）
                queries.add(MatchQuery.of(m -> m
                    .field(field)
                    .query(query)
                    .analyzer("standard")
                    .minimumShouldMatch("50%")  // 设置50%匹配度
                    .operator(Operator.And)      // 使用AND操作符确保所有词都匹配
                    .boost(5.0f)
                )._toQuery());
            }

            // 2. 添加通配符查询（用于特殊字符匹配）
            for (String field : fields) {
                queries.add(WildcardQuery.of(w -> w
                    .field(field)
                    .wildcard("*" + query + "*")
                    .caseInsensitive(true)
                    .boost(2.0f)
                )._toQuery());
            }

            // 3. 将所有查询条件组合为should查询
            BoolQuery.Builder shouldQueries = new BoolQuery.Builder();
            for (Query q : queries) {
                shouldQueries.should(q);
            }
            
            // 4. 设置最小匹配数为1
            boolQueryBuilder.minimumShouldMatch("1")
                           .should(shouldQueries.build()._toQuery());
        }

        // 8. 处理固定字段查询
        if(fixedFields != null && !fixedFields.isEmpty()){
            for (Map.Entry<String, String> entry : fixedFields.entrySet()) {
                Query fixedFieldQuery = MatchQuery.of(builder -> builder
                        .field(entry.getKey())
                        .query(entry.getValue())
                        .analyzer("standard")   // 使用标准分词器
                        .operator(Operator.Or)
                )._toQuery();
                boolQueryBuilder.must(fixedFieldQuery);
            }
        }

        // 9. 创建最终的查询
        Query boolQuery = boolQueryBuilder.build()._toQuery();

        // 10. 配置高亮
        Map<String, HighlightField> highlightFields = new HashMap<>();
        if(StringUtils.isNotEmpty(query)) {
            for (String field : fields) {
                highlightFields.put(field, HighlightField.of(h -> h
                        .matchedFields(field)
                        .numberOfFragments(3)
                        .fragmentSize(150)
                        .preTags("<em>")
                        .postTags("</em>")
                ));
            }
        }

        Highlight highlight = Highlight.of(h -> h
                .fields(highlightFields)
                .requireFieldMatch(true)
                .fragmentSize(150)
                .numberOfFragments(3)
        );

        // 11. 创建并执行搜索请求，添加排序
        SearchRequest.Builder searchRequestBuilder = new SearchRequest.Builder()
            .index(index)
            .query(boolQuery)
            .from(from)
            .size(pageSize)
            .highlight(highlight)
            .trackTotalHits(builder -> builder.enabled(true));

        // 添加排序
        searchRequestBuilder.sort(sort -> sort
            .field(f -> f
                .field("idxPushDate")
                .order(SortOrder.Desc)
            )
        );

        SearchRequest searchRequest = searchRequestBuilder.build();

        // 添加详细日志
        log.debug("Search query: {}", searchRequest.toString());
        log.debug("Search fields: {}", fields);
        log.debug("Fixed fields: {}", fixedFields);
        log.debug("Pagination: page={}, size={}, from={}", pageNum, pageSize, from);
        log.debug("Sort by: idxPushDate DESC");

        // 执行搜索
        SearchResponse<Map> response = client.search(searchRequest, Map.class);
        
        // 记录结果
        log.debug("Total hits: {}", response.hits().total().value());
        log.debug("Search response: {}", response);

        List<Map<String, Object>> results = new ArrayList<>();
        
        // 处理搜索结果
        for (Hit<Map> hit : response.hits().hits()) {
            Map<String, Object> source = hit.source();
            if (source != null) {
                // 处理高亮结果
                Map<String, List<String>> highlights = hit.highlight();
                
                // 处理 idxTextContent 字段
                processTextContent(source, "idxTextContent", highlights, query);
                
                // 处理 appendixDocumentList 中的 idxTextContent
                if (source.containsKey("appendixDocumentList") && source.get("appendixDocumentList") instanceof List) {
                    List<Map<String, Object>> appendixList = (List<Map<String, Object>>) source.get("appendixDocumentList");
                    for (Map<String, Object> appendix : appendixList) {
                        processTextContent(appendix, "idxTextContent", highlights, query);
                    }
                }
                
                // 处理其他高亮字段
                if (highlights != null && !highlights.isEmpty()) {
                    for (Map.Entry<String, List<String>> entry : highlights.entrySet()) {
                        String field = entry.getKey();
                        if (!"idxTextContent".equals(field)) {  // 跳过已处理的 idxTextContent
                            List<String> highlightedValues = entry.getValue();
                            if (highlightedValues != null && !highlightedValues.isEmpty()) {
                                source.put(field, highlightedValues.get(0));
                            }
                        }
                    }
                }
                results.add(source);
            }
        }
        
        // 返回包含总数和结果的Map
        Map<String, Object> returnMap = new HashMap<>();
        returnMap.put("total", response.hits().total().value());
        returnMap.put("records", results);
        returnMap.put("pageNum", pageNum);
        returnMap.put("pageSize", pageSize);
        returnMap.put("pages", Math.ceil((double)response.hits().total().value() / pageSize));
        
        return returnMap;
    }

    /**
     * 处理文本内容字段
     * @param source 源数据Map
     * @param fieldName 字段名
     * @param highlights 高亮结果
     * @param query 搜索关键词
     */
    private void processTextContent(Map<String, Object> source, String fieldName, Map<String, List<String>> highlights, String query) {
        if (source.containsKey(fieldName)) {
            String content = String.valueOf(source.get(fieldName));
            
            // 如果有高亮结果且有搜索关键词
            if (highlights != null && highlights.containsKey(fieldName) && StringUtils.isNotEmpty(query)) {
                List<String> highlightedValues = highlights.get(fieldName);
                if (highlightedValues != null && !highlightedValues.isEmpty()) {
                    String highlightedText = highlightedValues.get(0);
                    // 获取高亮文本的前后文
                    int highlightStart = content.indexOf(highlightedText.replace("<em>", "").replace("</em>", ""));
                    if (highlightStart >= 0) {
                        int start = Math.max(0, highlightStart - 75);
                        int end = Math.min(content.length(), highlightStart + highlightedText.length() + 75);
                        String result = (start > 0 ? "..." : "") + 
                                      content.substring(start, highlightStart) +
                                      highlightedText +
                                      content.substring(highlightStart + highlightedText.length(), end) +
                                      (end < content.length() ? "..." : "");
                        source.put(fieldName, result);
                    }
                }
            } else {
                // 没有高亮结果，直接截取前100个字符
                int endIndex = Math.min(content.length(), 150);
                String result = content.substring(0, endIndex) + (content.length() > 150 ? "..." : "");
                source.put(fieldName, result);
            }
        }
    }

    /**
     * 批量操作
     *
     * @param index      索引名称
     * @param operations 批量操作列表
     * @return 是否成功
     */
    public boolean bulkOperations(String index, List<BulkOperation> operations) {
        try {
            BulkResponse response = client.bulk(b -> b
                    .index(index)
                    .operations(operations));
            for (BulkResponseItem item : response.items()) {
                if (item.error() != null) {
                    System.err.println("Bulk operation failed: " + item.error().reason());
                    return false;
                }
            }
            return true;
        } catch (IOException e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 关闭客户端
     */
    public void close() {
        try {
            client._transport().close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 检查索引是否存在
     *
     * @param index 索引名称
     * @return 是否存在
     */
    public boolean indexExists(String index) {
        try {
            return client.indices().exists(e -> e.index(index)).value();
        } catch (IOException e) {
            log.error("Failed to check index existence: {}", e.getMessage());
            return false;
        }
    }

    @Override
    protected void finalize() throws Throwable {
        close();
        super.finalize();
    }
}
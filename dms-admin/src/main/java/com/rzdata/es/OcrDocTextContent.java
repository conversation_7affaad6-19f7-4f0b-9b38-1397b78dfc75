package com.rzdata.es;

import com.rzdata.process.domain.BasicFile;
import com.rzdata.process.service.IBasicFileService;
import com.rzdata.process.service.IStoreFileService;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hslf.usermodel.HSLFSlideShow;
import org.apache.poi.hwpf.HWPFDocument;
import org.apache.poi.hwpf.extractor.WordExtractor;
import org.apache.poi.hwpf.usermodel.Range;
import org.apache.poi.poifs.filesystem.DirectoryNode;
import org.apache.poi.poifs.filesystem.DocumentEntry;
import org.apache.poi.poifs.filesystem.DocumentInputStream;
import org.apache.poi.poifs.filesystem.POIFSFileSystem;
import org.apache.poi.xslf.usermodel.XMLSlideShow;
import org.apache.poi.xwpf.extractor.XWPFWordExtractor;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.apache.poi.xwpf.usermodel.XWPFTable;
import org.apache.poi.xwpf.usermodel.XWPFTableRow;
import org.apache.poi.xwpf.usermodel.XWPFTableCell;

import java.io.*;
import java.nio.charset.StandardCharsets;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.text.PDFTextStripper;
import java.util.stream.Collectors;
import java.text.SimpleDateFormat;
import java.util.Arrays;

@Slf4j
@Service
public class OcrDocTextContent {

    @Autowired
    private IBasicFileService basicFileService;

    @Autowired
    private IStoreFileService iStoreFileService;

    /**
     * 获取文件内容 - 适用于小文件
     */
    public String ocrDocContext(String fileId) {
        try {
            BasicFile fileInfo = basicFileService.getById(fileId);
            log.info("开始处理文件: {}, 大小: {} bytes", fileInfo.getFileName(), fileInfo.getFileSize());

            String fileName = fileInfo.getFileName().toLowerCase();

            // 检查是否为图片文件
            if (isImageFile(fileName)) {
                log.info("图片文件不需要提取文本: {}", fileName);
                return "";
            }

            // 根据文件类型选择不同的处理方法
            if (fileName.endsWith(".xlsx") || fileName.endsWith(".xls")) {
                return extractExcelContent(fileInfo);
            } else if (fileName.endsWith(".ppt") || fileName.endsWith(".pptx")) {
                return extractPptContent(fileInfo);
            } else if (fileName.endsWith(".pdf")) {
                return extractPdfContent(fileInfo);
            } else if (fileName.endsWith(".doc") || fileName.endsWith(".docx")) {
                return extractWordContent(fileInfo);
            }
            
            // 处理其他文本文件
            return extractTextWithEncoding(fileInfo);
        } catch (Exception e) {
            log.error("获取文件内容失败：", e);
            return "";
        }
    }

    /**
     * 提取Word文档内容
     */
    private String extractWordContent(BasicFile fileInfo) {
        StringBuilder content = new StringBuilder();
        InputStream inputStream = null;
        
        try {
            String fileName = fileInfo.getFileName().toLowerCase();
            inputStream = iStoreFileService.getInputStream(fileInfo);
            
            // 检查输入流是否有效
            if (!isValidInputStream(inputStream)) {
                log.error("无效的输入流，文件名: {}", fileName);
                return "";
            }
            
            // 根据文件扩展名判断格式
            if (fileName.endsWith(".doc")) {
                // 处理 OLE2 格式 (.doc)
                try {
                    POIFSFileSystem fs = null;
                    try {
                        fs = new POIFSFileSystem(inputStream);
                        DirectoryNode root = fs.getRoot();
                        
                        // 尝试使用多种方法提取文本
                        StringBuilder docText = new StringBuilder();
                        boolean hasValidContent = false;
                        
                        // 方法2: 如果方法1失败，尝试直接读取 WordDocument 流
                        if (root.hasEntry("WordDocument")) {
                            DocumentEntry documentEntry = (DocumentEntry) root.getEntry("WordDocument");
                            try (DocumentInputStream dis = fs.createDocumentInputStream("WordDocument")) {
                                byte[] bytes = new byte[documentEntry.getSize()];
                                dis.read(bytes);
                                
                                // 尝试不同的编码组合
                                String[][] encodingGroups = {
                                    {"GB18030", "GBK", "GB2312"},  // 中文编码组
                                    {"UTF-16LE", "UTF-16BE"},      // Unicode编码组
                                    {"UTF-8", "ISO-8859-1"},       // 其他常用编码
                                    {"Cp1252", "Cp936"}            // Windows编码
                                };
                                
                                // 遍历每个编码组
                                for (String[] encodingGroup : encodingGroups) {
                                    for (String encoding : encodingGroup) {
                                        try {
                                            String text = new String(bytes, encoding);
                                            // 检查文本是否包含有效的中文内容
                                            if (containsChineseChars(text) && containsValidText(text)) {
                                                // 按行处理文本
                                                String[] lines = text.split("\\r?\\n");
                                                for (String line : lines) {
                                                    if (isValidChineseParagraph(line)) {
                                                        docText.append(line.trim()).append("\n");
                                                        hasValidContent = true;
                                                    }
                                                }
                                                if (hasValidContent) {
                                                    log.info("成功使用编码 {} 解析文档", encoding);
                                                    break;
                                                }
                                            }
                                        } catch (Exception e) {
                                            log.debug("使用编码 {} 解析失败", encoding);
                                            continue;
                                        }
                                    }
                                    if (hasValidContent) break;
                                }
                            }
                        }
                        
                        // 方法3: 如果前两种方法都失败，尝试使用 HWPFDocument
                        if (!hasValidContent) {
                            try {
                                inputStream.reset();
                                HWPFDocument doc = new HWPFDocument(fs);
                                Range range = doc.getRange();
                                for (int i = 0; i < range.numParagraphs(); i++) {
                                    String text = range.getParagraph(i).text();
                                    if (isValidChineseParagraph(text)) {
                                        docText.append(text.trim()).append("\n");
                                        hasValidContent = true;
                                    }
                                }
                            } catch (Exception e) {
                                log.debug("HWPFDocument 提取失败", e);
                            }
                        }
                        
                    } finally {
                        if (fs != null) {
                            try {
                                fs.close();
                            } catch (IOException e) {
                                log.error("关闭文件系统失败", e);
                            }
                        }
                    }
                } catch (Exception e) {
                    log.error("处理DOC文件失败：", e);
                }
            } else if (fileName.endsWith(".docx")) {
                // 处理 OOXML 格式 (.docx)
                try {
                    XWPFDocument docx = new XWPFDocument(inputStream);
                    try {
                        // 使用 XWPFWordExtractor 提取文本
                        XWPFWordExtractor extractor = new XWPFWordExtractor(docx);
                        try {
                            content.append(extractor.getText());
                            
                            // 额外提取表格内容，确保完整性
                            for (XWPFTable table : docx.getTables()) {
                                for (XWPFTableRow row : table.getRows()) {
                                    for (XWPFTableCell cell : row.getTableCells()) {
                                        content.append(cell.getText()).append(" ");
                                    }
                                    content.append("\n");
                                }
                                content.append("\n");
                            }
                        } finally {
                            extractor.close();
                        }
                    } finally {
                        docx.close();
                    }
                } catch (Exception e) {
                    log.error("处理DOCX文件失败：", e);
                }
            }

            // 处理编码，确保输出UTF-8格式
            String result = new String(content.toString().getBytes(StandardCharsets.UTF_8), StandardCharsets.UTF_8);

            // 清理和格式化文本
            result = cleanAndFormatText(result);

            return result.trim();

        } catch (Exception e) {
            log.error("提取Word文档内容失败: ", e);
            return "";
        } finally {
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    log.error("关闭输入流失败: ", e);
                }
            }
        }
    }

    /**
     * 检查输入流是否有效
     */
    private boolean isValidInputStream(InputStream inputStream) {
        if (inputStream == null) {
            return false;
        }

        try {
            // 标记当前位置
            if (!inputStream.markSupported()) {
                inputStream = new BufferedInputStream(inputStream);
            }
            inputStream.mark(1024);

            // 尝试读取一个字节
            int firstByte = inputStream.read();

            // 重置到标记位置
            inputStream.reset();

            // 如果读取的第一个字节是-1，说明流是空的
            if (firstByte == -1) {
                log.warn("输入流为空");
                return false;
            }

            // 检查可用字节数
            int available = inputStream.available();
            if (available == 0) {
                log.warn("输入流没有可用数据");
                return false;
            }

            return true;
        } catch (IOException e) {
            log.error("检查输入流时发生错误: ", e);
            return false;
        }
    }

    /**
     * 清理和格式化文本
     */
    private String cleanAndFormatText(String text) {
        if (text == null || text.isEmpty()) {
            return "";
        }

        // 1. 移除多余的空白字符
        text = text.replaceAll("\\s+", " ");

        // 2. 移除重复的换行
        text = text.replaceAll("\\n\\s*\\n", "\n");

        // 3. 移除特殊字符
        text = text.replaceAll("[\\x00-\\x08\\x0B\\x0C\\x0E-\\x1F]", "");

        // 4. 处理中文标点前后的空格
        text = text.replaceAll("\\s*([，。！？；：、])\\s*", "$1");

        // 5. 确保段落之间有适当的间隔
        text = text.replaceAll("\\n{3,}", "\n\n");

        return text.trim();
    }

    /**
     * 提取Excel文件内容
     */
    private String extractExcelContent(BasicFile fileInfo) {
        StringBuilder content = new StringBuilder();
        try (InputStream inputStream = iStoreFileService.getInputStream(fileInfo)) {
            String fileName = fileInfo.getFileName().toLowerCase();
            
            if (fileName.endsWith(".xlsx")) {
                // 处理XLSX文件
                try (org.apache.poi.xssf.usermodel.XSSFWorkbook workbook = 
                        new org.apache.poi.xssf.usermodel.XSSFWorkbook(inputStream)) {
                    processWorkbook(workbook, content);
                }
            } else {
                // 处理XLS文件
                try (org.apache.poi.hssf.usermodel.HSSFWorkbook workbook = 
                        new org.apache.poi.hssf.usermodel.HSSFWorkbook(inputStream)) {
                    processWorkbook(workbook, content);
                }
            }

            // 处理提取的文本
            return cleanText(content.toString());
        } catch (Exception e) {
            log.error("Excel文件内容提取失败：{}", e.getMessage());
            return "";
        }
    }

    /**
     * 处理工作簿内容
     */
    private void processWorkbook(org.apache.poi.ss.usermodel.Workbook workbook, StringBuilder content) {
        for (int i = 0; i < workbook.getNumberOfSheets(); i++) {
            org.apache.poi.ss.usermodel.Sheet sheet = workbook.getSheetAt(i);
            if (sheet == null) continue;

            // 添加工作表名称
            content.append("【").append(sheet.getSheetName()).append("】\n");

            // 处理每一行
            for (org.apache.poi.ss.usermodel.Row row : sheet) {
                if (row == null) continue;

                // 处理每个单元格
                for (org.apache.poi.ss.usermodel.Cell cell : row) {
                    if (cell == null) continue;

                    String cellValue = getCellValueAsString(cell);
                    if (!cellValue.isEmpty()) {
                        content.append(cellValue).append("\t");
                    }
                }
                content.append("\n");
            }
            content.append("\n");
        }
    }

    /**
     * 获取单元格的字符串值
     */
    private String getCellValueAsString(org.apache.poi.ss.usermodel.Cell cell) {
        if (cell == null) return "";

        try {
            switch (cell.getCellType()) {
                case STRING:
                    return cell.getStringCellValue().trim();
                case NUMERIC:
                    if (org.apache.poi.ss.usermodel.DateUtil.isCellDateFormatted(cell)) {
                        return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
                                .format(cell.getDateCellValue());
                    }
                    // 避免数字显示为科学计数法
                    return String.valueOf(cell.getNumericCellValue());
                case BOOLEAN:
                    return String.valueOf(cell.getBooleanCellValue());
                case FORMULA:
                    try {
                        return String.valueOf(cell.getNumericCellValue());
                    } catch (Exception e) {
                        try {
                            return cell.getStringCellValue();
                        } catch (Exception ex) {
                            return "";
                        }
                    }
                default:
                    return "";
            }
        } catch (Exception e) {
            log.debug("获取单元格值失败", e);
            return "";
        }
    }

    /**
     * 提取PPT文档内容
     */
    private String extractPptContent(BasicFile fileInfo) {
        StringBuilder content = new StringBuilder();
        InputStream inputStream = null;
        try {
            inputStream = iStoreFileService.getInputStream(fileInfo);
            String fileName = fileInfo.getFileName().toLowerCase();

            // 先尝试判断文件格式
            boolean isXmlFormat = false;
            try {
                // 保存原始输入流的内容
                ByteArrayOutputStream baos = new ByteArrayOutputStream();
                byte[] buffer = new byte[4096];
                int bytesRead;
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    baos.write(buffer, 0, bytesRead);
                }
                byte[] bytes = baos.toByteArray();

                // 检查文件格式
                // Office 2007+ XML文件的特征是以PK开头
                isXmlFormat = bytes.length >= 2 && bytes[0] == 'P' && bytes[1] == 'K';

                // 重新创建输入流
                inputStream = new ByteArrayInputStream(bytes);
            } catch (Exception e) {
                log.error("检查文件格式失败：", e);
                return "";
            }

            if (isXmlFormat || fileName.endsWith(".pptx")) {
                // 处理PPTX文件 (Office 2007+ XML格式)
                try {
                XMLSlideShow ppt = new XMLSlideShow(inputStream);
                    for (org.apache.poi.xslf.usermodel.XSLFSlide slide : ppt.getSlides()) {
                        //content.append("【幻灯片】\n");
                        
                        // 获取所有形状
                        for (org.apache.poi.xslf.usermodel.XSLFShape shape : slide.getShapes()) {
                            if (shape instanceof org.apache.poi.xslf.usermodel.XSLFTextShape) {
                                org.apache.poi.xslf.usermodel.XSLFTextShape textShape = 
                                    (org.apache.poi.xslf.usermodel.XSLFTextShape) shape;
                                String text = textShape.getText();
                                if (text != null && !text.trim().isEmpty()) {
                                    content.append(text.trim()).append("\n");
                                }
                            }
                        }
                        content.append("\n");
                    }
                ppt.close();
                } catch (Exception e) {
                    log.error("处理PPTX文件失败：", e);
                }
            } else {
                // 处理PPT文件 (OLE2格式)
                try {
                    POIFSFileSystem fs = new POIFSFileSystem(inputStream);
                    DirectoryNode root = fs.getRoot();
                    
                    // 尝试读取Summary Information
                    if (root.hasEntry("SummaryInformation")) {
                        try (DocumentInputStream dis = fs.createDocumentInputStream("SummaryInformation")) {
                            org.apache.poi.hpsf.SummaryInformation si = 
                                (org.apache.poi.hpsf.SummaryInformation)
                                org.apache.poi.hpsf.PropertySetFactory.create(dis);
                            
                    // 获取标题
                            String title = si.getTitle();
                            if (title != null && !title.trim().isEmpty()) {
                                content.append("标题: ").append(title).append("\n");
                            }
                            
                            // 获取主题
                            String subject = si.getSubject();
                            if (subject != null && !subject.trim().isEmpty()) {
                                content.append("主题: ").append(subject).append("\n");
                            }
                            
                            // 获取备注
                            String comments = si.getComments();
                            if (comments != null && !comments.trim().isEmpty()) {
                                content.append("备注: ").append(comments).append("\n");
                            }
                        }
                    }
                    fs.close();
                } catch (Exception e) {
                    log.error("处理PPT文件失败：", e);
                }
            }

            String result = cleanText(content.toString());
            if (result.trim().isEmpty()) {
                log.warn("未能提取到有效的PPT文本内容");
                return "无法提取文本内容";
            }
            return result;

        } catch (Exception e) {
            log.error("PPT文件内容提取失败：{}", e.getMessage());
            return "";
        } finally {
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    log.error("关闭输入流失败", e);
                }
            }
        }
    }

    /**
     * 清理文本内容
     */
    private String cleanText(String text) {
        if (text == null || text.isEmpty()) {
            return "";
        }

        // 移除控制字符，但保留换行和制表符
        text = text.replaceAll("[\\p{Cntrl}&&[^\r\n\t]]", "");
        
        // 移除不可打印字符
        text = text.replaceAll("[\\p{C}&&[^\n\r\t]]", "");
        
        // 规范化空白字符
        text = text.replaceAll("\\s+", " ");
        
        // 移除特殊字符
        text = text.replaceAll("[\\x00-\\x08\\x0B\\x0C\\x0E-\\x1F]", "");
        
        return text.trim();
    }

    /**
     * 验证文本是否有效
     */
    private boolean isValidText(String text) {
        if (text == null || text.trim().isEmpty()) {
            return false;
        }

        // 检查文本长度
        if (text.length() < 2) {
        return false;
        }

        // 统计有效字符数
        int validChars = 0;
        int totalChars = 0;

        for (char c : text.toCharArray()) {
            totalChars++;
            if (Character.isLetterOrDigit(c) || 
                Character.isWhitespace(c) || 
                isPunctuation(c) || 
                isChineseChar(c)) {
                validChars++;
            }
        }

        // 要求有效字符占比超过50%
        return totalChars > 0 && (double)validChars / totalChars > 0.5;
    }

    /**
     * 提取PDF文件内容
     */
    private String extractPdfContent(BasicFile fileInfo) {
        try (InputStream inputStream = iStoreFileService.getInputStream(fileInfo);
             PDDocument document = PDDocument.load(inputStream)) {

            PDFTextStripper stripper = new PDFTextStripper();
            // 设置提取参数
            stripper.setSortByPosition(true);
            stripper.setStartPage(1);
            stripper.setEndPage(document.getNumberOfPages());

            // 提取文本
            String text = stripper.getText(document);

            // 处理PDF提取的文本
            text = processPdfText(text);

            log.info("PDF文件处理完成，提取文本长度: {}", text.length());
            return text;
        } catch (Exception e) {
            log.error("PDF文件内容提取失败：{}", e.getMessage());
            return "";
        }
    }

    /**
     * 处理PDF提取的文本
     */
    private String processPdfText(String text) {
        if (text == null || text.isEmpty()) {
            return "";
        }

        // 替换特殊字符和多余的空白
        text = text.replaceAll("[\\x00-\\x08\\x0B\\x0C\\x0E-\\x1F]", ""); // 移除控制字符
        text = text.replaceAll("\\s+", " "); // 合并多个空白字符

        // 处理常见的PDF乱码字符
        text = text.replaceAll("□", "");
        text = text.replaceAll("○", "");
        text = text.replaceAll("●", "");
        text = text.replaceAll("■", "");
        text = text.replaceAll("▲", "");
        text = text.replaceAll("▼", "");
        text = text.replaceAll("◆", "");
        text = text.replaceAll("◇", "");

        // 移除零宽字符
        text = text.replaceAll("\\u200B|\\u200C|\\u200D|\\uFEFF", "");

        return text.trim();
    }

    /**
     * 使用编码检测提取文本内容
     */
    private String extractTextWithEncoding(BasicFile fileInfo) {
        try (InputStream inputStream = iStoreFileService.getInputStream(fileInfo);
             ByteArrayOutputStream result = new ByteArrayOutputStream()) {

            // 读取所有字节
            byte[] buffer = new byte[4096];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                result.write(buffer, 0, bytesRead);
            }
            byte[] bytes = result.toByteArray();

            // 检测编码
            String encoding = detectFileEncoding(bytes);
            log.info("文件编码检测结果: {}", encoding);

            // 尝试不同的编码
            String[] encodings = {encoding, "UTF-8", "GBK", "GB18030", "GB2312", "BIG5"};
            String bestResult = null;
            int maxValidChars = 0;

            for (String enc : encodings) {
                try {
                    String decoded = new String(bytes, enc);
                    int validChars = countValidChars(decoded);

                    if (validChars > maxValidChars) {
                        maxValidChars = validChars;
                        bestResult = decoded;
                        log.debug("使用编码 {} 解析得到有效字符数: {}", enc, validChars);
                    }
                } catch (Exception e) {
                    log.debug("编码 {} 解析失败", enc);
                }
            }

            if (bestResult != null) {
                // 处理文本内容
                bestResult = processTextContent(bestResult);
                log.info("文本解析完成，使用最佳编码: {}, 有效字符数: {}", encoding, maxValidChars);
                return bestResult;
            }

            // 如果所有编码都失败，使用默认编码
            return new String(bytes, "GB18030");
        } catch (Exception e) {
            log.error("文本内容提取失败：", e);
            return "";
        }
    }

    /**
     * 处理文本内容
     */
    private String processTextContent(String text) {
        if (text == null || text.isEmpty()) {
            return "";
        }

        // 移除不可见字符
        text = text.replaceAll("[\\p{C}]", "");

        // 规范化空白字符
        text = text.replaceAll("\\s+", " ");

        // 移除特殊标点符号
        text = text.replaceAll("[\\p{Ps}\\p{Pe}\\p{Pi}\\p{Pf}]", "");

        return text.trim();
    }

    /**
     * 统计有效字符数量
     */
    private int countValidChars(String text) {
        if (text == null || text.isEmpty()) {
            return 0;
        }

        int count = 0;
        for (char c : text.toCharArray()) {
            if (isValidChar(c)) {
                count++;
            }
        }
        return count;
    }

    /**
     * 判断是否为有效字符
     */
    private boolean isValidChar(char c) {
        // 中文字符范围
        if (c >= 0x4E00 && c <= 0x9FFF) {
            return true;
        }

        // ASCII可打印字符
        if (c >= 0x20 && c <= 0x7E) {
            return true;
        }

        // 中文标点符号
        if (c >= 0x3000 && c <= 0x303F) {
            return true;
        }

        // 全角字符
        if (c >= 0xFF00 && c <= 0xFFEF) {
            return true;
        }

        return false;
    }

    /**
     * 检测文件编码
     */
    private String detectFileEncoding(byte[] bytes) {
        // 检查BOM标记
        if (bytes.length >= 3) {
            if (bytes[0] == (byte)0xEF && bytes[1] == (byte)0xBB && bytes[2] == (byte)0xBF) {
                return "UTF-8";
            }
            if (bytes[0] == (byte)0xFE && bytes[1] == (byte)0xFF) {
                return "UTF-16BE";
            }
            if (bytes[0] == (byte)0xFF && bytes[1] == (byte)0xFE) {
                return "UTF-16LE";
            }
        }

        // 尝试检测中文编码
        try {
            // 先尝试GB18030（包含GB2312和GBK）
            String text = new String(bytes, "GB18030");
            if (containsChineseChars(text)) {
                return "GB18030";
            }

            // 尝试UTF-8
            text = new String(bytes, "UTF-8");
            if (containsChineseChars(text)) {
                return "UTF-8";
            }

            // 尝试其他编码
            String[] encodings = {"GBK", "GB2312", "BIG5"};
            for (String encoding : encodings) {
                try {
                    text = new String(bytes, encoding);
                    if (containsChineseChars(text)) {
                        return encoding;
                    }
                } catch (Exception e) {
                    continue;
                }
            }
        } catch (Exception e) {
            log.debug("编码检测过程中出现异常", e);
        }

        // 默认返回GB18030
        return "GB18030";
    }

    /**
     * 检查是否包含中文字符
     */
    private boolean containsChineseChars(String str) {
        if (str == null || str.isEmpty()) {
            return false;
        }

        int chineseCount = 0;
        int totalCount = 0;

        for (char c : str.toCharArray()) {
            if (c > 127) {
                totalCount++;
                // 检查是否是中文字符（CJK统一汉字）
                if (c >= 0x4E00 && c <= 0x9FFF) {
                    chineseCount++;
                }
            }
        }

        // 如果非ASCII字符中超过40%是中文，则认为是中文内容
        return totalCount > 0 && (double)chineseCount / totalCount > 0.4;
    }

    /**
     * 判断是否是中文字符
     */
    private boolean isChineseChar(char c) {
        // 基本汉字范围
        if (c >= 0x4E00 && c <= 0x9FFF) {
            return true;
        }

        // 扩展汉字范围
        if (c >= 0x3400 && c <= 0x4DBF) {  // CJK扩展A
            return true;
        }
        if (c >= 0x20000 && c <= 0x2A6DF) {  // CJK扩展B
            return true;
        }
        if (c >= 0x2A700 && c <= 0x2B73F) {  // CJK扩展C
            return true;
        }

        // 兼容汉字范围
        return c >= 0xF900 && c <= 0xFAFF;  // CJK兼容汉字
    }

    /**
     * 检查文本是否包含有效内容
     */
    private boolean containsValidText(String text) {
        if (text == null || text.trim().isEmpty()) {
            return false;
        }

        // 检查是否包含可打印字符
        for (char c : text.toCharArray()) {
            if (Character.isLetterOrDigit(c) ||
                Character.isWhitespace(c) ||
                isPunctuation(c) ||
                isChineseChar(c)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 检查段落是否包含有效的中文内容
     */
    private boolean isValidChineseParagraph(String paragraph) {
        if (paragraph == null || paragraph.trim().isEmpty()) {
            return false;
        }

        // 移除特殊字符和控制字符
        paragraph = paragraph.replaceAll("[\\x00-\\x1F\\x7F-\\x9F]", "").trim();

        // 如果清理后为空，返回false
        if (paragraph.isEmpty()) {
            return false;
        }

        // 统计有效字符
        int validChars = 0;
        int chineseChars = 0;

        for (char c : paragraph.toCharArray()) {
            if (isChineseChar(c)) {
                chineseChars++;
                validChars++;
            } else if (Character.isLetterOrDigit(c) ||
                      isPunctuation(c) ||
                      Character.isWhitespace(c)) {
                validChars++;
            }
        }

        // 要求：
        // 1. 总长度至少2个字符
        // 2. 有效字符占比超过80%
        // 3. 包含至少1个中文字符
        int totalChars = paragraph.length();
        return totalChars >= 2 &&
               ((double)validChars / totalChars) > 0.8 &&
               chineseChars > 0;
    }

    /**
     * 判断是否是标点符号
     */
    private boolean isPunctuation(char c) {
        // 常用标点范围
        if (c >= 0x2000 && c <= 0x206F) {
            return true;
        }

        // CJK标点符号范围
        if (c >= 0x3000 && c <= 0x303F) {
            return true;
        }

        // 全角字符范围
        if (c >= 0xFF00 && c <= 0xFFEF) {
            return true;
        }

        // ASCII标点符号
        if (".,:;!?\"'()[]{}/-_+=<>@#$%^&*~`|\\".indexOf(c) >= 0) {
            return true;
        }

        // 常用中文标点的Unicode编码
        return c == 0x3001 || // 、
               c == 0x3002 || // 。
               c == 0xFF0C || // ，
               c == 0xFF01 || // ！
               c == 0xFF1F || // ？
               c == 0xFF1B || // ；
               c == 0xFF1A || // ：
               c == 0x201C || // "
               c == 0x201D || // "
               c == 0x2018 || // '
               c == 0x2019 || // '
               c == 0xFF08 || // （
               c == 0xFF09 || // ）
               c == 0x3010 || // 【
               c == 0x3011 || // 】
               c == 0x300A || // 《
               c == 0x300B;   // 》
    }

    /**
     * 判断是否为图片文件
     */
    private boolean isImageFile(String fileName) {
        String[] imageExtensions = {".jpg", ".jpeg", ".png", ".gif", ".bmp", ".tiff", ".webp"};
        for (String ext : imageExtensions) {
            if (fileName.endsWith(ext)) {
                return true;
            }
        }
        return false;
    }

}

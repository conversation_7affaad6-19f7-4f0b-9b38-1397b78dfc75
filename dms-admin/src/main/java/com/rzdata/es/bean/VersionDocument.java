package com.rzdata.es.bean;

import lombok.Data;

import java.util.List;

/**
 * @Description: 文档对象
 * @author: gj
 * @date 2023/4/13
 */
@Data
public class VersionDocument {
    //业务主键Id
    private String idxId;
    //文件编号
    private String idxDocId;
    //文件名称
    private String idxDocName;
    //文件版本号
    private String idxVersionValue;
    //文件状态
    private String idxStatus;
    //文件状态名称
    private String idxStatusName;
    //物料编号
    private String idxPartNumber;
    //文件类型
    private String idxDataType;
    //文件类型名称
    private String idxDataTypeName;
    //编制人
    private String idxUserName;
    //内部文件编号
    private String idxInternalDocId;
    //发布时间
    private String idxPushDate;
    //关联类型: DOC正文 APPENDIX附件 RECORD记录 REF_DOC关联其他文件
    private String idxLinkType;
    //文件内容
    private String idxTextContent;
    //文件id
    private String idxFileId;
    //附件内容
    private List<AppendixDocument> appendixDocumentList;

    /**
     * 是否需要显示子记录，0需要，1不需要
     */
    private String hasChild;

    private Boolean hasPerms;

    private Boolean isBorrow;
}

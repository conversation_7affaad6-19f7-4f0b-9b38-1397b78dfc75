package com.rzdata.es.controller;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.rzdata.es.bean.VersionDocument;
import com.rzdata.es.service.ElasticsearchService;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.core.validate.QueryGroup;
import com.rzdata.framework.utils.StringUtils;
import com.rzdata.process.domain.Version;
import com.rzdata.process.domain.bo.ApplyRelationBo;
import com.rzdata.process.domain.bo.DocLinkLogBo;
import com.rzdata.process.domain.vo.ApplyRelationVo;
import com.rzdata.process.domain.vo.DocLinkSearchResultVo;
import com.rzdata.process.service.IVersionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.qiyuesuo.sdk.api.VersionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@Validated
@Api(value = "ES控制器", tags = {"ES管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/es")
public class ElasticsearchController {

    @Autowired
    private ElasticsearchService elasticsearchService;

    @Autowired
    private IVersionService versionService;

    /**
     * 单个es处理
     * @param id
     */
    @GetMapping("/getVesionId/{id}")
    public void getVesionId(@PathVariable("id") String id) {
        Version byId = versionService.getById(id);
        elasticsearchService.docDelPublish(byId);
        elasticsearchService.docPublish(byId);
    }

    /**
     * 同步所有有效文件到ES
     */
    @GetMapping("/syncVesionEs")
    public void syncVesionEs() {
        List<Version> list = versionService.list(new LambdaQueryWrapper<Version>()
                .eq(Version::getStatus, 1)
        );
        if(StringUtils.isNotEmpty(list)){
            long l = System.currentTimeMillis();
            log.info("开始："+l);
            for(Version version:list){
                //ES 操作先删后插
                try {
                    elasticsearchService.docDelPublish(version);
                    elasticsearchService.docPublish(version);
                } catch (Exception e) {

                }
            }
            log.info("结束："+(System.currentTimeMillis()-l));
        }
    }


    /**
     * 首页关键字查询文件关联记录列表
     */
    @ApiOperation("首页关键字查询文件关联记录列表")
    @GetMapping("/search/list")
    public TableDataInfo<DocLinkSearchResultVo> searchList(@Validated(QueryGroup.class) DocLinkLogBo bo) {
        return elasticsearchService.docEsLinkSearchPage(bo);
    }

}

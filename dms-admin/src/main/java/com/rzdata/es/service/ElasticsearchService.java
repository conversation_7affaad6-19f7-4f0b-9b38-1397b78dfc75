package com.rzdata.es.service;


import com.rzdata.es.bean.VersionDocument;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.process.domain.DocLinkLog;
import com.rzdata.process.domain.Version;
import com.rzdata.process.domain.bo.DocLinkLogBo;
import com.rzdata.process.domain.vo.DocLinkSearchResultVo;
import org.springframework.stereotype.Service;


public interface ElasticsearchService {

    /**
     * 文件发布
     * @param version
     */
    void docPublish(Version version);


    /**
     * 文件修订发布
     * @param newVersion
     */
    void docEditPublish(Version newVersion,Version lastVersion);


    /**
     * 文件作废
     */
    void docDelPublish(Version version);


    /**
     * Es分页查询
     * @param bo
     * @return
     */
    TableDataInfo<DocLinkSearchResultVo> docEsLinkSearchPage(DocLinkLogBo bo);

}

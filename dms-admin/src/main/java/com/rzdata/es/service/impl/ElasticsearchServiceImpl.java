package com.rzdata.es.service.impl;

import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.protobuf.ServiceException;
import com.rzdata.es.ElasticsearchUtil;
import com.rzdata.es.OcrDocTextContent;
import com.rzdata.es.bean.AppendixDocument;
import com.rzdata.es.service.ElasticsearchService;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.utils.PageUtils;
import com.rzdata.framework.utils.StringUtils;
import com.rzdata.process.domain.BasicFile;
import com.rzdata.process.domain.DocLinkLog;
import com.rzdata.process.domain.DocVersionLink;
import com.rzdata.process.domain.Version;
import com.rzdata.process.domain.bo.DocLinkLogBo;
import com.rzdata.process.domain.vo.BasicFileVo;
import com.rzdata.process.domain.vo.DocLinkLogVo;
import com.rzdata.process.domain.vo.DocLinkSearchResultVo;
import com.rzdata.process.domain.vo.DocVersionLinkVo;
import com.rzdata.process.enums.LinkTypeEnum;
import com.rzdata.process.service.*;
import com.rzdata.system.service.ISysDictDataService;
import com.rzdata.system.service.ISysDictTypeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import co.elastic.clients.elasticsearch._types.mapping.Property;
import com.google.common.collect.Maps;

/**
 * ES 业务处理类
 */
@Slf4j
@Service
public class ElasticsearchServiceImpl implements ElasticsearchService {

    @Autowired
    private IDocVersionLinkService docVersionLinkService;

    @Autowired
    private IDocLinkLogService docLinkLogService;

    @Autowired
    private ISysDictDataService dictDataService;

    @Autowired
    private IStandardService standardService;

    @Autowired
    private OcrDocTextContent ocrDocContext;

    @Value("${es.host}")
    private String esHost;

    @Value("${es.port}")
    private int esPort;

    @Value("${es.username}")
    private String username;

    @Value("${es.password}")
    private String password;

    @Value("${es.indexname}")
    private String indexname;

    @Value("${es.enabled}")
    private boolean enabled;

    //private static final String INDEX_NAME = "version_index";
    private static final String DICT_TYPE_STATUS = "standard_status";


    /**
     * Es分页查询
     * @param bo
     * @return
     */
    @Override
    public TableDataInfo<DocLinkSearchResultVo> docEsLinkSearchPage(DocLinkLogBo bo) {
        //初始化客户端
        ElasticsearchUtil util = new ElasticsearchUtil(esHost,esPort,username,password);
        try {
            if("content".equals(bo.getSearchType())){
                Map<String, Object> searchResult = util.searchWithPagingAndMultiField(
                        indexname, // 索引名称
                        bo.getKeyword(),  // 查询关键字
                        Arrays.asList("idxTextContent", "appendixDocumentList.idxTextContent"), // 查询字段
                        null, // 固定字段和值
                        bo.getPageNum(), // 分页起始位置
                        bo.getPageSize() // 每页大小
                );
                return pageChuLi(searchResult);
            }
            Map<String, Object> searchResult = util.searchWithPagingAndMultiField(
                    indexname, // 索引名称
                    bo.getKeyword(),  // 查询关键字
                    Arrays.asList("idxDocId", "idxDocName", "idxVersionValue", "idxStatusName", "idxDataTypeName", "idxUserName", "idxInternalDocId"), // 查询字段
                    null, // 固定字段和值
                    bo.getPageNum(), // 分页起始位置
                    bo.getPageSize() // 每页大小
            );
            return pageChuLi(searchResult);

        } catch (Exception e) {
            log.error("ES查询异常", e);
            return new TableDataInfo<>();
        } finally {
            util.close();
        }
    }

    private TableDataInfo<DocLinkSearchResultVo> pageChuLi(Map<String, Object> searchResult){
        List<DocLinkSearchResultVo> resultVos = new ArrayList<>();
        if(searchResult.get("records") != null){
            JSONObject jsonObject = new JSONObject(searchResult);
            List<Map<String, Object>> records = (List<Map<String, Object>>) jsonObject.get("records");
            resultVos = records.stream().map(record -> {
                DocLinkSearchResultVo docLinkSearchResultVo = new DocLinkSearchResultVo();
                docLinkSearchResultVo.setVersionId((String) record.get("idxId"));
                docLinkSearchResultVo.setDocId((String) record.get("idxDocId"));
                docLinkSearchResultVo.setDocName((String) record.get("idxDocName"));
                docLinkSearchResultVo.setVersionValue((String) record.get("idxVersionValue"));
                docLinkSearchResultVo.setStatus((String) record.get("idxStatus"));
                docLinkSearchResultVo.setStatusName((String) record.get("idxStatusName"));
                docLinkSearchResultVo.setDataType((String) record.get("idxDataType"));
                docLinkSearchResultVo.setDataTypeName((String) record.get("idxDataTypeName"));
                docLinkSearchResultVo.setUserName((String) record.get("idxUserName"));
                docLinkSearchResultVo.setInternalDocId((String) record.get("idxInternalDocId"));
                docLinkSearchResultVo.setPushDate((String) record.get("idxPushDate"));
                docLinkSearchResultVo.setLinkType((String) record.get("idxLinkType"));
                docLinkSearchResultVo.setFileId((String) record.get("idxFileId"));
                if(record.get("idxTextContent")!=null){
                    docLinkSearchResultVo.setTextContent((String) record.get("idxTextContent"));
                }
                if(record.get("appendixDocumentList")!=null){
                    docLinkSearchResultVo.setAppendixDocumentList(com.alibaba.fastjson.JSONObject.parseArray(record.get("appendixDocumentList").toString(), AppendixDocument.class));
                }
                return docLinkSearchResultVo;
            }).collect(Collectors.toList());
        }
        Page<DocLinkSearchResultVo> page = new Page<>();
        page.setRecords(resultVos);
        if(searchResult.get("total") != null){
            page.setTotal(((Number) searchResult.get("total")).longValue());  // 设置实际的总数
        }
        return PageUtils.buildDataInfo(docLinkLogService.docLinkSearchDataBuild(page));
    }


    /**
     * 获取状态字典值
     * @param status 状态码
     * @return 状态名称
     */
    private String getStatusDictLabel(String status) {
        return dictDataService.selectDictLabel(DICT_TYPE_STATUS, status);
    }

    /**
     * 初始化索引映射
     * @return 返回ES索引映射配置
     */
    private Map<String, Property> createVersionMapping() {
        try {
            Map<String, Property> mappings = Maps.newHashMap();
            
            // 基础字段映射
            mappings.put("idxId", Property.of(p -> p.keyword(k -> k)));
            
            // 使用标准分词器的字段
            mappings.put("idxDocId", Property.of(p -> p
                    .text(t -> t
                            .analyzer("standard")
                            .searchAnalyzer("standard")
                    )
            ));
            mappings.put("idxDocName", Property.of(p -> p
                    .text(t -> t
                            .analyzer("standard")
                            .searchAnalyzer("standard")
                    )
            ));

            // 版本和状态相关字段
            mappings.put("idxVersionValue", Property.of(p -> p
                    .text(t -> t
                            .analyzer("standard")
                            .searchAnalyzer("standard")
                    )
            ));
            mappings.put("idxStatus", Property.of(p -> p.keyword(k -> k)));
            mappings.put("idxStatusName", Property.of(p -> p
                    .text(t -> t
                            .analyzer("standard")
                            .searchAnalyzer("standard")
                    )
            ));

            // 文档类型相关字段
            mappings.put("idxDataType", Property.of(p -> p.keyword(k -> k)));
            mappings.put("idxDataTypeName", Property.of(p -> p
                    .text(t -> t
                            .analyzer("standard")
                            .searchAnalyzer("standard")
                    )
            ));
            mappings.put("idxPartNumber", Property.of(p -> p.keyword(k -> k)));

            // 用户和时间相关字段
            mappings.put("idxUserName", Property.of(p -> p
                    .text(t -> t
                            .analyzer("standard")
                            .searchAnalyzer("standard")
                    )
            ));
            mappings.put("idxInternalDocId", Property.of(p -> p
                    .text(t -> t
                            .analyzer("standard")
                            .searchAnalyzer("standard")
                    )
            ));
            mappings.put("idxPushDate", Property.of(p -> p.keyword(k -> k)));

            // 关联类型字段
            mappings.put("idxLinkType", Property.of(p -> p.keyword(k -> k)));
            //文件id
            mappings.put("idxFileId", Property.of(p -> p.keyword(k -> k)));

            // 修改文本内容字段的映射配置
            mappings.put("idxTextContent", Property.of(p -> p
                    .text(t -> t
                            .analyzer("standard")
                            .searchAnalyzer("standard")
                    )
            ));

            // 修改附件列表字段的映射配置
            mappings.put("appendixDocumentList", Property.of(p -> p.nested(n -> n
                    .properties("idxLinkLogId", f -> f.keyword(k -> k))
                    .properties("idxTextContent", f -> f.text(t -> t
                            .analyzer("standard")
                            .searchAnalyzer("standard"))))));
                    
            return mappings;
        } catch (Exception e) {
            log.error("创建ES映射失败", e);
            throw new RuntimeException("创建ES映射失败: " + e.getMessage(), e);
        }
    }

    /**
     * 将Date转换为格式化的日期时间字符串
     */
    private String formatDateTime(Date date) {
        if (date == null) {
            return null;
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return sdf.format(date);
    }

    /**
     * 文件发布
     * @param version
     */
    @Override
    public void docPublish(Version version) {
        if(!enabled){
            return;
        }
        //初始化客户端
        ElasticsearchUtil elasticsearchUtil = new ElasticsearchUtil(esHost,esPort,username,password);

        // 检查索引是否存在
        try {
            if (!elasticsearchUtil.indexExists(indexname)) {
                elasticsearchUtil.createIndex(indexname, createVersionMapping());
            }
        } catch (Exception e) {
            log.warn("索引已存在或创建失败", e);
            return;
        }

        List<DocLinkLogVo> docLinkLogVos = docLinkLogService.listVo(new LambdaQueryWrapper<DocLinkLog>()
                .select(DocLinkLog::getFileId,DocLinkLog::getId,DocLinkLog::getLinkType)
                .eq(DocLinkLog::getVersionId, version.getId())
        );
        if(StringUtils.isNotEmpty(docLinkLogVos)){
            //正文类型只有DOC
            List<DocLinkLogVo> collect = docLinkLogVos.stream()
                    .filter(v -> LinkTypeEnum.DOC.name().equals(v.getLinkType()))
                    .collect(Collectors.toList());
            if(StringUtils.isNotEmpty(collect)){
                DocLinkLogVo docLinkLogVo = collect.get(0);
                // 创建ES文档对象
                Map<String, Object> document = new HashMap<>();
                document.put("idxId", version.getId());
                document.put("idxDocId", version.getDocId());
                document.put("idxDocName", version.getDocName());
                String statusLabel = getStatusDictLabel(version.getStatus());
                document.put("idxStatus", version.getStatus());
                document.put("idxStatusName", statusLabel);
                document.put("idxVersionValue", version.getVersionValue());
                document.put("idxPartNumber", ""); // 根据需要设置物料编号
                // 获取文件类型名称
                document.put("idxDataType", version.getDataType());
                document.put("idxDataTypeName", standardService.getDocClassName(version.getStandardId()));
                document.put("idxUserName", version.getUserName());
                document.put("idxInternalDocId", version.getInternalDocId());
                // 修改日期存储方式，存储格式化的日期时间字符串
                if (version.getReleaseTime() != null) {
                    document.put("idxPushDate", formatDateTime(version.getReleaseTime()));
                }
                document.put("idxLinkType", docLinkLogVo.getLinkType());
                document.put("idxFileId", docLinkLogVo.getFileId());
                String content = ocrDocContext.ocrDocContext(docLinkLogVo.getFileId());
                document.put("idxTextContent", content);

                //保存附件,附件存在多个
                List<DocLinkLogVo> collect1 = docLinkLogVos.stream().filter(v -> !LinkTypeEnum.DOC.name().equals(v.getLinkType())).collect(Collectors.toList());
                if(StringUtils.isNotEmpty(collect1)){
                    List<Map> mapList=new ArrayList<>();
                    for(DocLinkLogVo docLinkLogVo1:collect1){
                        Map<String, Object> map = new HashMap<>();
                        map.put("idxLinkLogId", docLinkLogVo1.getId());
                        String content1 = ocrDocContext.ocrDocContext(docLinkLogVo1.getFileId());
                        map.put("idxTextContent", content1);
                        mapList.add(map);
                    }
                    document.put("appendixDocumentList", mapList);
                }

                try {
                    // 索引文档
                    boolean success = elasticsearchUtil.indexDocument(indexname, version.getId(), document);
                    if (!success) {
                        log.error("文档索引失败，版本ID：{}", version.getId());
                    } else {
                        log.info("文档索引成功，版本ID：{}", version.getId());
                    }
                } catch (Exception e) {
                    log.error("文档索引异常，版本ID：{}", version.getId(), e);
                }
                // 关闭客户端
                elasticsearchUtil.close();
            }
        }
    }

    /**
     * 文件修订发布
     * @param newVersion
     * @param lastVersion
     */
    @Override
    public void docEditPublish(Version newVersion, Version lastVersion) {
        if(!enabled){
            return;
        }
        //先删除原来的
        docDelPublish(lastVersion);

        //保存新的
        docPublish(newVersion);
    }

    /**
     * 文件作废
     * ES删除对应的数据
     * @param version
     */
    @Override
    public void docDelPublish(Version version) {
        if(!enabled){
            return;
        }
        //初始化客户端
        ElasticsearchUtil elasticsearchUtil = new ElasticsearchUtil(esHost,esPort,username,password);

        // 检查索引是否存在
        if (!elasticsearchUtil.indexExists(indexname)) {
            log.warn("索引不存在");
            return;
        }
        boolean b = elasticsearchUtil.deleteDocument(indexname, version.getId());
        System.out.println("删除："+b);

        // 关闭客户端
        elasticsearchUtil.close();
    }


}

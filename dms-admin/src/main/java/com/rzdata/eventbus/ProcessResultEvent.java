package com.rzdata.eventbus;

import cn.hutool.core.util.BooleanUtil;
import com.blueland.bpmclient.model.BpmClientInputModel;
import com.blueland.bpmclient.model.ProcessInstanceModel;
import com.rzdata.process.domain.bo.BpmClientInputModelBo;
import com.rzdata.process.enums.ApplyStatusEnum;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2022/1/3 下午9:44
 * @Version 1.0
 * @Desc 流程处理事件
 */
@Data
public class ProcessResultEvent implements IEvent {
    private String applyId;
    private String status;
    private String msgInfo;
    /**
     * 申请状态 {@link com.rzdata.process.enums.ApplyStatusEnum}
     */
    private String applyStatus;
    private BpmClientInputModel model;

    private String type;

    private Date setupTime;
    /**
     * 步骤
     */
    private Integer step;
    /**
     * 下个环节是否会审
     */
    private Boolean jointReview;
    /**
     * 流程实例对象
     */
    private ProcessInstanceModel processInst;
    /**
     * 流程对象
     */
    private BpmClientInputModel bpmClientInputModel;

    private String applyType;

    private String bizType;

    private Boolean transferStatus;

    public ProcessResultEvent(BpmClientInputModelBo bpmClientInputModelBo,String msgInfo) {
        this.model = bpmClientInputModelBo.getModel();
        this.applyId = model.getWf_businessKey();
        this.status = bpmClientInputModelBo.getStatus();
        this.msgInfo = msgInfo;
        this.applyStatus = BooleanUtil.isFalse(bpmClientInputModelBo.getApplyStatus())? ApplyStatusEnum.UN_PASS.getCode():ApplyStatusEnum.PASS.getCode();
        this.setupTime = bpmClientInputModelBo.getSetupTime();
        this.step = bpmClientInputModelBo.getStep();
        this.jointReview = bpmClientInputModelBo.getJointReview();
        this.bpmClientInputModel = bpmClientInputModelBo.getBpmClientInputModel();
        this.processInst = bpmClientInputModelBo.getProcessInst();
        this.applyType = bpmClientInputModelBo.getType();
        this.bizType = bpmClientInputModelBo.getBizType();
        this.transferStatus = bpmClientInputModelBo.getTransferStatus();
    }
}

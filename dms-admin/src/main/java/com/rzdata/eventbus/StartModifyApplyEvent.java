package com.rzdata.eventbus;

import com.rzdata.process.domain.bo.ModifyApplyBo;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2022/1/7 上午9:51
 * @Version 1.0
 * @Desc 创建增修废子流程
 */
@Data
public class StartModifyApplyEvent implements IEvent {
    /**
     * 增修废对象
     */
    private ModifyApplyBo modifyApplyBo;
    /**
     * 上一流程的申请ID
     */
    private String fireApplyId;

    /**
     * 上一流程的申请类型
     */
    private String fireApplyType;

    public StartModifyApplyEvent(String fireApplyId, String fireApplyType, ModifyApplyBo modifyApplyBo) {
        this.fireApplyId = fireApplyId;
        this.fireApplyType = fireApplyType;
        this.modifyApplyBo = modifyApplyBo;
    }
}

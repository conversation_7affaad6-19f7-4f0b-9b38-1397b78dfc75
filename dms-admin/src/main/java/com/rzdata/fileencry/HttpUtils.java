package com.rzdata.fileencry;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpRequest;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.Map;

/**
 * @auther xcy
 * @create 2022-06-08 11:37
 */
public class HttpUtils {

//    Map<String, String> paramMap = new HashMap<>();
//    Map<String, String> headersMap = new HashMap<>();
//        headersMap.put("Authorization", "Bearer 1111111111111");
//        headersMap.put("Content-Type", "application/json; charset=utf-8");
//    //            String str = HttpUtils.httpGet("http://127.0.0.1:8083/blueland-plateform-render/usmToErp/selectStaffByNum/00027", paramMap, headersMap);
//    List<String> ids = Arrays.asList("luob,wuliehui".split(","));
//    String str = HttpUtils.httpPost("http://127.0.0.1:8083/blueland-plateform-render/usmToErp/selectStaffNumByPostCodes", JSONUtil.toJsonStr(ids), headersMap);
//    String s = str;


    /**
     * 发送post请求
     * @param url 请求地址
     * @param json 请求参数 json格式
     * @param headerMap 请求头
     * @return
     */
    public static String httpPost(String url, String json, Map<String, String> headerMap, Map<String, String> paramMap) throws IOException {
        String result = HttpRequest.post(ObjectUtil.isEmpty(paramMap) ? url : buildUrl(url, paramMap))
                .addHeaders(headerMap)
                .body(json)
                .execute().body();
        return result;
    }

    /**
     * 发送get请求
     * @param url 请求地址
     * @param paramMap 请求参数
     * @param headerMap 请求头
     * @return
     * @throws IOException
     */
    public static String httpGet(String url, Map<String, String> paramMap, Map<String, String> headerMap) throws IOException {
        //链式构建请求
        String result = HttpRequest.get(buildUrl(url, paramMap))
                .addHeaders(headerMap)
                .execute().body();
        return result;
    }

    /**
     * 发送form表单请求
     * @param url 请求地址
     * @param paramMap 请求参数
     * @param headerMap 请求头
     * @return
     */
    public static String httpForm(String url, Map<String, Object> paramMap, Map<String, String> headerMap) {
        String result = HttpRequest.post(url)
                .form(paramMap)//表单内容
                .addHeaders(headerMap)
                .execute().body();
        return result;
    }

    private static String buildUrl(String url, Map<String, String> paramMap) throws UnsupportedEncodingException {
        StringBuilder urlBuilder = new StringBuilder(url).append("?");
        if(ObjectUtil.isNotEmpty(paramMap)){
            for (String key : paramMap.keySet()) {
                urlBuilder.append(key)
                        .append("=")
                        .append(URLEncoder.encode(paramMap.get(key),"utf-8"))
                        .append("&");
            }
        }
        return urlBuilder.substring(0, urlBuilder.length() - 1);
    }

}

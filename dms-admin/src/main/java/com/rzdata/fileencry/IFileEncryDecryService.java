package com.rzdata.fileencry;

import cn.hutool.json.JSONObject;
import com.rzdata.process.domain.vo.BasicFileVo;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2024-05-16 -9:26
 */
public interface IFileEncryDecryService {

    /**
     * 登录
     * @return
     */
    Map<String, String> login();

    /**
     * 注销
     */
    void logout(String loginId);

    /**
     * 加密文件
     * LoginID 是 String 登录ID
     * Param 是 Object 下面的所有参数都是包含在Param里面
     * files 是 Array 文件列表
     * setting 否 Array 设置权限，只能设置一个设置权限
     * guid 否 String 安全区域ID
     * level 否 String 安全级别
     * access 否 Array 访问权限
     * guid 否 String 安全区域ID
     * level 否 String 安全级别
     * filepolicy 否 Array 用户权限，加密成用户权限格式时必须设置。
     * objtype 否 String
     * 对象类型（0:创建者，1:用户，2:用户组 6：所有用户都
     * user 否 String
     * creator 否 String
     * 非0时标记是创建者。有该字段时以该字段为准，没有时
     * 按objtype。
     * modify 否 String 修改(0-禁止，1-允许)
     * copy 否 String 复制(0-禁止，1-允许，2-不设置)
     * print 否 String 打印(0-禁止，1-允许，2-不设置)
     * screenshot 否 String 截屏(0-禁止，1-允许，2-不设置)
     * setrights 否 String 设置(0-禁止，1-允许)
     * decrypt 否 String 解密(0-禁止，1-不设置)
     * starttime 否 String 开始时间(格式：2019-1-1，不填表示不限制)
     * stoptime 否 String 结束时间(格式：2019-1-1，不填表示不限制)
     */
    JSONObject encryptFile(Map<String, Object> map);

    /**
     * 解密文件
     * @param filePath 文件路径
     * @return
     */
    Map<String, String> decryptFile(String filePath, String loginId);

    /**
     * 判断文件是否加密
     * @param filePath
     * @return
     */
    Map<String, Object> isSdFile(String filePath, String loginId);

    /**
     * 判断文件是否加密
     * @param filePath
     * @return
     */
    Map<String, String> isSdFileAndDecrypt(String filePath);

    /**
     * 判断文件是否快速加密
     * @param filePath
     * @return
     */
    Map<String, String> isFastFile(String filePath, String loginId);

    Map<String, String> uploadFile(String filePath, String loginId);

    Map<String, String> downloadFile(String filePath, String loginId);

    byte[] getFileBytes(String filePath);

    Map<String, String> uploading(String filePath) throws Exception;


    boolean downfile(String filePath, HttpServletResponse response);
}

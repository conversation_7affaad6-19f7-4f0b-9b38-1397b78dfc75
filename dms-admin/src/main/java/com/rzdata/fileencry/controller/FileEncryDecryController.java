package com.rzdata.fileencry.controller;

import com.rzdata.fileencry.FilePathBo;
import com.rzdata.fileencry.IFileEncryDecryService;
import com.rzdata.framework.annotation.Log;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.enums.BusinessType;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2024-05-16 -11:00
 */
@RequestMapping("/file/encryt")
@RestController
public class FileEncryDecryController {

    @Autowired
    IFileEncryDecryService iFileEncryDecryService;

    @ApiOperation("登录")
    @GetMapping("/login")
    public AjaxResult login() {
        return AjaxResult.success(iFileEncryDecryService.login());
    }

    @ApiOperation("登出")
    @GetMapping("/logout")
    public AjaxResult logout() {
        iFileEncryDecryService.logout("");
        return AjaxResult.success();
    }

    @ApiOperation("解密文件")
    @PostMapping("decryptFile")
    public AjaxResult decryptFile(@RequestBody FilePathBo bo) {
        return AjaxResult.success(iFileEncryDecryService.decryptFile(bo.getFilePath(), bo.getLoginId()));
    }

    @ApiOperation("判断文件是否加密")
    @PostMapping("/isSdFile")
    public AjaxResult isSdFile(@RequestBody FilePathBo bo) {
        return AjaxResult.success(iFileEncryDecryService.isSdFile(bo.getFilePath(), bo.getLoginId()));
    }

    @ApiOperation("判断文件是否加密，并且解密")
    @PostMapping("/isSdFileAndDecrypt")
    public AjaxResult isSdFileAndDecrypt(@RequestBody FilePathBo bo) {
        return AjaxResult.success(iFileEncryDecryService.isSdFileAndDecrypt(bo.getFilePath()));
    }

    @ApiOperation("判断文件是否快速加密")
    @PostMapping("/isFastFile")
    public AjaxResult isFastFile(@RequestBody FilePathBo bo) {
        return AjaxResult.success(iFileEncryDecryService.isFastFile(bo.getFilePath(), bo.getLoginId()));
    }

    @ApiOperation("文件上传")
    @PostMapping("/uploadFile")
    public AjaxResult uploadFile(@RequestBody FilePathBo bo) {
        return AjaxResult.success(iFileEncryDecryService.uploadFile(bo.getFilePath(), bo.getLoginId()));
    }

    @ApiOperation("上传本地存储")
    @PostMapping(value = "/local_upload")
    public AjaxResult<Map<String, String>> uploading(@RequestBody FilePathBo bo) throws Exception{
        return AjaxResult.success(iFileEncryDecryService.uploading(bo.getFilePath()));
    }

    @ApiOperation("下载")
    @PostMapping(value = "/downfile")
    public void downfile(@RequestBody FilePathBo bo, HttpServletResponse response) throws Exception{
        iFileEncryDecryService.downfile(bo.getFilePath(), response);
    }

    @PostMapping("/downloadFile")
    public AjaxResult<Map<String, String>> downloadFile(@RequestBody FilePathBo bo) {
        return AjaxResult.success(iFileEncryDecryService.downloadFile(bo.getFilePath(), bo.getLoginId()));
    }
}

package com.rzdata.fileencry.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.rzdata.asas7.util.Constants;
import com.rzdata.fileencry.HttpUtils;
import com.rzdata.fileencry.IFileEncryDecryService;
import com.rzdata.framework.core.redis.RedisCache;
import com.rzdata.framework.utils.spring.SpringUtils;
import com.rzdata.process.domain.vo.BasicFileVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @create 2024-05-16 -9:27
 * 文件加解密服务
 */
@Slf4j
@Service
public class FileEncryDecryServiceImpl implements IFileEncryDecryService {

    @Value("${file.encrypt.host}")
    String BaseUrl;

    @Value("${file.encrypt.username}")
    String UserName;

    @Value("${file.encrypt.password}")
    String Password;

    @Override
    public Map<String, String> login() {
        // 组装返回结果
        Map<String, String> data = new HashMap<>();
        try {
            // 组装请求参数
            Map<String, Object> dto = new HashMap<>();
            dto.put("Name", UserName);
            dto.put("Password", Password);
            // 发送请求  http://************:8086/interface/wapi/login
            //String result = HttpUtil.post(BaseUrl + "/interface/wapi/login", JSONUtil.toJsonStr(dto));
            Map<String, String> paramMap = new HashMap<>();
            Map<String, String> headersMap = new HashMap<>();
            headersMap.put("Content-Type", "application/json");
            String result =  HttpUtils.httpPost(BaseUrl + "/interface/wapi/login", JSONUtil.toJsonStr(dto), headersMap, paramMap);
            if (ObjectUtil.isNotEmpty(result)) {
                JSONObject jsonObject = new JSONObject(result);
                // 错误代码，0 表示没有错误
                data.put("error", jsonObject.getStr("error"));
                // 错误描述
                data.put("desc", jsonObject.getStr("desc"));
                // 当前登录管理员的ID
                data.put("managerid", jsonObject.getStr("managerid"));
                data.put("loginid", jsonObject.getStr("loginid"));
//                if (jsonObject.getStr("error") == "0" && ObjectUtil.isNotEmpty(jsonObject.getStr("loginid"))) {
//                    // 当登录成功时有效guid（即其他接口使用的LoginID）
//                    data.put("loginid", jsonObject.getStr("loginid"));
//                    // 存入redis 设置失效为25分钟
//                    SpringUtils.getBean(RedisCache.class).setCacheObject(Constants.MH_FILE_TOKEN, jsonObject.getStr("loginid"), 25, TimeUnit.MINUTES);
//                }
            }
            log.info("登录信息=======>" + result);
        } catch (Exception e) {
            log.error("文件加解密服务登录失败", e);
        }
        return data;
    }

    @Override
    public void logout(String loginId) {
        // loginId不为空的时候才需要注销
        if (ObjectUtil.isNotEmpty(loginId)) {
            // 组装请求参数
            Map<String, Object> dto = new HashMap<>();
            dto.put("LoginID", loginId);
            // 发送请求
//            String result = HttpUtil.post(BaseUrl + "/interface/wapi/logout", dto);
            Map<String, String> paramMap = new HashMap<>();
            Map<String, String> headersMap = new HashMap<>();
            headersMap.put("Content-Type", "application/json");
            try {
                String result =  HttpUtils.httpPost(BaseUrl + "/interface/wapi/logout", JSONUtil.toJsonStr(dto), headersMap, paramMap);
                log.info("登出信息=======>" + result);
            } catch (Exception e) {
                log.error("文件加解密服务登出失败", e);
            }
        }
    }

    @Override
    public JSONObject encryptFile(Map<String, Object> map) {
        // 获取loginid
        String loginId = SpringUtils.getBean(RedisCache.class).getCacheObject(Constants.MH_FILE_TOKEN);
        // 判断loginid是否失效，重新登录
        if (ObjectUtil.isEmpty(loginId) || loginId == null) {
            loginId = login().get("loginid")+"";
        }
        map.put("LoginID", loginId);

        // 发送请求
        Map<String, String> data = new HashMap<>();
//        String result = HttpUtil.post(BaseUrl + "/interface/wapi/encryptFile", map);
        Map<String, String> paramMap = new HashMap<>();
        Map<String, String> headersMap = new HashMap<>();
        headersMap.put("Content-Type", "application/json");
        try {
            String result =  HttpUtils.httpPost(BaseUrl + "/interface/wapi/encryptFile", JSONUtil.toJsonStr(map), headersMap, paramMap);
        } catch (Exception e) {
            log.error("文件加解密登出失败", e);
        }
        return null;
    }

    @Override
    public Map<String, String> decryptFile(String filePath, String loginId) {
        // 获取loginid
        //String loginId = login().get("loginid")+"";
        // 判断loginid是否失效，重新登录
//        if (ObjectUtil.isEmpty(loginId) || loginId == null) {
//            loginId = login().get("loginid")+"";
//        }
//        Map<String,String> fileMap = uploadFile(ObjectUtil.isNotEmpty(filePath) ? filePath : "C:\\Users\\<USER>\\Desktop\\新建文件夹\\加密_OA待办服务接口说明.docx");
        // 组装请求参数
        Map<String, Object> dto = new HashMap<>();
        dto.put("LoginID", loginId);
        dto.put("File", filePath);
        // 发送请求
        Map<String, String> data = new HashMap<>();
//        String result = HttpUtil.post(BaseUrl + "/interface/wapi/decryptFile", dto);
        Map<String, String> paramMap = new HashMap<>();
        Map<String, String> headersMap = new HashMap<>();
        headersMap.put("Content-Type", "application/json");
        try {
            String result =  HttpUtils.httpPost(BaseUrl + "/interface/wapi/decryptFile", JSONUtil.toJsonStr(dto), headersMap, paramMap);
            if (ObjectUtil.isNotEmpty(result)) {
                JSONObject jsonObject = new JSONObject(result);
                // 错误代码，0表示成功，其他表示错误
                data.put("error", jsonObject.getStr("error"));
                // 错误描述
                data.put("desc", jsonObject.getStr("desc"));
            }
            log.info("文件解密结果=======>" + result);
        } catch (Exception e) {
            log.error("文件解密失败", e);
        }
        return data;
    }

    @Override
    public Map<String, Object> isSdFile(String filePath, String loginId) {
        // 判断loginid是否失效，重新登录
//        if (ObjectUtil.isEmpty(loginId) || loginId == null) {
//            loginId = login().get("loginid")+"";
//        }
        // 组装请求参数
        // 上传文件
//        Map<String,String> fileMap = uploadFile(ObjectUtil.isNotEmpty(filePath) ? filePath : "C:\\Users\\<USER>\\Desktop\\新建文件夹\\加密_OA待办服务接口说明.docx");
        Map<String, Object> dto = new HashMap<>();
        dto.put("LoginID", loginId);
        dto.put("File", filePath);
        // 发送请求
        Map<String, Object> data = new HashMap<>();
        //String result = HttpUtil.post(BaseUrl + "/interface/wapi/isSdFile", dto);
        Map<String, String> paramMap = new HashMap<>();
        Map<String, String> headersMap = new HashMap<>();
        headersMap.put("Content-Type", "application/json");
        try {
            String result =  HttpUtils.httpPost(BaseUrl + "/interface/wapi/isSdFile", JSONUtil.toJsonStr(dto), headersMap, paramMap);
            if (ObjectUtil.isNotEmpty(result)) {
                JSONObject jsonObject = new JSONObject(result);
                // 错误代码，0表示成功，其他表示错误
                data.put("error", jsonObject.getStr("error"));
                // 错误描述
                data.put("desc", jsonObject.getStr("desc"));
                // 是加密文档返回true，不是则返回false
                data.put("encrypt", jsonObject.get("encrypt") + "");
            }
        } catch (Exception e) {
            log.error("判断文件是否加密失败", e);
        }
        return data;
    }

    @Override
    public Map<String, String> isSdFileAndDecrypt(String filePath) {
        log.info("文件路径=======>" + filePath);
        Map<String, String> dataMap = new HashMap<>();
        // 登录 判断是否加密 解密 注销
        String loginId = login().get("loginid")+"";
        // 判断是否加密
        Map<String, Object> isSdFileMap = isSdFile(filePath, loginId);
        dataMap.put("encrypt", isSdFileMap.get("encrypt")+"");
        // 错误代码，0表示成功，其他表示错误
        dataMap.put("error", isSdFileMap.get("error")+"");
        log.info("文件是否解密结果=======>" + isSdFileMap);
        if (isSdFileMap != null && isSdFileMap.get("encrypt").equals("true")) {
            // 解密
            Map<String, String> decryptMap = decryptFile(filePath, loginId);
            log.info("文件加密结果=======>" + decryptMap);
            dataMap.put("encrypt_error", decryptMap.get("error"));
            // 错误描述
            dataMap.put("encrypt_desc", decryptMap.get("desc"));
        }
        log.info("文件解密结果=======>" + dataMap);
        // 注销
        logout(loginId);
        return dataMap;
    }

    /**
     * 快速加密文档，是指文档未使用用户权限加密，或者在使用用户权限加密时，文档是所有用户都有
     * 阅读权限。
     * @param filePath
     * @return
     */
    @Override
    public Map<String, String> isFastFile(String filePath, String loginId) {
        // 组装请求参数
        Map<String, Object> dto = new HashMap<>();
        dto.put("LoginID", loginId);
        dto.put("Func", "IsFastFile");
        Map<String, Object> param = new HashMap<>();
        param.put("filepath", filePath);
        dto.put("Param", param);
        // 发送请求
        Map<String, String> data = new HashMap<>();
        //String result = HttpUtil.post(BaseUrl + "/interface/wapi/callfunction", dto);
        Map<String, String> paramMap = new HashMap<>();
        Map<String, String> headersMap = new HashMap<>();
        headersMap.put("Content-Type", "application/json");
        try {
            String result =  HttpUtils.httpPost(BaseUrl + "/interface/wapi/callfunction", JSONUtil.toJsonStr(dto), headersMap, paramMap);
            if (ObjectUtil.isNotEmpty(result)) {
                JSONObject jsonObject = new JSONObject(result);
                // 错误代码，0表示成功，其他表示错误
                data.put("error", jsonObject.getStr("error"));
                // 错误描述
                data.put("desc", jsonObject.getStr("desc"));
            }
            log.info("文件是否快速加密=======>" + result);
        } catch (Exception e) {
            log.error("判断文件是否快速加密失败", e);
        }
        return data;
    }

    @Override
    public Map<String, String> uploadFile(String filePath, String loginId) {
        // 组装请求参数
        Map<String, Object> dto = new HashMap<>();
        dto.put("LoginID", loginId);
        dto.put("Func", "uploadfile");
        // 将文件路径转换为Path对象
        Path path = Paths.get(filePath);
        // 读取文件内容到字节数组
        try {
            byte[] fileContent = Files.readAllBytes(path);
            // 将字节数组转换为Base64编码的字符串
            String base64String = Base64.getEncoder().encodeToString(fileContent);
            // 输出Base64编码的字符串
            dto.put("Param", base64String);
        } catch (IOException e) {
            e.printStackTrace();
        }
        // 发送请求
        Map<String, String> data = new HashMap<>();
        //String result = HttpUtil.post(BaseUrl + "/interface/wapi/callfunction", dto);
        Map<String, String> paramMap = new HashMap<>();
        Map<String, String> headersMap = new HashMap<>();
        headersMap.put("Content-Type", "application/json");
        try {
            String result =  HttpUtils.httpPost(BaseUrl + "/interface/wapi/callfunction", JSONUtil.toJsonStr(dto), headersMap, paramMap);
            if (ObjectUtil.isNotEmpty(result)) {
                JSONObject jsonObject = new JSONObject(result);
                // 错误代码，0表示成功，其他表示错误
                data.put("error", jsonObject.getStr("error"));
                // 错误描述
                data.put("desc", jsonObject.getStr("desc"));
                data.put("file", jsonObject.getStr("file"));
            }
        } catch (Exception e) {
            log.error("文件上传失败", e);
        }
        return data;
    }

    @Override
    public Map<String, String> downloadFile(String filePath, String loginId) {
        // 组装请求参数
        Map<String, Object> dto = new HashMap<>();
        dto.put("LoginID", loginId);
        dto.put("Func", "downloadfile");
        dto.put("Param", filePath);
        // 发送请求
        Map<String, String> data = new HashMap<>();
        //String result = HttpUtil.post(BaseUrl + "/interface/wapi/callfunction", dto);
        Map<String, String> paramMap = new HashMap<>();
        Map<String, String> headersMap = new HashMap<>();
        headersMap.put("Content-Type", "application/json");
        try {
            String result =  HttpUtils.httpPost(BaseUrl + "/interface/wapi/callfunction", JSONUtil.toJsonStr(dto), headersMap, paramMap);
            if (ObjectUtil.isNotEmpty(result)) {
                JSONObject jsonObject = new JSONObject(result);
                // 错误代码，0表示成功，其他表示错误
                data.put("error", jsonObject.getStr("error"));
                // 错误描述
                data.put("desc", jsonObject.getStr("desc"));
                data.put("data", jsonObject.getStr("data"));
            }
        } catch (Exception e) {
            log.error("文件下载失败", e);
        }
        return data;
    }

    @Override
    public byte[] getFileBytes(String filePath) {
        // 登录 上传 判断是否加密 解密 下载 重新上传 注销
        String loginId = login().get("loginid")+"";
        // 上传 获取上传之后的路径
        Map<String,String> upMap = uploadFile(filePath, loginId);
        // 判断是否加密
        Map<String, Object> isSdFileMap = isSdFile(upMap.get("file")+"", loginId);
        if (isSdFileMap != null && isSdFileMap.get("encrypt").equals("true")) {
            // 解密
            Map<String, String> decryptMap = decryptFile(upMap.get("file")+"", loginId);
            // 下载
            Map<String, String> downMap = downloadFile(decryptMap.get("file")+"", loginId);
            if (downMap != null && downMap.get("data") != null) {
                return Base64.getDecoder().decode(downMap.get("data"));
            }
        }
        // 注销
        logout(loginId);
        return null;
    }

    @Override
    public 	Map<String, String> uploading(String filePath) throws Exception {
        log.info("开始时间", System.currentTimeMillis());
        Map<String, String> decryptMap = new HashMap<>();
        String loginId = login().get("loginid")+"";
        // 上传 获取上传之后的路径
        Map<String,String> upMap = uploadFile(filePath, loginId);
        // 判断是否加密
        Map<String, Object> isSdFileMap = isSdFile(upMap.get("file")+"", loginId);
        if (isSdFileMap != null && isSdFileMap.get("encrypt").equals("true")) {
            // 解密
            decryptMap = decryptFile(upMap.get("file")+"", loginId);
        }
        // 注销
        logout(loginId);
        log.info("结束时间", System.currentTimeMillis());
        return decryptMap;
    }

    @Override
    public  boolean downfile(String filePath, HttpServletResponse response) {
        log.info("下载开始时间", System.currentTimeMillis());
        response.setContentType("application/octet-stream");
        response.setHeader("content-type", "application/octet-stream");
        response.setHeader("Content-Disposition", "attachment;fileName=" + "测试文件");
        byte[] buffer = new byte[1024];
        //输出流
        // 登录
        String loginId = login().get("loginid")+"";
        Map<String, String> downMap = downloadFile(filePath, loginId);
        String base64String = null;
        if (downMap != null && downMap.get("data") != null) {
            base64String = downMap.get("data");
        }

        OutputStream os = null;
        try (FileInputStream fis = convert(base64String);
             BufferedInputStream bis = new BufferedInputStream(fis);) {
            os = response.getOutputStream();
            int i = bis.read(buffer);
            while (i != -1) {
                os.write(buffer);
                i = bis.read(buffer);
            }
            return true;
        } catch (Exception e) {
            log.error("download file error:", e);
        }
        log.info("下载结束时间", System.currentTimeMillis());
        return false;
    }

    public FileInputStream convert(String base64String) throws IOException {
        // Decode the Base64 string to a byte array
        byte[] data = Base64.getDecoder().decode(base64String);

        // Create a temporary file to hold the decoded data
        java.io.File tempFile = java.nio.file.Files.createTempFile("base64-", ".tmp").toFile();
        tempFile.deleteOnExit();
        // Write the decoded data to the temporary file
        try (java.io.FileOutputStream fos = new java.io.FileOutputStream(tempFile)) {
            fos.write(data);
        }

        // Open a FileInputStream to the temporary file
        return new FileInputStream(tempFile);
    }

}

package com.rzdata.job.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.blueland.bpmclient.model.BpmClientInputModel;
import com.blueland.bpmclient.model.ProcessDefinitionModel;
import com.rzdata.config.CustomConfig;
import com.rzdata.config.ProcessConfig;
import com.rzdata.framework.constant.Constants;
import com.rzdata.framework.constant.NumberConstants;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.core.domain.entity.SysUser;
import com.rzdata.framework.enums.UserStatus;
import com.rzdata.framework.utils.DateUtils;
import com.rzdata.framework.utils.StringUtils;
import com.rzdata.process.domain.BorrowApplyItem;
import com.rzdata.process.domain.ReviewApplyItem;
import com.rzdata.process.domain.Standard;
import com.rzdata.process.domain.Version;
import com.rzdata.process.domain.bo.BpmClientInputModelBo;
import com.rzdata.process.domain.bo.ReviewApplyBo;
import com.rzdata.process.domain.bo.ReviewApplyItemBo;
import com.rzdata.process.domain.vo.BorrowApplyVo;
import com.rzdata.process.enums.RecordStatusEnum;
import com.rzdata.process.enums.SendType;
import com.rzdata.process.service.*;
import com.rzdata.system.service.ISysConfigService;
import com.rzdata.system.service.ISysNotifyLogService;
import com.rzdata.system.service.ISysUserService;
import com.rzdata.system.service.WorkflowService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.rzdata.framework.constant.Constants.PROCESS_KEY;

/**
 * 文件作废，复审，失效，阅读记录任务调度
 */
@Slf4j
@Service
public class FileJobService {
    @Autowired
    IStandardService iStandardService;

    @Autowired
    IVersionService iVersionService;

    @Autowired
    IReviewApplyService iReviewApplyService;

    @Autowired
    IBorrowApplyService iBorrowApplyService;

    @Autowired
    IBorrowApplyItemService iBorrowApplyItemService;

    @Autowired
    IStoreFileService iStoreFileService;

    @Autowired
    ISysUserService iSysUserService;

    @Autowired
    WorkflowService workflowService;

    @Autowired
    ISysConfigService configService;

    @Autowired
    CustomConfig customConfig;

    @Autowired
    ISysNotifyLogService sysNotifyLogService;


    /**
     * 文件自动作废
     *
     * @throws Exception
     */
    @XxlJob("fileCanCelJobHandler")
    public void fileCanCelJobHandler() throws Exception {
        XxlJobHelper.log("文件自动作废....");
        QueryWrapper<Version> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(Version:: getStatus, NumberConstants.ONE);
        List<Version> list = iVersionService.list(queryWrapper);
        if (ObjectUtil.isNotEmpty(list)) {
            Date date = new Date();
            for (Version version : list) {
                if (ObjectUtil.isNotEmpty(version.getEndDate()) && date.after(version.getEndDate())) {
                    // 失效当前文件
                    version.setStatus(NumberConstants.ZERO+"");
                    iVersionService.updateById(version);
                }
            }
        }
    }

    @XxlJob("lsFileReviewJobHandler")
    public AjaxResult lsFileReviewJobHandler() throws Exception {
        String jobParam = XxlJobHelper.getJobParam();
        int days = 0;
        //获取流程配置
        String procDefKey = this.customConfig.getReviewFlowKey();
        //角色可以
        String roleKey = "";
        // 是否为临时文件
        String fileType = "Y";
        if (StringUtils.isNotEmpty(jobParam)) {
            JSONObject json = JSONUtil.parseObj(jobParam);
            if (ObjectUtil.isNotEmpty(json.get("days"))){
                days = (int) json.get("days");
            }
            if (ObjectUtil.isNotEmpty(json.get("procDefKey"))){
                procDefKey = (String) json.get("procDefKey");
            }
            if (ObjectUtil.isNotEmpty(json.get("roleKey"))){
                roleKey = (String) json.get("roleKey");
            }
            if (ObjectUtil.isNotEmpty(json.get("fileType"))) {
                fileType = (String) json.get("fileType");
            }
        }

        String errMsg = "";
        XxlJobHelper.log("系统自动推送文件复审流程-开始");
        List<HashMap> listMap = this.iReviewApplyService.selectUnReviewList(days, fileType);
        XxlJobHelper.log("获取待复审数据记录="+listMap.size());
        if (listMap.size()<1) {
            errMsg = "无待复审文件记录"+ DateUtils.getTime();
            XxlJobHelper.log(errMsg);
            return AjaxResult.success(errMsg);
        }
        //获取公司文控
        List<SysUser> userList = iSysUserService.selectListAllByRoleKey("company_file_manager").stream().filter(sysUser -> UserStatus.OK.getCode().equals(sysUser.getStatus())).collect(Collectors.toList());

        ProcessConfig processConfig = JSONUtil.toBean(configService.selectConfigByKey(PROCESS_KEY), ProcessConfig.class);
        BpmClientInputModelBo bpmClientInputModelBo = new BpmClientInputModelBo();
        ProcessDefinitionModel processDefinitionModel = workflowService.getProcessDefinitionModel(procDefKey);
        int count = listMap.size();
        int succ = 0;
        for (HashMap itemMap : listMap) {
            // 获取版本
            Version version = this.iVersionService.getById(itemMap.get("version_id").toString());
            try{
                // 流程接收者
                SysUser reminder = null;
                // 优先获取文件的编制人，当编制人不存在或者无效的情况下发送给文控
                if(StringUtils.isNotEmpty(version.getUserName())) {
                    SysUser user = this.iSysUserService.selectUserByUserName(version.getUserName());
                    if (StringUtils.isNotNull(user)&& UserStatus.OK.getCode().equals(user.getStatus())) {
                        reminder = user;
                    }
                }
                // 当编制人不存在或者无效的情况下 设置了发个某个角色 且有编制部门
                if (reminder == null && StringUtils.isNotEmpty(roleKey) && StringUtils.isNotEmpty(version.getDeptId())) {
                    List<SysUser> roleUserList = iSysUserService.getFullPathRoleUserList(roleKey,version.getDeptId());
                    if (roleUserList.size() > 0) {
                        reminder = roleUserList.get(0);
                    }
                }
                if(reminder == null && userList.size() > 0) {
                    // 当编制人不存在或者无效的情况下发送给文控
                    reminder = userList.get(0);
                }
                // 接收者没有找到，继续下一个文件
                if(reminder == null) {
                    errMsg += "versionId="+version.getId()+",没有找到复审流程接收者\n";
                    continue;
                }
                // 文件复审基本信息
                ReviewApplyBo bo = new ReviewApplyBo();
                bo.setApplyTitle(version.getDocName());
                bo.setReason("系统自动触发");
                bo.setUserName(reminder.getUserName());
                bo.setDeptId(reminder.getDeptId());
                bo.setDataType(version.getDataType());
                // 复审文件清单
                List<ReviewApplyItemBo> itemList = new ArrayList<>();
                ReviewApplyItemBo itemBo = new ReviewApplyItemBo();
                Standard standard= iStandardService.getById(version.getStandardId());
                itemBo.setDocName(standard.getDocName());
                itemBo.setDocId(version.getDocId());
                itemBo.setDocClass(standard.getDocClass());
                itemBo.setVersionId(version.getId());
                itemBo.setVersionValue(version.getVersionValue());
                itemBo.setUserName(version.getUserName());
                itemBo.setDeptId(version.getDeptId());
                itemList.add(itemBo);
                bo.setItemList(itemList);
                bo.setEditStatus(true);
                // 组装流程参数
                BpmClientInputModel bpmClientInputModel = new BpmClientInputModel();
                bpmClientInputModel.setWf_procDefId(processDefinitionModel.getProcDefId());
                bpmClientInputModel.setWf_procDefKey(processDefinitionModel.getProcDefKey());
                bpmClientInputModel.setWf_procTitle(bo.getApplyTitle());
                // 流程发起者
                bpmClientInputModel.setWf_sendUserId(reminder.getUserName());
                bpmClientInputModel.setWf_sendUserOrgId(reminder.getDeptId());
                // 设置接收人
                List<Map<String,Object>> list = new ArrayList<>();
                Map<String,Object> map = new HashMap<>();
                map.put("receiveUserId",reminder.getUserName());
                map.put("receiveUserOrgId",reminder.getDeptId());
                list.add(map);
                bpmClientInputModel.setWf_receivers(list);
                bpmClientInputModelBo.setModel(bpmClientInputModel);
                String type = "ls_" + processConfig.getProcDefKeyFSSQ();
                bpmClientInputModelBo.setType(type);
                bo.setBpmClientInputModel(bpmClientInputModelBo);
                // 流程暂存
                bo.setStatus(RecordStatusEnum.DRAFT.getCode());
                bo.setRecordStatus(RecordStatusEnum.DRAFT.getCode());
                // 流程保存
                iReviewApplyService.insertByBo(bo);
                // 发送待办邮件提醒
                Map<String, String> param = new HashMap<>();
                StringBuilder content = new StringBuilder();
                String createDateTime = DateUtil.format(new Date(), Constants.YYYY_MM_DD);
                String afterDateTime = DateUtil.format(DateUtil.offsetMonth(new Date(), 1), Constants.YYYY_MM_DD);
                for (ReviewApplyItemBo reviewApplyItem : itemList) {
                    content.append("<p><span style='font-family: 宋体;font-size: 14px'>");
                    content.append("文件名称：");
                    content.append(StrUtil.isNotBlank(reviewApplyItem.getDocName()) ? reviewApplyItem.getDocName() : "-");
                    content.append(",");
                    content.append("版本：");
                    content.append(StrUtil.isNotBlank(reviewApplyItem.getVersionValue()) ? reviewApplyItem.getVersionValue() : "-");
                    content.append(",");
                    content.append("回顾期限：");
                    content.append(createDateTime);
                    content.append("至");
                    content.append(afterDateTime);
                    content.append("（1个月）");
                    content.append(",");
                    content.append("文件编号：");
                    content.append(StrUtil.isNotBlank(reviewApplyItem.getDocId()) ? reviewApplyItem.getDocId() : "-");
                    content.append(",");
                    content.append("编制人：");
                    content.append(reminder.getNickName());
                    content.append("</span></p>");
                }
                param.put("title", bpmClientInputModel.getWf_procTitle());
                param.put("content", content.toString());
                if (ObjectUtil.isNotEmpty(userList)) {
                    for (SysUser sysUser : userList) {
                        if (!Objects.equals(sysUser.getUserId(), reminder.getUserId())) {
                            sysNotifyLogService.sendEmail(SendType.FS.getCode(),sysUser.getNickName(),sysUser.getEmail(),sysUser.getUserId(),param);
                        }
                    }
                }

                XxlJobHelper.log(String.format("文件复审流程自动触发,流程标题：%s,接收人：%s",bo.getApplyTitle(),bo.getUserName()));
                // 执行成功数累加
                succ++;
            } catch (Exception e) {
                log.error("versionId="+version.getId()+",fileReviewJobHandler异常",e);
                errMsg += "versionId="+version.getId()+",发生异常"+e.getLocalizedMessage()+"\n";
                XxlJobHelper.log(ExceptionUtil.getRootCauseMessage(e));
            }
        }
        // 执行进度
        String process = "process="+succ+"/"+count;
        XxlJobHelper.log(process);
        XxlJobHelper.log(errMsg);
        XxlJobHelper.log("系统自动推送文件复审流程-结束");
        // 返回消息
        if(errMsg.length()>0) {
            return AjaxResult.error(errMsg,process);
        } else {
            return AjaxResult.success(errMsg,process);
        }
    }


    /**
     * 文件复审（单个文件单个流程推送给编制人，若是编制人无效则推送给公司文控）
     *
     * @throws Exception
     */
    @XxlJob("fileReviewJobHandler")
    public AjaxResult fileReviewJobHandler() throws Exception {
        String jobParam = XxlJobHelper.getJobParam();
        int days = 0;
        //获取流程配置
        String procDefKey = this.customConfig.getReviewFlowKey();
        //角色可以
        String roleKey = "";
        // 是否为临时文件
        String fileType = "";
        if (StringUtils.isNotEmpty(jobParam)) {
            JSONObject json = JSONUtil.parseObj(jobParam);
            if (ObjectUtil.isNotEmpty(json.get("days"))){
                days = (int) json.get("days");
            }
            if (ObjectUtil.isNotEmpty(json.get("procDefKey"))){
                procDefKey = (String) json.get("procDefKey");
            }
            if (ObjectUtil.isNotEmpty(json.get("roleKey"))){
                roleKey = (String) json.get("roleKey");
            }
            if (ObjectUtil.isNotEmpty(json.get("fileType"))) {
                fileType = (String) json.get("fileType");
            }
        }

        String errMsg = "";
        XxlJobHelper.log("系统自动推送文件复审流程-开始");
        List<HashMap> listMap = this.iReviewApplyService.selectUnReviewList(days, fileType);
        XxlJobHelper.log("获取待复审数据记录="+listMap.size());
        if (listMap.size()<1) {
            errMsg = "无待复审文件记录"+ DateUtils.getTime();
            XxlJobHelper.log(errMsg);
            return AjaxResult.success(errMsg);
        }
        //获取公司文控
        List<SysUser> userList = iSysUserService.selectListAllByRoleKey("company_file_manager").stream().filter(sysUser -> UserStatus.OK.getCode().equals(sysUser.getStatus())).collect(Collectors.toList());

        ProcessConfig processConfig = JSONUtil.toBean(configService.selectConfigByKey(PROCESS_KEY), ProcessConfig.class);
        BpmClientInputModelBo bpmClientInputModelBo = new BpmClientInputModelBo();
        ProcessDefinitionModel processDefinitionModel = workflowService.getProcessDefinitionModel(procDefKey);
        int count = listMap.size();
        int succ = 0;
        for (HashMap itemMap : listMap) {
            // 获取版本
            Version version = this.iVersionService.getById(itemMap.get("version_id").toString());
            try{
                // 流程接收者
                SysUser reminder = null;
                // 优先获取文件的编制人，当编制人不存在或者无效的情况下发送给文控
                if(StringUtils.isNotEmpty(version.getUserName())) {
                    SysUser user = this.iSysUserService.selectUserByUserName(version.getUserName());
                    if (StringUtils.isNotNull(user)&& UserStatus.OK.getCode().equals(user.getStatus())) {
                        reminder = user;
                    }
                }
                // 当编制人不存在或者无效的情况下 设置了发个某个角色 且有编制部门
                if (reminder == null && StringUtils.isNotEmpty(roleKey) && StringUtils.isNotEmpty(version.getDeptId())) {
                    List<SysUser> roleUserList = iSysUserService.getFullPathRoleUserList(roleKey,version.getDeptId());
                    if (roleUserList.size() > 0) {
                        reminder = roleUserList.get(0);
                    }
                }
                if(reminder == null && userList.size() > 0) {
                    // 当编制人不存在或者无效的情况下发送给文控
                    reminder = userList.get(0);
                }
                // 接收者没有找到，继续下一个文件
                if(reminder == null) {
                    errMsg += "versionId="+version.getId()+",没有找到复审流程接收者\n";
                    continue;
                }
                // 文件复审基本信息
                ReviewApplyBo bo = new ReviewApplyBo();
                bo.setApplyTitle(version.getDocName());
                bo.setReason("系统自动触发");
                bo.setUserName(reminder.getUserName());
                bo.setDeptId(reminder.getDeptId());
                bo.setDataType(version.getDataType());
                // 复审文件清单
                List<ReviewApplyItemBo> itemList = new ArrayList<>();
                ReviewApplyItemBo itemBo = new ReviewApplyItemBo();
                Standard standard= iStandardService.getById(version.getStandardId());
                itemBo.setDocName(standard.getDocName());
                itemBo.setDocId(version.getDocId());
                itemBo.setDocClass(standard.getDocClass());
                itemBo.setVersionId(version.getId());
                itemBo.setVersionValue(version.getVersionValue());
                itemBo.setUserName(version.getUserName());
                itemBo.setDeptId(version.getDeptId());
                itemList.add(itemBo);
                bo.setItemList(itemList);
                bo.setEditStatus(true);

                // 组装流程参数
                BpmClientInputModel bpmClientInputModel = new BpmClientInputModel();
                bpmClientInputModel.setWf_procDefId(processDefinitionModel.getProcDefId());
                bpmClientInputModel.setWf_procDefKey(processDefinitionModel.getProcDefKey());
                bpmClientInputModel.setWf_procTitle(bo.getApplyTitle());
                // 流程发起者
                bpmClientInputModel.setWf_sendUserId(reminder.getUserName());
                bpmClientInputModel.setWf_sendUserOrgId(reminder.getDeptId());
                // 设置接收人
                List<Map<String,Object>> list = new ArrayList<>();
                Map<String,Object> map = new HashMap<>();
                map.put("receiveUserId",reminder.getUserName());
                map.put("receiveUserOrgId",reminder.getDeptId());
                list.add(map);
                bpmClientInputModel.setWf_receivers(list);
                bpmClientInputModelBo.setModel(bpmClientInputModel);
                bpmClientInputModelBo.setType(processConfig.getProcDefKeyFSSQ());
                bo.setBpmClientInputModel(bpmClientInputModelBo);
                // 流程暂存
                bo.setStatus(RecordStatusEnum.DRAFT.getCode());
                bo.setRecordStatus(RecordStatusEnum.DRAFT.getCode());
                // 流程保存
                iReviewApplyService.insertByBo(bo);
                XxlJobHelper.log(String.format("文件复审流程自动触发,流程标题：%s,接收人：%s",bo.getApplyTitle(),bo.getUserName()));
                // 执行成功数累加
                succ++;
            } catch (Exception e) {
                log.error("versionId="+version.getId()+",fileReviewJobHandler异常",e);
                errMsg += "versionId="+version.getId()+",发生异常"+e.getLocalizedMessage()+"\n";
                XxlJobHelper.log(ExceptionUtil.getRootCauseMessage(e));
            }
        }
        // 执行进度
        String process = "process="+succ+"/"+count;
        XxlJobHelper.log(process);
        XxlJobHelper.log(errMsg);
        XxlJobHelper.log("系统自动推送文件复审流程-结束");
        // 返回消息
        if(errMsg.length()>0) {
            return AjaxResult.error(errMsg,process);
        } else {
            return AjaxResult.success(errMsg,process);
        }
    }



    /**
     * 文件复审-备份（根据文件编制部门分组批量推送给部门文件管理员）
     *
     * @throws Exception
     */
    @XxlJob("fileReviewJobHandlerBak")
    public void fileReviewJobHandlerBak() throws Exception {
        XxlJobHelper.log("文件复审....");
        String today = DateUtil.today();
        List<Version> versionList = iVersionService.list(new LambdaQueryWrapper<Version>().le(Version::getReviewTime,DateUtil.tomorrow()).eq(Version::getStatus, Constants.ONE));
        if (versionList.size()<1) {
            return;
        }
        //获取流程配置
        ProcessConfig processConfig = JSONUtil.toBean(configService.selectConfigByKey(PROCESS_KEY), ProcessConfig.class);
        String procDefKey = processConfig.getProcDefKeyFSSQ();
        BpmClientInputModelBo bpmClientInputModelBo = new BpmClientInputModelBo();
        ProcessDefinitionModel processDefinitionModel = workflowService.getProcessDefinitionModel(procDefKey);
        //获取部门文件管理员
        List<SysUser> userList = iSysUserService.selectListAllByRoleKey("dept_file_admin");
        //按编制部门分组
        Map<String, List<Version>> deptIdMap = versionList.stream().collect(Collectors.groupingBy(Version::getDeptId));
        for (Map.Entry<String, List<Version>> deptIdEntry : deptIdMap.entrySet()) {
            String deptId = deptIdEntry.getKey();
            SysUser user = userList.stream().filter(sysUser -> deptId.equals(sysUser.getDeptId())).findFirst().orElse(null);
            if (user==null) {
                XxlJobHelper.log(deptId+"部门没有设置部门文件管理员");
                continue;
            }

            List<Version> dvlist = deptIdEntry.getValue();
            //按数据分组
            Map<String, List<Version>> dataTypeMap = dvlist.stream().collect(Collectors.groupingBy(Version::getDataType));
            for (Map.Entry<String, List<Version>> dataTypeEntry : dataTypeMap.entrySet()) {
                ReviewApplyBo bo = new ReviewApplyBo();
                bo.setApplyTitle(today+"自动发起复审");
                bo.setDeptId(deptId);
                bo.setUserName(user.getUserName());
                String dataType = dataTypeEntry.getKey();
                bo.setDataType(dataType);
                List<Version> ddvlist = dataTypeEntry.getValue();
                List<ReviewApplyItemBo> itemList = new ArrayList<>();
                for (Version version:ddvlist) {
                    ReviewApplyItemBo itemBo = new ReviewApplyItemBo();
                    Standard standard= iStandardService.getById(version.getStandardId());
                    itemBo.setDocName(standard.getDocName());
                    itemBo.setDocId(version.getDocId());
                    itemBo.setDocClass(standard.getDocClass());
                    itemBo.setVersionId(version.getId());
                    itemBo.setVersionValue(version.getVersionValue());
                    itemBo.setUserName(version.getUserName());
                    itemBo.setDeptId(version.getDeptId());
                    itemList.add(itemBo);
                }
                bo.setItemList(itemList);
                //组装流程参数
                BpmClientInputModel bpmClientInputModel = new BpmClientInputModel();
                bpmClientInputModel.setWf_procDefId(processDefinitionModel.getProcDefId());
                bpmClientInputModel.setWf_procDefKey(processDefinitionModel.getProcDefKey());
                bpmClientInputModel.setWf_procTitle(bo.getApplyTitle());
                bpmClientInputModel.setWf_sendUserId(bo.getUserName());
                bpmClientInputModel.setWf_sendUserOrgId(bo.getDeptId());
                // 设置接收人
                List<Map<String,Object>> list = new ArrayList<>();
                Map<String,Object> map = new HashMap<>();
                map.put("receiveUserId",bo.getUserName());
                map.put("receiveUserOrgId",bo.getDeptId());
                list.add(map);
                bpmClientInputModel.setWf_receivers(list);
                bpmClientInputModelBo.setModel(bpmClientInputModel);
                bpmClientInputModelBo.setType(procDefKey);
                bo.setBpmClientInputModel(bpmClientInputModelBo);
                //草稿
                bo.setRecordStatus(RecordStatusEnum.DRAFT.getCode());
                iReviewApplyService.insertByBo(bo);
            }
        }
    }

    /**
     * 文件借阅失效
     *
     * @throws Exception
     */
    @XxlJob("fileBorrowCanJobHandler")
    public void fileBorrowCanJobHandler() throws Exception {
        XxlJobHelper.log("文件借阅失效....");
    }

    /**
     * 文件阅读统计
     *
     * @throws Exception
     */
    @XxlJob("fileReadJobHandler")
    public void fileReadJobHandler() throws Exception {
        XxlJobHelper.log("文件阅读统计....");
        iStoreFileService.opstatiStics();
    }




    public void init() {
        log.info("init");
    }

    public void destroy() {
        log.info("destory");
    }

}

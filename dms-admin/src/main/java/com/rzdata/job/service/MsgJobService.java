package com.rzdata.job.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.rzdata.config.CustomConfig;
import com.rzdata.framework.constant.DocMsgConstants;
import com.rzdata.framework.constant.NumberConstants;
import com.rzdata.framework.constant.ProcessStatusConstants;
import com.rzdata.framework.constant.RoleConstants;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.core.domain.entity.SysDept;
import com.rzdata.framework.core.domain.entity.SysUser;
import com.rzdata.framework.utils.StringUtils;
import com.rzdata.process.domain.DocMessage;
import com.rzdata.process.domain.Version;
import com.rzdata.process.domain.vo.ReviewApplyVo;
import com.rzdata.process.domain.vo.StandardVo;
import com.rzdata.process.enums.DocMessageEnum;
import com.rzdata.process.enums.MsgTypeEnum;
import com.rzdata.process.enums.SendType;
import com.rzdata.process.service.*;
import com.rzdata.process.utils.MsgTemplateUtils;
import com.rzdata.system.domain.SysNotifyLog;
import com.rzdata.system.domain.vo.SysUserVo;
import com.rzdata.system.service.ISysDeptService;
import com.rzdata.system.service.ISysNotifyLogService;
import com.rzdata.system.service.ISysUserService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 消息任务服务类
 * 主要处理系统中各类定时消息通知任务
 */
@Slf4j
@Service
public class MsgJobService {
    @Autowired
    IStandardService iStandardService;

    @Autowired
    IVersionService iVersionService;

    @Autowired
    IReviewApplyService iReviewApplyService;

    @Autowired
    ISysUserService iSysUserService;

    @Autowired
    IDocMessageService iDocMessageService;

    @Autowired
    IDocMessageService docMessageService;

    private static final long KEY_INTERVAL = 20;

    @Autowired
    CustomConfig customConfig;

    @Autowired
    ISysDeptService sysDeptService;

    @Autowired
    ISysNotifyLogService sysNotifyLogService;

    @Autowired
    private IMessageSendEntryService messageSendEntryService;

    /**
     * 文件复审到期提醒任务
     * 定时检查即将到期需要复审的文件，并发送提醒消息给相关负责人
     *
     * @return AjaxResult 任务执行结果
     */
    @XxlJob("reviewRemindJobHandler")
    public AjaxResult reviewRemindJobHandler() {
        String errMsg = "";
        String fileType = getFileTypeFromJobParam();

        XxlJobHelper.log("文件复审到期提醒-开始");

        // 校验配置
        String validationError = validateConfig();
        if (validationError != null) {
            XxlJobHelper.log(validationError);
            return AjaxResult.error(validationError);
        }

        // 获取公司文控
        List<SysUser> fileManagers = iSysUserService.selectListAllByRoleKey("company_file_manager");
        String[] reviewMsgForwardDays = customConfig.getReviewMsgForwardDay().split(",");

        int totalCount = 0;
        int successCount = 0;

        for (String days : reviewMsgForwardDays) {
            XxlJobHelper.log("开始搜索距离" + days + "天的数据-开始");
            List<HashMap> unReviewList = iReviewApplyService.selectUnReviewList(Integer.parseInt(days), fileType);
            XxlJobHelper.log("获取数据行记录=" + unReviewList.size());
            totalCount += unReviewList.size();

            for (HashMap itemMap : unReviewList) {
                Version version = iVersionService.getById(itemMap.get("version_id").toString());
                try {
                    // 获取提醒接收者
                    SysUser reminder = getReminder(version, fileManagers);
                    if (reminder == null) {
                        errMsg += "versionId=" + version.getId() + ",没有找到复审消息接收者\n";
                        continue;
                    }

                    // 构建消息内容
                    String msgContent = buildMessageContent(version, days);
                    String linkUrl = MsgTemplateUtils.buildPcFileDetailUrl(version.getDocId(), version.getId());
                    String mobileUrl = MsgTemplateUtils.buildMobilFileDetailUrl(version.getDocId(), version.getId());
                    Map<String, String> emailParams = buildReviewEmail(version, linkUrl, days);

                    // 发送各类通知
                    sendNotifications(reminder, version, msgContent, linkUrl, mobileUrl, emailParams);
                    successCount++;

                } catch (Exception e) {
                    String error = String.format("versionId=%s,发生异常%s\n", version.getId(), e.getLocalizedMessage());
                    log.error("reviewRemindJobHandler异常", e);
                    errMsg += error;
                    XxlJobHelper.log(ExceptionUtil.getRootCauseMessage(e));
                }
            }
            XxlJobHelper.log("开始搜索距离" + days + "天的数据-结束");
        }

        // 记录执行结果
        String process = String.format("process=%d/%d", successCount, totalCount);
        XxlJobHelper.log(process);
        XxlJobHelper.log(errMsg);
        XxlJobHelper.log("文件复审到期提醒-结束");

        return errMsg.length() > 0 ? AjaxResult.error(errMsg, process) : AjaxResult.success(errMsg, process);
    }

    /**
     * 复审期提醒（不用）
     *
     * @throws Exception
     */
    @XxlJob("reviewRemindJobHandlerBak")
    @Deprecated
    public void reviewRemindJobHandlerBak() throws Exception {
        XxlJobHelper.log("文件复审期提醒....");
        List<String> statusList = Arrays.asList(ProcessStatusConstants.TO_DO, ProcessStatusConstants.TO_DRAFT);
        List<Integer> postIds = Arrays.asList(NumberConstants.TWO, NumberConstants.THREE);
        List<ReviewApplyVo> raVoList = iReviewApplyService.selectUndueList(statusList);
        if (ObjectUtil.isNotEmpty(raVoList)) {
            Map<String, List<ReviewApplyVo>> map = raVoList.stream()
                    .collect(Collectors.groupingBy(ReviewApplyVo::getId));
            Date date = DateUtil.date();
            for (Map.Entry<String, List<ReviewApplyVo>> entry : map.entrySet()) {
                List<ReviewApplyVo> reviewApplyVos = entry.getValue();
                for (ReviewApplyVo reviewApplyVo : reviewApplyVos) {
                    Date dueTime = DateUtil.offset(reviewApplyVo.getCreateTime(), DateField.YEAR,
                            reviewApplyVo.getReviewCycle().intValue());
                    long interval = DateUtil.between(date, dueTime, DateUnit.DAY, false);
                    if (NumberUtil.equals(interval, KEY_INTERVAL)) {
                        List<String> deptIds = Collections.singletonList(reviewApplyVo.getDeptId());
                        List<SysUserVo> userList = iSysUserService.selectUserByDeptPost(deptIds, postIds,
                                reviewApplyVo.getTenantId());
                        List<DocMessage> docMessageList = new ArrayList<>();
                        if (ObjectUtil.isNotEmpty(userList)) {
                            for (SysUserVo suv : userList) {
                                DocMessage docMessage = new DocMessage();
                                docMessage.setApplyId(entry.getKey());
                                // docMessage.setDocId(reviewApplyVo.getDocId());
                                // docMessage.setVersionValue(reviewApplyVo.getVersionValue());
                                // docMessage.setDocClass(reviewApplyVo.getDocClass());
                                // docMessage.setDocName(reviewApplyVo.getDocName());
                                docMessage.setDeptId(reviewApplyVo.getDeptId());
                                docMessage.setDeptName(suv.getDeptName());
                                docMessage.setMsgStatus(NumberConstants.ZERO);
                                docMessage.setMsgInfo("复审（" + DateUtil.formatDateTime(dueTime) + "）");
                                docMessage.setMsgClass(MsgTypeEnum.REVIEW.getType());
                                docMessage.setCreateTime(date);
                                docMessage.setRecoveryUser(suv.getUserName());
                                docMessage.setRecoveryUserId(suv.getUserId());
                                docMessageList.add(docMessage);
                            }
                        }
                        iDocMessageService.saveBatch(docMessageList);
                    }
                }
            }
        }
    }

    /**
     * 文件有效期提醒任务
     * 检查文件到期时间，提前发送提醒消息
     *
     * @throws Exception
     */
    @XxlJob("invalidRemindJobHandler")
    public void invalidRemindJobHandler() throws Exception {
        XxlJobHelper.log("执行文件有效期提醒任务....");

        // 获取部门文控和部门经理岗位ID
        List<Integer> postIds = Arrays.asList(NumberConstants.TWO, NumberConstants.THREE);

        // 获取有效文件列表
        List<StandardVo> validFiles = iStandardService.selectValidFile();
        if (ObjectUtil.isEmpty(validFiles)) {
            return;
        }

        Date currentDate = DateUtil.date();

        // 处理每个文件
        validFiles.stream()
                .filter(file -> shouldSendReminder(file, currentDate))
                .forEach(file -> processFileReminders(file, postIds));
    }

    /**
     * 判断是否需要发送提醒
     */
    private boolean shouldSendReminder(StandardVo file, Date currentDate) {
        return NumberUtil.equals(
                DateUtil.between(currentDate, file.getEndDate(), DateUnit.DAY, false),
                KEY_INTERVAL);
    }

    /**
     * 处理单个文件的提醒消息
     */
    private void processFileReminders(StandardVo file, List<Integer> postIds) {
        // 获取需要通知的用户
        List<String> deptIds = Collections.singletonList(file.getDeptId());
        List<SysUserVo> notifyUsers = iSysUserService.selectUserByDeptPost(
                deptIds,
                postIds,
                file.getTenantId());

        if (ObjectUtil.isNotEmpty(notifyUsers)) {
            notifyUsers.forEach(user -> sendNotifications(file, user));
        }
    }

    /**
     * 发送各类通知
     */
    private void sendNotifications(StandardVo file, SysUserVo user) {
        String linkUrl = MsgTemplateUtils.buildPcFileDetailUrl(file.getDocId(), file.getVersionId());
        String mobileUrl = MsgTemplateUtils.buildMobilFileDetailUrl(file.getDocId(), file.getVersionId());
        String msgContent = buildMessageContent(file);
        Map<String, String> emailParams = buildEmailParams(file, linkUrl, mobileUrl);

        // 发送邮件
        try {
            sysNotifyLogService.sendEmail(SendType.LIFESPAN.getCode(), user.getNickName(),
                    user.getEmail(), user.getUserId(), emailParams);
        } catch (Exception e) {
            log.error("发送回收邮件异常: ", e);
        }

        // 发送站内信
        try {
            sendInternalMessage(file, user, msgContent, linkUrl, mobileUrl);
        } catch (Exception e) {
            log.error("发送站内信异常: ", e);
        }

        // 发送企业微信消息
        try {
            messageSendEntryService.sendMsgQywx(file.getDocId(), file.getVersionId(),
                    user.getUserName(), msgContent);
        } catch (Exception e) {
            log.error("发送企业微信消息异常: ", e);
        }
    }

    /**
     * 构建消息内容
     */
    private String buildMessageContent(StandardVo file) {
        return String.format(DocMessageEnum.SEND_MSG_TYPE_LIFESPAN.getMsg(),
                file.getDocName(), file.getVersionValue(),
                DateUtil.formatDateTime(file.getEndDate()));
    }

    /**
     * 构建邮件参数
     */
    private Map<String, String> buildEmailParams(StandardVo file, String linkUrl, String mobileUrl) {
        Map<String, String> param = new HashMap<>();
        param.put("pcLink", linkUrl);
        param.put("mobileLink", mobileUrl);
        param.put("docName", file.getDocName());
        param.put("version", file.getVersionValue());
        return param;
    }

    /**
     * 发送站内信
     */
    private void sendInternalMessage(StandardVo file, SysUserVo user,
            String msgContent, String linkUrl, String mobileUrl) {
        docMessageService.sendInstationMessage(null, file.getDocId(),
                file.getDocClass(), file.getDocName(), file.getVersionId(),
                file.getVersionValue(), file.getDeptId(), null, user.getUserName(),
                user.getUserId(), msgContent, DocMsgConstants.MSG_TYPE_MSG,
                MsgTypeEnum.VALIDITY_LIFESPAN.getType(), linkUrl, mobileUrl, file.getTenantId());
    }

    /**
     * 构建文档提醒消息
     */
    private DocMessage buildDocMessage(StandardVo file, SysUserVo user, Date currentDate) {
        DocMessage message = new DocMessage();
        message.setDocId(file.getId());
        message.setVersionValue(file.getVersionValue());
        message.setDocClass(file.getDocClass());
        message.setDocName(file.getDocName());
        message.setDeptId(file.getDeptId());
        message.setDeptName(user.getDeptName());
        message.setMsgStatus(NumberConstants.ZERO);
        message.setMsgInfo("到期（" + DateUtil.formatDateTime(file.getEndDate()) + "）");
        message.setMsgClass(MsgTypeEnum.INVALIDATION.getType());
        message.setCreateTime(currentDate);
        message.setRecoveryUser(user.getUserName());
        message.setRecoveryUserId(user.getUserId());
        return message;
    }

    /**
     * 编制人离职消息通知任务
     * 当文档编制人离职时，通知部门文控或部门经理进行文档交接
     * 
     * 处理流程：
     * 1. 查询所有有编制人的文档版本
     * 2. 检查编制人是否离职
     * 3. 确定消息接收人（优先部门经理，其次公司文控）
     * 4. 发送文档交接通知
     * 
     * 特殊处理：
     * - 避免重复发送相同通知
     * - 确保消息发送给正确部门的负责人
     */
    @XxlJob("resignationsSendMsg")
    public void resignationsSendMsg() {
        // 编制人离职，需要发送给部门文控、部门经理
        // 1.先查询编制人员
        List<Version> versionList = iVersionService
                .list(new LambdaQueryWrapper<Version>().isNotNull(Version::getUserName));
        if (CollUtil.isEmpty(versionList)) {
            return;
        }

        // 2. 获取所有不重复的编制人用户名
        List<String> userNameList = versionList.stream()
                .filter(item -> StrUtil.isNotBlank(item.getUserName()))
                .map(Version::getUserName)
                .distinct()
                .collect(Collectors.toList());

        // 3. 查询已离职的编制人（状态为0表示离职）
        List<SysUser> sysUserList = iSysUserService.list(new LambdaQueryWrapper<SysUser>()
                .in(SysUser::getUserName, userNameList)
                .eq(SysUser::getStatus, Constants.ZERO));
        if (CollUtil.isEmpty(sysUserList)) {
            return;
        }

        // 4. 查询消息接收人
        // 4.1 查询部门经理
        List<SysUser> deptNameList = iSysUserService.selectListAllByRoleKey(RoleConstants.DEPT_MANAGE);
        // 4.2 查询公司文控（作为备选接收人）
        List<SysUser> companyFileList = iSysUserService.selectListAllByRoleKey(RoleConstants.COMPANY_FILE_MANAGER);

        // 5. 获取相关部门信息
        // 5.1 获取离职人员所在部门
        List<String> sysUserDeptIdList = sysUserList.stream()
                .map(SysUser::getDeptId)
                .collect(Collectors.toList());
        List<SysDept> userDeptList = sysDeptService.list(new LambdaQueryWrapper<SysDept>()
                .in(SysDept::getDeptId, sysUserDeptIdList));

        // 5.2 获取公司文控所在部门
        List<SysDept> companyDeptList = new ArrayList<>();
        if (CollUtil.isNotEmpty(companyFileList)) {
            List<String> companyDeptNameList = sysUserList.stream().map(SysUser::getDeptId)
                    .collect(Collectors.toList());
            companyDeptList.addAll(
                    sysDeptService.list(new LambdaQueryWrapper<SysDept>().in(SysDept::getDeptId, companyDeptNameList)));
        }

        // 查询编制人员离职提醒邮件消息
        List<SysNotifyLog> sysNotifyLogList = sysNotifyLogService.list(new LambdaQueryWrapper<SysNotifyLog>()
                .eq(SysNotifyLog::getStatus, com.rzdata.framework.constant.Constants.LONG_ONE)
                .eq(SysNotifyLog::getTitle, SendType.LZ.getMsg()));

        // 7. 发送离职通知邮件
        if (CollUtil.isEmpty(sysNotifyLogList)) {
            // 7.1 如果没有发送过通知，直接发送
            sendLsEmail(sysUserList, deptNameList, companyFileList, userDeptList, companyDeptList);
            return;
        }

        // 7.2 如果已发送过通知，筛选需要重新发送的用户
        List<SysUser> sendUserList = filterUsersNeedNotification(sysUserList, deptNameList,
                companyFileList, sysNotifyLogList, userDeptList, companyDeptList);

        // 8. 发送邮件给筛选后的用户
        if (CollUtil.isNotEmpty(sendUserList)) {
            sendLsEmail(sendUserList, deptNameList, companyFileList, userDeptList, companyDeptList);
        }
    }

    /**
     * 发送离职邮件
     * 
     * @param sysUserList
     * @param deptNameList
     * @param companyFileList
     * @param userDeptList
     * @param companyDeptList
     */
    private void sendLsEmail(List<SysUser> sysUserList, List<SysUser> deptNameList, List<SysUser> companyFileList,
            List<SysDept> userDeptList, List<SysDept> companyDeptList) {
        for (SysUser sysUser : sysUserList) {
            Map<String, String> param = new HashMap<>();
            param.put("userName", sysUser.getNickName());
            if (CollUtil.isNotEmpty(deptNameList)) {
                for (SysUser deptUser : deptNameList) {
                    if (StrUtil.isBlank(deptUser.getEmail())) {
                        continue;
                    }
                    if (sysUser.getDeptId().equals(deptUser.getDeptId())) {
                        sysNotifyLogService.sendEmail(SendType.LZ.getCode(), deptUser.getNickName(),
                                deptUser.getEmail(), deptUser.getUserId(), param);
                    }

                }
            } else if (CollUtil.isNotEmpty(companyFileList)) {
                List<SysDept> sysDeptList = userDeptList.stream()
                        .filter(item -> item.getDeptId().equals(sysUser.getDeptId())).collect(Collectors.toList());
                if (CollUtil.isEmpty(sysDeptList)) {
                    continue;
                }
                SysDept sysDept = sysDeptList.get(0);
                for (SysUser deptFileUser : companyFileList) {
                    if (StrUtil.isBlank(deptFileUser.getEmail())) {
                        continue;
                    }
                    List<SysDept> comDeptList = companyDeptList.stream()
                            .filter(item -> item.getDeptId().equals(deptFileUser.getDeptId()))
                            .collect(Collectors.toList());
                    if (CollUtil.isEmpty(comDeptList)) {
                        continue;
                    }
                    SysDept deptDept = comDeptList.get(0);
                    if (getCompanyName(sysDept.getDeptFullPathId())
                            .equals(getCompanyName(deptDept.getDeptFullPathId()))) {
                        sysNotifyLogService.sendEmail(SendType.LZ.getCode(), deptFileUser.getNickName(),
                                deptFileUser.getEmail(), deptFileUser.getUserId(), param);
                    }
                }
            }

        }
    }

    public String getCompanyName(String name) {
        // 使用逗号切割字符串
        String[] parts = name.split(",");

        // 如果数组长度小于2，则说明没有倒数第二个元素
        if (parts.length >= 2) {
            // 获取倒数第二个元素
            return parts[parts.length - 2];
        }
        return parts[parts.length - 1];
    }

    /**
     * 筛选需要发送通知的用户
     * 过滤掉已经发送过通知的用户
     */
    private List<SysUser> filterUsersNeedNotification(List<SysUser> sysUserList, List<SysUser> deptNameList,
            List<SysUser> companyFileList, List<SysNotifyLog> sysNotifyLogList,
            List<SysDept> userDeptList, List<SysDept> companyDeptList) {

        return sysUserList.stream()
                .filter(sysUser -> {
                    for (SysNotifyLog sysNotifyLog : sysNotifyLogList) {
                        // 检查部门经理是否已收到通知
                        for (SysUser deptUser : deptNameList) {
                            if (CollUtil.isNotEmpty(deptNameList) &&
                                    sysNotifyLog.getMessage().contains(sysUser.getNickName()) &&
                                    sysNotifyLog.getRemindId().contains(deptUser.getUserId())) {
                                return false;
                            }
                        }
                        // 检查公司文控是否已收到通知
                        for (SysUser deptFileUser : companyFileList) {
                            SysDept sysDept = userDeptList.stream()
                                    .filter(item -> item.getDeptId().equals(sysUser.getDeptId()))
                                    .findFirst().orElse(null);
                            SysDept deptDept = companyDeptList.stream()
                                    .filter(item -> item.getDeptId().equals(deptFileUser.getDeptId()))
                                    .findFirst().orElse(null);
                            if (CollUtil.isNotEmpty(companyFileList) &&
                                    sysDept != null && deptDept != null &&
                                    getCompanyName(sysDept.getDeptFullPathId())
                                            .equals(getCompanyName(deptDept.getDeptFullPathId()))
                                    &&
                                    sysNotifyLog.getMessage().contains(sysUser.getNickName()) &&
                                    sysNotifyLog.getRemindId().contains(deptFileUser.getUserId())) {
                                return false;
                            }
                        }
                    }
                    return true;
                })
                .collect(Collectors.toList());
    }

    /**
     * 从任务参数中获取文件类型
     */
    private String getFileTypeFromJobParam() {
        String jobParam = XxlJobHelper.getJobParam();
        if (StringUtils.isNotEmpty(jobParam)) {
            JSONObject json = JSONUtil.parseObj(jobParam);
            if (ObjectUtil.isNotEmpty(json.get("fileType"))) {
                return (String) json.get("fileType");
            }
        }
        return "";
    }

    /**
     * 校验复审配置
     * 
     * @return 错误信息，如果配置正确则返回null
     */
    private String validateConfig() {
        if (!customConfig.isReviewEnable()) {
            return "application.xml reviewEnable is false, review flow is not run!";
        }
        if (StringUtils.isEmpty(customConfig.getReviewMsgForwardDay())) {
            return "application.xml reviewMsgForwardDay is empty!";
        }
        return null;
    }

    /**
     * 获取提醒接收者
     * 
     * @param version      文档版本
     * @param fileManagers 文控人员列表
     * @return 提醒接收者
     */
    private SysUser getReminder(Version version, List<SysUser> fileManagers) {
        String confReminder = customConfig.getReviewMsgReminder();
        if (confReminder.contains("editer")) {
            // 优先获取文件的编制人
            if (StringUtils.isNotEmpty(version.getUserName())) {
                SysUser reminder = iSysUserService.selectUserByUserName(version.getUserName());
                if (reminder != null) {
                    return reminder;
                }
            }
            // 编制人不存在时，使用第一个文控
            return fileManagers.isEmpty() ? null : fileManagers.get(0);
        } else {
            // 获取配置指定角色人群
            List<SysUser> confRoleUsers = iSysUserService.selectListAllByRoleKey(confReminder);
            return confRoleUsers.isEmpty() ? null : confRoleUsers.get(0);
        }
    }

    /**
     * 构建复审提醒消息内容
     */
    private String buildMessageContent(Version version, String days) {
        return String.format("【文件复审】%s(%s)即将在[%s]天后自动推送文件复审流程。",
                version.getDocName(),
                version.getDocId(),
                days);
    }

    /**
     * 发送各类通知 - 文件复审提醒
     */
    private void sendNotifications(SysUser reminder, Version version, String msgContent,
            String linkUrl, String mobileUrl, Map<String, String> emailParams) {
        // 发送邮件
        try {
            sysNotifyLogService.sendEmail(SendType.FILE_REVIEW_PROMPT.getCode(), reminder.getNickName(),
                    reminder.getEmail(), reminder.getUserId(), emailParams);
        } catch (Exception e) {
            log.error("发送复审提醒邮件异常: ", e);
        }

        // 发送站内信
        try {
            docMessageService.sendInstationMessage(null, version.getDocId(), null,
                    version.getDocName(), version.getId(), version.getVersionValue(),
                    version.getDeptId(), null, reminder.getUserName(), reminder.getUserId(),
                    msgContent, DocMsgConstants.MSG_TYPE_MSG, MsgTypeEnum.FILE_REVIEW_PROMPT.getType(), linkUrl, mobileUrl, null);
        } catch (Exception e) {
            log.error("发送站内信异常: ", e);
        }

        // 发送企业微信消息
        try {
            messageSendEntryService.sendMsgQywx(version.getDocId(), version.getId(),
                    reminder.getUserName(), msgContent);
        } catch (Exception e) {
            log.error("发送企业微信消息异常: ", e);
        }
    }

    /**
     * 构建复审邮件参数
     * 
     * @return
     */
    private Map<String, String> buildReviewEmail(Version version, String linkUrl, String days) {
        Map<String, String> param = new HashMap<>();
        param.put("link", linkUrl);
        param.put("docName", version.getDocName());
        param.put("version", version.getVersionValue());
        param.put("dayInfo", days);
        return param;
    }

}

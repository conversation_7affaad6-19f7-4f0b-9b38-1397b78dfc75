package com.rzdata.job.service;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.rzdata.process.service.SuperviseWorkflowEntranceService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class SuperviseWorkflowJobService {

    @Autowired
    SuperviseWorkflowEntranceService superviseWorkflowEntranceService;

    @XxlJob("superviseWorkflowJobHandler")
    public void superviseWorkflowJobHandler() throws Exception {
        String jobParamStr = XxlJobHelper.getJobParam();
        log.info("superviseWorkflowJobHandler, jobParamStr:{}", jobParamStr);
        if(StrUtil.isNotEmpty(jobParamStr)){
            JSONObject jobParamJson = JSONUtil.parseObj(jobParamStr);
            String superviseTimeValue = jobParamJson.getStr("superviseTimeValue");
            superviseWorkflowEntranceService.launchSuperviseWorkflow(superviseTimeValue);
        }
    }
}

package com.rzdata.oa.controller;

import cn.hutool.core.util.URLUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.rzdata.framework.core.domain.entity.SysUser;
import com.rzdata.framework.core.service.UserService;
import com.rzdata.framework.utils.StringUtils;
import com.rzdata.oa.config.OaProperties;
import com.rzdata.oa.service.TodoService;
import com.rzdata.oa.webservice.*;
import com.rzdata.system.service.SysLoginService;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.UnsupportedEncodingException;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Map;
import java.util.regex.Pattern;

@Validated
@Api(value = "Oa 单点登录进入待办", tags = {"OA 单点登录进入待办"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/sso")
@Slf4j
public class OssAccessTodoController {
    private final OaProperties oaProperties;
    private final SysLoginService loginService;
    private final TodoService dozerService;

    @Autowired
    private UserService userService;

    @SneakyThrows
    @GetMapping("oa/accessTodo")
    public void accessTodo(String redirectUri, String code,HttpServletRequest request, HttpServletResponse response) {
        if (StringUtils.isNotEmpty(code)) {
            log.info("带code获取身份请求<<<<<<{}",JSONUtil.toJsonPrettyStr(request.getParameterMap()));
            //根据code获取access_token
            String tokenUrl = oaProperties.getAuth().getUrl() + "/sso/oauth/accessToken?client_id=" + oaProperties.getAuth().getClient()
                    + "&client_secret=" + oaProperties.getAuth().getSecret() +
                    "&grant_type=authorization_code&redirect_uri=" + redirectUri + "&code=" + code;
            String access_token = HttpUtil.post(tokenUrl, new HashMap<>());
            log.error("POST{},获得access_token:{}",tokenUrl,access_token);
            String accessToken = JSONUtil.parseObj(access_token).getStr("access_token");
            //根据access_token获取用户信息
            String userUrl = oaProperties.getAuth().getUrl() + "/sso/oauth/userInfo?access_token=" + accessToken;
            String user_info = HttpUtil.post(userUrl, new HashMap<>());
            log.error("POST{}通过token，获取到用户信息:{}",userUrl,user_info);
            String uid = JSONUtil.parseObj(user_info).getStr("uid");
            //获取到业务信息后，业务系统自身业务逻辑
            String userName = uid+"@mehowcy.com";
            SysUser sysUser =userService.selectUserByUserName(userName);
            if(sysUser == null){
                userName=uid+"@mehowmy.com";
            }

            String token = loginService.sso(userName);
            redirectUri = URLEncoder.encode(request.getQueryString().replace("redirectUri=",""),"utf-8");
            // 获取请求的 Agent
            String userAgent = request.getHeader("User-Agent");
            String baseUrl = oaProperties.getAuth().getFrontUrl();
            if (StringUtils.isMobile(userAgent)) {
                baseUrl = oaProperties.getAuth().getMbfrontUrl();
            }
            // 前端单点登录跳转
            response.sendRedirect(baseUrl + "?redirect=" + redirectUri + "&token=" + token);
        } else {
            log.info("获取code 回调请求<<<<<<{}",JSONUtil.toJsonPrettyStr(request.getParameterMap()));
            //重定向认证 获取code
            String authUrl = oaProperties.getAuth().getUrl() + "/sso/oauth/authorize?client_id=" + oaProperties.getAuth().getClient()
                    + "&response_type=code&redirect_uri=" + URLEncoder.encode(oaProperties.getAuth().getAccessUrl() + "?redirectUri=" + redirectUri,"utf-8");
            log.info("请求url:>>>>>>{}",authUrl);

            response.sendRedirect(authUrl);
        }
    }

    @PostMapping("/mock/sso/oauth/accessToken")
    public Map<String, String> getAccessToken(String clientId, String client_secret, String grant_type, String redirectUri, String code) {
        Map<String, String> map = new HashMap<>();
        map.put("token", "tokentest");
        return map;
    }

    @SneakyThrows
    @GetMapping("/mock/sso/oauth/authorize")
    public void getAccessCode(HttpServletRequest request, HttpServletResponse response) {
        String client_id = request.getParameter("client_id"), response_type = request.getParameter("response_type"), redirect_uri = request.getParameter("redirect_uri");
        //redirect_uri = ecodeUrlParam(redirect_uri);
        response.sendRedirect(redirect_uri+ "&code=test");
    }

    @PostMapping("/mock/sso/oauth/userInfo")
    public Map<String, String> getUserInfo(String accessToken) {
        Map<String, String> map = new HashMap<>();
        map.put("uid", "adminmh");
        return map;
    }

    private String ecodeUrlParam(String url) {
        try {
            url = url.replace("#", "%23");
            // 解析原始URL
            URI originalUri = new URI(url);

            // 获取原始查询字符串
            String originalQuery = originalUri.getQuery();
            // 对查询字符串进行编码
            if (originalQuery != null) {;
                // 第一个=的位置：
                int firstEqualSignIndex = originalQuery.indexOf("=");
                String front= originalQuery.substring(0,firstEqualSignIndex+1);
                String after= originalQuery.substring(firstEqualSignIndex+1);
                after = URLEncoder.encode(after, "UTF-8");
                // 重新构建URI，使用编码后的查询字符串
                URI encodedUri = new URI(originalUri.getScheme(), originalUri.getAuthority(),
                        originalUri.getPath(), front+after, originalUri.getFragment());

                // 将编码后的URI转换为字符串
                String redirectUri = encodedUri.toString();
                return redirectUri;
            }
            return originalUri.toString();

        } catch (URISyntaxException | UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        return null;
    }
    /***************** 用于测试 **********************/
    @PostMapping("/getTodoCount")
    public NotifyTodoAppResult getTodoCount(@RequestBody NotifyTodoGetCountContext countContext) {
        log.error("获取待办数量");
        NotifyTodoAppResult result = dozerService.getTodoCount(countContext);
        log.error("返回结果:{}",result);
        return result;
    }
    @PostMapping("/sendTodo")
    public NotifyTodoAppResult sendTodo(@RequestBody NotifyTodoSendContext sendContext) {
        log.error("发送待办");
        NotifyTodoAppResult result = dozerService.sendTodo(sendContext);
        log.error("返回结果:{}",result);
        return result;
    }
    @PostMapping("/doneTodo")
    public NotifyTodoAppResult doneTodo(@RequestBody NotifyTodoRemoveContext doneContext) {
        log.error("完成待办");
        NotifyTodoAppResult result = dozerService.doneTodo(doneContext);
        log.error("返回结果:{}",result);
        return result;
    }
    @PostMapping("/deleteTodo")
    public NotifyTodoAppResult deleteTodo(@RequestBody NotifyTodoRemoveContext removeContext) {
        log.error("删除待办");
        NotifyTodoAppResult result = dozerService.deleteTodo(removeContext);
        log.error("返回结果:{}",result);
        return result;
    }

}

package com.rzdata.plugins.filesync;

import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 读取项目相关配置
 *
 * <AUTHOR> Li
 */

@Data
@Accessors(chain = true)
@Component
@ConfigurationProperties(prefix = "sync")
public class SyncConfig {

    /**
     * 是否开启同步
     */
    private boolean enabled;

    /**
     * 路径
     */
    private String url;

    /**
     * 账号
     */
    private String account;
    /**
     * 密码
     */
    private String password;

}
package com.rzdata.plugins.filesync.impl;

import com.alibaba.fastjson.JSONObject;
import com.rzdata.SyncHelper;
import com.rzdata.framework.core.service.TokenService;
import com.rzdata.framework.utils.ServletUtils;
import com.rzdata.plugins.filesync.IFileSyncService;
import com.rzdata.plugins.filesync.SyncConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/*
 * 添加生效文件或者作废文件到爱数门户的文档中心
 *
 * @author: xiefc
 * @date:2023/11/1 14:02
 */
@Slf4j
@Service
public class AsFileSyncService implements IFileSyncService {

    @Autowired
    SyncConfig syncConfig;

    @Resource
    TokenService tokenService;

    /**
     * 添加生效文件到爱数文档库
     *
     * @param versionId
     * @return
     */
    @Override
    public JSONObject add(String versionId) {
        JSONObject result = null;
        if(!syncConfig.isEnabled()) {
            log.error("同步DMS生效文件到爱数文档库配置未开启，请先检查。");
            return result;
        }
        try {
            SyncHelper syncHelper = new SyncHelper();
            result = syncHelper.add(syncConfig.getUrl(),syncConfig.getAccount(),syncConfig.getPassword(),versionId,tokenService.getToken(ServletUtils.getRequest()));
        } catch (Exception e) {
            log.error("同步DMS添加生效文件到爱数文档库异常，doc_version_id="+versionId,e);
        }
        return result;
    }

    @Override
    public JSONObject update(String versionId) {
        JSONObject result = null;
        if(!syncConfig.isEnabled()) {
            log.error("同步DMS生效文件到爱数文档库配置未开启，请先检查。");
            return result;
        }
        try {
            SyncHelper syncHelper = new SyncHelper();
            result = syncHelper.update(syncConfig.getUrl(),syncConfig.getAccount(),syncConfig.getPassword(),versionId,tokenService.getToken(ServletUtils.getRequest()));
        } catch (Exception e) {
            log.error("同步DMS更新生效文件到爱数文档库异常，doc_version_id="+versionId,e);
        }
        return result;
    }

    /**
     * 添加作废文件到爱数文档库
     *
     * @param versionId
     * @return
     */
    @Override
    public JSONObject disuse(String versionId) {
        JSONObject result = null;
        if(!syncConfig.isEnabled()) {
            log.error("同步DMS作废文件到爱数文档库配置未开启，请先检查。");
            return result;
        }
        try {
            SyncHelper syncHelper = new SyncHelper();
            result = syncHelper.disuse(syncConfig.getUrl(),syncConfig.getAccount(),syncConfig.getPassword(),versionId,tokenService.getToken(ServletUtils.getRequest()));
        } catch (Exception e) {
            log.error("同步DMS添加作废文件到爱数文档库异常，doc_version_id="+versionId,e);
        }
        return result;
    }
}

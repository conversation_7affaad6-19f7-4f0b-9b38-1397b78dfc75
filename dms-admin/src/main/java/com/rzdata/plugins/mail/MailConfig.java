package com.rzdata.plugins.mail;

import cn.hutool.extra.mail.MailAccount;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class MailConfig {

    @Bean
    @ConfigurationProperties(prefix = "mail")
    public MailAccount mailAccount() {
        return new MailAccount();
    }
}

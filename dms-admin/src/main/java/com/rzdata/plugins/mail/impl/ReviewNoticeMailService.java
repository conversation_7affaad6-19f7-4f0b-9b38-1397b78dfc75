package com.rzdata.plugins.mail.impl;

import cn.hutool.core.io.FileUtil;
import cn.hutool.system.SystemUtil;
import com.rzdata.framework.constant.CommonI18nConstant;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.core.domain.entity.SysUser;
import com.rzdata.framework.utils.DateUtils;
import com.rzdata.framework.utils.I18nUtils;
import com.rzdata.framework.utils.StringUtils;
import com.rzdata.plugins.mail.BizMailDto;
import com.rzdata.plugins.mail.IReviewNoticeMailService;
import com.rzdata.process.api.EmailController;
import com.rzdata.process.domain.DocMessage;
import com.rzdata.process.domain.Version;
import com.rzdata.process.service.IStandardService;
import com.rzdata.process.service.IVersionService;
import com.rzdata.setting.service.IDocClassService;
import com.rzdata.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/*
 * @author: xiefc
 * @date:2023/12/7 10:26
 */
@Service
public class ReviewNoticeMailService implements IReviewNoticeMailService {

    @Autowired
    ISysUserService sysUserService;

    @Autowired
    IDocClassService docClassService;

    @Autowired
    EmailController emailController;

    @Autowired
    IVersionService versionService;

    /**
     * 通知邮件模板
     */
    private static final String MAIL_TEMPLATE = "reviewBatchMailTemplate.html";



    /**
     * 文件复审到期提醒
     *
     * @param docMsgObj
     * @return
     */
    @Override
    public AjaxResult notice(DocMessage docMsgObj) {
        // 对同一个人发送邮件通知，包含多个待复审文件清单的逻辑未写完
        return this.core(docMsgObj);
    }


    /**
     * 文件复审到期提醒
     *
     * @param docMsgObj
     * @return
     */
    private AjaxResult core(DocMessage docMsgObj) {
        // 判断入参
        if(StringUtils.isNotEmpty(docMsgObj.getRecoveryUser())) {
            return AjaxResult.error(I18nUtils.getTitle(CommonI18nConstant.SERVICE_MAIL_ACCEPTER_NOT_NULL));
        }
        if(StringUtils.isNotEmpty(docMsgObj.getDocId())) {
            return AjaxResult.error("doc_modify_apply id"+I18nUtils.getTitle(CommonI18nConstant.SERVICE_MAIL_NOT_NULL));
        }
        //
        StringBuilder msgSp = new StringBuilder(50);
        // 总数、成功数量
        int count = 1;
        int success = 0;
        // 默认为生效文件模板
        String path = System.getProperty(SystemUtil.USER_DIR)+"/templates/"+ MAIL_TEMPLATE;

        String template = FileUtil.readUtf8String(path);
        // 当前文件分类、所属阶级、文件编号、文件名称、版本号、摘要内容、文件分类中文全路径
        //String fullPathName = this.docClassService.getClassFullPath(stdd.getDocClass());
        template = template.replace("{days}","30");
        //template = template.replace("{tableData}",fullPathName.split("/")[0]);
        // 邮件接收者
        SysUser user = this.sysUserService.selectUserByUserName(docMsgObj.getRecoveryUser());
        if(user == null) {
            msgSp.append("目标用户："+user.getUserName()+"，无效;");
        } else if(StringUtils.isNotEmpty(user.getEmail())) {
            // 邮件地址不为空
            BizMailDto newDto = new BizMailDto();
            newDto.setTarget(user.getEmail());
            newDto.setTitle("DMS系统-"+docMsgObj.getMsgInfo());
            newDto.setContent(template);
            // 发送邮件
            AjaxResult sendRes = this.emailController.push(newDto);
            if(sendRes.getCode() == 200) {
                success ++;
                // 底层发送邮件的回执消息
                msgSp.append(sendRes.getData().toString());
            } else {
                msgSp.append("目标用户："+user.getUserName()+"，发送邮件失败="+sendRes.getMsg()+";");
            }
        } else if(StringUtils.isEmpty(user.getEmail())) {
            msgSp.append("目标用户："+user.getUserName()+"，未配置email属性;");
        }
        if(count == success) {
            // 全部成功
            return AjaxResult.success("process:"+success+"/"+count,msgSp.toString());
        } else {
            // 部分失败
            return AjaxResult.error("process:"+success+"/"+count,msgSp.toString());
        }
    }

    private String buildTableDataHtml(String versionId) {
        if(StringUtils.isEmpty(versionId)) {
            return "";
        }
        StringBuilder sb = new StringBuilder(50);
        Version currVersion = this.versionService.getById(versionId);
        /*
        <tr>
						<td style="text-indent:2em;">QP-018 忠告性通知发布与实施控制程序A1.pdf</td>
						<td>A0</td>
						<td>王录</td>
						<td>CE组(工程部)</td>
						<td>2023-11-24</td>
						<td>2023-11-30</td>
						<td>2024-11-30</td>
					</tr>*/
        sb.append("<tr>");
        sb.append("<td>"+currVersion.getDocName()+"</td>");
        sb.append("<td>"+currVersion.getDocId()+"</td>");
        sb.append("<td>"+currVersion.getVersionValue()+"</td>");
        sb.append("<td>"+currVersion.getUserName()+"</td>");
        sb.append("<td>"+currVersion.getDeptId()+"</td>");
        sb.append("<td>"+ DateUtils.format(currVersion.getStartDate(),"yyyy-MM-dd") +"</td>");
        sb.append("<td>"+ DateUtils.format(currVersion.getReleaseTime(),"yyyy-MM-dd") +"</td>");
        sb.append("<td>"+ DateUtils.format(currVersion.getReviewTime(),"yyyy-MM-dd") +"</td>");
        sb.append("</tr>");
        return sb.toString();
    }
}

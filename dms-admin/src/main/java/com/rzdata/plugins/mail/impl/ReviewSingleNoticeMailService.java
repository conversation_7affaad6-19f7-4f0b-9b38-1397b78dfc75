package com.rzdata.plugins.mail.impl;

import cn.hutool.core.io.FileUtil;
import cn.hutool.system.SystemUtil;
import com.rzdata.framework.constant.CommonI18nConstant;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.core.domain.entity.SysUser;
import com.rzdata.framework.utils.DateUtils;
import com.rzdata.framework.utils.I18nUtils;
import com.rzdata.framework.utils.StringUtils;
import com.rzdata.plugins.mail.BizMailDto;
import com.rzdata.plugins.mail.IReviewNoticeMailService;
import com.rzdata.process.api.EmailController;
import com.rzdata.process.domain.DocMessage;
import com.rzdata.process.domain.Standard;
import com.rzdata.process.domain.Version;
import com.rzdata.process.mapper.StandardMapper;
import com.rzdata.process.mapper.VersionMapper;
import com.rzdata.setting.service.IDocClassService;
import com.rzdata.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/*
 * @author: xiefc
 * @date:2023/12/7 10:26
 */
@Service
public class ReviewSingleNoticeMailService implements IReviewNoticeMailService {

    @Autowired
    ISysUserService sysUserService;

    @Autowired
    StandardMapper stddService;

    @Autowired
    IDocClassService docClassService;

    @Autowired
    EmailController emailController;

    @Autowired
    VersionMapper versionMapper;

    /**
     * 通知邮件模板
     */
    private static final String MAIL_TEMPLATE = "reviewSingleMailTemplate.html";



    /**
     * 文件复审到期提醒
     *
     * @param docMsgObj
     * @return
     */
    @Override
    public AjaxResult notice(DocMessage docMsgObj) {
        // 对同一个人发送邮件通知，包含多个待复审文件清单的逻辑未写完
        return this.core(docMsgObj);
    }


    /**
     * 文件复审到期提醒
     *
     * @param docMsgObj
     * @return
     */
    private AjaxResult core(DocMessage docMsgObj) {
        // 判断入参
        if(StringUtils.isEmpty(docMsgObj.getRecoveryUser())) {
            return AjaxResult.error(I18nUtils.getTitle(CommonI18nConstant.SERVICE_MAIL_ACCEPTER_NOT_NULL));
        }
        if(StringUtils.isEmpty(docMsgObj.getVersionId())) {
            return AjaxResult.error("doc_modify_apply id"+I18nUtils.getTitle(CommonI18nConstant.SERVICE_MAIL_NOT_NULL));
        }
        //
        StringBuilder msgSp = new StringBuilder(50);
        // 总数、成功数量
        int count = 1;
        int success = 0;
        Version version = versionMapper.selectById(docMsgObj.getVersionId());
        Standard stdd = stddService.selectById(version.getStandardId());
        // 默认为生效文件模板
        String path = System.getProperty(SystemUtil.USER_DIR)+"/templates/"+ MAIL_TEMPLATE;

        String template = FileUtil.readUtf8String(path);
        // 距离复审日期、当前文件分类、所属阶级、文件编号、文件名称、版本号
        String fullPathName = this.docClassService.getClassFullPath(stdd.getDocClass());
        // 从站内消息中获取 距离天数
        String days = "";
        if(docMsgObj.getMsgInfo().contains("[")) {
            // 内容格式：【文件复审】%s(%s)即将在[45]天后自动推送文件复审流程。
            days = docMsgObj.getMsgInfo().substring(docMsgObj.getMsgInfo().indexOf("[")+1,docMsgObj.getMsgInfo().indexOf("]"));
        }
        template = template.replace("{days}",days);
        template = template.replace("{docCurrClassName}",this.docClassService.getById(stdd.getDocClass()).getClassName());
        template = template.replace("{docFirstClassName}",fullPathName.split("/")[0]);
        template = template.replace("{docId}",version.getDocId());
        template = template.replace("{docName}",version.getDocName());
        template = template.replace("{versionValue}",version.getVersionValue());
        // 生效日期、发布日期、下次复审日期
        template = template.replace("{startDate}",DateUtils.format(version.getStartDate(),"yyyy-MM-dd"));
        template = template.replace("{releaseDate}",DateUtils.format(version.getReleaseTime(),"yyyy-MM-dd"));
        template = template.replace("{nextReviewDate}",DateUtils.format(version.getReviewTime(),"yyyy-MM-dd"));
        // 文件分类中文全路径
        template = template.replace("{fullPathName}",fullPathName);

        // 邮件接收者
        SysUser user = this.sysUserService.selectUserByUserName(docMsgObj.getRecoveryUser());
        if(user == null) {
            msgSp.append("目标用户："+user.getUserName()+"，无效;");
        } else if(StringUtils.isNotEmpty(user.getEmail())) {
            // 邮件地址不为空
            BizMailDto newDto = new BizMailDto();
            newDto.setTarget(user.getEmail());
            newDto.setTitle("DMS系统-"+docMsgObj.getMsgInfo());
            newDto.setContent(template);
            // 发送邮件
            AjaxResult sendRes = this.emailController.push(newDto);
            if(sendRes.getCode() == 200) {
                success ++;
                // 底层发送邮件的回执消息
                msgSp.append(sendRes.getData().toString());
            } else {
                msgSp.append("目标用户："+user.getUserName()+"，发送邮件失败="+sendRes.getMsg()+";");
            }
        } else if(StringUtils.isEmpty(user.getEmail())) {
            msgSp.append("目标用户："+user.getUserName()+"，未配置email属性;");
        }
        if(count == success) {
            // 全部成功
            return AjaxResult.success("process:"+success+"/"+count,msgSp.toString());
        } else {
            // 部分失败
            return AjaxResult.error("process:"+success+"/"+count,msgSp.toString());
        }
    }

}

package com.rzdata.plugins.mail.impl;

import cn.hutool.core.io.FileUtil;
import cn.hutool.extra.mail.MailAccount;
import cn.hutool.extra.mail.MailUtil;
import com.rzdata.framework.constant.Constants;
import com.rzdata.framework.utils.JsonUtils;
import com.rzdata.framework.utils.spring.SpringUtils;
import com.rzdata.plugins.mail.BizMailDto;
import com.rzdata.plugins.mail.ISendMessageService;
import com.rzdata.system.service.ISysConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.util.Arrays;

@Service("EMAIL")
@Slf4j
public class SendEmailMessageService implements ISendMessageService {

    @Resource
    MailAccount mailAccount;


    /**
     * 已经废弃，仅限于测试
     *
     * @param target
     * @param title
     * @param content
     * @param filepath
     * @return
     */
    @Deprecated
    @Override
    public String send(String target, String title, String content, String... filepath) {
        String result = null;
        StringBuilder sp = new StringBuilder(100);
        sp.append("<html xmlns='http://www.w3.org/1999/xhtml'>");
        sp.append("<head>");
        sp.append("<meta http-equiv='Content-Type' content='text/html; charset=utf-8' />");
        sp.append("<meta name='viewport' content='width=device-width, initial-scale=1.0' />");
        sp.append("</head>");
        sp.append("<body>");
        sp.append(content);
        sp.append("</body>");
        sp.append("</html>");
        String html = content;
        File[] files = Arrays.stream(filepath).parallel()
            .map(FileUtil::file)
            .toArray(File[]::new);
        result = MailUtil.send(mailAccount, target, title, html, true, files);
        return result;
    }

    @Override
    public String send(BizMailDto mailDto) {
        String value = SpringUtils.getBean(ISysConfigService.class).selectConfigByKey(Constants.SEND_MSG_EMAIL_VX);
        if(!(value.equals(Constants.SEND_MSG_ALL) || value.equals(Constants.SEND_MSG_EMAIL))) {
            log.error("SendEmailMessageService-->send----发送邮件消息配置未启用###");
            return "发送邮件消息配置未启用";
        }
        // 组装附件
        File[] files = null;
        if(mailDto.getFilePaths() != null && mailDto.getFilePaths().length > 0) {
            files = Arrays.stream(mailDto.getFilePaths()).parallel()
                    .map(FileUtil::file)
                    .toArray(File[]::new);
        }
        if(!mailAccount.isAuth()){
            return "";
        }
        log.error("配置体内容："+ JsonUtils.toJsonString(mailAccount));
        log.error("消息体内容："+ JsonUtils.toJsonString(mailDto));
        // 发送邮件
        String result = MailUtil.send(mailAccount, mailDto.getTarget(), mailDto.getTitle(), mailDto.getContent(), true, files);
        return result;
    }
}

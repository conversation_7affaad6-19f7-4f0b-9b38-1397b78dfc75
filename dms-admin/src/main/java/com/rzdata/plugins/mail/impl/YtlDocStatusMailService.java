package com.rzdata.plugins.mail.impl;

import cn.hutool.core.io.FileUtil;
import cn.hutool.system.SystemUtil;
import com.rzdata.framework.constant.CommonI18nConstant;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.core.domain.entity.SysUser;
import com.rzdata.framework.utils.I18nUtils;
import com.rzdata.framework.utils.StringUtils;
import com.rzdata.plugins.mail.BizMailDto;
import com.rzdata.plugins.mail.IDocStatusMailService;
import com.rzdata.process.api.EmailController;
import com.rzdata.process.domain.Standard;
import com.rzdata.process.domain.Version;
import com.rzdata.process.mapper.StandardMapper;
import com.rzdata.setting.service.IDocClassService;
import com.rzdata.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/*
 * @author: xiefc
 * @date:2023/11/14 16:05
 */
@Service
public class YtlDocStatusMailService implements IDocStatusMailService {

    @Autowired
    ISysUserService sysUserService;

    @Autowired
    StandardMapper stddService;

    @Autowired
    IDocClassService docClassService;

    @Autowired
    EmailController emailController;

    /**
     * 作废文件邮件模板
     */
    private static final String DEFAULT_DISUSE_FILE_MAIL_TEMPLATE = "disUseFileMailTemplate.html";

    /**
     * 生效文件邮件模板
     */
    private static final String DEFAULT_ADD_FILE_MAIL_TEMPLATE = "addFileMailTemplate.html";


    /**
     * 新增文件邮件通知
     *
     * @param targets
     * @param version
     * @return
     */
    @Override
    public AjaxResult addFileMsg(String[] targets, Version version) {
        return this.core(targets,version);
    }

    /**
     * 作废文件邮件通知
     *
     * @param targets
     * @param version
     * @return
     */
    @Override
    public AjaxResult disUseFileMsg(String[] targets, Version version) {
        return this.core(targets,version);
    }

    /**
     * 核心方法
     *
     * @param targets 对应userName
     * @param version
     * @return
     */
    private AjaxResult core(String[] targets, Version version) {
        // 判断入参
        if(targets == null || targets.length == 0) {
            return AjaxResult.error(I18nUtils.getTitle(CommonI18nConstant.SERVICE_YTL_DOC_RECEIVER_ARRAY_NOT_NULL));
        }
        if(version == null) {
            return AjaxResult.error(I18nUtils.getTitle(CommonI18nConstant.SERVICE_YTL_DOC_INVALID_VERSION));
        }
        //
        StringBuilder msgSp = new StringBuilder(50);
        // 总数、成功数量
        int count = targets.length;
        int success = 0;
        Standard stdd = stddService.selectById(version.getStandardId());
        // 默认为生效文件模板
        String path = System.getProperty(SystemUtil.USER_DIR)+"/templates/"+ DEFAULT_ADD_FILE_MAIL_TEMPLATE;
        if(version.getStatus().equals("2")) {
            // 作废文件模板
            path = System.getProperty(SystemUtil.USER_DIR)+"/templates/"+ DEFAULT_DISUSE_FILE_MAIL_TEMPLATE;
        }
        String template = FileUtil.readUtf8String(path);
        // 当前文件分类、所属阶级、文件编号、文件名称、版本号、摘要内容、文件分类中文全路径
        String fullPathName = this.docClassService.getClassFullPath(stdd.getDocClass());
        template = template.replace("{docCurrClassName}",this.docClassService.getById(stdd.getDocClass()).getClassName());
        template = template.replace("{docFirstClassName}",fullPathName.split("/")[0]);
        template = template.replace("{docId}",version.getDocId());
        template = template.replace("{docName}",version.getDocName());
        template = template.replace("{versionValue}",version.getVersionValue());
        template = template.replace("{content}",version.getContent());
        template = template.replace("{fullPathName}",fullPathName);
        for(String userName : targets) {
            // 邮件接收者
            String receiveMail = "";
            if(userName.contains("allUser")) {
                receiveMail = "allUser";
            } else {
                SysUser user = this.sysUserService.selectUserByUserName(userName);
                receiveMail = user.getEmail();
            }

            if(StringUtils.isNotEmpty(receiveMail)) {
                // 邮件地址不为空
                BizMailDto newDto = new BizMailDto();
                newDto.setTarget(receiveMail);
                newDto.setTitle(version.getStatus().equals("1") ? "DMS系统-文件发行通知" : "DMS系统-文件作废通知");
                newDto.setContent(template);
                // 发送邮件
                AjaxResult sendRes = this.emailController.push(newDto);
                if(sendRes.getCode() == 200) {
                    success ++;
                    // 底层发送邮件的回执消息
                    msgSp.append(sendRes.getData().toString());
                } else {
                    msgSp.append("目标用户："+userName+"，发送邮件失败="+sendRes.getMsg()+";");
                }
            } else {
                msgSp.append("目标用户："+userName+"，未配置email属性;");
            }
        }
        if(count == success) {
            // 全部成功
            return AjaxResult.success("process:"+success+"/"+count,msgSp.toString());
        } else {
            // 部分失败
            return AjaxResult.error("process:"+success+"/"+count,msgSp.toString());
        }

    }

}

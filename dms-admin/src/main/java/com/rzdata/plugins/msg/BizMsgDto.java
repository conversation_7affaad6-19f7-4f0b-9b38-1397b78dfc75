package com.rzdata.plugins.msg;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/*
 * 业务消息传输对象
 *
 * @author: xiefc
 * @date:2024/9/25 11:02
 */
@Data
public class BizMsgDto implements java.io.Serializable {

    @ApiModelProperty(value = "消息类型，1 短信提醒 2 邮件提醒 3 系统提醒 4 微信提醒", name = "type")
    private String type;

    @ApiModelProperty(value = "消息接收方，传用户账号，如liudehua", name = "receiver")
    private String receiver;

    @ApiModelProperty(value = "消息标题", name = "title")
    private String title;

    @ApiModelProperty(value = "消息内容", name = "content")
    private String content;

    @ApiModelProperty(value = "业务ID,如订单编码", name = "bizId")
    private String bizId;

}

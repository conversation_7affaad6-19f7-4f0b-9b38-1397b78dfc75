package com.rzdata.plugins.msg;

import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Accessors(chain = true)
@Component
@ConfigurationProperties(prefix = "msg.qywx")
public class MsgConfig {

    /**
     * 是否开启
     */
    private boolean enabled;

    /**
     * 消息中心API地址
     */
    private String msgCenterUrl;

    /**
     * 消息中心租户编码
     */
    private String tenantCode;

    /**
     * 消息中心应用编码
     */
    private String appCode;

    /**
     * 账号（预留）
     */
    private String account;
    /**
     * 密码（预留）
     */
    private String password;

    /**
     * 令牌（预留）
     */
    private String token;

}

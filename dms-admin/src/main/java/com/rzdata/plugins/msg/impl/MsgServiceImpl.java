package com.rzdata.plugins.msg.impl;

import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.rzdata.asas7.util.HttpUtils;
import com.rzdata.framework.constant.Constants;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.utils.spring.SpringUtils;
import com.rzdata.plugins.msg.BizMsgDto;
import com.rzdata.plugins.msg.MsgConfig;
import com.rzdata.plugins.msg.MsgService;
import com.rzdata.process.domain.DocMessage;
import com.rzdata.system.domain.bo.SysApiLogBo;
import com.rzdata.system.service.ISysApiLogService;
import com.rzdata.system.service.ISysConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Map;
import java.util.UUID;

/*
 * 集成消息组件接口实现类
 *
 * @author: xiefc
 * @date:2024/9/25 11:02
 */
@Slf4j
@Service
public class MsgServiceImpl implements MsgService {

    @Resource
    MsgConfig msgConfig;

    @Resource
    ISysApiLogService sysApiLogService;

    @Override
    public AjaxResult send(BizMsgDto msgDto) {
        String value = SpringUtils.getBean(ISysConfigService.class).selectConfigByKey(Constants.SEND_MSG_EMAIL_VX);
        if(!(value.equals(Constants.SEND_MSG_ALL) || value.equals(Constants.SEND_MSG_VX))) {
            log.error("SendEmailMessageService-->send----发送微信消息配置未启用###");
            return AjaxResult.error("发送微信消息配置未启用");
        }
        // 发送消息
        return this.sendMsgByMsgCenter(msgDto);
    }

    /**
     * 转换对象
     *
     * @param docMsg
     * @return
     */
    @Override
    public BizMsgDto convert(DocMessage docMsg) {
        BizMsgDto result = new BizMsgDto();
        result.setReceiver(docMsg.getRecoveryUser());
        if(StrUtil.isNotBlank(docMsg.getHtmlContent())){
            result.setContent(docMsg.getHtmlContent());
        }else{
            result.setContent(docMsg.getMsgInfo());
        }
        result.setTitle("DMS体系文件待办提醒");
        // 默认企业微信消息提醒
        result.setType(docMsg.getMsgType().equals("qywx") ? "4" : "4");
        // 业务唯一ID
        result.setBizId(docMsg.getId());
        return result;
    }

    /**
     * 对接消息中心组件
     *
     * @param msgDto
     * @return
     */
    private AjaxResult sendMsgByMsgCenter(BizMsgDto msgDto) {
        AjaxResult result = AjaxResult.success();
        if(!msgConfig.isEnabled()){
            return result;
        }
        Map<String, Object> params = Maps.newHashMap();
        String body = "";
        Date reqDate = null;
        Date responseDate = null;
        try {
            params.put("tenantCode", msgConfig.getTenantCode());
            params.put("appCode", msgConfig.getAppCode());
            params.put("msgType", msgDto.getType());
            params.put("msgTitle", msgDto.getTitle());
            params.put("msgContent", msgDto.getContent());
            params.put("msgReceiver", msgDto.getReceiver());
            Map<String, String> requestHeaders = Maps.newHashMap();
            reqDate = new Date();
            body = HttpUtils.getInstance().postForObject(msgConfig.getMsgCenterUrl(),params,requestHeaders,String.class,null);
            responseDate = new Date();
            if (log.isInfoEnabled()) {
                log.info("qywx send body====" + body);
            }
            // 处理返回值和记录操作日志
            JSONObject resJson = JSONObject.parseObject(body);
            boolean res = resJson.getBoolean("result");
            if(res) {
                // 成功
                this.insertMsgLog(msgDto.getType(), JSON.toJSONString(params),body,resJson.getString("resultMsg"),1,reqDate,responseDate);
            } else {
                // 失败
                this.insertMsgLog(msgDto.getType(),JSON.toJSONString(params),body,resJson.getString("resultMsg"),0,reqDate,responseDate);
                result = AjaxResult.error("操作失败",body);
            }
        } catch (Exception e) {
            // 异常
            log.error("发送消息异常:" + e + "---返回map:" + body,e);
            this.insertMsgLog(msgDto.getType(),JSON.toJSONString(params),JSON.toJSONString(body), ExceptionUtil.getRootCauseMessage(e),0,reqDate,responseDate);
            result = AjaxResult.error("操作失败", ExceptionUtil.getRootCauseMessage(e));
        }
        return result;
    }

    /**
     * 记录操作日志
     *
     * @param operParam
     * @param jsonResult
     * @param errorMsg
     * @param status
     */
    private void insertMsgLog(String msgType,String operParam,String jsonResult,String errorMsg,long status,Date reqDate,Date responseDate) {
        SysApiLogBo newLog = new SysApiLogBo();
        newLog.setId(UUID.randomUUID().toString());
        newLog.setTenantId(msgConfig.getTenantCode());
        newLog.setAppId(msgConfig.getAppCode());
        newLog.setSourceType("待办提醒");
        newLog.setApiUrl(msgConfig.getMsgCenterUrl());
        newLog.setApiType("POST");
        newLog.setRequestParam(operParam);
        newLog.setRequestTime(reqDate);
        newLog.setResponseStatus("200");
        newLog.setResponseInfo(jsonResult);
        newLog.setResponseTime(responseDate);
        newLog.setStatus(status);
        newLog.setRemark("对接消息中心企业微信");
        newLog.setCreateTime(new Date());
        sysApiLogService.insertByBo(newLog);
        /*
        OperLog operLog = OperLog.builder().title(msgType.equals("1") ? "msg_sms" : "msg_wx").businessType(1).method("SendMessageService.sendMessageByHr").requestMethod("POST").operatorType(1)
                .operName("cps_admin")
                .operParam(operParam)
                .jsonResult(jsonResult)
                .errorMsg(errorMsg.length() > 1000 ? errorMsg.substring(0,1000) : errorMsg)
                .status(status)
                .operTime(LocalDateTime.now()).build();
        operLogService.save(operLog);
         */
    }
}

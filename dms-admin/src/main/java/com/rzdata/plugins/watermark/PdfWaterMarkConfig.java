package com.rzdata.plugins.watermark;

import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * PDF文件水印基础配置
 *
 * <AUTHOR>
 * 2023-7-15
 */

@Data
@Accessors(chain = true)
@Component
@ConfigurationProperties(prefix = "watermark")
public class PdfWaterMarkConfig {

    /**
     * 是否启用
     */
    private boolean enable;

    /**
     * 是否异步
     */
    private boolean asynchronous;

    /**
     * 水印PDF文件输出目录
     */
    private String outPdfDirPath;


}

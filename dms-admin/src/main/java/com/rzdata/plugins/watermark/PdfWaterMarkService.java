package com.rzdata.plugins.watermark;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.qrcode.QrCodeUtil;
import cn.hutool.extra.qrcode.QrConfig;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.rzdata.framework.constant.CommonI18nConstant;
import com.rzdata.framework.constant.Constants;
import com.rzdata.framework.exception.ServiceException;
import com.rzdata.framework.utils.I18nUtils;
import com.rzdata.process.domain.BasicFile;
import com.rzdata.process.domain.vo.BasicFileVo;
import com.rzdata.process.service.IBasicFileService;
import com.rzdata.process.service.IStoreFileService;
import com.rzdata.setting.domain.DocClassWatermarkSettingDetail;
import com.rzdata.setting.domain.Signature;
import com.rzdata.setting.domain.bo.WatermarkParamBo;
import com.rzdata.setting.domain.vo.SignatureVo;
import com.rzdata.setting.service.IDocClassWatermarkSettingDetailService;
import com.rzdata.setting.service.IDocClassWatermarkSettingService;
import com.rzdata.setting.service.ISignatureService;

import lombok.extern.slf4j.Slf4j;
import net.rzdata.file.Client;
import net.rzdata.file.watermark.Watermark;
import org.apache.commons.lang3.StringUtils;
import org.jeewx.api.qrcode.QRCode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.BufferedInputStream;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.OutputStream;
import java.io.InputStream;
import java.net.URLEncoder;
import java.rmi.ServerException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class PdfWaterMarkService {

    @Resource
    PdfWaterMarkConfig waterMarkConfig;

    @Autowired
    IDocClassWatermarkSettingService docClassWatermarkSettingService;

    @Autowired
    IDocClassWatermarkSettingDetailService docClassWatermarkSettingDetailService;

    @Autowired
    IBasicFileService basicFileService;

    @Autowired
    private IStoreFileService iStoreFileService;

    @Autowired
    private ISignatureService signatureService;

    @Autowired
    private IBasicFileService fileService;

    /**
     * param:参数（发布日期、生效日期、分发日期、作废日期）都需单独定位
     */
    public void process(BasicFile sourceBasicFile, String destFilePath, WatermarkParamBo param) {
        if(!waterMarkConfig.isEnable()) {
            throw new ServiceException(I18nUtils.getTitle(CommonI18nConstant.SERVICE_PDF_WM_NOT_ACTIVATED));
        }
        try {
            List<Watermark> watermarks = new ArrayList<>();

            /**
             * 需要区分出主文件、版本附件、记录文件
             * 主文件、记录文件读取自身对应分类配置的水印设置，（查询应用范围未均支持和主文件项）
             * 版本附件沿用主文件配置的水印（查询应用范围未均支持和版本附件项）
             *
             * 根据水印设置动态展示,2024.05.06,CL
             * 类型分为（文件新增、文件修订、文件作废、文件打印），类型取值逻辑：1、如果“分发号文字”有值则为文件打印类型;2、其余类型根据“变更类型”判定
             *
             * 根据文件分类ID，类型，查询文件水印设置功能，如果没有则向上查找对应动态配置；
             */
            boolean startDateFlag = ObjectUtil.isNotEmpty(param)&&StringUtils.isNotEmpty(param.getStartDate());
            boolean releaseTimeFlag = ObjectUtil.isNotEmpty(param)&&StringUtils.isNotEmpty(param.getReleaseTime());
            boolean dispenseDateFlag = ObjectUtil.isNotEmpty(param)&&StringUtils.isNotEmpty(param.getDispenseDate());
            boolean cancelDataFlag = ObjectUtil.isNotEmpty(param)&&StringUtils.isNotEmpty(param.getCancelData());
            if (ObjectUtil.isNotEmpty(param)&&ObjectUtil.isNotEmpty(param.getDocClassWatermarkSettingDetailList())) {
                List<DocClassWatermarkSettingDetail> docClassWatermarkSettingDetailList = param.getDocClassWatermarkSettingDetailList();
                for (DocClassWatermarkSettingDetail docClassWatermarkSettingDetail:docClassWatermarkSettingDetailList) {
                    if(Constants.TEXT.equals(docClassWatermarkSettingDetail.getWatermarkSettingType())){
                        // 文件编号，file_number
                        if(StringUtils.isNotEmpty(param.getDocId()) && Constants.FILE_NUMBER.equals(docClassWatermarkSettingDetail.getWatermarkSettingCode())) {
                            watermarks.add(Watermark.builder().text(I18nUtils.getTitle(CommonI18nConstant.FILE_PDF_DOC_ID) + ":"+param.getDocId()).pages(Watermark.PageOption.valueOf(docClassWatermarkSettingDetail.getPageOption())).position(docClassWatermarkSettingDetail.getXPosition(), docClassWatermarkSettingDetail.getYPosition()).fontSize(docClassWatermarkSettingDetail.getTypeSize()).build());
                            continue;
                        }
                        // 文件版本号，file_version
                        if(StringUtils.isNotEmpty(param.getVersionValue()) && Constants.FILE_VERSION.equals(docClassWatermarkSettingDetail.getWatermarkSettingCode())) {
                            watermarks.add(Watermark.builder().text(I18nUtils.getTitle(CommonI18nConstant.FILE_PDF_VERSION) + ":"+param.getVersionValue()).pages(Watermark.PageOption.valueOf(docClassWatermarkSettingDetail.getPageOption())).position(docClassWatermarkSettingDetail.getXPosition(), docClassWatermarkSettingDetail.getYPosition()).fontSize(docClassWatermarkSettingDetail.getTypeSize()).build());
                            continue;
                        }
                        // 发布日期，release_date
                        if(releaseTimeFlag && Constants.RELEASE_DATE.equals(docClassWatermarkSettingDetail.getWatermarkSettingCode())) {
                            watermarks.add(Watermark.builder().text(param.getReleaseTime()).pages(Watermark.PageOption.valueOf(docClassWatermarkSettingDetail.getPageOption())).position(docClassWatermarkSettingDetail.getXPosition(), docClassWatermarkSettingDetail.getYPosition()).fontSize(docClassWatermarkSettingDetail.getTypeSize()).build());
                            continue;
                        }
                        // 生效日期，effective_date
                        if(startDateFlag && Constants.EFFECTIVE_DATE.equals(docClassWatermarkSettingDetail.getWatermarkSettingCode())) {
                            watermarks.add(Watermark.builder().text(param.getStartDate()).pages(Watermark.PageOption.valueOf(docClassWatermarkSettingDetail.getPageOption())).position(docClassWatermarkSettingDetail.getXPosition(), docClassWatermarkSettingDetail.getYPosition()).fontSize(docClassWatermarkSettingDetail.getTypeSize()).build());
                            continue;
                        }
                        // 分发日期，dispense_date
                        if(dispenseDateFlag && Constants.DISPENSE_DATE.equals(docClassWatermarkSettingDetail.getWatermarkSettingCode())) {
                            watermarks.add(Watermark.builder().text(param.getDispenseDate()).pages(Watermark.PageOption.valueOf(docClassWatermarkSettingDetail.getPageOption())).position(docClassWatermarkSettingDetail.getXPosition(), docClassWatermarkSettingDetail.getYPosition()).fontSize(docClassWatermarkSettingDetail.getTypeSize()).build());
                            continue;
                        }
                        // 作废日期，cancel_date
                        if(cancelDataFlag && Constants.CANCEL_DATE.equals(docClassWatermarkSettingDetail.getWatermarkSettingCode())) {
                            watermarks.add(Watermark.builder().text(param.getCancelData()).pages(Watermark.PageOption.valueOf(docClassWatermarkSettingDetail.getPageOption())).position(docClassWatermarkSettingDetail.getXPosition(), docClassWatermarkSettingDetail.getYPosition()).fontSize(docClassWatermarkSettingDetail.getTypeSize()).build());
                            continue;
                        }
                        // 留用日期，retain_date
                        if(cancelDataFlag && Constants.RETAIN_DATE.equals(docClassWatermarkSettingDetail.getWatermarkSettingCode())) {
                            watermarks.add(Watermark.builder().text(param.getRetainData()).pages(Watermark.PageOption.valueOf(docClassWatermarkSettingDetail.getPageOption())).position(docClassWatermarkSettingDetail.getXPosition(), docClassWatermarkSettingDetail.getYPosition()).fontSize(docClassWatermarkSettingDetail.getTypeSize()).build());
                            continue;
                        }
                        // 分发号文字,dispense_text
                        if(StringUtils.isNotEmpty(param.getDistributeChar()) && Constants.DISPENSE_TEXT.equals(docClassWatermarkSettingDetail.getWatermarkSettingCode())) {
                            watermarks.add(Watermark.builder().text(param.getDistributeChar()).pages(Watermark.PageOption.valueOf(docClassWatermarkSettingDetail.getPageOption()))
                                    // 设置字体大小和颜色
                                    .color(Color.red).fontSize(docClassWatermarkSettingDetail.getTypeSize()).position(docClassWatermarkSettingDetail.getXPosition(), docClassWatermarkSettingDetail.getYPosition()).build());
                            continue;
                        }
                        // 编制人文本
                        if(StrUtil.isNotEmpty(param.getOrganizerHandleTime()) && Constants.ORGANIZER_TEXT.equals(docClassWatermarkSettingDetail.getWatermarkSettingCode())) {
                            watermarks.add(Watermark.builder().text(I18nUtils.getTitle(CommonI18nConstant.FILE_PDF_ORGANIZER) + ":").pages(Watermark.PageOption.valueOf(docClassWatermarkSettingDetail.getPageOption())).position(docClassWatermarkSettingDetail.getXPosition(), docClassWatermarkSettingDetail.getYPosition()).fontSize(docClassWatermarkSettingDetail.getTypeSize()).build());
                            continue;
                        }
                        // 审核人文本
                        if(StrUtil.isNotEmpty(param.getOrganizerHandleTime()) && Constants.AUDITOR_TEXT.equals(docClassWatermarkSettingDetail.getWatermarkSettingCode())) {
                            watermarks.add(Watermark.builder().text(I18nUtils.getTitle(CommonI18nConstant.FILE_PDF_AUDITOR) + ":").pages(Watermark.PageOption.valueOf(docClassWatermarkSettingDetail.getPageOption())).position(docClassWatermarkSettingDetail.getXPosition(), docClassWatermarkSettingDetail.getYPosition()).fontSize(docClassWatermarkSettingDetail.getTypeSize()).build());
                            continue;
                        }
                        // 批准人文本
                        if(StrUtil.isNotEmpty(param.getOrganizerHandleTime()) && Constants.APPROVER_TEXT.equals(docClassWatermarkSettingDetail.getWatermarkSettingCode())) {
                            watermarks.add(Watermark.builder().text(I18nUtils.getTitle(CommonI18nConstant.FILE_PDF_APPROVER) + ":").pages(Watermark.PageOption.valueOf(docClassWatermarkSettingDetail.getPageOption())).position(docClassWatermarkSettingDetail.getXPosition(), docClassWatermarkSettingDetail.getYPosition()).fontSize(docClassWatermarkSettingDetail.getTypeSize()).build());
                            continue;
                        }
                        // 编制人处理时间
                        if(StrUtil.isNotEmpty(param.getOrganizerHandleTime()) && Constants.ORGANIZER_HANDLE_DATE.equals(docClassWatermarkSettingDetail.getWatermarkSettingCode())) {
                            watermarks.add(Watermark.builder().text(param.getOrganizerHandleTime()).pages(Watermark.PageOption.valueOf(docClassWatermarkSettingDetail.getPageOption())).position(docClassWatermarkSettingDetail.getXPosition(), docClassWatermarkSettingDetail.getYPosition()).fontSize(docClassWatermarkSettingDetail.getTypeSize()).build());
                            continue;
                        }
                        // 审核人处理时间
                        if(StrUtil.isNotEmpty(param.getOrganizerHandleTime()) && Constants.AUDITOR_HANDLE_DATE.equals(docClassWatermarkSettingDetail.getWatermarkSettingCode())) {
                            watermarks.add(Watermark.builder().text(param.getAuditorHandleTime()).pages(Watermark.PageOption.valueOf(docClassWatermarkSettingDetail.getPageOption())).position(docClassWatermarkSettingDetail.getXPosition(), docClassWatermarkSettingDetail.getYPosition()).fontSize(docClassWatermarkSettingDetail.getTypeSize()).build());
                            continue;
                        }
                        // 批准人处理时间
                        if(StrUtil.isNotEmpty(param.getOrganizerHandleTime()) && Constants.APPROVER_HANDLE_DATE.equals(docClassWatermarkSettingDetail.getWatermarkSettingCode())) {
                            watermarks.add(Watermark.builder().text(param.getApproverHandleTime()).pages(Watermark.PageOption.valueOf(docClassWatermarkSettingDetail.getPageOption())).position(docClassWatermarkSettingDetail.getXPosition(), docClassWatermarkSettingDetail.getYPosition()).fontSize(docClassWatermarkSettingDetail.getTypeSize()).build());
                            continue;
                        }
                        // 内部文件编号，internal_file_number
                        if(StringUtils.isNotEmpty(param.getDocId()) && Constants.INTERNAL_FILE_NUMBER_TEXT.equals(docClassWatermarkSettingDetail.getWatermarkSettingCode())) {
                            watermarks.add(Watermark.builder().text(I18nUtils.getTitle(CommonI18nConstant.FILE_PDF_INTERNAL_DOC_ID) + ":"+param.getInternalFileNumber()).pages(Watermark.PageOption.valueOf(docClassWatermarkSettingDetail.getPageOption())).position(docClassWatermarkSettingDetail.getXPosition(), docClassWatermarkSettingDetail.getYPosition()).fontSize(docClassWatermarkSettingDetail.getTypeSize()).build());
                            continue;
                        }
                    }else{
                        /**
                         * 受控章，control_image
                         * 外来文件、临时文件章如果在“体系文件类型设置”中水印设置中设置了生效，则取对应的“外来文件章”、“临时文件章”替换受控章
                         *
                         * 示例：
                         * 外来文件：取外来文件章替换受控章
                         * 临时文件：取临时文件章替换受控章
                         * 其他：正常取受控章
                         */
                        if(ObjectUtil.isNotEmpty(param.getForeignFilePath()) && Constants.FOREIGN_FILE_IMAGE.equals(docClassWatermarkSettingDetail.getWatermarkSettingCode())) {
                            File foreignFile = iStoreFileService.getFile(param.getForeignFilePath());
                            watermarks.add(Watermark.builder().image(foreignFile).scale(Float.parseFloat(docClassWatermarkSettingDetail.getScale()+"F")).pages(Watermark.PageOption.valueOf(docClassWatermarkSettingDetail.getPageOption()))
                                    .position(docClassWatermarkSettingDetail.getXPosition(), docClassWatermarkSettingDetail.getYPosition()).build());
                            continue;
                        }
                        if(ObjectUtil.isNotEmpty(param.getCtrlMarkFilePath()) && Constants.CONTROL_IMAGE.equals(docClassWatermarkSettingDetail.getWatermarkSettingCode())) {
                            // 加载图片水印
                            File watermarkFile= iStoreFileService.getFile(param.getCtrlMarkFilePath());
                            watermarks.add(Watermark.builder().image(watermarkFile).scale(Float.parseFloat(docClassWatermarkSettingDetail.getScale()+"F")).pages(Watermark.PageOption.valueOf(docClassWatermarkSettingDetail.getPageOption()))
                                    .position(docClassWatermarkSettingDetail.getXPosition(), docClassWatermarkSettingDetail.getYPosition()).build());
                            continue;
                        }
                        if(ObjectUtil.isNotEmpty(param.getTemporaryFilePath()) && Constants.TEMPORARY_FILE_IMAGE.equals(docClassWatermarkSettingDetail.getWatermarkSettingCode())) {
                            File temporaryFile= iStoreFileService.getFile(param.getTemporaryFilePath());
                            watermarks.add(Watermark.builder().image(temporaryFile).scale(Float.parseFloat(docClassWatermarkSettingDetail.getScale()+"F")).pages(Watermark.PageOption.valueOf(docClassWatermarkSettingDetail.getPageOption()))
                                    .position(docClassWatermarkSettingDetail.getXPosition(), docClassWatermarkSettingDetail.getYPosition()).build());
                            continue;
                        }
                        // 作废章,cancel_image
                        if(ObjectUtil.isNotEmpty(param.getCancelMarkFilePath()) && Constants.CANCEL_IMAGE.equals(docClassWatermarkSettingDetail.getWatermarkSettingCode())) {
                            // 加载图片水印
                            File cancelMarkFile= iStoreFileService.getFile(param.getCancelMarkFilePath());
                            watermarks.add(Watermark.builder().image(cancelMarkFile).scale(Float.parseFloat(docClassWatermarkSettingDetail.getScale()+"F")).pages(Watermark.PageOption.valueOf(docClassWatermarkSettingDetail.getPageOption()))
                                    .position(docClassWatermarkSettingDetail.getXPosition(), docClassWatermarkSettingDetail.getYPosition()).build());
                            continue;
                        }
                        // 留用章,retain_image
                        if(ObjectUtil.isNotEmpty(param.getRetainMarkFilePath()) && Constants.RETAIN_IMAGE.equals(docClassWatermarkSettingDetail.getWatermarkSettingCode())) {
                            // 留用水印
                            File retainMarkFile= iStoreFileService.getFile(param.getRetainMarkFilePath());
                            watermarks.add(Watermark.builder().image(retainMarkFile).scale(Float.parseFloat(docClassWatermarkSettingDetail.getScale()+"F")).pages(Watermark.PageOption.valueOf(docClassWatermarkSettingDetail.getPageOption()))
                                    .position(docClassWatermarkSettingDetail.getXPosition(), docClassWatermarkSettingDetail.getYPosition()).build());
                            continue;
                        }
                        // 分发号水印,dispense_image
                        if(ObjectUtil.isNotEmpty(param.getDistributeMarkFilePath())  && Constants.DISPENSE_IMAGE.equals(docClassWatermarkSettingDetail.getWatermarkSettingCode())) {
                            File distributeMarkFile= iStoreFileService.getFile(param.getDistributeMarkFilePath());
                            watermarks.add(Watermark.builder().image(distributeMarkFile).scale(Float.parseFloat(docClassWatermarkSettingDetail.getScale()+"F")).pages(Watermark.PageOption.valueOf(docClassWatermarkSettingDetail.getPageOption()))
                                    .position(docClassWatermarkSettingDetail.getXPosition(), docClassWatermarkSettingDetail.getYPosition()).build());
                            continue;
                        }
                        // 编制人，organizer
                        if(ObjectUtil.isNotEmpty(param.getOrganizerSignatureFile()) && Constants.ORGANIZER_IMAGE.equals(docClassWatermarkSettingDetail.getWatermarkSettingCode())) {
                            try {
                                File organizerMarkFile = iStoreFileService.getFile(param.getOrganizerSignatureFile());
                                if(organizerMarkFile.exists()){
                                    watermarks.add(Watermark.builder().image(organizerMarkFile).scale(Float.parseFloat(docClassWatermarkSettingDetail.getScale()+"F")).pages(Watermark.PageOption.valueOf(docClassWatermarkSettingDetail.getPageOption()))
                                            .position(docClassWatermarkSettingDetail.getXPosition(), docClassWatermarkSettingDetail.getYPosition()).build());
                                }
                            } catch (Exception e) {
                                log.error("PdfWaterMarkService-->process----编制人处理异常###", e);
                            }
                            continue;
                        }
                        // 审核人，auditor
                        if(ObjectUtil.isNotEmpty(param.getAuditorSignatureFile()) && Constants.AUDITOR_IMAGE.equals(docClassWatermarkSettingDetail.getWatermarkSettingCode())) {
                            try {
                                File auditorMarkFile = iStoreFileService.getFile(param.getAuditorSignatureFile());
                                if(auditorMarkFile.exists()){
                                    watermarks.add(Watermark.builder().image(auditorMarkFile).scale(Float.parseFloat(docClassWatermarkSettingDetail.getScale()+"F")).pages(Watermark.PageOption.valueOf(docClassWatermarkSettingDetail.getPageOption()))
                                            .position(docClassWatermarkSettingDetail.getXPosition(), docClassWatermarkSettingDetail.getYPosition()).build());
                                }
                            } catch (Exception e) {
                                log.error("PdfWaterMarkService-->process----审核人处理异常###", e);
                            }
                            continue;
                        }
                        // 批准人，approver
                        if(ObjectUtil.isNotEmpty(param.getApproverSignatureFile()) && Constants.APPROVER_IMAGE.equals(docClassWatermarkSettingDetail.getWatermarkSettingCode())) {
                            try {
                                File approverMarkFile = iStoreFileService.getFile(param.getApproverSignatureFile());
                                if(approverMarkFile.exists()){
                                    watermarks.add(Watermark.builder().image(approverMarkFile).scale(Float.parseFloat(docClassWatermarkSettingDetail.getScale()+"F")).pages(Watermark.PageOption.valueOf(docClassWatermarkSettingDetail.getPageOption()))
                                            .position(docClassWatermarkSettingDetail.getXPosition(), docClassWatermarkSettingDetail.getYPosition()).build());
                                }
                            } catch (Exception e) {
                                log.error("PdfWaterMarkService-->process----批准人处理异常###", e);
                            }
                            continue;
                        }
                        //二维码
                        if(ObjectUtil.isNotEmpty(param.getQrCode()) && Constants.QRCODE_IMAGE.equals(docClassWatermarkSettingDetail.getWatermarkSettingCode())) {
                            try {
                                QrConfig qrConfig = new QrConfig();
                                qrConfig.setBackColor(null);
                                qrConfig.setHeight(200);
                                qrConfig.setWidth(200);
                                File qr = QrCodeUtil.generate(param.getQrCode(),qrConfig,FileUtil.createTempFile(".png",true));
                                if(qr.exists()){
                                    watermarks.add(Watermark.builder().image(qr).scale(Float.parseFloat(docClassWatermarkSettingDetail.getScale()+"F")).pages(Watermark.PageOption.valueOf(docClassWatermarkSettingDetail.getPageOption()))
                                            .position(docClassWatermarkSettingDetail.getXPosition(), docClassWatermarkSettingDetail.getYPosition()).build());
                                }
                            } catch (Exception e) {
                                log.error("PdfWaterMarkService-->process----二维码处理异常###", e);
                            }
                            continue;
                        }
                    }
                }
            }
            // 加载WORD对应的原始PDF文件
            BufferedInputStream inputStream=iStoreFileService.getInputStream(sourceBasicFile);

            // 调用中间件进行添加水印
            Client client = new Client();
            OutputStream outputStream = FileUtil.getOutputStream(destFilePath);
            client.pdfAddWaterMark(inputStream, outputStream, watermarks);
            IoUtil.close(inputStream);
            IoUtil.close(outputStream);
        } catch (Exception e) {
            log.error("PDF文件添加水印异常",e);
            throw new ServiceException(I18nUtils.getTitle(CommonI18nConstant.SERVICE_PDF_WM_PDF_ADD_WM_EXCEPTION));
        }
    }
    public void preview(InputStream in, OutputStream out, List<DocClassWatermarkSettingDetail> docClassWatermarkSettingDetailList) {
        if(!waterMarkConfig.isEnable()) {
            throw new ServiceException(I18nUtils.getTitle(CommonI18nConstant.SERVICE_PDF_WM_NOT_ACTIVATED));
        }
        try {
            List<Watermark> watermarks = new ArrayList<>();

            /**
             * 需要区分出主文件、版本附件、记录文件
             * 主文件、记录文件读取自身对应分类配置的水印设置，（查询应用范围未均支持和主文件项）
             * 版本附件沿用主文件配置的水印（查询应用范围未均支持和版本附件项）
             *
             * 根据水印设置动态展示,2024.05.06,CL
             * 类型分为（文件新增、文件修订、文件作废、文件打印），类型取值逻辑：1、如果“分发号文字”有值则为文件打印类型;2、其余类型根据“变更类型”判定
             *
             * 根据文件分类ID，类型，查询文件水印设置功能，如果没有则向上查找对应动态配置；
             */

               for (DocClassWatermarkSettingDetail docClassWatermarkSettingDetail:docClassWatermarkSettingDetailList) {
                    if(Constants.TEXT.equals(docClassWatermarkSettingDetail.getWatermarkSettingType())){
                        // 文件编号，file_number
                        if(Constants.FILE_NUMBER.equals(docClassWatermarkSettingDetail.getWatermarkSettingCode())) {
                            watermarks.add(Watermark.builder().text(I18nUtils.getTitle(CommonI18nConstant.FILE_PDF_DOC_ID) + ":DOC_NUMBER").pages(Watermark.PageOption.valueOf(docClassWatermarkSettingDetail.getPageOption())).position(docClassWatermarkSettingDetail.getXPosition(), docClassWatermarkSettingDetail.getYPosition()).fontSize(docClassWatermarkSettingDetail.getTypeSize()).build());
                            continue;
                        }
                        // 文件版本号，file_version
                        if(Constants.FILE_VERSION.equals(docClassWatermarkSettingDetail.getWatermarkSettingCode())) {
                            watermarks.add(Watermark.builder().text(I18nUtils.getTitle(CommonI18nConstant.FILE_PDF_VERSION) + ":VERSION_NUMBER").pages(Watermark.PageOption.valueOf(docClassWatermarkSettingDetail.getPageOption())).position(docClassWatermarkSettingDetail.getXPosition(), docClassWatermarkSettingDetail.getYPosition()).fontSize(docClassWatermarkSettingDetail.getTypeSize()).build());
                            continue;
                        }
                        // 发布日期，release_date
                        if(Constants.RELEASE_DATE.equals(docClassWatermarkSettingDetail.getWatermarkSettingCode())) {
                            watermarks.add(Watermark.builder().text("发布日期").pages(Watermark.PageOption.valueOf(docClassWatermarkSettingDetail.getPageOption())).position(docClassWatermarkSettingDetail.getXPosition(), docClassWatermarkSettingDetail.getYPosition()).fontSize(docClassWatermarkSettingDetail.getTypeSize()).build());
                            continue;
                        }
                        // 生效日期，effective_date
                        if(Constants.EFFECTIVE_DATE.equals(docClassWatermarkSettingDetail.getWatermarkSettingCode())) {
                            watermarks.add(Watermark.builder().text("生效日期").pages(Watermark.PageOption.valueOf(docClassWatermarkSettingDetail.getPageOption())).position(docClassWatermarkSettingDetail.getXPosition(), docClassWatermarkSettingDetail.getYPosition()).fontSize(docClassWatermarkSettingDetail.getTypeSize()).build());
                            continue;
                        }
                        // 分发日期，dispense_date
                        if(Constants.DISPENSE_DATE.equals(docClassWatermarkSettingDetail.getWatermarkSettingCode())) {
                            watermarks.add(Watermark.builder().text("分发日期").pages(Watermark.PageOption.valueOf(docClassWatermarkSettingDetail.getPageOption())).position(docClassWatermarkSettingDetail.getXPosition(), docClassWatermarkSettingDetail.getYPosition()).fontSize(docClassWatermarkSettingDetail.getTypeSize()).build());
                            continue;
                        }
                        // 作废日期，cancel_date
                        if(Constants.CANCEL_DATE.equals(docClassWatermarkSettingDetail.getWatermarkSettingCode())) {
                            watermarks.add(Watermark.builder().text("作废日期").pages(Watermark.PageOption.valueOf(docClassWatermarkSettingDetail.getPageOption())).position(docClassWatermarkSettingDetail.getXPosition(), docClassWatermarkSettingDetail.getYPosition()).fontSize(docClassWatermarkSettingDetail.getTypeSize()).build());
                            continue;
                        }
                        // 留用日期，retain_date
                        if(Constants.RETAIN_DATE.equals(docClassWatermarkSettingDetail.getWatermarkSettingCode())) {
                            watermarks.add(Watermark.builder().text("留用日期").pages(Watermark.PageOption.valueOf(docClassWatermarkSettingDetail.getPageOption())).position(docClassWatermarkSettingDetail.getXPosition(), docClassWatermarkSettingDetail.getYPosition()).fontSize(docClassWatermarkSettingDetail.getTypeSize()).build());
                            continue;
                        }
                        // 分发号文字,dispense_text
                        if(Constants.DISPENSE_TEXT.equals(docClassWatermarkSettingDetail.getWatermarkSettingCode())) {
                            watermarks.add(Watermark.builder().text("分发号文字").pages(Watermark.PageOption.valueOf(docClassWatermarkSettingDetail.getPageOption()))
                                    // 设置字体大小和颜色
                                    .color(Color.red).fontSize(docClassWatermarkSettingDetail.getTypeSize()).position(docClassWatermarkSettingDetail.getXPosition(), docClassWatermarkSettingDetail.getYPosition()).build());
                            continue;
                        }
                        // 编制人文本
                        if(Constants.ORGANIZER_TEXT.equals(docClassWatermarkSettingDetail.getWatermarkSettingCode())) {
                            watermarks.add(Watermark.builder().text(I18nUtils.getTitle(CommonI18nConstant.FILE_PDF_ORGANIZER) + ":").pages(Watermark.PageOption.valueOf(docClassWatermarkSettingDetail.getPageOption())).position(docClassWatermarkSettingDetail.getXPosition(), docClassWatermarkSettingDetail.getYPosition()).fontSize(docClassWatermarkSettingDetail.getTypeSize()).build());
                            continue;
                        }
                        // 审核人文本
                        if( Constants.AUDITOR_TEXT.equals(docClassWatermarkSettingDetail.getWatermarkSettingCode())) {
                            watermarks.add(Watermark.builder().text(I18nUtils.getTitle(CommonI18nConstant.FILE_PDF_AUDITOR) + ":").pages(Watermark.PageOption.valueOf(docClassWatermarkSettingDetail.getPageOption())).position(docClassWatermarkSettingDetail.getXPosition(), docClassWatermarkSettingDetail.getYPosition()).fontSize(docClassWatermarkSettingDetail.getTypeSize()).build());
                            continue;
                        }
                        // 批准人文本
                        if(Constants.APPROVER_TEXT.equals(docClassWatermarkSettingDetail.getWatermarkSettingCode())) {
                            watermarks.add(Watermark.builder().text(I18nUtils.getTitle(CommonI18nConstant.FILE_PDF_APPROVER) + ":").pages(Watermark.PageOption.valueOf(docClassWatermarkSettingDetail.getPageOption())).position(docClassWatermarkSettingDetail.getXPosition(), docClassWatermarkSettingDetail.getYPosition()).fontSize(docClassWatermarkSettingDetail.getTypeSize()).build());
                            continue;
                        }
                        // 编制人处理时间
                        if(Constants.ORGANIZER_HANDLE_DATE.equals(docClassWatermarkSettingDetail.getWatermarkSettingCode())) {
                            watermarks.add(Watermark.builder().text("编制人处理时间").pages(Watermark.PageOption.valueOf(docClassWatermarkSettingDetail.getPageOption())).position(docClassWatermarkSettingDetail.getXPosition(), docClassWatermarkSettingDetail.getYPosition()).fontSize(docClassWatermarkSettingDetail.getTypeSize()).build());
                            continue;
                        }
                        // 审核人处理时间
                        if(Constants.AUDITOR_HANDLE_DATE.equals(docClassWatermarkSettingDetail.getWatermarkSettingCode())) {
                            watermarks.add(Watermark.builder().text("审核人处理时间").pages(Watermark.PageOption.valueOf(docClassWatermarkSettingDetail.getPageOption())).position(docClassWatermarkSettingDetail.getXPosition(), docClassWatermarkSettingDetail.getYPosition()).fontSize(docClassWatermarkSettingDetail.getTypeSize()).build());
                            continue;
                        }
                        // 批准人处理时间
                        if(Constants.APPROVER_HANDLE_DATE.equals(docClassWatermarkSettingDetail.getWatermarkSettingCode())) {
                            watermarks.add(Watermark.builder().text("批准人处理时间").pages(Watermark.PageOption.valueOf(docClassWatermarkSettingDetail.getPageOption())).position(docClassWatermarkSettingDetail.getXPosition(), docClassWatermarkSettingDetail.getYPosition()).fontSize(docClassWatermarkSettingDetail.getTypeSize()).build());
                            continue;
                        }
                        // 内部文件编号，internal_file_number
                        if(Constants.INTERNAL_FILE_NUMBER_TEXT.equals(docClassWatermarkSettingDetail.getWatermarkSettingCode())) {
                            watermarks.add(Watermark.builder().text(I18nUtils.getTitle(CommonI18nConstant.FILE_PDF_INTERNAL_DOC_ID) + ":INTERNAL_FILE_NUMBER").pages(Watermark.PageOption.valueOf(docClassWatermarkSettingDetail.getPageOption())).position(docClassWatermarkSettingDetail.getXPosition(), docClassWatermarkSettingDetail.getYPosition()).fontSize(docClassWatermarkSettingDetail.getTypeSize()).build());
                            continue;
                        }
                    }else{
                        /**
                         * 受控章，control_image
                         * 外来文件、临时文件章如果在“体系文件类型设置”中水印设置中设置了生效，则取对应的“外来文件章”、“临时文件章”替换受控章
                         *
                         * 示例：
                         * 外来文件：取外来文件章替换受控章
                         * 临时文件：取临时文件章替换受控章
                         * 其他：正常取受控章
                         */
                        // 查找所有签章列表
                        List<SignatureVo> signatureList = signatureService.listVo(new LambdaQueryWrapper<Signature>().eq(Signature::getDeleteFlag,0));
                        // 取signatureId 列表,关联查询对应的文件map
                        Map<String,BasicFile> signatureFileMap = fileService.list(new LambdaQueryWrapper<BasicFile>()
                            .in(BasicFile::getId, signatureList.stream().map(SignatureVo::getFileId).collect(Collectors.toList())))
                            .stream()
                            .collect(Collectors.toMap(
                                BasicFile::getId,
                                Function.identity()
                            ));

                        // 签章
                        for(SignatureVo signatureVo : signatureList){
                            // 设置文件对象
                            signatureVo.setBasicFile(signatureFileMap.get(signatureVo.getFileId()));
                            if (docClassWatermarkSettingDetail.getWatermarkSettingName().equals(signatureVo.getSignatureName())){
                                File foreignFile = iStoreFileService.getFile(signatureVo.getBasicFile());
                                watermarks.add(Watermark.builder().image(foreignFile).scale(Float.parseFloat(docClassWatermarkSettingDetail.getScale()+"F")).pages(Watermark.PageOption.valueOf(docClassWatermarkSettingDetail.getPageOption()))
                                    .position(docClassWatermarkSettingDetail.getXPosition(), docClassWatermarkSettingDetail.getYPosition()).build());
                                break;
                            }
                        }
                          // 编制人，organizer
                        if(Constants.ORGANIZER_IMAGE.equals(docClassWatermarkSettingDetail.getWatermarkSettingCode())) {
                            try {
                                    watermarks.add(Watermark.builder().text("编制人").scale(Float.parseFloat(docClassWatermarkSettingDetail.getScale()+"F")).pages(Watermark.PageOption.valueOf(docClassWatermarkSettingDetail.getPageOption()))
                                            .position(docClassWatermarkSettingDetail.getXPosition(), docClassWatermarkSettingDetail.getYPosition()).build());

                            } catch (Exception e) {
                                log.error("PdfWaterMarkService-->process----编制人处理异常###", e);
                            }
                            continue;
                        }
                        // 审核人，auditor
                        if(Constants.AUDITOR_IMAGE.equals(docClassWatermarkSettingDetail.getWatermarkSettingCode())) {
                            try {
                                    watermarks.add(Watermark.builder().text("审核人").scale(Float.parseFloat(docClassWatermarkSettingDetail.getScale()+"F")).pages(Watermark.PageOption.valueOf(docClassWatermarkSettingDetail.getPageOption()))
                                            .position(docClassWatermarkSettingDetail.getXPosition(), docClassWatermarkSettingDetail.getYPosition()).build());
                                
                            } catch (Exception e) {
                                log.error("PdfWaterMarkService-->process----审核人处理异常###", e);
                            }
                            continue;
                        }
                        // 批准人，approver
                        if(Constants.APPROVER_IMAGE.equals(docClassWatermarkSettingDetail.getWatermarkSettingCode())) {
                            try {
                                    watermarks.add(Watermark.builder().text("批准人").scale(Float.parseFloat(docClassWatermarkSettingDetail.getScale()+"F")).pages(Watermark.PageOption.valueOf(docClassWatermarkSettingDetail.getPageOption()))
                                            .position(docClassWatermarkSettingDetail.getXPosition(), docClassWatermarkSettingDetail.getYPosition()).build());
                                
                            } catch (Exception e) {
                                log.error("PdfWaterMarkService-->process----批准人处理异常###", e);
                            }
                            continue;
                        }
                        
                        // 分发号水印,dispense_image
                        if(Constants.DISPENSE_IMAGE.equals(docClassWatermarkSettingDetail.getWatermarkSettingCode())) {
                            watermarks.add(Watermark.builder().text("分发号").scale(Float.parseFloat(docClassWatermarkSettingDetail.getScale()+"F")).pages(Watermark.PageOption.valueOf(docClassWatermarkSettingDetail.getPageOption()))
                                    .position(docClassWatermarkSettingDetail.getXPosition(), docClassWatermarkSettingDetail.getYPosition()).build());
                            continue;
                        }
                        //二维码
                        if(Constants.QRCODE_IMAGE.equals(docClassWatermarkSettingDetail.getWatermarkSettingCode())) {
                            try {
                                QrConfig qrConfig = new QrConfig();
                                qrConfig.setBackColor(null);
                                qrConfig.setHeight(200);
                                qrConfig.setWidth(200);
                                File qr = QrCodeUtil.generate("www.baidu.com",qrConfig,FileUtil.createTempFile(".png",true));
                                if(qr.exists()){
                                    watermarks.add(Watermark.builder().image(qr).scale(Float.parseFloat(docClassWatermarkSettingDetail.getScale()+"F")).pages(Watermark.PageOption.valueOf(docClassWatermarkSettingDetail.getPageOption()))
                                            .position(docClassWatermarkSettingDetail.getXPosition(), docClassWatermarkSettingDetail.getYPosition()).build());
                                }
                            } catch (Exception e) {
                                log.error("PdfWaterMarkService-->process----二维码处理异常###", e);
                            }
                        }
                    }
                }

            // 加载WORD对应的原始PDF文件

            // 调用中间件进行添加水印
            Client client = new Client();
            client.pdfAddWaterMark(in, out, watermarks);
            IoUtil.close(in);
            IoUtil.close(out);
        } catch (Exception e) {
            log.error("PDF文件添加水印异常",e);
            throw new ServiceException(I18nUtils.getTitle(CommonI18nConstant.SERVICE_PDF_WM_PDF_ADD_WM_EXCEPTION));
        }
    }


    /**
     * 预览PDF的时候增加文字水印
     *
     * @param pdfFileId
     * @param text
     * @param response
     * @throws ServerException
     * @throws FileNotFoundException
     */
    public void addPreviewWaterMark(String pdfFileId, String text, HttpServletResponse response) throws Exception {
        BasicFileVo fileVo = basicFileService.getVoById(pdfFileId);
        String fileName = URLEncoder.encode(fileVo.getFileName(), "utf8");
        response.setContentType("application/octet-stream");
        response.setHeader("content-type", "application/octet-stream");
        response.setHeader("Content-Disposition", "attachment;fileName=" + fileName);
        response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
        BufferedInputStream inputStream = iStoreFileService.getInputStream(BeanUtil.toBean(fileVo, BasicFile.class));
//        List<Watermark> watermarks = new ArrayList<>();
        /**
         * 任务：10301 【文件打印】主文件、记录文件打印后，不需要展示登录账号水印
         * 2024.05.13-CL
         */
//        // 水印文字
//        watermarks.add(Watermark.builder()
//                .text(text)
//                .pages(Watermark.PageOption.ALL)
//                .positionType(Watermark.PositionType.TILE_ALL)
//                .color(Color.black)
//                .fontSize(28)
//                .transparent(0.1f)
//                .scale(0.5f)
//                .build()
//        );
        // 调用中间件进行添加水印
//        Client client = new Client();
        OutputStream outputStream = response.getOutputStream();
//        client.pdfAddWaterMark(inputStream, outputStream, watermarks);
        IoUtil.copy(inputStream,outputStream);
        IoUtil.close(inputStream);
        IoUtil.close(outputStream);
    }
}


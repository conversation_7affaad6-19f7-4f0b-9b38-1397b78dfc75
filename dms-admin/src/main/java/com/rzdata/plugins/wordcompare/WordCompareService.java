package com.rzdata.plugins.wordcompare;


import com.rzdata.framework.constant.CommonI18nConstant;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.utils.I18nUtils;
import com.rzdata.plugins.watermark.PdfWaterMarkConfig;
import lombok.extern.slf4j.Slf4j;
import net.rzdata.file.transformer.client.RzFileTransformerClient;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.*;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * WORD文件比对
 */
@Slf4j
@Service
public class WordCompareService {

    @Resource
    PdfWaterMarkConfig waterMarkConfig;

    @Autowired
    RzFileTransformerClient client;

    /**
     * WORD文件比对
     *
     * @param wordFilePath1 WORD文件1绝对路径
     * @param wordFilePath2 WORD文件2绝对路径
     * @param destFilePath 比对结果文件绝对路径
     * @return
     */
    public AjaxResult process(String wordFilePath1, String wordFilePath2, String destFilePath) {
        if(!waterMarkConfig.isEnable()) {
            return AjaxResult.error(I18nUtils.getTitle(CommonI18nConstant.SERVICE_PDF_WM_NOT_ACTIVATED));
        }
        String errMsg = "";
        try {
            // wordFilePath1
            File wordFile1 = new File(wordFilePath1);
            if(StringUtils.isNotEmpty(wordFilePath1) && !wordFile1.exists()) {
                errMsg = I18nUtils.getTitle(CommonI18nConstant.SERVICE_WORD_PATH1_NOT_FOUND)+ wordFilePath1;
                log.error(errMsg);
                return AjaxResult.error(errMsg);
            }
            // wordFilePath2
            File wordFile2 = new File(wordFilePath2);
            if(StringUtils.isNotEmpty(wordFilePath2) && !wordFile2.exists()) {
                errMsg = I18nUtils.getTitle(CommonI18nConstant.SERVICE_WORD_PATH2_NOT_FOUND)+ wordFilePath2;
                log.error(errMsg);
                return AjaxResult.error(errMsg);
            }

            // 整理参数
            List<byte[]> array = new ArrayList<>();
            InputStream input1 = new FileInputStream(wordFile1);
            InputStream input2 = new FileInputStream(wordFile2);
            array.add(this.fileToBytes(input1));
            array.add(this.fileToBytes(input2));

            List<String> EXCEL_EXTENSIONS = Arrays.asList("xls", "xlsx");
            String fileName = wordFile1.getName();
            int lastIndex = fileName.lastIndexOf(".");
            String extension = fileName.substring(lastIndex + 1).toLowerCase();
            byte[] compareResPdf = null;

            if (EXCEL_EXTENSIONS.contains(extension)){
                compareResPdf = client.transformFromExcelOfficeComparePdf(array);
            }else{
                compareResPdf = client.transformFromOfficeComparePdf(array);
            }

            // 写入文件
            FileUtils.writeByteArrayToFile(new File(destFilePath),compareResPdf);
            // 保存比对后的结果PDF文件
            File resFile = new File(destFilePath);
            // 判断结果文件是否存在
            if(!resFile.exists()) {
                log.error(I18nUtils.getTitle(CommonI18nConstant.SERVICE_WORD_SAVE_COMPARE_FILE_NOT_EXIST),resFile);
                return AjaxResult.error(I18nUtils.getTitle(CommonI18nConstant.SERVICE_WORD_SAVE_FILE_NOT_EXIST)+destFilePath);
            }
        } catch (Exception e) {
            log.error("word比对文件异常",e);
            return AjaxResult.error(I18nUtils.getTitle(CommonI18nConstant.SERVICE_WORD_FILE_COMPARE_ERR),e);
        }
        return AjaxResult.success(I18nUtils.getTitle(CommonI18nConstant.RESULT_AJAX_SUCCESS),destFilePath);
    }

    private byte[] fileToBytes(InputStream fis) throws IOException {
        ByteArrayOutputStream bos = null;
        byte[] resultBytes=null;
        try {
            bos=new ByteArrayOutputStream();
            byte[] bytes=new byte[1024];
            int len;
            while((len=fis.read(bytes))!=-1){
                bos.write(bytes,0,len);
            }
            resultBytes = bos.toByteArray();
        } catch (IOException e) {
            log.error("fileToBytes异常",e);
        } finally {
            bos.close();
            fis.close();
        }
        return resultBytes;
    }

}


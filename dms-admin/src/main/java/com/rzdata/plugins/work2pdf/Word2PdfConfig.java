package com.rzdata.plugins.work2pdf;

import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * Word转Pdf基础配置
 *
 * <AUTHOR>
 * 2023-7-15
 */

@Data
@Accessors(chain = true)
@Component
@ConfigurationProperties(prefix = "word2pdf")
public class Word2PdfConfig {

    /**
     * 是否启用
     */
    private boolean enable;

    /**
     * 是否异步
     */
    private boolean asynchronous;


    /**
     * PDF文件输出目录
     *
     */
    private String outPdfDirPath;

}

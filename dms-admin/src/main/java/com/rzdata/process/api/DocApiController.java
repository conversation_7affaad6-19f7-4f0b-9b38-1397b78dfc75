package com.rzdata.process.api;

import cn.hutool.core.lang.tree.Tree;
import com.rzdata.framework.annotation.Log;
import com.rzdata.framework.constant.Constants;
import com.rzdata.framework.core.controller.BaseController;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.core.validate.QueryGroup;
import com.rzdata.framework.enums.BusinessType;
import com.rzdata.framework.utils.TreeBuildUtils;
import com.rzdata.process.domain.bo.*;
import com.rzdata.process.domain.dto.VersionDTO;
import com.rzdata.process.service.*;
import com.rzdata.setting.domain.bo.DocClassBo;
import com.rzdata.setting.domain.vo.DocClassVo;
import com.rzdata.setting.service.IDocClassService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.*;


/**
 * <AUTHOR>
 */
@Validated
@Slf4j
@RestController
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RequestMapping(value = "/api/doc")
@Api(value = "文件服务", tags = {"文件服务"})
public class DocApiController extends BaseController {

	private final IVersionService iVersionService;

	private final IDocClassService iDocClassService;

	@Value("${api.limitDocClass}")
	private String limitDocClass;
	/**
	 * 对外提供的流程暂存服务
	 */
	@ApiOperation("对外提供的文件类型查询服务")
	@PostMapping("/class/tree")
	@Log(title = "文件服务", businessType = BusinessType.INSERT)
	public AjaxResult<List<Tree<String>>> classTree(@RequestBody DocClassBo bo) {
		bo.setClassStatus(Constants.ONE);
		bo.setParentId(limitDocClass);
		List<DocClassVo> list = iDocClassService.queryList(bo);
		return AjaxResult.success(TreeBuildUtils.build(list, (vo, tree) ->
				tree.setId(vo.getId())
						.setParentId(vo.getParentClassId())
						.setName(vo.getClassName())
						.setWeight(vo.getSort())));
	}

	/**
	 * 对外提供的流程暂存服务
	 */
	@ApiOperation("对外提供的文件查询服务")
	@GetMapping("/page")
	@Log(title = "文件服务", businessType = BusinessType.INSERT)
	public TableDataInfo<VersionDTO> page(@Validated(QueryGroup.class) VersionBo bo) {
		return iVersionService.pageApi(bo,limitDocClass);
	}
}

package com.rzdata.process.api;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.rzdata.framework.annotation.Log;
import com.rzdata.framework.constant.CommonI18nConstant;
import com.rzdata.framework.constant.Constants;
import com.rzdata.framework.core.controller.BaseController;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.core.domain.entity.SysUser;
import com.rzdata.framework.enums.BusinessType;
import com.rzdata.framework.utils.I18nUtils;
import com.rzdata.plugins.mail.BizMailDto;
import com.rzdata.plugins.mail.ISendMessageService;
import com.rzdata.system.service.ISysUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 发送邮件测试
 *
 * <AUTHOR>
 * @date 2023-11-4
 */
@Slf4j
@Validated
@Api(value = "发送邮件", tags = {"发送邮件"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/process/api/email/")
public class EmailController extends BaseController {


    @Resource
    ISendMessageService emailMessageService;

    @Resource
    ISysUserService sysUserService;

    /**
     * 发送邮件
     */
    @ApiOperation("发送邮件")
    @GetMapping(value="/push",produces="application/json;charset=utf-8")
    @Log(title = "发送邮件", businessType = BusinessType.INSERT)
    public AjaxResult push(@RequestBody BizMailDto mailDto) {
        try {
            if(StringUtils.isEmpty(mailDto.getTarget()) || StringUtils.isEmpty(mailDto.getTitle()) || StringUtils.isEmpty(mailDto.getContent()) ) {
                return AjaxResult.error(I18nUtils.getTitle(CommonI18nConstant.EMAIL_PARAM_INCOMPLETE));
            }
            // 发送邮件
            StringBuilder sb = new StringBuilder(100);
            if(mailDto.getTarget().contains("allUser")) {
                // 分批发送全公司所有人员
                List<List<String>> batchUserEmail = this.splitUser(200);
                for(List<String> item : batchUserEmail) {
                   String batchReceMails = item.stream().collect(Collectors.joining(Constants.ID_SPLIT_KEY));
                   mailDto.setTarget(batchReceMails);
                   String result = this.emailMessageService.send(mailDto);
                   sb.append(result);
                }
            } else {
                // 正常发送
                String result = this.emailMessageService.send(mailDto);
                sb.append(result);
            }
            return AjaxResult.success(I18nUtils.getTitle(CommonI18nConstant.EMAIL_SEND_SUCCESS),sb.toString());
        } catch (Exception e) {
            log.error("发送邮件异常",e);
            return AjaxResult.error(I18nUtils.getTitle(CommonI18nConstant.EMAIL_SEND_ERR),ExceptionUtil.stacktraceToString(e));
        }
    }

    /**
     * 发送邮件
     */
    @ApiOperation("发送邮件")
    @GetMapping(value="/pushByParam",produces="application/json;charset=utf-8")
    @Log(title = "发送邮件", businessType = BusinessType.INSERT)
    public AjaxResult push(String target,String title, String content) {
        try {
            if(StringUtils.isEmpty(target) || StringUtils.isEmpty(title) || StringUtils.isEmpty(content) ) {
                return AjaxResult.error(I18nUtils.getTitle(CommonI18nConstant.EMAIL_PARAM_INCOMPLETE));
            }
            String sendRes = this.emailMessageService.send(target,title,content);
            return AjaxResult.success(I18nUtils.getTitle(CommonI18nConstant.EMAIL_SEND_SUCCESS),sendRes);
        } catch (Exception e) {
            log.error("发送邮件异常",e);
            return AjaxResult.error(I18nUtils.getTitle(CommonI18nConstant.EMAIL_SEND_ERR),ExceptionUtil.stacktraceToString(e));
        }
    }


    private List<List<String>> splitUser(int partNum) {
        List<List<String>> result  = null;
        QueryWrapper query = new QueryWrapper();
        query.eq("status","0");
        query.eq("del_flag","0");
        query.eq("user_type","00");
        query.isNotNull("email");
        query.ne("email","''");
        List<SysUser> list = this.sysUserService.list(query.select("email"));
        List<String> listEmail = list.stream().map(SysUser::getEmail).collect(Collectors.toList());
        result =  ListUtil.split(listEmail,partNum);
        return result;
    }
}

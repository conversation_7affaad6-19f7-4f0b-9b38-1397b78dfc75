package com.rzdata.process.api;

import com.alibaba.fastjson.JSONObject;
import com.rzdata.framework.annotation.Log;
import com.rzdata.framework.constant.CommonI18nConstant;
import com.rzdata.framework.core.controller.BaseController;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.core.domain.entity.SysUser;
import com.rzdata.framework.enums.BusinessType;
import com.rzdata.framework.utils.I18nUtils;
import com.rzdata.process.controller.DocMessageController;
import com.rzdata.process.service.MessageDingDingService;
import com.rzdata.system.service.ISysUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 接收流程平台触发消息
 *
 * <AUTHOR>
 * @date 2023-7-14
 */
@Slf4j
@Validated
@Api(value = "接收流程平台触发消息", tags = {"接收流程平台触发消息"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/process/api/flowPlat/")
public class FlowPlatMsgController extends BaseController {

    @Resource
    ISysUserService sysUserService;

    @Resource
    MessageDingDingService dingDingService;

    @Resource
    DocMessageController docMessageController;

    /**
     * 接收流程平台触发消息（提醒流程代办人）
     */
    @ApiOperation("接收流程平台触发消息")
    @PostMapping(value="/receiveMsg",produces="application/json;charset=utf-8")
    @Log(title = "待办消息", businessType = BusinessType.INSERT)
    public AjaxResult sendMsg(@RequestBody String flowMsg) {
        try {
            log.info("flowMsg: {}", flowMsg);
            JSONObject json = JSONObject.parseObject(flowMsg);
            String title =  json.getString("msgTitle");
            String msgReceiver =  json.getString("msgReceiver");

            if(StringUtils.isEmpty(title) || StringUtils.isEmpty(msgReceiver)) {
                return AjaxResult.error(I18nUtils.getTitle(CommonI18nConstant.FLOW_PLAT_MSG_TITLE_RECEIVER_NULL),flowMsg);
            }
            SysUser receiver = sysUserService.selectUserByUserName(msgReceiver);
            if(receiver != null) {
                JSONObject newMsg = new JSONObject();
                newMsg.put("msgType","qywx");
                newMsg.put("deptId","flow");
                newMsg.put("msgClass","100");
                newMsg.put("applyId","100");
                newMsg.put("msgInfo",title);
                newMsg.put("recoveryUser",msgReceiver);
                // 发送站内消息
                return this.docMessageController.pushSimpleMsg(JSONObject.toJSONString(newMsg));
            } else {
                return AjaxResult.error("msgReceiver值不可识别",flowMsg);
            }
            /*
            if(receiver != null && StringUtils.isNotEmpty(receiver.getThirdId())) {
                // 发送企业钉钉消息
                return this.dingDingService.sendMsg(receiver.getThirdId(),title,null);
            } else {
                return AjaxResult.error("msgReceiver值不可识别或thirdId为空",flowMsg);
            }*/
        } catch (Exception e) {
            log.error("接收流程平台触发消息异常",e);
        }
        return AjaxResult.success();
    }


}

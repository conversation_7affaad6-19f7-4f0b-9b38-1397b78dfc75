package com.rzdata.process.api;

import cn.hutool.core.exceptions.ExceptionUtil;
import com.rzdata.framework.core.controller.BaseController;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.job.service.FileJobService;
import com.rzdata.job.service.MsgJobService;
import com.rzdata.plugins.filesync.impl.AsFileSyncService;
import com.rzdata.plugins.watermark.PdfWaterMarkService;
import com.rzdata.process.domain.BasicFile;
import com.rzdata.process.service.DocBuildSignatureService;
import com.rzdata.process.service.IBasicFilePdfService;
import com.rzdata.process.service.MessageDingDingService;
import com.rzdata.process.utils.DateUtils;
import com.rzdata.setting.domain.bo.WatermarkParamBo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.io.IOException;

/**
 * 测试Controller
 *
 * <AUTHOR>
 * @date 2023-6-29
 */
@Validated
@Api(value = "测试控制器", tags = {"测试控制器"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/process/test")
public class TestController extends BaseController {

    @Resource
    MessageDingDingService messageService;

    @Resource
    PdfWaterMarkService pdfWaterMarkService;

    @Resource
    DocBuildSignatureService docSignatureService;

    @Resource
    com.rzdata.process.service.IBasicFileService basicFileService;

    @Resource
    IBasicFilePdfService basicFilePdfService;

    @Resource
    MsgJobService msgJobService;

    @Resource
    FileJobService fileJobService;

    @Resource
    AsFileSyncService asFileSyncService;



    /**
     * 测试发送钉钉消息
     */
    @ApiOperation("发送消息")
    @GetMapping("/sendMsg")
    public AjaxResult sendMsg(String userIds,String msg) {
        return  this.messageService.sendMsg(userIds,msg,null);
    }


    /**
     * PDF文件添加水印
     */
    @ApiOperation("PDF文件添加水印")
    @GetMapping("/pdfAddWaterMark")
    public AjaxResult pdfAddWaterMark(String sourceWordFileId, String destFilePath,
                                      WatermarkParamBo param) {
        BasicFile fileVo = basicFileService.getById(sourceWordFileId);
        this.pdfWaterMarkService.process(fileVo,destFilePath,param);
        return AjaxResult.success();
    }

    /**
     * word转PDF
     */
    @ApiOperation("word转PDF")
    @GetMapping("/word2pdf")
    public AjaxResult<Void> word2pdf(String sourceWordFileId) throws IOException {
        BasicFile fileVo = basicFileService.getById(sourceWordFileId);
        return AjaxResult.success(docSignatureService.processWord2Pdf(fileVo));
    }

    /**
     * pdf增加水印
     */
    @ApiOperation("pdf增加水印")
    @GetMapping("/processPdfWaterMark")
    public AjaxResult processPdfWaterMark(String docClass,String originalPdfFileId,String type,WatermarkParamBo param) {
        BasicFile fileVo = basicFileService.getById(originalPdfFileId);
        return AjaxResult.success(docSignatureService.processPdfWaterMark(docClass,fileVo,type,param));
    }

    /**
     * createPdfFile
     */
    @ApiOperation("createPdfFile")
    @GetMapping("/createPdfFile")
    public AjaxResult createPdfFile(String basicFileId) throws Exception {
        BasicFile fileVo = basicFileService.getById(basicFileId);
        return AjaxResult.success(this.basicFileService.createPdfFile(fileVo));
    }

    /**
     * createEncryptFileId
     */
    @ApiOperation("createEncryptFileId")
    @GetMapping("/createEncryptFileId")
    public AjaxResult createEncryptFileId(String docClass,String originalPdfFileId,String type,WatermarkParamBo param) {
        BasicFile fileVo = basicFileService.getById(originalPdfFileId);
        return AjaxResult.success(this.basicFileService.createEncryptFileId(docClass,fileVo,type,param));
    }


    /**
     * compare
     */
    @ApiOperation("compare")
    @GetMapping("/compare")
    public AjaxResult compare(String basicFileId1,String basicFileId2,String businessId) {
        try {
            return AjaxResult.success(this.basicFilePdfService.fileCompareToPdf(basicFileId1,basicFileId2,businessId));
        } catch (Exception e) {
            return AjaxResult.error("compare异常", ExceptionUtil.stacktraceToString(e));
        }
    }

    /**
     * 复审提醒
     * reviewMsgReminder
     */
    @ApiOperation("reviewRemindJobHandler")
    @GetMapping("/reviewRemindJobHandler")
    public AjaxResult reviewRemindJobHandler() {
        try {
            return this.msgJobService.reviewRemindJobHandler();
        } catch (Exception e) {
            return AjaxResult.error("reviewMsgReminder异常", ExceptionUtil.stacktraceToString(e));
        }
    }


    /**
     * 复审提醒
     * reviewMsgReminder
     */
    @ApiOperation("fileReviewJobHandler")
    @GetMapping("/fileReviewJobHandler")
    public AjaxResult fileReviewJobHandler() {
        try {
            return this.fileJobService.fileReviewJobHandler();
        } catch (Exception e) {
            return AjaxResult.error("fileReviewJobHandler异常", ExceptionUtil.stacktraceToString(e));
        }
    }

    /**
     *
     * 同步生效文件到爱数文档库
     */
    @ApiOperation("fileSync")
    @GetMapping("/filesync")
    public AjaxResult fileSyncAdd(String module, String versionId) {
        try {
            if(module.equals("add")) {
                // 文件新增
                return AjaxResult.success(this.asFileSyncService.add(versionId));
            } else if(module.equals("update")) {
                // 文件更新
                return AjaxResult.success(this.asFileSyncService.update(versionId));
            } else if(module.equals("disuse")) {
                // 文件作废
                return AjaxResult.success(this.asFileSyncService.disuse(versionId));
            }
        } catch (Exception e) {
            return AjaxResult.error("fileSync异常", ExceptionUtil.stacktraceToString(e));
        }
        return AjaxResult.success(DateUtils.getTime());
    }
}

package com.rzdata.process.api;

import com.rzdata.framework.annotation.Log;
import com.rzdata.framework.core.controller.BaseController;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.enums.BusinessType;
import com.rzdata.plugins.mail.IDocStatusMailService;
import com.rzdata.process.domain.Version;
import com.rzdata.process.service.IVersionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 发送邮件测试
 *
 * <AUTHOR>
 * @date 2023-11-4
 */
@Slf4j
@Validated
@Api(value = "测试发送邮件", tags = {"测试发送邮件"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/process/api/testemail/")
public class TestEmailController extends BaseController {


    @Resource
    IVersionService versionService;

    @Resource
    IDocStatusMailService docStatusMailService;

    /**
     * 发送邮件
     */
    @ApiOperation("测试文件版本生效邮件通知")
    @GetMapping(value="/push/addfile",produces="application/json;charset=utf-8")
    @Log(title = "发送邮件", businessType = BusinessType.INSERT)
    public AjaxResult pushAddFile() {
        String[] targets = {"admin","<EMAIL>"};
        Version obj = this.versionService.getById("1724321541518434305");
        return this.docStatusMailService.addFileMsg(targets,obj);
    }

    @ApiOperation("测试文件版本作废邮件通知")
    @GetMapping(value="/push/disusefile",produces="application/json;charset=utf-8")
    @Log(title = "发送邮件", businessType = BusinessType.INSERT)
    public AjaxResult pushDisUseFile() {
        String[] targets = {"admin"};
        Version obj = this.versionService.getById("1719260238121889793");
        return this.docStatusMailService.disUseFileMsg(targets,obj);
    }


}

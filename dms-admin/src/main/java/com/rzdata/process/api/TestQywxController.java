package com.rzdata.process.api;

import com.rzdata.framework.core.controller.BaseController;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.plugins.msg.BizMsgDto;
import com.rzdata.plugins.msg.MsgService;
import com.rzdata.process.domain.DocMessage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 发送企业微信测试
 *
 * <AUTHOR>
 * @date 2024-9-25
 */
@Slf4j
@Validated
@Api(value = "测试发送企业微信", tags = {"测试发送企业微信"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/process/api/testqywx/")
public class TestQywxController extends BaseController {


    @Resource
    MsgService msgService;

    /**
     * 测试发送企业微信
     */
    @ApiOperation("发送消息")
    @GetMapping("/sendMsg")
    public AjaxResult sendMsg(String type,String receiver,String title,String content,String bizId) {
        BizMsgDto newMsg = new BizMsgDto();
        newMsg.setType(type);
        newMsg.setReceiver(receiver);
        newMsg.setTitle(title);
        newMsg.setContent(content);
        newMsg.setBizId(bizId);
        return this.msgService.send(newMsg);
    }

    /**
     * 测试发送企业微信
     */
    @ApiOperation("发送消息")
    @PostMapping("/sendMsgByDocMessage")
    public AjaxResult sendMsgByDocMessage(@RequestBody DocMessage msg) {
        BizMsgDto newMsg = this.msgService.convert(msg);
        return this.msgService.send(newMsg);
    }


}

package com.rzdata.process.api;

import com.rzdata.framework.core.controller.BaseController;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.process.controller.WorkflowController;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;


/**
 * <p> Title: 提供给门户统一待办调用，获取任务待办的数据集合</p>
 * <p> Description: </p>
 * @<NAME_EMAIL>
 * @创建时间 2020-10-10 20:28:28
 * @版本 1.00
 * @修改记录
 * <pre>
 * 版本   修改人    修改时间    修改内容描述
 * </pre>
 */
@Controller
@RequestMapping("/process/api/uniteTask/")
public class UniteTaskController extends BaseController {


	@Resource
	WorkflowController workflowController;
	
	/**
	 * 待办清单
	 * 
	 * @param recUserId
	 * @param request
	 * @param response
	 * @return


	@RequestMapping(value="/execute",method=RequestMethod.GET )
	@ResponseBody
	public List<Map> execute(String type,String recUserId,HttpServletRequest request, HttpServletResponse response) {	
		if(StringUtils.isEmpty(recUserId)) { // 没有传入用户参数的情况
			return null;
		} else if(StringUtils.isEmpty(type)) { // 没有传入类型参数的情况
			return null;
		} else {
			return this.taskService.getUniteTask(type,recUserId);
		}
	} */


	/**
	 * 待办数量
	 * 
	 * @param type
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value="/executeNum",method=RequestMethod.GET )
	@ResponseBody
	public AjaxResult executeNum(Integer type, HttpServletRequest request, HttpServletResponse response) {
		if(type == null) { // 没有传入类型参数的情况
			return AjaxResult.error("没有传入类型参数type");
		} else {
			return AjaxResult.success(this.workflowController.getRecordCount(type.intValue(),request));
		}
	}
	


}

package com.rzdata.process.api;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.blueland.bpmclient.model.BpmClientInputModel;
import com.blueland.bpmclient.model.ProcessDefinitionModel;
import com.rzdata.framework.annotation.Log;
import com.rzdata.framework.constant.Constants;
import com.rzdata.framework.constant.NumberConstants;
import com.rzdata.framework.core.controller.BaseController;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.core.domain.entity.SysUser;
import com.rzdata.framework.enums.BusinessType;
import com.rzdata.framework.utils.BeanCopyUtils;
import com.rzdata.framework.utils.StringUtils;
import com.rzdata.process.domain.BasicFile;
import com.rzdata.process.domain.Standard;
import com.rzdata.process.domain.Version;
import com.rzdata.process.domain.bo.*;
import com.rzdata.process.domain.vo.*;
import com.rzdata.process.enums.LinkTypeEnum;
import com.rzdata.process.enums.RecordStatusEnum;
import com.rzdata.process.service.*;
import com.rzdata.setting.domain.vo.DistributeGroupVo;
import com.rzdata.setting.domain.vo.DocClassFlowVo;
import com.rzdata.setting.service.IDistributeGroupService;
import com.rzdata.setting.service.IDocClassFlowService;
import com.rzdata.setting.service.IVersionRuleDetailService;
import com.rzdata.system.service.ISysUserService;
import com.rzdata.system.service.WorkflowService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.net.URLEncoder;
import java.sql.SQLIntegrityConstraintViolationException;
import java.util.*;


/**
 * <AUTHOR>
 */
@Validated
@Slf4j
@RestController
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RequestMapping(value = "/api/workflow")
@Api(value = "流程服务", tags = {"流程服务"})
public class WorkflowApiController extends BaseController {

	private final WorkflowService workflowService;
	private final ISysUserService userService;

	private final IModifyApplyService iModifyApplyService;

	private final IVersionService iVersionService;

	private final IStandardService iStandardService;

	private final IGenerateIdService iGenerateIdService;

	private final IModifyApplyLinkService iModifyApplyLinkService;

	private final IDocClassFlowService iDocClassFlowService;

	private final IDocDistributeService iDocDistributeService;

	private final IDistributeGroupService iDistributeGroupService;

	private final IBasicFilePdfService iBasicFilePdfService;

	private final IVersionRuleDetailService iVersionRuleDetailService;
	/**
	 * 对外提供的流程暂存服务
	 */
	@ApiOperation("对外提供的文件任务流程暂存服务")
	@PostMapping("/save")
	@Log(title = "流程服务", businessType = BusinessType.INSERT)
	public AjaxResult save(@RequestBody List<ModifyApplyBo> list) {
		for (ModifyApplyBo bo: list){
			saveOne(bo,null);
		}
		return AjaxResult.success();
	}

	/**
	 * 对外提供的流程暂存服务
	 */
	@ApiOperation("对外提供的文件任务流程暂存服务")
	@PostMapping("/save/one")
	@Log(title = "流程服务", businessType = BusinessType.INSERT)
	public AjaxResult<Void> saveOne(@RequestPart("bo") ModifyApplyBo bo,@RequestPart(value = "file",required = false) MultipartFile file) {
		try {
			bo.setRecordStatus(RecordStatusEnum.DRAFT.getCode());
			bo.setChangeType(bo.getChangeType().toUpperCase());
			String changeType = bo.getChangeType().toLowerCase();
			if (file!=null) {
				BasicFile basicFile = iBasicFilePdfService.uploading(file);
				ModifyApplyLinkBo standardDoc = new ModifyApplyLinkBo();
				standardDoc.setDocName(basicFile.getFileName());
				standardDoc.setFileId(basicFile.getId());
				standardDoc.setProtoFileId(basicFile.getId());
				bo.setStandardDoc(standardDoc);
			}
			if (!Constants.CHANGE_TYPE_ADD.toLowerCase().equals(changeType)) {
				//修订、作废必传versionId
				StandardVo standardVo = iStandardService.queryDetail(bo.getVersionId(),Constants.ONE);
				bo.setExt1(standardVo.getExt1());
				bo.setExt2(standardVo.getExt2());
				bo.setExt3(standardVo.getExt3());
				bo.setExt4(standardVo.getExt4());
				bo.setExt5(standardVo.getExt5());
				bo.setExt6(standardVo.getExt6());
				bo.setExt7(standardVo.getExt7());
				bo.setExt8(standardVo.getExt8());
				bo.setExt9(standardVo.getExt9());
				bo.setExt10(standardVo.getExt10());
				bo.setExt11(standardVo.getExt11());
				bo.setExt12(standardVo.getExt12());
				bo.setExt13(standardVo.getExt13());
				bo.setExt14(standardVo.getExt14());
				bo.setExt15(standardVo.getExt15());
				bo.setExt16(standardVo.getExt16());
				bo.setExt17(standardVo.getExt17());
				bo.setExt18(standardVo.getExt18());
				bo.setExt19(standardVo.getExt19());
				bo.setExt20(standardVo.getExt20());
				bo.setDocStatus(standardVo.getStatus());
				bo.setCompliance(standardVo.getCompliance());
				bo.setClassType(standardVo.getClassType());
				bo.setUpVersionId(standardVo.getUpVersionId());
				bo.setParentDocId(standardVo.getParentDocId());
				bo.setDocId(standardVo.getDocId());
				bo.setVersionId(standardVo.getVersionId());
				bo.setDocClass(standardVo.getDocClass());
				bo.setProjectId(standardVo.getProjectId());
				bo.setProjectName(standardVo.getProjectName());
				bo.setVersionValue(standardVo.getVersionValue());
				//关联文件
				ModifyApplyLinkBo linkBo = new ModifyApplyLinkBo();
				linkBo.setVersionId(standardVo.getVersionId());
				linkBo.setLinkType(LinkTypeEnum.REF_DOC.name());
				List<ModifyApplyLinkVo> docLinksVo = iModifyApplyLinkService.selectListByVersionLink(linkBo);
				List<ModifyApplyLinkBo> docLinksBo = BeanUtil.copyToList(docLinksVo,ModifyApplyLinkBo.class);
				bo.setDocLinks(docLinksBo);
				//关联记录
				linkBo.setLinkType(LinkTypeEnum.RECORD.name());
				List<ModifyApplyLinkVo> recordLinksVo = iModifyApplyLinkService.selectListByVersionLink(linkBo);
				List<ModifyApplyLinkBo> recordLinksBo =  BeanUtil.copyToList(recordLinksVo,ModifyApplyLinkBo.class);
				bo.setRecordLinks(recordLinksBo);

				//关联记录(多对多)
				linkBo.setLinkType(LinkTypeEnum.NOTE.name());
				List<ModifyApplyLinkVo> noteLinksVo = iModifyApplyLinkService.selectListByVersionLink(linkBo);
				List<ModifyApplyLinkBo> noteLinksBo =  BeanUtil.copyToList(noteLinksVo,ModifyApplyLinkBo.class);
				bo.setNoteLinks(noteLinksBo);

				//主文件记录
				linkBo.setLinkType(LinkTypeEnum.NOTE_DOC.name());
				List<ModifyApplyLinkVo> noteDocLinksVo = iModifyApplyLinkService.selectListByVersionLink(linkBo);
				List<ModifyApplyLinkBo> noteDocLinksBo =  BeanUtil.copyToList(noteDocLinksVo,ModifyApplyLinkBo.class);
				bo.setNoteDocLinks(noteDocLinksBo);
			}

			bo.setYNDistribute(Constants.VALUE_N);
			bo.setApplyTime(new Date());
			bo.setWhetherRetain(Constants.VALUE_N);
			bo.setDistributeType(Constants.TYPE_PERSON);

			if (!Constants.CHANGE_TYPE_DISUSE.toLowerCase().equals(changeType)) {
				//版本号
				bo.setVersionValue(iVersionRuleDetailService.getNextVersion(bo.getVersionValue(),bo.getDocClass()));
				//分发和权限
				List<ModifyApplyDistributeBo> distributeList = new ArrayList<>();

				List<DistributeGroupVo> groupVoList=iDistributeGroupService.listByDocClass(bo.getDocClass());
				groupVoList.forEach(item->{
					if (Constants.DISTRIBUTE_TYPE_PRINT.equals(item.getType())) {
						bo.setDistributeType(item.getScopeType());
						if (item.getItemList()!=null && item.getItemList().size() > 0) {
							bo.setYNDistribute(Constants.VALUE_Y);
							distributeList.addAll(BeanUtil.copyToList(item.getItemList(),ModifyApplyDistributeBo.class));
						}
					} else if(Constants.DISTRIBUTE_TYPE_PURVIEW.equals(item.getType())) {
						bo.setTrainType(item.getScopeType());
						if (item.getItemList()!=null && item.getItemList().size() > 0) {
							distributeList.addAll(BeanUtil.copyToList(item.getItemList(),ModifyApplyDistributeBo.class));
						}
					}
				});

				//培训
//				DocDistributeBo bo1 = new DocDistributeBo();
//				bo1.setVersionId(bo.getVersionId());
//				bo1.setNeType(Constants.DISTRIBUTE_TYPE_PRINT);
//				List<DocDistributeVo> trainDistributeList = iDocDistributeService.queryList(bo1);
//				if (trainDistributeList!=null&&trainDistributeList.size()>0) {
//					bo.setTrainType(trainType);
//					trainDistributeList.forEach(item->{
//						ModifyApplyDistributeBo distribute = new ModifyApplyDistributeBo();
//						distribute.setNums(NumberConstants.ONE);
//						distribute.setReceiveUserDeptId(item.getReceiveUserDeptId());
//						distribute.setReceiveUserDept(item.getReceiveUserDept());
//						distribute.setReceiveUserName(item.getReceiveUserName());
//						distribute.setReceiveNickName(item.getReceiveNickName());
//						distribute.setType(item.getType());
//						distribute.setCategory(Constants.DISTRIBUTE_TYPE_TRAIN);
//						if (Constants.TYPE_DEPT.equals(item.getType())){
//							distribute.setNums(userService.getNumByDeptId(item.getReceiveUserDeptId()));
//						}else if (Constants.TYPE_COMPANY.equals(item.getType())){
//							distribute.setNums(userService.getNumByTenantId(item.getReceiveUserDeptId()));
//						}
//						distributeList.add(distribute);
//					});
//				}
//				//分发
//				List<DocDistributeVo> printDistributeList = iDocDistributeService.getPrintGroupList(bo.getVersionId());
//				if (printDistributeList!=null&&printDistributeList.size()>0) {
//					bo.setYNDistribute(Constants.VALUE_Y);
//					bo.setDistributeType(Constants.TYPE_PERSON);
//					printDistributeList.forEach(item->{
//						ModifyApplyDistributeBo distribute = new ModifyApplyDistributeBo();
//						distribute.setNums(item.getNums());
//						distribute.setReceiveUserDeptId(item.getReceiveUserDeptId());
//						distribute.setReceiveUserDept(item.getReceiveUserDept());
//						distribute.setReceiveUserName(item.getReceiveUserName());
//						distribute.setReceiveNickName(item.getReceiveNickName());
//						distribute.setType(Constants.TYPE_PERSON);
//						distribute.setCategory(Constants.DISTRIBUTE_TYPE_PRINT);
//						distributeList.add(distribute);
//					});
//				}
				bo.setDistributeList(distributeList);
			}

			DocClassFlowVo vo= iDocClassFlowService.getByUpDocClassAndBizType(bo.getDocClass(),bo.getChangeType(),"");
			if (vo==null||StringUtils.isEmpty(vo.getFlowKey())) {
				throw new Exception("该文件类型尚未进行流程设置，将无法发起子流程！");
			}
  			String procDefKey = vo.getFlowKey();

			//组装流程参数
			BpmClientInputModelBo bpmClientInputModelBo = new BpmClientInputModelBo();
			ProcessDefinitionModel processDefinitionModel = workflowService.getProcessDefinitionModel(procDefKey);
			BpmClientInputModel bpmClientInputModel = new BpmClientInputModel();
			bpmClientInputModel.setWf_procDefId(processDefinitionModel.getProcDefId());
			bpmClientInputModel.setWf_procDefKey(processDefinitionModel.getProcDefKey());
			bpmClientInputModel.setWf_procTitle(bo.getDocName());
			bpmClientInputModel.setWf_sendUserId(bo.getUserName());
			bpmClientInputModel.setWf_sendUserOrgId(bo.getDeptId());
			// 设置接收人
			List<Map<String,Object>> list = new ArrayList<>();
			Map<String,Object> map = new HashMap<>();
			map.put("receiveUserId",bo.getUserName());
			map.put("receiveUserOrgId",bo.getDeptId());
			list.add(map);
			bpmClientInputModel.setWf_receivers(list);
			bpmClientInputModelBo.setModel(bpmClientInputModel);
			bpmClientInputModelBo.setType(changeType+"_doc");
			/** 标识字段，不为空时，更新站内消息表的linkUrl **/
			if(StringUtils.isNotBlank(bo.getApplyIdTemp())){
				bpmClientInputModelBo.setApplyIdTemp(bo.getApplyIdTemp());
			}
			bo.setBpmClientInputModel(bpmClientInputModelBo);
			bo.setId(null);
			iModifyApplyService.insertByBo(bo);
		} catch (Exception e) {
			log.error("流程暂存服务异常",e);
		}
		return AjaxResult.success();
	}

	private static final String BOM = "BOM";
	private static final String DATATYPE_PROJECT = "project";

	private static final String WORKFLOW_STATUS_PUBLISH = "PUBLISH";

	@ApiOperation("bom流程结束时的对文件的操作")
	@PostMapping("/bomFile")
	@Log(title = "流程服务", businessType = BusinessType.INSERT)
	public AjaxResult bomFile(@RequestBody ProBomDetailBo bomBo) {
		try {
			//workflowStatus为ABOLISH为作废。workflowStatus为PUBLISH的，relateId为空是新增、不为空是修订。
			if (WORKFLOW_STATUS_PUBLISH.equals(bomBo.getWorkflowStatus())) {
				if (ObjectUtil.isEmpty(bomBo.getRelateId())) {
					//新增
					//标准文件
					Standard standard = new Standard();
					//文件类型归类为“采购物资清单”
					standard.setDocClass(BOM);
					//文件标题格式默认为“母件名称+母件编码+BOM”
					standard.setDocName(bomBo.getParentMaterialName()+"-"+bomBo.getParentMaterialCode()+"-"+BOM);
					standard.setProjectId(bomBo.getGroupId());
					standard.setDataType(DATATYPE_PROJECT);
					iStandardService.save(standard);
					//版本
					SysUser user = userService.selectUserById(bomBo.getCreateBy());
					Version version = new Version();
					version.setUserName(user.getUserName());
					version.setDeptId(user.getDeptId());
					version.setId(bomBo.getId());
					version.setStandardId(standard.getId());
					//编号规则：BOM-项目代码-3位顺序号
					DocNoVo docNoVo = iGenerateIdService.generateDocId(BOM,bomBo.getGroupCode());
					version.setDocId(docNoVo.getDocId());
					version.setStartDate(bomBo.getVersionDate());
					version.setVersionValue(bomBo.getFileVersion());
					version.setDataType(DATATYPE_PROJECT);
					version.setReleaseTime(DateUtil.date());
					iVersionService.save(version);
//				}else{
//					//修订
//					Version oldVersion = iVersionService.getById(bomBo.getRelateId());
//					oldVersion.setEndDate(new Date());
//					oldVersion.setStatus(Constants.TWO);
//					Version newVersion = new Version();
//					newVersion.setId(bomBo.getId());
//					newVersion.setStandardId(oldVersion.getStandardId());
//					newVersion.setDocId(oldVersion.getDocId());
//					newVersion.setStartDate(bomBo.getVersionDate());
//					newVersion.setVersionValue(bomBo.getFileVersion());
//					newVersion.setDataType(DATATYPE_PROJECT);
//					newVersion.setReleaseTime(DateUtil.date());
//					//生效新版本
//					iVersionService.save(newVersion);
//					//失效旧版本
//					iVersionService.updateById(oldVersion);
				}
//			}else{
//				//作废
//				iVersionService.update(new LambdaUpdateWrapper<Version>().eq(Version::getId,bomBo.getId()).set(Version::getStatus,Constants.TWO).set(Version::getEndDate,new Date()));
			}
		} catch (SQLIntegrityConstraintViolationException e){
			return AjaxResult.error("DMS文件已存在！");
		} catch (Exception e){
			return AjaxResult.error("DMS系统异常，请联系管理员");
		}
		return AjaxResult.success();
	}
}

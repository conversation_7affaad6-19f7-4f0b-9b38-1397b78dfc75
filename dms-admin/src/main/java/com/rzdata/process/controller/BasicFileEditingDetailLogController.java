package com.rzdata.process.controller;

import java.util.List;
import java.util.Arrays;

import com.rzdata.framework.core.domain.model.LoginUser;
import com.rzdata.framework.utils.SecurityUtils;
import com.rzdata.process.domain.bo.BasicFileEditingDetailLogBo;
import com.rzdata.process.domain.vo.BasicFileEditingDetailLogVo;
import com.rzdata.process.service.IBasicFileEditingDetailLogService;
import lombok.RequiredArgsConstructor;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.rzdata.framework.annotation.RepeatSubmit;
import com.rzdata.framework.annotation.Log;
import com.rzdata.framework.core.controller.BaseController;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import com.rzdata.framework.core.validate.QueryGroup;
import com.rzdata.framework.enums.BusinessType;
import com.rzdata.framework.utils.poi.ExcelUtil;
import com.rzdata.framework.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiOperation;

/**
 * 编辑明细日志Controller
 *
 * <AUTHOR>
 * @date 2025-04-30
 */
@Validated
@Api(value = "编辑明细日志控制器", tags = {"编辑明细日志管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/system/fileEditingDetailLog")
public class BasicFileEditingDetailLogController extends BaseController {

    private final IBasicFileEditingDetailLogService iBasicFileEditingDetailLogService;

    /**
     * 查询编辑明细日志列表
     */
    @ApiOperation("查询编辑明细日志列表")
    @PreAuthorize("@ss.hasPermi('system:fileEditingDetailLog:list')")
    @GetMapping("/list")
    public TableDataInfo<BasicFileEditingDetailLogVo> list(@Validated(QueryGroup.class) BasicFileEditingDetailLogBo bo) {
        return iBasicFileEditingDetailLogService.queryPageList(bo);
    }

    /**
     * 导出编辑明细日志列表
     */
    @ApiOperation("导出编辑明细日志列表")
    @PreAuthorize("@ss.hasPermi('system:fileEditingDetailLog:export')")
    @Log(title = "编辑明细日志", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public void export(@Validated BasicFileEditingDetailLogBo bo, HttpServletResponse response) {
        List<BasicFileEditingDetailLogVo> list = iBasicFileEditingDetailLogService.queryList(bo);
        ExcelUtil.exportExcel(list, "编辑明细日志", BasicFileEditingDetailLogVo.class, response);
    }

    /**
     * 获取编辑明细日志详细信息
     */
    @ApiOperation("获取编辑明细日志详细信息")
    @PreAuthorize("@ss.hasPermi('system:fileEditingDetailLog:query')")
    @GetMapping("/{id}")
    public AjaxResult<BasicFileEditingDetailLogVo> getInfo(@ApiParam("主键")
                                                  @NotNull(message = "主键不能为空")
                                                  @PathVariable("id") String id) {
        return AjaxResult.success(iBasicFileEditingDetailLogService.queryById(id));
    }

    /**
     * 新增编辑明细日志
     */
    @ApiOperation("新增编辑明细日志")
    @Log(title = "编辑明细日志", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public AjaxResult<Void> add(@Validated(AddGroup.class) @RequestBody BasicFileEditingDetailLogBo bo) {
        return toAjax(iBasicFileEditingDetailLogService.insertByBo(bo) ? 1 : 0);
    }

    @ApiOperation("检查文件编辑状态")
    @PostMapping("/check/{protoFileId}")
    public AjaxResult checkEditStatus(@RequestBody BasicFileEditingDetailLogBo bo) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        return iBasicFileEditingDetailLogService.checkFileEditStatus(bo, loginUser.getUsername());
    }

    @ApiOperation("释放文件编辑锁")
    @GetMapping("/release/{protoFileId}")
    public AjaxResult releaseEditLock(@PathVariable("protoFileId") String protoFileId) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        iBasicFileEditingDetailLogService.releaseFileEditLock(protoFileId, loginUser.getUsername());
        return AjaxResult.success("已释放编辑锁");
    }

    /**
     * 修改编辑明细日志
     */
    @ApiOperation("修改编辑明细日志")
    @Log(title = "编辑明细日志", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public AjaxResult<Void> edit(@Validated(EditGroup.class) @RequestBody BasicFileEditingDetailLogBo bo) {
        return toAjax(iBasicFileEditingDetailLogService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除编辑明细日志
     */
    @ApiOperation("删除编辑明细日志")
    @PreAuthorize("@ss.hasPermi('system:fileEditingDetailLog:remove')")
    @Log(title = "编辑明细日志" , businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult<Void> remove(@ApiParam("主键串")
                                       @NotEmpty(message = "主键不能为空")
                                       @PathVariable String[] ids) {
        return toAjax(iBasicFileEditingDetailLogService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}

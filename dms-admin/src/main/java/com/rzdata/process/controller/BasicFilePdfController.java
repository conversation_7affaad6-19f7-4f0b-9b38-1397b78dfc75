package com.rzdata.process.controller;

import cn.hutool.core.exceptions.ExceptionUtil;
import com.rzdata.framework.annotation.Log;
import com.rzdata.framework.annotation.RepeatSubmit;
import com.rzdata.framework.core.controller.BaseController;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import com.rzdata.framework.core.validate.QueryGroup;
import com.rzdata.framework.enums.BusinessType;
import com.rzdata.framework.utils.poi.ExcelUtil;
import com.rzdata.process.domain.bo.BasicFilePdfBo;
import com.rzdata.process.domain.vo.BasicFilePdfVo;
import com.rzdata.process.service.IBasicFilePdfService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * 附件转PDFController
 *
 * <AUTHOR>
 * @date 2023-07-31
 */
@Validated
@Api(value = "附件转PDF控制器", tags = {"附件转PDF管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/process/filePdf")
public class BasicFilePdfController extends BaseController {

    private final IBasicFilePdfService iBasicFilePdfService;

    /**
     * 查询附件转PDF列表
     */
    @ApiOperation("查询附件转PDF列表")
    @GetMapping("/list")
    public TableDataInfo<BasicFilePdfVo> list(@Validated(QueryGroup.class) BasicFilePdfBo bo) {
        return iBasicFilePdfService.queryPageList(bo);
    }

    @ApiOperation("compare")
    @GetMapping("/compare")
    public AjaxResult compare(String basicFileId1,String basicFileId2,String businessId) {
        try {
            return AjaxResult.success(this.iBasicFilePdfService.fileCompareToPdf(basicFileId1,basicFileId2,businessId));
        } catch (Exception e) {
            return AjaxResult.error("compare异常", ExceptionUtil.stacktraceToString(e));
        }
    }

    /**
     * 导出附件转PDF列表
     */
    @ApiOperation("导出附件转PDF列表")
    @PreAuthorize("@ss.hasPermi('system:filePdf:export')")
    @Log(title = "附件转PDF", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public void export(@Validated BasicFilePdfBo bo, HttpServletResponse response) {
        List<BasicFilePdfVo> list = iBasicFilePdfService.queryList(bo);
        ExcelUtil.exportExcel(list, "附件转PDF", BasicFilePdfVo.class, response);
    }

    /**
     * 获取附件转PDF详细信息
     */
    @ApiOperation("获取附件转PDF详细信息")
    @PreAuthorize("@ss.hasPermi('system:filePdf:query')")
    @GetMapping("/{id}")
    public AjaxResult<BasicFilePdfVo> getInfo(@ApiParam("主键")
                                                  @NotNull(message = "主键不能为空")
                                                  @PathVariable("id") String id) {
        return AjaxResult.success(iBasicFilePdfService.queryById(id));
    }

    /**
     * 新增附件转PDF
     */
    @ApiOperation("新增附件转PDF")
    @PreAuthorize("@ss.hasPermi('system:filePdf:add')")
    @Log(title = "附件转PDF", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public AjaxResult<Void> add(@Validated(AddGroup.class) @RequestBody BasicFilePdfBo bo) {
        return toAjax(iBasicFilePdfService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改附件转PDF
     */
    @ApiOperation("修改附件转PDF")
    @PreAuthorize("@ss.hasPermi('system:filePdf:edit')")
    @Log(title = "附件转PDF", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public AjaxResult<Void> edit(@Validated(EditGroup.class) @RequestBody BasicFilePdfBo bo) {
        return toAjax(iBasicFilePdfService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除附件转PDF
     */
    @ApiOperation("删除附件转PDF")
    @PreAuthorize("@ss.hasPermi('system:filePdf:remove')")
    @Log(title = "附件转PDF" , businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult<Void> remove(@ApiParam("主键串")
                                       @NotEmpty(message = "主键不能为空")
                                       @PathVariable String[] ids) {
        return toAjax(iBasicFilePdfService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}

package com.rzdata.process.controller;

import com.rzdata.framework.annotation.Log;
import com.rzdata.framework.annotation.RepeatSubmit;
import com.rzdata.framework.core.controller.BaseController;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import com.rzdata.framework.core.validate.QueryGroup;
import com.rzdata.framework.enums.BusinessType;
import com.rzdata.framework.utils.poi.ExcelUtil;
import com.rzdata.plugins.filesync.IFileSyncService;
import com.rzdata.process.domain.bo.DocDistributeBo;
import com.rzdata.process.domain.dto.VersionDistributeDTO;
import com.rzdata.process.domain.vo.DocDistributeVo;
import com.rzdata.process.domain.vo.DocTrainReadVo;
import com.rzdata.process.service.IDocDistributeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * 文件分发明细Controller
 *
 * <AUTHOR>
 * @date 2023-07-20
 */
@Validated
@Api(value = "文件分发明细控制器", tags = {"文件分发明细管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/process/distribute")
public class DocDistributeController extends BaseController {

    private final IDocDistributeService iDocDistributeService;

    private final IFileSyncService asFileSyncService;

    /**
     * 查询文件分发明细列表
     */
    @ApiOperation("查询文件分发明细列表")
//    @PreAuthorize("@ss.hasPermi('process:distribute:list')")
    @GetMapping("/page")
    public TableDataInfo<DocDistributeVo> page(@Validated(QueryGroup.class) DocDistributeBo bo) {
        return iDocDistributeService.queryPageList(bo);
    }

    /**
     * 查询文件分发明细列表
     */
    @ApiOperation("查询文件分发文件列表")
//    @PreAuthorize("@ss.hasPermi('process:distribute:list')")
    @GetMapping("/page/doc")
    public TableDataInfo<DocDistributeVo> pageDoc(@Validated(QueryGroup.class) DocDistributeBo bo) {
        return iDocDistributeService.queryPageDocList(bo);
    }


    /**
     * 查询文件分发明细列表
     */
    @ApiOperation("查询文件分发文件列表")
    @GetMapping("/auth/filter/page/doc")
    public TableDataInfo<DocDistributeVo> authFilterPageDoc(@Validated(QueryGroup.class) DocDistributeBo bo) {
        return iDocDistributeService.queryAuthFilterPageDocList(bo);
    }


    /**
     * 查询阅读同意数据列表
     */
    @ApiOperation("查询阅读同意数据列表")
    @PostMapping("/read/train/page")
    public TableDataInfo<DocDistributeVo> readTrainPage(@RequestBody DocDistributeBo bo) {
        return iDocDistributeService.readTrainPage(bo);
    }


    /**
     * 查询文件分发明细列表
     */
    @ApiOperation("查询文件分发明细列表")
//    @PreAuthorize("@ss.hasPermi('process:distribute:list')")
    @GetMapping("/list")
    public AjaxResult<List<DocDistributeVo>> list(@Validated(QueryGroup.class) DocDistributeBo bo) {
        return AjaxResult.success(iDocDistributeService.queryList(bo));
    }


    /**
     * 查询文件分发明细列表
     */
    @ApiOperation("查询文件分发明细列表")
    @GetMapping("/auth/filter/list")
    public AjaxResult<List<DocDistributeVo>> authFilterList(@Validated(QueryGroup.class) DocDistributeBo bo) {
        return AjaxResult.success(iDocDistributeService.authFilterList(bo));
    }


    /**
     * 查询文件分发明细列表
     */
    @ApiOperation("查询文件分发明细列表")
    @PostMapping("/postList")
    public AjaxResult<List<DocDistributeVo>> postList(@RequestBody DocDistributeBo bo) {
        return AjaxResult.success(iDocDistributeService.postQueryList(bo));
    }

    /**
     * 查询文件分发明细列表
     */
    @ApiOperation("查询文件打印分组列表列表")
//    @PreAuthorize("@ss.hasPermi('process:distribute:list')")
    @GetMapping("/list/print/group")
    public AjaxResult<List<DocDistributeVo>> getPrintGroupList(@Validated(QueryGroup.class) DocDistributeBo bo) {
        return AjaxResult.success(iDocDistributeService.getPrintGroupList(bo.getVersionId()));
    }

    /**
     * 通过fileId找到对应的文件分发人
     */
    @ApiOperation("通过fileId找到对应的文件分发人")
    @GetMapping("/list/print/groupByFileId")
    public AjaxResult<DocDistributeVo> getPrintById(@Validated(QueryGroup.class) DocDistributeBo bo) {
        return AjaxResult.success(iDocDistributeService.getPrintAuthById(bo.getId(),bo.getFileId()));
    }

    /**
     * 打印找到对应的文件分发人 (批量打印专用)
     */
    @ApiOperation("通过fileId找到对应的文件分发人")
    @GetMapping("/list/print/getPrintAuthBatchById")
    public AjaxResult<List<DocDistributeVo>> getPrintAuthBatchById(@Validated(QueryGroup.class) DocDistributeBo bo) {
        return AjaxResult.success(iDocDistributeService.getPrintAuthBatchById(bo.getId(),bo.getFileIds()));
    }

    /**
     * 签收
     */
    @ApiOperation("签收")
//    @PreAuthorize("@ss.hasPermi('process:distribute:receive')")
    @Log(title = "文件分发明细" , businessType = BusinessType.UPDATE)
    @GetMapping("/receive/{ids}")
    public AjaxResult<Void> receiveByIds(@ApiParam("主键串")
                                   @NotEmpty(message = "主键不能为空")
                                   @PathVariable String[] ids) {
        return toAjax(iDocDistributeService.receiveByIds(Arrays.asList(ids)));
    }

    /**
     * 签收
     */
    @ApiOperation("回收")
//    @PreAuthorize("@ss.hasPermi('process:distribute:recovery')")
    @Log(title = "文件分发明细" , businessType = BusinessType.UPDATE)
    @GetMapping("/recovery/{ids}")
    public AjaxResult<Void> recoveryByIds(@ApiParam("主键串")
                                         @NotEmpty(message = "主键不能为空")
                                         @PathVariable String[] ids) {
        return toAjax(iDocDistributeService.recoveryByIds(Arrays.asList(ids)));
    }

    /**
     * 丢失
     */
    @ApiOperation("丢失")
//    @PreAuthorize("@ss.hasPermi('process:distribute:recovery')")
    @Log(title = "文件分发明细" , businessType = BusinessType.UPDATE)
    @GetMapping("/lost/{ids}")
    public AjaxResult<Void> lostByIds(@ApiParam("主键串")
                                          @NotEmpty(message = "主键不能为空")
                                          @PathVariable String[] ids) {
        return toAjax(iDocDistributeService.lostByIds(Arrays.asList(ids)));
    }

    /**
     * 导出文件分发明细列表
     */
    @ApiOperation("导出文件分发明细列表")
    @PreAuthorize("@ss.hasPermi('process:distribute:export')")
    @Log(title = "文件分发明细", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public void export(@Validated DocDistributeBo bo, HttpServletResponse response) {
        List<DocDistributeVo> list = iDocDistributeService.queryList(bo);
        ExcelUtil.exportExcel(list, "文件分发明细", DocDistributeVo.class, response);
    }

    /**
     * 获取文件分发明细详细信息
     */
    @ApiOperation("获取文件分发明细详细信息")
    @GetMapping("/{id}")
    public AjaxResult<DocDistributeVo> getInfo(@ApiParam("主键")
                                                  @NotNull(message = "主键不能为空")
                                                  @PathVariable("id") String id) {
        return AjaxResult.success(iDocDistributeService.queryById(id));
    }

    /**
     * 新增文件分发明细
     */
    @ApiOperation("新增文件分发明细")
    @PreAuthorize("@ss.hasPermi('process:distribute:add')")
    @Log(title = "文件分发明细", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public AjaxResult<Void> add(@Validated(AddGroup.class) @RequestBody DocDistributeBo bo) {
        return toAjax(iDocDistributeService.insertByBo(bo) ? 1 : 0);
    }

    @ApiOperation("更新文件分发明细")
    @Log(title = "文件分发明细", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/update/list")
    public AjaxResult<VersionDistributeDTO> updateList(@RequestBody VersionDistributeDTO dto) {
        VersionDistributeDTO result = iDocDistributeService.updateList(dto);
        // 同步DMS更新生效文件到爱数文档库（同步工程执行先删除、后新增）
        this.asFileSyncService.update(dto.getVersionId());
        return AjaxResult.success(result);
    }

    @ApiOperation("新增文件分发明细")
    @Log(title = "文件分发明细", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/update/code")
    public AjaxResult<Void> updateCodeList(@RequestBody VersionDistributeDTO dto) {
        return toAjax(iDocDistributeService.updateCodeList(dto));
    }

    /**
     * 修改文件分发明细
     */
    @ApiOperation("修改文件分发明细")
    @PreAuthorize("@ss.hasPermi('process:distribute:edit')")
    @Log(title = "文件分发明细", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public AjaxResult<Void> edit(@Validated(EditGroup.class) @RequestBody DocDistributeBo bo) {
        return toAjax(iDocDistributeService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除文件分发明细
     */
    @ApiOperation("删除文件分发明细")
    @PreAuthorize("@ss.hasPermi('process:distribute:remove')")
    @Log(title = "文件分发明细" , businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult<Void> remove(@ApiParam("主键串")
                                       @NotEmpty(message = "主键不能为空")
                                       @PathVariable String[] ids) {
        return toAjax(iDocDistributeService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }



    /**
     * 详情
     */
    @ApiOperation("查询阅读明细详情")
    @GetMapping("/trainDetailList")
    public TableDataInfo<DocDistributeVo> trainDetailList(DocDistributeBo bo) {
        return iDocDistributeService.trainDetailList(bo);
    }

    @ApiOperation("查询阅读明细详情")
    @GetMapping("/trainDetailStatusList")
    public AjaxResult<List<DocDistributeVo>> trainDetailStatusList(DocDistributeBo bo) {
        return AjaxResult.success(iDocDistributeService.trainDetailStatusList(bo));
    }


    /**
     * 发送待回收邮件
     */
    @ApiOperation("发送待回收邮件")
    @PreAuthorize("@ss.hasPermi('fileProcessing:recovery:sendEmail')")
    @GetMapping("/sendRecoveryEMail/{ids}")
    public AjaxResult<Void> sendRecoveryEMail(@ApiParam("主键串")
                                                  @NotEmpty(message = "主键不能为空")
                                                  @PathVariable String[] ids) {
        return toAjax(iDocDistributeService.sendRecoveryEMail(Arrays.asList(ids)) ? 1 : 0);
    }

    @ApiOperation("导出文件分发明细列表")
    @PostMapping("/post/export")
    public void postExport(DocDistributeBo bo, HttpServletResponse response) {
        List<DocDistributeVo> list = iDocDistributeService.postQueryList(bo);
        list.forEach(item -> item.setCodeNum(item.getCode() < 10 ? "0".concat(String.valueOf(item.getCode()))  : String.valueOf(item.getCode())));
        ExcelUtil.exportExcel(list, "文件分发明细", DocDistributeVo.class, response);
    }

}

package com.rzdata.process.controller;

import java.util.List;
import java.util.Arrays;

import lombok.RequiredArgsConstructor;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.rzdata.framework.annotation.RepeatSubmit;
import com.rzdata.framework.annotation.Log;
import com.rzdata.framework.core.controller.BaseController;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import com.rzdata.framework.core.validate.QueryGroup;
import com.rzdata.framework.enums.BusinessType;
import com.rzdata.framework.utils.poi.ExcelUtil;
import com.rzdata.process.domain.vo.DocEditLogVo;
import com.rzdata.process.domain.bo.DocEditLogBo;
import com.rzdata.process.service.IDocEditLogService;
import com.rzdata.framework.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiOperation;

/**
 * 文件编辑日志Controller
 *
 * <AUTHOR>
 * @date 2025-03-03
 */
@Validated
@Api(value = "文件编辑日志控制器", tags = {"文件编辑日志管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/process/editLog")
public class DocEditLogController extends BaseController {

    private final IDocEditLogService iDocEditLogService;

    /**
     * 查询文件编辑日志列表
     */
    @ApiOperation("查询文件编辑日志列表")
    @GetMapping("/list")
    public TableDataInfo<DocEditLogVo> list(@Validated(QueryGroup.class) DocEditLogBo bo) {
        return iDocEditLogService.queryPageList(bo);
    }

    /**
     * 导出文件编辑日志列表
     */
    @ApiOperation("导出文件编辑日志列表")
    @PreAuthorize("@ss.hasPermi('process:editLog:export')")
    @Log(title = "文件编辑日志", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public void export(@Validated DocEditLogBo bo, HttpServletResponse response) {
        List<DocEditLogVo> list = iDocEditLogService.queryList(bo);
        ExcelUtil.exportExcel(list, "文件编辑日志", DocEditLogVo.class, response);
    }

    /**
     * 获取文件编辑日志详细信息
     */
    @ApiOperation("获取文件编辑日志详细信息")
    @PreAuthorize("@ss.hasPermi('process:editLog:query')")
    @GetMapping("/{id}")
    public AjaxResult<DocEditLogVo> getInfo(@ApiParam("主键")
                                                  @NotNull(message = "主键不能为空")
                                                  @PathVariable("id") String id) {
        return AjaxResult.success(iDocEditLogService.queryById(id));
    }

    /**
     * 新增文件编辑日志
     */
    @ApiOperation("新增文件编辑日志")
    @PreAuthorize("@ss.hasPermi('process:editLog:add')")
    @Log(title = "文件编辑日志", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public AjaxResult<Void> add(@Validated(AddGroup.class) @RequestBody DocEditLogBo bo) {
        return toAjax(iDocEditLogService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改文件编辑日志
     */
    @ApiOperation("修改文件编辑日志")
    @PreAuthorize("@ss.hasPermi('process:editLog:edit')")
    @Log(title = "文件编辑日志", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public AjaxResult<Void> edit(@Validated(EditGroup.class) @RequestBody DocEditLogBo bo) {
        return toAjax(iDocEditLogService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除文件编辑日志
     */
    @ApiOperation("删除文件编辑日志")
    @PreAuthorize("@ss.hasPermi('process:editLog:remove')")
    @Log(title = "文件编辑日志" , businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult<Void> remove(@ApiParam("主键串")
                                       @NotEmpty(message = "主键不能为空")
                                       @PathVariable String[] ids) {
        return toAjax(iDocEditLogService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}

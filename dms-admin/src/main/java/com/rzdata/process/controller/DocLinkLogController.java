package com.rzdata.process.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.rzdata.framework.annotation.Log;
import com.rzdata.framework.annotation.RepeatSubmit;
import com.rzdata.framework.constant.CommonI18nConstant;
import com.rzdata.framework.core.controller.BaseController;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import com.rzdata.framework.core.validate.QueryGroup;
import com.rzdata.framework.enums.BusinessType;
import com.rzdata.framework.utils.DateUtils;
import com.rzdata.framework.utils.I18nUtils;
import com.rzdata.framework.utils.poi.ExcelUtil;
import com.rzdata.process.domain.*;
import com.rzdata.process.domain.bo.DocLinkLogBo;
import com.rzdata.process.domain.vo.DocLinkLogVo;
import com.rzdata.process.domain.vo.DocLinkSearchDetailVo;
import com.rzdata.process.domain.vo.DocLinkSearchResultVo;
import com.rzdata.process.domain.vo.DocVersionLinkVo;
import com.rzdata.process.service.IDocLinkLogService;
import com.rzdata.process.service.IDocVersionLinkService;
import com.rzdata.process.service.IStandardService;
import com.rzdata.process.service.IVersionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 文件关联记录Controller
 *
 * <AUTHOR>
 * @date 2022-01-06
 */
@Validated
@Slf4j
@Api(value = "文件关联记录控制器", tags = {"文件关联记录管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/process/linkLog")
public class DocLinkLogController extends BaseController {

    private final IDocLinkLogService iDocLinkLogService;

    private final IDocVersionLinkService docVersionLinkService;

    private final IStandardService standardService;

    private final IVersionService versionService;

    /**
     * 查询文件关联记录列表
     */
    @ApiOperation("查询文件关联记录列表")
    @GetMapping("/list")
    public TableDataInfo<DocLinkLogVo> list(@Validated(QueryGroup.class) DocLinkLogBo bo) {
        return iDocLinkLogService.queryPageList(bo);
    }

    /**
     * 查询文件关联记录列表
     */
    @ApiOperation("查询文件关联记录列表")
    @GetMapping("/link/list")
    public AjaxResult<List<DocLinkLogVo>> linkList(@Validated(QueryGroup.class) DocLinkLogBo bo) {
        return AjaxResult.success(iDocLinkLogService.queryDocLinkVo(bo.getVersionId(),bo.getLinkType()));
    }

    /**
     * 首页关键字查询文件关联记录列表
     */
    @ApiOperation("首页关键字查询文件关联记录列表")
    @GetMapping("/search/list")
    public TableDataInfo<DocLinkSearchResultVo> searchList(@Validated(QueryGroup.class) DocLinkLogBo bo) {
        return iDocLinkLogService.docLinkSearchPage(bo);
    }

    /**
     * 首页关键字查询文件关联记录明细列表
     */
    @ApiOperation("首页关键字查询文件关联记录明细列表")
    @GetMapping("/search/detail/{ids}")
    public List<DocLinkSearchDetailVo> searchDetail(@PathVariable String[] ids) {
        return iDocLinkLogService.docLinkSearchDetailList(Arrays.asList(ids));
    }

    /**
     * 首页关键字查询文件关联记录明细下载本地存储
     */
    @ApiOperation("首页关键字查询文件关联记录明细下载本地存储")
    @GetMapping("/search/download/{ids}")
    public void searchDownLoad(@PathVariable String[] ids, HttpServletResponse response) throws Exception {
        iDocLinkLogService.docLinkSearchDownload(Arrays.asList(ids), response);
    }

    /**
     * 导出文件关联记录列表
     */
    @ApiOperation("导出文件关联记录列表")
    @Log(title = "文件关联记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(@Validated DocLinkLogBo bo, HttpServletResponse response) {
        List<DocLinkLogVo> list = iDocLinkLogService.queryList(bo);
        ExcelUtil.exportExcel(list, "文件关联记录", DocLinkLogVo.class, response);
    }

    /**
     * 获取文件关联记录详细信息
     */
    @ApiOperation("获取文件关联记录详细信息")
    @GetMapping("/{id}")
    public AjaxResult<DocLinkLogVo> getInfo(@ApiParam("主键")
                                                  @NotNull(message = "主键不能为空")
                                                  @PathVariable("id") String id) {
        return AjaxResult.success(iDocLinkLogService.queryById(id));
    }

    /**
     * 新增文件关联记录
     */
    @ApiOperation("新增文件关联记录")
    @Log(title = "文件关联记录", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public AjaxResult<Void> add(@Validated(AddGroup.class) @RequestBody DocLinkLogBo bo) {
        return toAjax(iDocLinkLogService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改文件关联记录
     */
    @ApiOperation("修改文件关联记录")
    @Log(title = "文件关联记录", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public AjaxResult<Void> edit(@Validated(EditGroup.class) @RequestBody DocLinkLogBo bo) {
        try {
            return toAjax(iDocLinkLogService.updateByBo(bo) ? 1 : 0);
        }catch (Exception e){
            e.printStackTrace();
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 删除文件关联记录
     */
    @ApiOperation("删除文件关联记录")
    @Log(title = "文件关联记录" , businessType = BusinessType.DELETE)
    @GetMapping("/delete/{id}")
    public AjaxResult<Void> delete(@ApiParam("主键串")
                                   @NotEmpty(message = "主键不能为空")
                                   @PathVariable("id") String id) {
        return toAjax(iDocLinkLogService.deleteById(id));
    }

    /**
     * 删除文件关联记录
     */
    @ApiOperation("删除文件关联记录")
    @Log(title = "文件关联记录" , businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult<Void> remove(@ApiParam("主键串")
                                       @NotEmpty(message = "主键不能为空")
                                       @PathVariable String[] ids) {
        return toAjax(iDocLinkLogService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }



    @ApiOperation("初始化关联记录到记录台账")
    @Log(title = "初始化关联记录到记录台账" , businessType = BusinessType.UPDATE)
    @GetMapping("/init2Account")
    public AjaxResult init2Account2(String dataType,String docClass,
                                    String standardId,String versionId,
                                    String linkCode) {
        // 总数
        int count = 0;
        // 成功数量
        int successCount = 0;
        StringBuilder errorMsg = new StringBuilder(100);
        List<DocLinkLogVo> linkLogList = this.iDocLinkLogService.queryInit2Account(dataType,docClass,standardId,versionId,linkCode);
        count = linkLogList.size();
        for(DocLinkLogVo logVo : linkLogList) {
            // 查询关联日志
            QueryWrapper<DocVersionLink> query = new QueryWrapper<>();
            query.eq("link_id",logVo.getId());
            DocVersionLinkVo versionLinkObj = docVersionLinkService.getVoOne(query);
            // 获取当前文件版本
            Version currVersion = this.versionService.getById(versionLinkObj.getVersionId());
            if(currVersion == null) {
                return AjaxResult.error(currVersion.getId()+" "+ I18nUtils.getTitle(CommonI18nConstant.DOC_LINK_FILE_VERSION_NOT_EXIST));
            }
            // 获取当前文件台账
            Standard sourceStdd = this.standardService.getById(currVersion.getStandardId());
            if(sourceStdd == null) {
                return AjaxResult.error(currVersion.getId()+" "+I18nUtils.getTitle(CommonI18nConstant.DOC_LINK_FILE_LEDGER_NOT_EXIST));
            }
            DocLinkLog logObj = new DocLinkLog();
            BeanUtils.copyProperties(logVo,logObj);
            AjaxResult res = this.iDocLinkLogService.init2AccountByLinkLog(sourceStdd,currVersion,logObj);
            if(res.getCode() == 200) {
                successCount++;
            } else {
                // 显示错误日志
                errorMsg.append("DocLinkLog id="+logVo.getId()+",msg="+res.getMsg()).append("/n");
                log.error(res.getMsg());
            }
            log.info("deal process:"+successCount+"/"+count);
        }
        // 返回处理成功的文件版本个数/总数
        Map res = new HashMap();
        res.put("result",successCount+"/"+count);
        res.put("error",errorMsg.toString());
        System.out.println(errorMsg.toString());
        return AjaxResult.success(DateUtils.getTime(),res);
    }
}

package com.rzdata.process.controller;

import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.utils.poi.ExcelUtil;
import com.rzdata.process.domain.bo.DocDisStatisticsBo;
import com.rzdata.process.domain.bo.DocStatisticsBo;
import com.rzdata.process.domain.bo.DocStatisticsTrainingBo;
import com.rzdata.process.domain.bo.StandardBo;
import com.rzdata.process.domain.vo.*;
import com.rzdata.process.service.IDocStatisticsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/2/8 13:43
 * @Version 1.0
 * @Description 文件统计控制器
 */

@Validated
@Slf4j
@Api(value = "文件统计控制器", tags = {"文件统计管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/process/doc-statistics")
public class DocStatisticsController{

    @Resource
    IDocStatisticsService docStatisticsService;

    /**
     * 变更类型统计
     */
    @ApiOperation(value="变更类型统计")
    @PostMapping("/change-type")
    TableDataInfo<DocStatisticsChangeTypeVo> changeType(@RequestBody DocStatisticsBo docStatisticsBo){
        return docStatisticsService.changeType(docStatisticsBo);
    }
    @ApiOperation(value="变更类型统计")
    @PostMapping("/change-type-sum")
    AjaxResult<DocStatisticsChangeTypeVo> changeTypeSum(@RequestBody DocStatisticsBo docStatisticsBo){
        List<String> docClassList = docStatisticsBo.getDocClassList();
        if (docClassList!=null) {
            docClassList.add(docStatisticsBo.getDocClass());
            docStatisticsBo.setDocClass(null);
            docStatisticsBo.setDocClassList(docClassList);
        }
        return AjaxResult.success(docStatisticsService.changeTypeSum(docStatisticsBo));
    }

    /**
     * 导出文件处理统计
     */
    @PostMapping("/export-change-type")
    public void export(
        DocStatisticsBo docStatisticsBo,
        HttpServletResponse response
    ) {
        docStatisticsBo.setPageNum(1);
        docStatisticsBo.setPageSize(Integer.MAX_VALUE);
        List<DocStatisticsChangeTypeExcelVo> excelData = docStatisticsService.getExcelData(docStatisticsBo);
        ExcelUtil.exportExcel(excelData, "文件变更统计", DocStatisticsChangeTypeExcelVo.class, response);
    }

    /**
     * 变更要素统计
     */
    @ApiOperation(value="变更要素统计")
    @PostMapping("/change-factor")
    AjaxResult<List<DocStatisticsChangeFactorVo>> changeFactor(@RequestBody DocStatisticsBo docStatisticsBo){
        return docStatisticsService.changeFactor(docStatisticsBo);
    }


    /**
     * 文件培训统计
     */
    @PostMapping("/training")
    public AjaxResult<TableDataInfo<DocStatisticsTrainingVo>> training(@RequestBody DocStatisticsTrainingBo bo) {
        TableDataInfo<DocStatisticsTrainingVo> training = docStatisticsService.training(bo);
        return AjaxResult.success(training);
    }

    @PostMapping("/export-training")
    public void exportTraining(DocStatisticsTrainingBo bo, HttpServletResponse response) {
        bo.setPageNum(1);
        bo.setPageSize(Integer.MAX_VALUE);
        List<DocStatisticsTrainingExcelVo> excelData = docStatisticsService.exportExcelData(bo);
        ExcelUtil.exportExcel(excelData, "文件变更统计", DocStatisticsTrainingExcelVo.class, response);
    }
}

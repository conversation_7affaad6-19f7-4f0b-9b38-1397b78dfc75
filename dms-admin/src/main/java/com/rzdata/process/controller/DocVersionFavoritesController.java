package com.rzdata.process.controller;

import java.util.Arrays;

import com.rzdata.process.domain.bo.DocDistributeBo;
import com.rzdata.process.domain.vo.DocDistributeVo;
import com.rzdata.process.service.IDocVersionFavoritesService;
import lombok.RequiredArgsConstructor;
import javax.validation.constraints.*;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.rzdata.framework.annotation.Log;
import com.rzdata.framework.core.controller.BaseController;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.enums.BusinessType;
import com.rzdata.framework.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiOperation;

/**
 * 我的收藏Controller
 *
 * <AUTHOR>
 * @date 2023-01-03
 */
@Validated
@Api(value = "我的收藏控制器", tags = {"我的收藏管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/process/versionFavorites")
public class DocVersionFavoritesController extends BaseController {

    private final IDocVersionFavoritesService iDocVersionFavoritesService;

    @ApiOperation("查询我的收藏列表")
    @GetMapping("/page")
    public TableDataInfo<DocDistributeVo> page(DocDistributeBo bo) {
        return iDocVersionFavoritesService.queryPageFavorites(bo);
    }



    /**
     * 新增我的收藏
     */
    @ApiOperation("新增或者删除收藏")
    @PreAuthorize("@ss.hasPermi('system:versionFavorites:add')")
    @Log(title = "我的收藏", businessType = BusinessType.INSERT)
    @GetMapping("/saveOrRemove/{versionId}")
    public AjaxResult<Void> saveOrRemove(@ApiParam("主键串")
                                    @NotEmpty(message = "主键不能为空")
                                    @PathVariable String versionId) {
        return toAjax(iDocVersionFavoritesService.saveOrRemoveFavorites(versionId) ? 1 : 0);
    }

    /**
     * 删除我的收藏
     */
    @ApiOperation("删除我的收藏")
    @PreAuthorize("@ss.hasPermi('system:versionFavorites:remove')")
    @Log(title = "我的收藏" , businessType = BusinessType.DELETE)
    @GetMapping("/remove/{ids}")
    public AjaxResult<Void> remove(@ApiParam("主键串")
                                       @NotEmpty(message = "主键不能为空")
                                       @PathVariable String[] ids) {
        return toAjax(iDocVersionFavoritesService.deleteWithValidByIds(Arrays.asList(ids)) ? 1 : 0);
    }
}

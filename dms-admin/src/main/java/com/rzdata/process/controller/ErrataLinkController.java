package com.rzdata.process.controller;

import java.util.List;
import java.util.Arrays;

import lombok.RequiredArgsConstructor;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.rzdata.framework.annotation.RepeatSubmit;
import com.rzdata.framework.annotation.Log;
import com.rzdata.framework.core.controller.BaseController;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import com.rzdata.framework.core.validate.QueryGroup;
import com.rzdata.framework.enums.BusinessType;
import com.rzdata.framework.utils.poi.ExcelUtil;
import com.rzdata.process.domain.vo.ErrataLinkVo;
import com.rzdata.process.domain.bo.ErrataLinkBo;
import com.rzdata.process.service.IErrataLinkService;
import com.rzdata.framework.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiOperation;

/**
 * 文件关联勘误记录Controller
 *
 * <AUTHOR>
 * @date 2025-01-13
 */
@Validated
@Api(value = "文件关联勘误记录控制器", tags = {"文件关联勘误记录管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/process/errataLink")
public class ErrataLinkController extends BaseController {

    private final IErrataLinkService iErrataLinkService;

    /**
     * 查询文件关联勘误记录列表
     */
    @ApiOperation("查询文件关联勘误记录列表")
    @GetMapping("/list")
    public TableDataInfo<ErrataLinkVo> list(@Validated(QueryGroup.class) ErrataLinkBo bo) {
        return iErrataLinkService.queryPageList(bo);
    }

    /**
     * 导出文件关联勘误记录列表
     */
    @ApiOperation("导出文件关联勘误记录列表")
    @Log(title = "文件关联勘误记录", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public void export(@Validated ErrataLinkBo bo, HttpServletResponse response) {
        List<ErrataLinkVo> list = iErrataLinkService.queryList(bo);
        ExcelUtil.exportExcel(list, "文件关联勘误记录", ErrataLinkVo.class, response);
    }

    /**
     * 获取文件关联勘误记录详细信息
     */
    @ApiOperation("获取文件关联勘误记录详细信息")
    @GetMapping("/{id}")
    public AjaxResult<ErrataLinkVo> getInfo(@ApiParam("主键")
                                                  @NotNull(message = "主键不能为空")
                                                  @PathVariable("id") String id) {
        return AjaxResult.success(iErrataLinkService.queryById(id));
    }

    /**
     * 新增文件关联勘误记录
     */
    @ApiOperation("新增文件关联勘误记录")
    @Log(title = "文件关联勘误记录", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public AjaxResult<Void> add(@Validated(AddGroup.class) @RequestBody ErrataLinkBo bo) {
        return toAjax(iErrataLinkService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改文件关联勘误记录
     */
    @ApiOperation("修改文件关联勘误记录")
    @Log(title = "文件关联勘误记录", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public AjaxResult<Void> edit(@Validated(EditGroup.class) @RequestBody ErrataLinkBo bo) {
        return toAjax(iErrataLinkService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除文件关联勘误记录
     */
    @ApiOperation("删除文件关联勘误记录")
    @Log(title = "文件关联勘误记录" , businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult<Void> remove(@ApiParam("主键串")
                                       @NotEmpty(message = "主键不能为空")
                                       @PathVariable String[] ids) {
        return toAjax(iErrataLinkService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}

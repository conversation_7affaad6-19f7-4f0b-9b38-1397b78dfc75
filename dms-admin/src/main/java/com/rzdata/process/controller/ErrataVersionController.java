package com.rzdata.process.controller;

import java.util.List;
import java.util.Arrays;

import cn.hutool.core.bean.BeanUtil;
import com.rzdata.process.domain.dto.ErrataVersionDTO;
import lombok.RequiredArgsConstructor;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.rzdata.framework.annotation.RepeatSubmit;
import com.rzdata.framework.annotation.Log;
import com.rzdata.framework.core.controller.BaseController;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import com.rzdata.framework.core.validate.QueryGroup;
import com.rzdata.framework.enums.BusinessType;
import com.rzdata.framework.utils.poi.ExcelUtil;
import com.rzdata.process.domain.vo.ErrataVersionVo;
import com.rzdata.process.domain.bo.ErrataVersionBo;
import com.rzdata.process.service.IErrataVersionService;
import com.rzdata.framework.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiOperation;

/**
 * 勘误文件版本记录Controller
 *
 * <AUTHOR>
 * @date 2025-01-08
 */
@Validated
@Api(value = "勘误文件版本记录控制器", tags = {"勘误文件版本记录管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/process/errataVersion")
public class ErrataVersionController extends BaseController {

    private final IErrataVersionService iErrataVersionService;

    /**
     * 查询勘误文件版本记录列表
     */
    @ApiOperation("查询勘误文件版本记录列表")
    @GetMapping("/list")
    public TableDataInfo<ErrataVersionVo> list(@Validated(QueryGroup.class) ErrataVersionBo bo) {
        return iErrataVersionService.queryPageList(bo);
    }

    /**
     * 导出勘误文件版本记录列表
     */
    @ApiOperation("导出勘误文件版本记录列表")
    @Log(title = "勘误文件版本记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(@Validated ErrataVersionBo bo, HttpServletResponse response) {
        List<ErrataVersionVo> list = iErrataVersionService.queryList(bo);
        List<ErrataVersionDTO> exportList = BeanUtil.copyToList(list,ErrataVersionDTO.class);
        ExcelUtil.exportExcel(exportList, "勘误文件版本记录", ErrataVersionDTO.class, response);
    }

    /**
     * 获取勘误文件版本记录详细信息
     */
    @ApiOperation("获取勘误文件版本记录详细信息")
    @GetMapping("/{id}")
    public AjaxResult<ErrataVersionVo> getInfo(@ApiParam("主键")
                                                  @NotNull(message = "主键不能为空")
                                                  @PathVariable("id") String id) {
        return AjaxResult.success(iErrataVersionService.queryById(id));
    }

    /**
     * 获取勘误文件版本记录详细信息
     */
    @ApiOperation("获取勘误文件版本记录详细信息")
    @PostMapping("/prev")
    public AjaxResult<ErrataVersionVo> getPrevInfo(@RequestBody ErrataVersionBo bo) {
        return AjaxResult.success(iErrataVersionService.queryPrev(bo));
    }

    /**
     * 新增勘误文件版本记录
     */
    @ApiOperation("新增勘误文件版本记录")
    @Log(title = "勘误文件版本记录", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public AjaxResult<Void> add(@Validated(AddGroup.class) @RequestBody ErrataVersionBo bo) throws Exception {
        return toAjax(iErrataVersionService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改勘误文件版本记录
     */
    @ApiOperation("修改勘误文件版本记录")
    @Log(title = "勘误文件版本记录", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public AjaxResult<Void> edit(@Validated(EditGroup.class) @RequestBody ErrataVersionBo bo) {
        return toAjax(iErrataVersionService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除勘误文件版本记录
     */
    @ApiOperation("删除勘误文件版本记录")
    @Log(title = "勘误文件版本记录" , businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult<Void> remove(@ApiParam("主键串")
                                       @NotEmpty(message = "主键不能为空")
                                       @PathVariable String[] ids) {
        return toAjax(iErrataVersionService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}

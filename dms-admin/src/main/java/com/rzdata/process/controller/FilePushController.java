package com.rzdata.process.controller;

import java.util.List;
import java.util.Arrays;

import lombok.RequiredArgsConstructor;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.rzdata.framework.annotation.RepeatSubmit;
import com.rzdata.framework.annotation.Log;
import com.rzdata.framework.core.controller.BaseController;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import com.rzdata.framework.core.validate.QueryGroup;
import com.rzdata.framework.enums.BusinessType;
import com.rzdata.framework.utils.poi.ExcelUtil;
import com.rzdata.process.domain.vo.FilePushVo;
import com.rzdata.process.domain.bo.FilePushBo;
import com.rzdata.process.service.IFilePushService;
import com.rzdata.framework.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiOperation;

/**
 * 文件推送Controller
 *
 * <AUTHOR>
 * @date 2025-08-05
 */
@Validated
@Api(value = "文件推送控制器", tags = {"文件推送管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/process/filePush")
public class FilePushController extends BaseController {

    private final IFilePushService iFilePushService;

    /**
     * 查询文件推送列表
     */
    @ApiOperation("查询文件推送列表")
    @GetMapping("/list")
    public TableDataInfo<FilePushVo> list(@Validated(QueryGroup.class) FilePushBo bo) {
        return iFilePushService.queryPageList(bo);
    }

    /**
     * 导出文件推送列表
     */
    @ApiOperation("导出文件推送列表")
    @Log(title = "文件推送", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public void export(@Validated FilePushBo bo, HttpServletResponse response) {
        List<FilePushVo> list = iFilePushService.queryList(bo);
        ExcelUtil.exportExcel(list, "文件推送", FilePushVo.class, response);
    }

    /**
     * 获取文件推送详细信息
     */
    @ApiOperation("获取文件推送详细信息")
    @GetMapping("/{id}")
    public AjaxResult<FilePushVo> getInfo(@ApiParam("主键")
                                                  @NotNull(message = "主键不能为空")
                                                  @PathVariable("id") String id) {
        return AjaxResult.success(iFilePushService.queryById(id));
    }

    /**
     * 新增文件推送
     */
    @ApiOperation("新增文件推送")
    @Log(title = "文件推送", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public AjaxResult<Void> add(@Validated(AddGroup.class) @RequestBody FilePushBo bo) {
        return toAjax(iFilePushService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改文件推送
     */
    @ApiOperation("修改文件推送")
    @Log(title = "文件推送", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public AjaxResult<Void> edit(@Validated(EditGroup.class) @RequestBody FilePushBo bo) {
        return toAjax(iFilePushService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除文件推送
     */
    @ApiOperation("删除文件推送")
    @Log(title = "文件推送" , businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult<Void> remove(@ApiParam("主键串")
                                       @NotEmpty(message = "主键不能为空")
                                       @PathVariable String[] ids) {
        return toAjax(iFilePushService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}

package com.rzdata.process.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.rzdata.framework.annotation.Log;
import com.rzdata.framework.constant.CommonI18nConstant;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.core.domain.model.LoginUser;
import com.rzdata.framework.enums.BusinessType;
import com.rzdata.framework.utils.DateUtils;
import com.rzdata.framework.utils.I18nUtils;
import com.rzdata.framework.utils.SecurityUtils;
import com.rzdata.plugins.watermark.PdfWaterMarkService;
import com.rzdata.process.domain.ModifyApply;
import com.rzdata.process.domain.vo.BasicFileVo;
import com.rzdata.process.service.FileSignatureService;
import com.rzdata.process.service.IBasicFileService;
import com.rzdata.process.service.IModifyApplyService;
import com.rzdata.process.service.IStoreFileService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/8/11
 * @Version 1.0
 * @Description PDF文件增加签章水印控制器
 */
@Validated
@Slf4j
@Api(value = "PDF文件增加签章水印控制器", tags = {"PDF文件增加签章水印控制器"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/process/file-signature")
public class FileSignatureController {


    @Autowired
    FileSignatureService fileSignatureService;

    @Autowired
    PdfWaterMarkService pdfWaterMarkService;

    @Autowired
    IModifyApplyService modifyApplyService;

    /**
     * 签章生效和执行发布
     */
    @ApiOperation("签章生效和执行发布")
    @GetMapping("/signEffective")
    @Log(title = "签章生效" , businessType = BusinessType.INSERT)
    public AjaxResult<String> signEffective(String applyId,String type) {
        return AjaxResult.success(I18nUtils.getTitle(CommonI18nConstant.RESULT_AJAX_SUCCESS),fileSignatureService.signEffective(applyId,type));
    }

    /**
     * 签章生效和执行发布
     */
    @ApiOperation("签章生效和执行发布")
    @GetMapping("/signEffective/batch")
    @Log(title = "签章生效" , businessType = BusinessType.INSERT)
    public AjaxResult<List<ModifyApply>> signEffectiveBatch(String batchId, String type) {
        List<ModifyApply> list = modifyApplyService.list(new LambdaQueryWrapper<ModifyApply>().eq(ModifyApply::getBatchId,batchId).select(ModifyApply::getId));
        for (ModifyApply ma: list) {
            ma.setEncryptFileId(fileSignatureService.signEffective(ma.getId(),type));
        }
        return AjaxResult.success(list);
    }

    /**
     * 封面合稿生成
     */
    @ApiOperation("封面合稿生成")
    @GetMapping("/coverEffective")
    @Log(title = "封面合稿生成" , businessType = BusinessType.INSERT)
    public AjaxResult<String> coverEffective(String applyId,String type) {
        return AjaxResult.success(I18nUtils.getTitle(CommonI18nConstant.RESULT_AJAX_SUCCESS),fileSignatureService.coverEffective(applyId,type));
    }

    /**
     * 封面合稿生成
     */
    @ApiOperation("封面合稿生成")
    @GetMapping("/coverEffective/batch")
    @Log(title = "封面合稿生成" , businessType = BusinessType.INSERT)
    public AjaxResult<List<ModifyApply>> coverEffectiveBatch(String batchId,String type) {
        List<ModifyApply> list = modifyApplyService.list(new LambdaQueryWrapper<ModifyApply>().eq(ModifyApply::getBatchId,batchId).select(ModifyApply::getId));
        for (ModifyApply ma: list) {
            ma.setEncryptFileId(fileSignatureService.coverEffective(ma.getId(),type));
        }
        return AjaxResult.success(list);
    }
    /**
     * 封面合稿下载
     */
    @ApiOperation("封面合稿下载")
    @GetMapping("/cover/download")
    @Log(title = "封面合稿下载" , businessType = BusinessType.INSERT)
    public void coverDownload(String applyId,HttpServletResponse response) {
        fileSignatureService.coverDownload(applyId,response);
    }

    /**
     * 签章生效分发（用于文件分发-生成文件）
     */
    @ApiOperation("签章生效分发")
    @GetMapping("/signEffectiveDis")
    @Log(title = "签章生效分发" , businessType = BusinessType.INSERT)
    public AjaxResult signEffectiveDis(String docDistributeId,String distributeChar) {
        try {
            return fileSignatureService.signEffectiveDis(docDistributeId,distributeChar);
        } catch(Exception e) {
            log.error("签章生效分发异常",e);
            return AjaxResult.error(I18nUtils.getTitle(CommonI18nConstant.FILE_SIGNATURE_VALIDATION_ERR),e.getMessage());
        }
    }

    /**
     * 签章生效分发（用于文件分发-生成文件）
     */
    @ApiOperation("签章生效分发")
    @GetMapping("/signEffectiveDis/versionId")
    @Log(title = "签章生效分发" , businessType = BusinessType.INSERT)
    public AjaxResult signEffectiveDisByVersionId(String versionId) {
        try {
            return fileSignatureService.signEffectiveDisByVersionId(versionId);
        } catch(Exception e) {
            log.error("签章生效分发异常",e);
            return AjaxResult.error(I18nUtils.getTitle(CommonI18nConstant.FILE_SIGNATURE_VALIDATION_ERR),e.getMessage());
        }
    }

    @ApiOperation("预览PDF文件增加水印")
    @GetMapping("/previewPdfWatermark")
    public void previewPdfWatermark(String pdfFileId, HttpServletResponse response) throws Exception {
        LoginUser userObj = SecurityUtils.getLoginUser();
        String strDate = DateUtils.format(new Date(),"yyyy-MM-dd HH:mm");
        String text = strDate+"\n"+userObj.getUsername()+"\n"+userObj.getDeptName()+"\n"+userObj.getNickName();
        this.pdfWaterMarkService.addPreviewWaterMark(pdfFileId,text,response);
    }

}

package com.rzdata.process.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.json.JSONUtil;
import cn.hutool.system.SystemUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.read.metadata.ReadSheet;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.blueland.bpmclient.model.ProcessInstanceModel;
import com.rzdata.framework.annotation.Log;
import com.rzdata.framework.annotation.RepeatSubmit;
import com.rzdata.framework.constant.CommonI18nConstant;
import com.rzdata.framework.constant.DictConstants;
import com.rzdata.framework.constant.NumberConstants;
import com.rzdata.framework.core.controller.BaseController;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.core.domain.entity.SysDictData;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import com.rzdata.framework.enums.BusinessType;
import com.rzdata.framework.utils.I18nUtils;
import com.rzdata.framework.utils.JsonUtils;
import com.rzdata.framework.utils.StringUtils;
import com.rzdata.framework.utils.poi.ExcelUtil;
import com.rzdata.process.domain.BasicFile;
import com.rzdata.process.domain.ModifyApply;
import com.rzdata.process.domain.ModifyApplyLink;
import com.rzdata.process.domain.bo.ModifyApplyBatchBo;
import com.rzdata.process.domain.bo.ModifyApplyBo;
import com.rzdata.process.domain.dto.ConfigureDTO;
import com.rzdata.process.domain.vo.*;
import com.rzdata.process.enums.LinkClassEnum;
import com.rzdata.process.enums.LinkTypeEnum;
import com.rzdata.process.listener.ExcelListener;
import com.rzdata.process.listener.ibo.ModifyApplyIBo;
import com.rzdata.process.listener.ivo.ImportIVo;
import com.rzdata.process.service.*;
import com.rzdata.setting.domain.DocClass;
import com.rzdata.process.domain.dto.CreateNewNoDTO;
import com.rzdata.setting.service.IDocClassService;
import com.rzdata.system.service.ISysDictTypeService;
import com.rzdata.system.service.WorkflowService;
import io.swagger.annotations.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.BufferedInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.rmi.ServerException;
import java.util.*;
import java.util.stream.Collectors;
import com.rzdata.process.utils.OfficeFileUtil;

/**
 * 文件变更操作申请Controller
 *
 * <AUTHOR>
 * @date 2021-12-31
 */
@Validated
@Slf4j
@Api(value = "文件变更操作申请控制器", tags = {"文件变更操作申请管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/process/modifyApply")
public class ModifyApplyController extends BaseController {

    private final IModifyApplyService iModifyApplyService;

    private final IDocClassService iDocClassService;

    private final WorkflowService workflowService;

    private final IBasicFilePdfService basicFilePdfService;

    private final IModifyApplyLinkService iModifyApplyLinkService;

    private final ISysDictTypeService dictTypeService;

    private final IBasicFileService iBasicFileService;

    private final IBasicFileService basicFileService;

    private final IModifyApplyDistributeService modifyApplyDistributeService;

    private final IStoreFileService iStoreFileService;

    /**
     * 查询文件变更操作申请列表
     */
    @ApiOperation("查询文件变更操作申请列表")
    @GetMapping("/list")
    public TableDataInfo<ModifyApplyVo> list(ModifyApplyBo bo) {
        return iModifyApplyService.queryPageList(bo);
    }

    /**
     * 导出文件变更操作申请列表
     */
    @ApiOperation("导出文件变更操作申请列表")
    @Log(title = "文件变更操作申请", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public void export(@Validated ModifyApplyBo bo, HttpServletResponse response) {
        List<ModifyApplyVo> list = iModifyApplyService.queryList(bo);
        ExcelUtil.exportExcel(list, "文件变更操作申请", ModifyApplyVo.class, response);
    }

    /**
     * 校验文件是否存在批注，如果存在批注则返回批注状态以及涉及的文件
     */
    @ApiOperation("校验文件是否存在批注")
    @GetMapping("/check/annotation")
    public AjaxResult checkAnnotation(@Validated ModifyApplyBo bo) {
        List<Map<String,Object>> resultData = new ArrayList<Map<String,Object>>();
        if(StringUtils.isEmpty(bo.getBatchId()) && StringUtils.isEmpty(bo.getId())){
            return AjaxResult.error("参数异常");
        }
        List<String> ids = new ArrayList<String>();
        Map<String,Object> result = new HashMap<String,Object>();
        if(StringUtils.isNotEmpty(bo.getId())){
            ids.add(bo.getId());
        }
        if(StringUtils.isNotEmpty(bo.getBatchId())){
           List<ModifyApply> list = iModifyApplyService.list(new QueryWrapper<ModifyApply>().lambda().eq(ModifyApply::getBatchId,bo.getBatchId()));
           //获取id集合
            List<String> applyIds = list.stream()
                    .map(ModifyApply::getId)
                    .collect(Collectors.toList());
            ids.addAll(applyIds);
        }
        if(ids.size()==0){
            return AjaxResult.success();
        }

        List<ModifyApplyLink> modifyApplyLinkList= iModifyApplyLinkService.list(new QueryWrapper<ModifyApplyLink>().lambda()
                .in(ModifyApplyLink::getApplyId,ids).in(ModifyApplyLink::getLinkType,LinkTypeEnum.DOC.name(),LinkTypeEnum.APPENDIX.name()));
        //循环ids获取流程信息
        for(ModifyApplyLink modifyApplyLink:modifyApplyLinkList){
          //校验原始文件是否存在批注，只校验doc、docx、xls、xlsx、ppt、pptx
          BasicFile basicFile = basicFileService.getById(modifyApplyLink.getFileId());
          String allowedSuffixes = "doc, docx, xls, xlsx, ppt, pptx";
          if (!allowedSuffixes.contains(basicFile.getFileType().toLowerCase())) {
              continue;
          }
          Map<String,Object>  map = new HashMap<String,Object>();
          boolean hasComment = false;
          //获取文件
            try (BufferedInputStream input = iStoreFileService.getInputStream(basicFile)){
                //校验文件是否存在批注
                switch (basicFile.getFileType()) {
                    case "docx":
                        hasComment = OfficeFileUtil.hasWordComments(input);
                        break;
                    case "doc":
                        hasComment = OfficeFileUtil.hasOldWordComments(input);
                        break;
                    case "xlsx":
                        hasComment = OfficeFileUtil.hasExcelComments(input);
                        break;
                    case "xls":
                        hasComment = OfficeFileUtil.hasOldExcelComments(input);
                        break;
                    case "ppt":
                        hasComment = OfficeFileUtil.hasOldPPTComments(input);
                        break;
                    case "pptx":
                        hasComment = OfficeFileUtil.hasPPTComments(input);
                        break;
                    default:
                        log.warn("不支持的文件类型: {}", basicFile.getFileType());
                        break;
                }

                if (hasComment) {
                    ModifyApply modifyApply = iModifyApplyService.getById(modifyApplyLink.getApplyId());
                    // 存在批注，进行业务处理
                    log.error("文件 {} 存在批注", basicFile.getFileName());
                    map.put("applyId",modifyApplyLink.getApplyId());
                    map.put("fileName",modifyApplyLink.getDocName());
                    map.put("docName",modifyApply.getDocName());
                    resultData.add(map);
                } else {
                    log.error("文件 {} 不存在批注", basicFile.getFileName());
                }
            } catch (Exception e) {
            log.error("读取文件批注失败: {}", e.getMessage());
            throw new RuntimeException(e);
            }
        }
        return AjaxResult.success(resultData);
    }

    /**
     * 导出文件变更列表
     */
    @ApiOperation("导出文件变更列表")
    @Log(title = "文件变更导出", businessType = BusinessType.EXPORT)
    @PostMapping("/exportForChange")
    public void exportForChange(@Validated ModifyApplyBo bo, HttpServletResponse response) {
        bo.setPageSize(Integer.MAX_VALUE);
        List<ModifyApplyVo> list = this.list(bo).getRows();
        List<ModifyApplyExportVo> listVo = BeanUtil.copyToList(list, ModifyApplyExportVo.class);

        Map<String, String> map = iDocClassService.list().stream().
                collect(Collectors.toMap(DocClass::getId, DocClass::getClassName));

        for (ModifyApplyExportVo modifyApplyExportVo : listVo) {
            if(!"done".equals(modifyApplyExportVo.getRecordStatus()) && !"cancel".equals(modifyApplyExportVo.getRecordStatus())){
                modifyApplyExportVo.setUpdateTime(null);
            }
            modifyApplyExportVo.setDocClass(map.get(modifyApplyExportVo.getDocClass()));
        }

        ExcelUtil.exportExcel(listVo, "文件变更", ModifyApplyExportVo.class, response);
    }

    /**
     * 获取文件变更操作申请详细信息
     */
    @ApiOperation("获取文件变更操作申请详细信息")
    @GetMapping("/{id}")
    public AjaxResult<ModifyApplyVo> getInfo(@ApiParam("主键")
                                                  @NotNull(message = "主键不能为空")
                                                  @PathVariable("id") String id) {
        return AjaxResult.success(iModifyApplyService.queryById(id));
    }

    @ApiOperation("获取文件变更操作申请详细信息")
    @GetMapping("/bpmnId/{bpmnId}")
    public AjaxResult<ModifyApplyVo> getInfoByBpmnId(@ApiParam("主键")
                                                        @NotNull(message = "主键不能为空")
                                                        @PathVariable("bpmnId") String bpmnId) {
        return AjaxResult.success(iModifyApplyService.queryByBpmnId(bpmnId));
    }

    @ApiOperation("获取文件变更操作申请详细信息")
    @GetMapping("/batch/bpmnId/{bpmnId}")
    public AjaxResult<List<ModifyApplyVo>> getBatchInfoByBpmnId(@ApiParam("主键")
                                                                @NotNull(message = "主键不能为空")
                                                                @PathVariable("bpmnId") String bpmnId) {
        return AjaxResult.success(iModifyApplyService.getBatchInfoByBpmnId(bpmnId));
    }

    /**
     * 生效分发
     */
    @ApiOperation("生效分发")
    @GetMapping("/distribute/{id}")
    public AjaxResult<Boolean> distribute(@ApiParam("主键")
                                             @NotNull(message = "主键不能为空")
                                             @PathVariable("id") String id) {
        return AjaxResult.success(iModifyApplyService.distribute(id));
    }

    /**
     * 新增文件变更操作申请
     */
    @ApiOperation("新增文件变更操作申请")
    @Log(title = "文件变更操作申请", businessType = BusinessType.INSERT, changeOperation = true)
    @RepeatSubmit()
    @PostMapping()
    public AjaxResult<ProcessInstanceModel> add(@Validated(AddGroup.class) @RequestBody ModifyApplyBo bo) {
        ProcessInstanceModel processInstanceModel = iModifyApplyService.insertByBo(bo);
        return processInstanceModel!=null ? AjaxResult.success("success", processInstanceModel) : AjaxResult.error("fail", processInstanceModel);
    }

    /**
     * 新增文件变更操作申请
     */
    @ApiOperation("新增文件批量变更操作申请")
    @Log(title = "文件变更批量操作申请", businessType = BusinessType.INSERT, changeOperation = true)
    @RepeatSubmit()
    @PostMapping("/batch")
    public AjaxResult<ModifyApplyResultVo> addBatch(@RequestBody ModifyApplyBatchBo bo) {
        return AjaxResult.success(iModifyApplyService.insertBatchByBo(bo));
    }

    /**
     * 修改文件变更操作申请
     */
    @ApiOperation("修改文件变更操作申请")
    @Log(title = "文件变更操作申请", businessType = BusinessType.UPDATE, changeOperation = true)
    @PutMapping()
    public AjaxResult<Void> edit(@Validated(EditGroup.class) @RequestBody ModifyApplyBo bo) {
        if (!BooleanUtil.isTrue(bo.getOnlyEdit())&&!BooleanUtil.isTrue(bo.getNotUpdateTitle())){
            String procTitle = workflowService.updateFlowTitle(bo.getDocName(),bo.getId());
            iModifyApplyService.saveWorkFlowLog(BeanUtil.toBean(bo,ModifyApply.class),procTitle,null);
        }
        return toAjax(iModifyApplyService.updateByBo(bo) ? 1 : 0);
    }


    @ApiOperation("修改文件变更操作申请")
    @Log(title = "只修改文件业务数据变更操作申请", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/onlyEdit")
    public AjaxResult<Void> OnlyEdit(@Validated(EditGroup.class) @RequestBody ModifyApply modifyApply) {
        if (StringUtils.isEmpty(modifyApply.getBatchId())&&StringUtils.isEmpty(modifyApply.getId())) {
            return toAjax(false);
        }
        return toAjax(iModifyApplyService.update(modifyApply,new LambdaUpdateWrapper<ModifyApply>()
                .eq(StringUtils.isNotEmpty(modifyApply.getBatchId()),ModifyApply::getBatchId,modifyApply.getBatchId())
                .eq(StringUtils.isNotEmpty(modifyApply.getId()),ModifyApply::getId,modifyApply.getId())));
    }

    /**
     * 修改文件变更操作申请
     */
    @ApiOperation("修改文件批量变更操作申请")
    @Log(title = "文件变更批量操作申请", businessType = BusinessType.UPDATE, changeOperation = true)
    @RepeatSubmit()
    @PutMapping("/batch")
    public AjaxResult<List<String>> editBatch(@Validated(EditGroup.class) @RequestBody ModifyApplyBatchBo bo) {
        if (!BooleanUtil.isTrue(bo.getOnlyEdit())){
            String procTitle = workflowService.updateFlowTitle(bo.getProcTitle(),bo.getBatchId());
            iModifyApplyService.saveWorkFlowLog(BeanUtil.copyToList(bo.getDataList(),ModifyApply.class),procTitle,null);
        }
        return AjaxResult.success(iModifyApplyService.updateBatchByBo(bo));
    }

    /**
     * 删除文件变更操作申请
     */
    @ApiOperation("删除文件变更操作申请")
    @Log(title = "文件变更操作申请" , businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult<Void> remove(@ApiParam("主键串")
                                       @NotEmpty(message = "主键不能为空")
                                       @PathVariable String[] ids) {
        return toAjax(iModifyApplyService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }

    /**
     * 删除文件变更操作申请
     */
    @ApiOperation("生成编号")
    @Log(title = "生成编号" , businessType = BusinessType.INSERT)
    @GetMapping("/getDocNoByApplyId/{id}")
    public AjaxResult<DocNoVo> getDocNoByApplyId(@PathVariable("id") String id) {
        try{
            return AjaxResult.success(I18nUtils.getTitle(CommonI18nConstant.FILE_BASE_SUCCESS),iModifyApplyService.getDocNoByApplyId(id));
        } catch(Exception ex){
            ex.printStackTrace();
            return AjaxResult.error(ex.getMessage(),null);
        }
    }

    @ApiOperation("生成编号")
    @Log(title = "生成编号" , businessType = BusinessType.INSERT)
    @PostMapping("/getDocNoListByApplyId")
    public AjaxResult<List<DocNoVo>> getDocNoListByApplyId(@RequestBody List<String> applyIdList) {
        List<DocNoVo> list = new ArrayList<>();
        for (String applyId: applyIdList) {
            try{
                list.add(iModifyApplyService.getDocNoByApplyId(applyId));
            } catch(Exception ex){
                ex.printStackTrace();
            }
        }
        return AjaxResult.success(I18nUtils.getTitle(CommonI18nConstant.FILE_BASE_SUCCESS),list);
    }

    @ApiOperation("判断文件编号是否存在，true:存在，false：不存在")
    @PostMapping("/check/docId")
    public AjaxResult<Boolean> checkDocIdExist(@RequestBody CreateNewNoDTO bo) {
        return AjaxResult.success(I18nUtils.getTitle(CommonI18nConstant.FILE_BASE_SUCCESS),iModifyApplyService.checkDocIdExist(bo));
    }

    @ApiOperation("更新文件编号的流水号")
    @PostMapping("/doc_serialnumber")
    public AjaxResult<Boolean> updateDocSerialNumber(@RequestBody CreateNewNoDTO bo) {
        try{
            return AjaxResult.success(I18nUtils.getTitle(CommonI18nConstant.FILE_BASE_SUCCESS), iModifyApplyService.updateDocSerialNumber(bo));
        } catch(Exception ex){
            return new AjaxResult(500,ex.getMessage());
        }
    }

    @ApiOperation("更新主文件编号")
    @PostMapping("/update/docId")
    public AjaxResult<Boolean> updateDocId(@RequestBody CreateNewNoDTO bo) {
        try{
            return AjaxResult.success(I18nUtils.getTitle(CommonI18nConstant.RESULT_AJAX_SUCCESS),iModifyApplyService.updateDocId(bo));
        }catch (Exception e){
            e.printStackTrace();
            return AjaxResult.error(e.getMessage(),false);
        }
    }

    @ApiOperation("更新主文件编号")
    @PostMapping("/update/docId/list")
    public AjaxResult<Void> updateDocIdList(@RequestBody List<CreateNewNoDTO> bo) {
        try{
            iModifyApplyService.updateDocIdList(bo);
            return AjaxResult.success(I18nUtils.getTitle(CommonI18nConstant.RESULT_AJAX_SUCCESS));
        }catch (Exception e){
            e.printStackTrace();
            return AjaxResult.error(e.getMessage());
        }
    }

    @ApiOperation("更新记录文件编号")
    @PostMapping("/update/record/docId")
    public AjaxResult<Boolean> updateRecordDocId(@RequestBody List<CreateNewNoDTO> dtoList) {
        try{
            return AjaxResult.success(I18nUtils.getTitle(CommonI18nConstant.RESULT_AJAX_SUCCESS),iModifyApplyService.updateRecordDocId(dtoList));
        }catch (Exception e){
            e.printStackTrace();
            return AjaxResult.error(e.getMessage(),false);
        }
    }

    @ApiOperation("更新记录文件编号的流水号")
    @PostMapping("/record_serialnumber")
    public AjaxResult<Boolean> updateRecordSerialNumber(@RequestBody List<CreateNewNoDTO> listBo) {
        try{
            return AjaxResult.success(I18nUtils.getTitle(CommonI18nConstant.FILE_BASE_SUCCESS),
                    iModifyApplyService.updateRecordSerialNumber(listBo));
        }
        catch(Exception ex){
            return new AjaxResult(500,ex.getMessage());
        }
    }

    /**
     * 生成关联记录文件的文件编号
     */
    @ApiOperation("生成记录文件编号")
    @Log(title = "生成记录文件编号" , businessType = BusinessType.INSERT)
    @PostMapping("/getRecordDocNoByLinkId")
    public AjaxResult<List<DocNoVo>>
    getRecordDocNoByLinkId(@RequestBody Map<String,String> requestMap) {
        try{
            return AjaxResult.success(I18nUtils.getTitle(CommonI18nConstant.FILE_BASE_SUCCESS),iModifyApplyService.getRecordDocNoByLinkId(requestMap));
        } catch(Exception ex){
            ex.printStackTrace();
            return AjaxResult.error(ex.getMessage(),null);
        }
    }

    /**
     * 修改文件变更操作申请
     */
    @ApiOperation("修改文件变更操作申请")
    @Log(title = "文件变更操作申请", businessType = BusinessType.UPDATE, changeOperation = true)
    @PostMapping("updateById")
    public AjaxResult<Void> updateById(@RequestBody ModifyApply bo) {
        return toAjax(iModifyApplyService.updateById(bo));
    }


    /**
     * 历史文件初始化上传文件
     */
    @ApiOperation("批量新增上传文件夹")
    @ApiImplicitParams({@ApiImplicitParam(name = "file", value = "文件", dataType = "java.io.File", required = true),})
    @Log(title = "批量新增上传文件夹", businessType = BusinessType.INSERT)
    @PostMapping(value = "/batch/upload")
    public AjaxResult<ModifyApplyLink> batchUploading(@RequestPart("file") MultipartFile file, @RequestParam("formData") String formData, @RequestParam("linkType") String linkType) throws Exception{
        try {
            if(file.isEmpty()){
                return AjaxResult.error(I18nUtils.getTitle(CommonI18nConstant.FILE_STANDARD_FILE_NOT_NULL),null);
            }
            BasicFile basicFile = basicFilePdfService.uploading(file);
            ModifyApplyLink modifyApplyLink = JSONUtil.toBean(formData,ModifyApplyLink.class);
            modifyApplyLink.setIsDeleted(NumberConstants.ZERO);
            modifyApplyLink.setStatus(NumberConstants.ONE);
            modifyApplyLink.setFileId(basicFile.getId());
            if (linkType.equalsIgnoreCase(LinkTypeEnum.DOC.name())) {
                ModifyApplyBo bo = JSONUtil.toBean(formData,ModifyApplyBo.class);
                ModifyApply modifyApply = BeanUtil.toBean(bo, ModifyApply.class);
                modifyApply.setFileId(basicFile.getId());
                iModifyApplyService.save(modifyApply);
                modifyApplyDistributeService.updateModifyApplyDistribute(bo.getDistributeList(),modifyApply.getId());
                modifyApplyLink.setLinkType(LinkTypeEnum.DOC.name());
                modifyApplyLink.setLinkClass(LinkClassEnum.HOST.name());
                modifyApplyLink.setApplyId(modifyApply.getId());
            }else {
                modifyApplyLink.setLinkType(LinkTypeEnum.APPENDIX.name());
                modifyApplyLink.setLinkClass(LinkClassEnum.FILE.name());
            }
            iModifyApplyLinkService.save(modifyApplyLink);
            return  AjaxResult.success(modifyApplyLink);
        }catch (Exception e){
            return AjaxResult.error(e.getMessage(),null);
        }
    }

    /**
     * 导出标准文件列表
     */
    @ApiOperation("批量新增下载文件信息模版")
    @Log(title = "批量新增下载文件信息模版", businessType = BusinessType.EXPORT)
    @PostMapping("/batch/export")
    public void batchExport(@Validated ModifyApplyBo bo, HttpServletResponse response) throws IOException {
        String path = System.getProperty(SystemUtil.USER_DIR) + "/templates/lcwjxxmb.xlsx";
        // String path = "/dms-admin/templates/xtcshmb.xlsx";
        Map<String, List<?>> dataMap = new HashMap<>(3);
        //填充sheet1
        List<ModifyApplyVo> list = iModifyApplyService.queryList(bo);
        dataMap.put("0", list);
        ExcelUtil.exportExcel(dataMap, path,response);
    }

    /**
     * 导入文件修改数据
     */
    @ApiOperation("导入文件修改数据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "file", value = "文件", dataType = "java.io.File", required = true),
    })
    @Log(title = "标准文件和关联", businessType = BusinessType.UPDATE)
    @PostMapping(value = "/batch/import")
    public AjaxResult<ImportIVo> batchImport(@RequestPart("file") MultipartFile file) throws Exception{
        ImportIVo ivo = new ImportIVo();
        String path = iBasicFileService.uploading(file, "export_temp");

//        InputStream inputStream = file.getInputStream();
        InputStream inputStream = FileUtil.getInputStream(path);

        if(file.isEmpty() || !StringUtils.endsWith(file.getOriginalFilename(),"xlsx") || StringUtils.endsWith(file.getOriginalFilename(),"xls")){
            return AjaxResult.error(I18nUtils.getTitle(CommonI18nConstant.DOC_EXTERNAL_NOT_UP_TO_STANDARD),null);
        }
        //实例化实现了AnalysisEventListener接口的类
        ExcelListener listener = new ExcelListener();
        //传入参数
        ExcelReader excelReader = EasyExcel.read(inputStream,listener).build();
        //读取信息
        ReadSheet readSheet1 = EasyExcel.readSheet(0).head(ModifyApplyIBo.class).build();
        //读取数据
        excelReader.read(readSheet1);
        //获取数据
        List<Object> list = listener.getDatas();
        Map<String,List<SysDictData>> dictList = new HashMap<>();

        ConfigureDTO configure = new ConfigureDTO();
        configure.setDictList(dictList);
        for (int i = 1; i < list.size(); i++) {
            log.error("第"+i+"行");
            log.error(JsonUtils.toJsonString(list.get(i)));
            if(iModifyApplyService.updateByImport((ModifyApplyIBo) list.get(i),i+2,ivo,configure)) {
                ivo.initFileSum();
            }
        }
        IoUtil.close(inputStream);
        return AjaxResult.success(ivo);
    }

    /**
     * 主文件比对PDF
     */
    @ApiOperation("主文件比对PDF")
    @PostMapping("/handleCompareFile")
    public AjaxResult handleCompareFile(@RequestBody ModifyApplyBo modifyApplyBo){
        try {
            return AjaxResult.success("成功",iModifyApplyService.handleCompareFile(modifyApplyBo));
        }catch(Exception ex){
            ex.printStackTrace();
            return AjaxResult.error(ex.getMessage(),null);
        }
    }
}

package com.rzdata.process.controller;

import com.rzdata.framework.core.controller.BaseController;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.process.domain.vo.PrintFileDetailVo;
import com.rzdata.process.service.IPrintFileDetailService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 文件打印明细Controller
 *
 * <AUTHOR>
 * @date 2024-04-28
 */
@Validated
@Api(value = "文件打印明细控制器", tags = {"文件打印明细管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/process/printFileDetail")
public class PrintFileDetailController extends BaseController {

    private final IPrintFileDetailService iPrintFileDetailService;

    /**
     * 新增文件打印记录
     */
    @ApiOperation("新增文件打印明细")
    @PostMapping("/add")
    public void addPrintFileDetail(@RequestParam("pdfId") String pdfId) {
        iPrintFileDetailService.addPrintFileDetail(pdfId);
    }

    /**
     * 分发ID查询打印明细列表
     */
    @ApiOperation("新增文件打印明细")
    @GetMapping("/getListBydistributeId")
    public AjaxResult<List<PrintFileDetailVo>> getListBydistributeId(@RequestParam("distributeId") String distributeId) {
        return AjaxResult.success(iPrintFileDetailService.getListBydistributeId(distributeId));
    }
}

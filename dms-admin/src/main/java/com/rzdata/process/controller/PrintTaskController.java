package com.rzdata.process.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.protobuf.ServiceException;
import com.rzdata.framework.annotation.Log;
import com.rzdata.framework.annotation.RepeatSubmit;
import com.rzdata.framework.constant.Constants;
import com.rzdata.framework.core.controller.BaseController;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import com.rzdata.framework.core.validate.QueryGroup;
import com.rzdata.framework.enums.BusinessType;
import com.rzdata.framework.utils.StringUtils;
import com.rzdata.framework.utils.poi.ExcelUtil;
import com.rzdata.process.domain.DocDistribute;
import com.rzdata.process.domain.PrintTask;
import com.rzdata.process.domain.bo.PrintTaskBo;
import com.rzdata.process.domain.vo.DocDistributeVo;
import com.rzdata.process.domain.vo.PrintTaskVo;
import com.rzdata.process.service.IDocDistributeService;
import com.rzdata.process.service.IPrintTaskService;
import com.rzdata.system.domain.SysConfig;
import com.rzdata.system.mapper.SysConfigMapper;
import com.rzdata.system.service.ISysConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import java.util.Arrays;
import java.util.List;

/**
 * 打印任务Controller
 *
 * <AUTHOR>
 * @date 2024-01-18
 */
@Validated
@Api(value = "打印任务控制器", tags = {"打印任务管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/process/printTask")
public class PrintTaskController extends BaseController {

    private final IPrintTaskService iPrintTaskService;

    private final IDocDistributeService iDocDistributeService;

    private final SysConfigMapper sysConfigMapper;

    private static final String PRINT_RETURN_URL = "print_return_url";

    /**
     * 查询打印任务列表
     */
    @ApiOperation("查询打印任务列表")
    @PreAuthorize("@ss.hasPermi('process:printTask:list')")
    @GetMapping("/list")
    public TableDataInfo<PrintTaskVo> list(@Validated(QueryGroup.class) PrintTaskBo bo) {
        return iPrintTaskService.queryPageList(bo);
    }

    /**
     * 导出打印任务列表
     */
    @ApiOperation("导出打印任务列表")
    @Log(title = "打印任务", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(@Validated PrintTaskBo bo, HttpServletResponse response) {
        List<PrintTaskVo> list = iPrintTaskService.queryList(bo);
        ExcelUtil.exportExcel(list, "打印任务", PrintTaskVo.class, response);
    }

    /**
     * 新增打印任务
     */
    @ApiOperation("新增打印任务")
    @Log(title = "打印任务", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public AjaxResult<PrintTaskBo> add(@Validated(AddGroup.class) @RequestBody PrintTaskBo bo){
        iPrintTaskService.insertByBo(bo);
        SysConfig retConfig = sysConfigMapper.selectOne(new LambdaQueryWrapper<SysConfig>()
                .eq(SysConfig::getConfigKey, PRINT_RETURN_URL));
        if(StringUtils.isNotEmpty(retConfig)){
            bo.setPrintReturnUrl(retConfig.getConfigValue());
        }
        return AjaxResult.success(bo);
    }

    /**
     * 修改打印任务
     */
    @ApiOperation("修改打印任务")
    @Log(title = "打印任务", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public AjaxResult<Void> edit(@Validated(EditGroup.class) @RequestBody PrintTaskBo bo) {
        return toAjax(iPrintTaskService.updateByBo(bo) ? 1 : 0);
    }
    /**
     * 打印完成回调
     */
    @ApiOperation("打印完成回调")
    @Log(title = "打印任务", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/callback")
    public AjaxResult<Void> callback(@RequestBody PrintTaskBo bo) {
        String message=iPrintTaskService.updateByBo(bo) ? "操作成功" : "任务ID不存在，或已更新过了";
        PrintTask printTask = iPrintTaskService.getById(bo.getId());

        if(StringUtils.isNotEmpty(printTask.getDocDistributeId())){
            DocDistribute docDistribute = iDocDistributeService.getById(printTask.getDocDistributeId());
            if(docDistribute!=null && Constants.PATROL_TASK_STATUS_FAILURE.equals(printTask.getStatus())){
                //打印失败-1
                docDistribute.setPrintNums(docDistribute.getPrintNums()-1);
                iDocDistributeService.updateById(docDistribute);
            }
        }

        return AjaxResult.success(message);
    }

    /**
     * 删除打印任务
     */
    @ApiOperation("删除打印任务")
    @Log(title = "打印任务", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult<Void> remove(@ApiParam("主键串")
                                  @NotEmpty(message = "主键不能为空")
                                  @PathVariable String[] ids) {
        return toAjax(iPrintTaskService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }

    /**
     * 执行打印任务
     */
    @ApiOperation("执行打印任务")
    @Log(title = "打印任务", businessType = BusinessType.OTHER)
    @PostMapping("/execute/{id}")
    public AjaxResult<Void> execute(@ApiParam("打印任务ID")
                                   @NotEmpty(message = "打印任务ID不能为空")
                                   @PathVariable String id) {
        return toAjax(iPrintTaskService.executePrintTask(id) ? 1 : 0);
    }
    
} 
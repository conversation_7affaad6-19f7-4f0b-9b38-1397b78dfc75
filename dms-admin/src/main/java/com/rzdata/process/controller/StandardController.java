package com.rzdata.process.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.hutool.system.SystemUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.read.metadata.ReadSheet;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.rzdata.config.CustomConfig;
import com.rzdata.framework.annotation.Log;
import com.rzdata.framework.annotation.RepeatSubmit;
import com.rzdata.framework.constant.*;
import com.rzdata.framework.core.controller.BaseController;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.core.domain.entity.SysDept;
import com.rzdata.framework.core.domain.entity.SysDictData;
import com.rzdata.framework.core.domain.entity.SysUser;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import com.rzdata.framework.core.validate.QueryGroup;
import com.rzdata.framework.enums.BusinessType;
import com.rzdata.framework.exception.ServiceException;
import com.rzdata.framework.utils.DateUtils;
import com.rzdata.framework.utils.I18nUtils;
import com.rzdata.framework.utils.JsonUtils;
import com.rzdata.framework.utils.StringUtils;
import com.rzdata.framework.utils.poi.ExcelUtil;
import com.rzdata.process.domain.*;
import com.rzdata.process.domain.bo.StandardBo;
import com.rzdata.process.domain.dto.ConfigureDTO;
import com.rzdata.process.domain.vo.DocLinkLogVo;
import com.rzdata.process.domain.vo.StandardVo;
import com.rzdata.process.domain.vo.VersionVo;
import com.rzdata.process.enums.ApplyTypeEnum;
import com.rzdata.process.enums.LinkTypeEnum;
import com.rzdata.process.enums.RecordStatusEnum;
import com.rzdata.process.listener.ExcelListener;
import com.rzdata.process.listener.ibo.LinkLogIBo;
import com.rzdata.process.listener.ibo.StandardIBo;
import com.rzdata.process.listener.ivo.ImportIVo;
import com.rzdata.process.mapper.WorkflowApplyLogMapper;
import com.rzdata.process.service.*;
import com.rzdata.setting.domain.*;
import com.rzdata.setting.domain.bo.WatermarkParamBo;
import com.rzdata.setting.service.*;
import com.rzdata.system.service.*;
import io.swagger.annotations.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 标准文件Controller
 *
 * <AUTHOR>
 * @date 2021-12-30
 */
@Slf4j
@Validated
@Api(value = "标准文件控制器", tags = {"标准文件管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/process/standard")
public class StandardController extends BaseController {

    private final IStandardService iStandardService;

    private final IBasicFileService iBasicFileService;

    private final IVersionService iVersionService;
    private final IDocLinkLogService iDocLinkLogService;

    private final ISysDictTypeService dictTypeService;

    private final ISysUserService iSysUserService;

    private final ISysDeptService iSysDeptService;
    private final IProjectInfoService projectInfoService;

    @Autowired
    private IBasicFilePdfService basicFilePdfService ;

    @Autowired
    private IModifyApplyService iModifyApplyService;

    @Autowired
    private WorkflowApplyLogMapper workflowApplyLogMapper;

    private final IDocClassService iDocClassService;

    @Autowired
    private ICodeRuleLogService iCodeRuleLogService;

    @Autowired
    private IDocDistributeService iDocDistributeService;

    @Autowired
    private FileSignatureService fileSignatureService;

    @Autowired
    IModifyApplyService modifyApplyService;

    @Autowired
    IDocClassWatermarkSettingService docClassWatermarkSettingService;

    @Autowired
    private ISysConfigService configService;

    @Autowired
    CustomConfig customConfig;


    /**
     * 查询标准文件列表
     */
    @ApiOperation("查询标准文件列表")
    @GetMapping("/list")
    public TableDataInfo<StandardVo> list(@Validated(QueryGroup.class) StandardBo bo) {
        return iStandardService.selectPageList(bo);
    }

    /**
     * 查询文件台账列表 doc_standard
     */
    @ApiOperation("查询文件台账列表")
    @GetMapping("/listAccount")
    public TableDataInfo<StandardVo> listAccount(@Validated(QueryGroup.class) StandardBo bo) {
        return iStandardService.selectAccountList(bo);
    }

    /**
     * 历史文件初始化上传文件
     */
    @ApiOperation("历史文件初始化上传文件")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "file", value = "文件", dataType = "java.io.File", required = true),
    })
    @Log(title = "本地存储", businessType = BusinessType.INSERT)
    @PostMapping(value = "/upload")
    public AjaxResult<Boolean> uploading(@RequestPart("file") MultipartFile file, @RequestParam("docClass") String docClass, @RequestParam("dataType") String dataType, @RequestParam(value = "projectId",required = false) String projectId) throws Exception{
        try {
            if(file.isEmpty()){
                return AjaxResult.error(I18nUtils.getTitle(CommonI18nConstant.FILE_STANDARD_FILE_NOT_NULL),Boolean.FALSE);
            }
            StandardBo bo = new StandardBo();
            bo.setApplyTime(DateUtil.date());
            bo.setDocClass(docClass);
            String filename = file.getOriginalFilename();
            if(StringUtils.contains(filename,Constants.SLASH)){
                filename = filename.substring(filename.lastIndexOf(Constants.SLASH)+1);
                bo.setDocName(filename);
            }
            /*if(file.getSize() > 104857600){
                return AjaxResult.error(filename + I18nUtils.getTitle(CommonI18nConstant.SERVICE_STANDARD_FILE_TOO_LARGE),Boolean.FALSE);
            }*/
            if (filename.length() > 200) {
                return AjaxResult.error(filename + "文件名称不能超过200个字符",Boolean.FALSE);
            }
            // 文件名重复判断
//            LambdaQueryWrapper<Standard> queryWrapper = new LambdaQueryWrapper<>();
//            queryWrapper.eq(Standard::getDocName,StringUtils.trim(filename));
//            long count = iStandardService.count(queryWrapper);
//            if (count > 0) {
//                return AjaxResult.error(filename + I18nUtils.getTitle(CommonI18nConstant.SERVICE_STANDARD_FILE_NAME_REPEAT),Boolean.FALSE);
//            }
            BasicFile basicFile = basicFilePdfService.uploading(file);
            iStandardService.saveFileAndVersion(file.getOriginalFilename(), docClass, projectId, dataType, basicFile.getId(), basicFile.getFileName());
            return  AjaxResult.success(Boolean.TRUE);
        }catch (Exception e){
            return AjaxResult.error(e.getMessage(),null);
        }
    }


    /**
     * 替换文件
     */
    @ApiOperation("替换文件")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "file", value = "文件", dataType = "java.io.File", required = true),
    })
    @Log(title = "本地存储", businessType = BusinessType.INSERT)
    @PostMapping(value = "/replace/file")
    public AjaxResult<Void> replaceFile(@RequestPart("file") MultipartFile file,@RequestParam("id") String id,
                                        @RequestParam("versionId") String versionId,@RequestParam("docId")String docId){
        return iStandardService.replaceFile(file, id, versionId, docId);
    }
    /**
     * 导入文件修改数据
     */
    @ApiOperation("导入文件修改数据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "file", value = "文件", dataType = "java.io.File", required = true),
    })
    @Log(title = "标准文件和关联", businessType = BusinessType.UPDATE)
    @PostMapping(value = "/import")
    public AjaxResult<ImportIVo> importData(@RequestPart("file") MultipartFile file, @RequestPart("tenantId") String tenantId) throws Exception{
        if(StrUtil.isBlank(tenantId)){
            throw new ServiceException("tenantId is not null");
        }
        ImportIVo ivo = new ImportIVo();
        String path = iBasicFileService.uploading(file, "export_temp");

//        InputStream inputStream = file.getInputStream();
        InputStream inputStream = FileUtil.getInputStream(path);

        if(file.isEmpty() || !StringUtils.endsWith(file.getOriginalFilename(),"xlsx") || StringUtils.endsWith(file.getOriginalFilename(),"xls")){
            return AjaxResult.error(I18nUtils.getTitle(CommonI18nConstant.DOC_EXTERNAL_NOT_UP_TO_STANDARD),null);
        }
        //实例化实现了AnalysisEventListener接口的类
        ExcelListener listener = new ExcelListener();
        //传入参数
        ExcelReader excelReader = EasyExcel.read(inputStream,listener).build();
        //读取信息
        ReadSheet readSheet1 = EasyExcel.readSheet(0).head(StandardIBo.class).build();
        //读取数据
        excelReader.read(readSheet1);
        //获取数据
        List<Object> list = listener.getDatas();
        List<Object> list2 = new ArrayList<>();
        List<String> docNameList = new ArrayList<>();
        for (Object o : list) {
            JSONObject object = JSONUtil.parseObj(JSONUtil.toJsonStr(o));
//            if (ObjectUtil.isEmpty(object.getStr("docName")) ||object.getStr("docName").length() <= 200) {
                list2.add(o);
//            } else {
//                docNameList.add(object.getStr("docName"));
//            }
        }
        if(CollectionUtils.isEmpty(list2)){
            return AjaxResult.error(I18nUtils.getTitle(CommonI18nConstant.DOC_EXTERNAL_NOT_UP_TO_STANDARD),null);
        }

        List<SysUser> userList = iSysUserService.selectList();
        List<SysDept> deptList = iSysDeptService.list(new LambdaQueryWrapper<SysDept>()
                .eq(SysDept::getDelFlag, Constants.ZERO).eq(SysDept::getStatus,Constants.ZERO)
                .eq(SysDept::getDeptType, Constants.DEPT_TYPE)
                .eq(SysDept::getTenantId, tenantId)
                .orderByDesc(SysDept::getDeptLevel));
        List<DocClass> dcList = iDocClassService.list();
        List<SysDictData> formList = dictTypeService.selectDictDataByType(DictConstants.FORM_CONTROL);
        Map<String,List<SysDictData>> dictList = new HashMap<>();
        formList.forEach(item->{
            String[] keys = item.getRemark().split("@");
            if (GenConstants.HTML_SELECT.equals(keys[0])) {
                dictList.put(keys[1],dictTypeService.selectDictDataByType(keys[1]));
            }
        });
        ConfigureDTO configure = new ConfigureDTO();
        configure.setUserList(userList);
        configure.setDeptList(deptList);
        configure.setDcList(dcList);
        configure.setDictList(dictList);
        configure.setFormList(formList);
//        log.error(JsonUtils.toJsonString(configure.getDcList()));
        for (int i = 1; i < list2.size(); i++) {
            log.error("第"+i+"行");
            log.error(JsonUtils.toJsonString(list2.get(i)));
            if(iStandardService.updateByImport((StandardIBo) list2.get(i),i+2,ivo,configure)) {
                ivo.initFileSum();
            }
        }
        if (ObjectUtil.isNotEmpty(docNameList)) {
            return AjaxResult.error(docNameList.stream().distinct().collect(Collectors.joining(", ")) + "的文件名称超过200个字符",ivo);
        }
        return AjaxResult.success(ivo);
    }

    /**
     * 导入文件修改数据
     */
    @ApiOperation("导入文件修改数据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "file", value = "文件", dataType = "java.io.File", required = true),
    })
    @Log(title = "标准文件和关联", businessType = BusinessType.UPDATE)
    @PostMapping(value = "/import/link")
    public AjaxResult<ImportIVo> importLinkData(@RequestPart("file") MultipartFile file) throws Exception{
        //记录文件是一对多还是多对多
        String linkType = BooleanUtil.toBoolean(configService.selectConfigByKey("record.doc.type"))?LinkTypeEnum.NOTE.name():LinkTypeEnum.RECORD.name();
        ImportIVo ivo = new ImportIVo();
        InputStream inputStream = file.getInputStream();
        if(file.isEmpty() || !StringUtils.endsWith(file.getOriginalFilename(),"xlsx") || StringUtils.endsWith(file.getOriginalFilename(),"xls")){
            return AjaxResult.error("不符合要求，请重新上传",null);
        }
        //实例化实现了AnalysisEventListener接口的类
        ExcelListener listener = new ExcelListener();
        //传入参数
        ExcelReader excelReader = EasyExcel.read(inputStream,listener).build();

        //读取数据
        ReadSheet readSheet2 = EasyExcel.readSheet(1).head(LinkLogIBo.class).build();
        //获取数据
        excelReader.read(readSheet2);
        //读取数据
        List<Object> list = listener.getDatas();

        if(CollectionUtils.isEmpty(list)){
            return AjaxResult.error("不符合要求，请重新上传",null);
        }
        if (ObjectUtil.isNotEmpty(list)) {
            for (int i = 1; i < list.size(); i++) {
                if(ObjectUtil.isNotNull(list.get(i))) {
                    if (iStandardService.updateLinkLogByImport((LinkLogIBo) list.get(i), LinkTypeEnum.REF_DOC.name(), i + 2, ivo)) {
                        ivo.linkFileSum();
                    }
                }
            }
        }

        list.clear();
        //读取数据
        ReadSheet readSheet3 = EasyExcel.readSheet(2).head(LinkLogIBo.class).build();
        //获取数据
        excelReader.read(readSheet3);

        list = listener.getDatas();
        if(CollectionUtils.isEmpty(list)){
            return AjaxResult.error("不符合要求，请重新上传",null);
        }
        if(ObjectUtil.isNotEmpty(list)) {
            for (int i = 1; i < list.size(); i++) {
                if(ObjectUtil.isNotNull(list.get(i))) {
                    if (iStandardService.updateLinkLogByImport((LinkLogIBo) list.get(i),linkType, i + 2, ivo)) {
                        ivo.linkRecordSum();
                    }
                }
            }
        }
        return AjaxResult.success(ivo);
    }

    /**
     * 导出标准文件列表
     */
    @ApiOperation("导出标准文件列表")
    @Log(title = "标准文件", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(@Validated StandardBo bo, HttpServletResponse response) throws IOException {
        String path = System.getProperty(SystemUtil.USER_DIR) + "/templates/xtcshmb.xlsx";
        // String path = "/dms-admin/templates/xtcshmb.xlsx";
        List<SysDictData> standardStatusList = dictTypeService.selectDictDataByType(DictConstants.STANDARD_STATUS);
        List<SysDictData> changeTypeList = dictTypeService.selectDictDataByType(DictConstants.CHANGE_TYPE);
        List<SysUser> userList = iSysUserService.selectList();
        List<SysDept> deptList = iSysDeptService.selectList();
        Map<String, List<?>> dataMap = new HashMap<>(3);
        //填充sheet1
        List<StandardVo> list = iStandardService.selectList(bo);
        list.parallelStream().forEachOrdered(sv->{
            if (StringUtils.isNotBlank(sv.getStatus())) {
                standardStatusList.stream().filter(i -> i.getDictValue().equals(sv.getStatus())).findFirst().ifPresent(i->sv.setStatus(i.getDictLabel()));
            }
            if (StringUtils.isNotBlank(sv.getChangeType())) {
                changeTypeList.stream().filter(i -> i.getDictValue().equals(sv.getChangeType())).findFirst().ifPresent(i->sv.setChangeType(i.getDictLabel()));
            }
            sv.setDocClass(sv.getClassName());
            if (StringUtils.isBlank(sv.getChangeReason())) {
                sv.setChangeReason(Constants.VALUE_INIT);
            }
            if (StringUtils.isBlank(sv.getContent())) {
                sv.setContent(Constants.VALUE_INIT);
            }
            if (StringUtils.isBlank(sv.getChangeType())) {
                sv.setChangeType(Constants.VALUE_ADD);
            }
            if (StringUtils.isNotBlank(sv.getUserName())) {
                userList.stream().filter(i -> i.getUserName().equals(sv.getUserName())).findFirst().ifPresent(i->sv.setNickName(i.getNickName()));
            }
            if (ObjectUtil.isNotEmpty(sv.getDeptId())) {
                deptList.stream().filter(i -> i.getDeptId().equals(sv.getDeptId())).findFirst().ifPresent(i->sv.setDeptName(i.getDeptName()));
            }
        });
        dataMap.put("0", list);
        //填充sheet2
        bo.setLinkType(LinkTypeEnum.REF_DOC.name());
        List<String> ids = list.stream().map(StandardVo::getId).collect(Collectors.toList());
        List<DocLinkLogVo> list1 =iStandardService.exportLinkLog(bo,ids);
        dataMap.put("1", list1);
        //填充sheet3
        bo.setLinkType(LinkTypeEnum.RECORD.name());
        List<DocLinkLogVo> list2 =iStandardService.exportLinkLog(bo,ids);
        dataMap.put("2", list2);
        ExcelUtil.exportExcel(dataMap, path,response);
    }

    /**
     * 获取标准文件详细信息
     */
    @ApiOperation("获取标准文件详细信息")
    @GetMapping("/{id}")
    public AjaxResult<StandardVo> getInfo(@ApiParam("主键")
                                          @NotNull(message = "主键不能为空")
                                          @PathVariable("id") String id) {
        return AjaxResult.success(iStandardService.queryById(id));
    }

    /**
     * 获取标准文件全部的详细信息
     */
    @ApiOperation("获取标准文件全部的详细信息")
    @GetMapping("/getDetail")
    public AjaxResult<StandardVo> getDetail(@ApiParam("版本ID") String versionId, @ApiParam("是否修订") String flag) {
        StandardVo standardVo = iStandardService.queryDetail(versionId, flag);
        return AjaxResult.success(standardVo);
    }


    /**
     * 加入关联记录
     */
    @ApiOperation("加入关联记录")
    @Log(title = "关联记录", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/join")
    public AjaxResult<Void> join(@RequestBody StandardBo bo) {
        return toAjax(iStandardService.join(bo));
    }

    /**
     * 新增标准文件
     */
    @ApiOperation("新增标准文件")
    @Log(title = "标准文件", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public AjaxResult<Void> add(@Validated(AddGroup.class) @RequestBody StandardBo bo) {
        return toAjax(iStandardService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改标准文件和版本记录
     */
    @ApiOperation("修改标准文件和版本记录")
    @Log(title = "标准文件", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/update")
    public AjaxResult<Void> update(@Validated @RequestBody StandardBo bo) {
        StandardVo voById = iStandardService.queryInfoById(bo.getId());

        CodeRuleLog codeRuleLog = iCodeRuleLogService.docIdExist(bo.getDocId());
        if(ObjectUtil.isNotEmpty(codeRuleLog) && !StringUtils.equals(bo.getId(),codeRuleLog.getBusinessId())){
            return AjaxResult.error(I18nUtils.getTitle(CommonI18nConstant.FILE_STANDARD_FILE_ID_REPEAT));
        }

        if(ObjectUtil.isNotNull(voById)) {
            if (StringUtils.isNotBlank(voById.getVersionId()) && StringUtils.isNotBlank(voById.getDocId())) {
                QueryWrapper<WorkflowApplyLog> queryWrapper = new QueryWrapper<>();
                queryWrapper.lambda().eq(WorkflowApplyLog::getDocId, voById.getDocId());
                queryWrapper.lambda().eq(WorkflowApplyLog::getVersionId, voById.getVersionId());
                Long count = workflowApplyLogMapper.selectCount(queryWrapper);
                if (count > 0) {
                    return AjaxResult.error(I18nUtils.getTitle(CommonI18nConstant.FILE_STANDARD_NEW_DATA_NOT_EDIT));
                }
            }
        }
        // 文件名重复判断
        String filename = bo.getDocName();
        QueryWrapper<Standard> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(Standard::getDocName,StringUtils.trim(filename));
        Standard byId = iStandardService.getById(bo.getId());
        if(ObjectUtil.isNotNull(byId)) {
            if (StringUtils.equals(filename, byId.getDocName())) {
                queryWrapper.lambda().ne(Standard::getDocName, StringUtils.trim(byId.getDocName()));
            }
        }
        long count = iStandardService.count(queryWrapper);
        if (count > 0) {
            return AjaxResult.error(filename + I18nUtils.getTitle(CommonI18nConstant.SERVICE_STANDARD_FILE_NAME_REPEAT));
        }
        return toAjax(iStandardService.updateBoAndVersion(bo) ? 1 : 0);
    }

    /**
     * 修改标准文件
     */
    @ApiOperation("修改标准文件")
    @Log(title = "标准文件", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public AjaxResult<Void> edit(@Validated(EditGroup.class) @RequestBody StandardBo bo) {
        return toAjax(iStandardService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 批量生效
     */
    @ApiOperation("批量生效")
    @Log(title = "标准文件", businessType = BusinessType.UPDATE)
    @PostMapping("/update/status")
    public AjaxResult<Void> updateStatusByParam(@RequestBody StandardBo bo) {
        bo.setInitFile(Constants.ONE);
        List<VersionVo> versionList = iVersionService.versionAndStandardList(bo);
        if (versionList.size()>0) {
            log.error("versionList：{}",versionList.size());
            if (versionList.stream().anyMatch(versionVo -> StringUtils.isEmpty(versionVo.getDocId()))){
                throw new ServiceException("请完善所选数据，再生效");
            }
            List<String> noSignAccountTypes = ListUtil.toList(this.customConfig.getNoSignAddType().split(","));

            versionList.forEach(v->{
                try{
                    Version version = BeanUtil.toBean(v,Version.class);
                    String docClass = iStandardService.getDocClass(v.getStandardId());
                    if(!noSignAccountTypes.contains("import")){
                        fileSignatureService.refreshSignature(version,docClass,ApplyTypeEnum.ADD.name());
                    }
                    iDocDistributeService.refreshDistribute(BeanUtil.toBean(v,Version.class),docClass);
                }catch (Exception e){
                    log.error("执行批量生效异常",e);
                }
                // end forEach
            });
            // 假设以上代码执行成功，更新相关的文件台账为有效状态
            iStandardService.update(new LambdaUpdateWrapper<Standard>().set(Standard::getStatus,Constants.ONE).in(Standard::getId,versionList.stream().map(VersionVo::getStandardId).collect(Collectors.toList())));
        }
        return toAjax(true);
    }

    @ApiOperation("判断文件名称是否存在 true 存在 false 不存在")
    @GetMapping("/isExistByName")
    public AjaxResult<Boolean> isExistByName(StandardBo bo) {
        QueryWrapper<Version> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(Version:: getDocName, bo.getDocName());
        queryWrapper.lambda().eq(Version:: getStatus, Constants.ONE);
        //版本中是否存在 传了DocId 是修订
        queryWrapper.lambda().ne(StringUtils.isNotEmpty(bo.getDocId()),Version:: getDocId, bo.getDocId());
        if (iVersionService.count(queryWrapper) > 0 ) {
            return AjaxResult.success(true);
        }
        //流程中是否存在
        return AjaxResult.success(iModifyApplyService.count(new LambdaQueryWrapper<ModifyApply>()
                .eq(ModifyApply::getDocName, bo.getDocName())
                .eq(ModifyApply::getRecordStatus, RecordStatusEnum.DOING.getCode())
                .ne(StringUtils.isNotEmpty(bo.getApplyId()),ModifyApply::getId,bo.getApplyId())) > 0);
    }

    /**
     * 删除标准文件和版本记录
     */
    @ApiOperation("删除标准文件")
    @Log(title = "标准文件" , businessType = BusinessType.DELETE)
    @PostMapping("/delete")
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult<Void> delete(@RequestBody StandardBo bo) {
        if(StringUtils.isNotBlank(bo.getVersionId()) && StringUtils.isNotBlank(bo.getDocId())) {
            QueryWrapper<WorkflowApplyLog> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(WorkflowApplyLog::getDocId, bo.getDocId());
            queryWrapper.lambda().eq(WorkflowApplyLog::getVersionId, bo.getVersionId());
            Long count = workflowApplyLogMapper.selectCount(queryWrapper);
            if (count > 0) {
                return AjaxResult.error(I18nUtils.getTitle(CommonI18nConstant.SERVICE_STANDARD_NEW_DATA_NOT_DELETE));
            }
        }
        if(iStandardService.deleteByBo(bo)){
            return AjaxResult.success();
        }else {
            return AjaxResult.error(I18nUtils.getTitle(CommonI18nConstant.SERVICE_STANDARD_DELETE_FAIL));
        }
    }
    @GetMapping("/delete/{ids}")
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult<Void> deleteList(@ApiParam("主键串")
                                       @NotEmpty(message = "主键不能为空")
                                       @PathVariable String[] ids) {
        try {
            iStandardService.deleteList(Arrays.asList(ids));
            return AjaxResult.success();
        }catch (Exception e){
            e.printStackTrace();
            return AjaxResult.error(e.getMessage());
        }
    }
    /**
     * 删除标准文件
     */
    @ApiOperation("删除标准文件")
    @Log(title = "标准文件" , businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult<Void> remove(@ApiParam("主键串")
                                   @NotEmpty(message = "主键不能为空")
                                   @PathVariable String[] ids) {
        return toAjax(iStandardService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }


    /**
     * 关联记录文件
     */
    @ApiOperation("关联记录文件")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "file", value = "文件", dataType = "java.io.File", required = true),
    })
    @Log(title = "本地存储", businessType = BusinessType.INSERT)
    @PostMapping(value = "/record/file")
    public AjaxResult<Boolean> recordFile(@RequestPart("file") MultipartFile file,@RequestParam("id") String id,@RequestParam("versionId") String versionId) throws Exception{
        if(file.getSize() > 104857600){
            return AjaxResult.error(I18nUtils.getTitle(CommonI18nConstant.SERVICE_STANDARD_FILE_TOO_LARGE),false);
        }
        BasicFile basicFile = basicFilePdfService.uploading(file);
        StandardBo bo=new StandardBo();
        bo.setId(id);
        bo.setVersionId(versionId);
        bo.setFileId(basicFile.getId());
        bo.setDocName(basicFile.getFileName());
        try {
            iStandardService.addRecord(bo);
            return AjaxResult.success(I18nUtils.getTitle(CommonI18nConstant.RESULT_AJAX_SUCCESS),true);
        }catch (Exception e){
            e.printStackTrace();
            return AjaxResult.error(e.getMessage(),false);
        }
    }


}

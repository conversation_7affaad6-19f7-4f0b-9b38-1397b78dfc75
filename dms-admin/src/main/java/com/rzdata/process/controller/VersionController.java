package com.rzdata.process.controller;

import cn.hutool.core.bean.BeanUtil;
import com.rzdata.framework.annotation.Log;
import com.rzdata.framework.annotation.RepeatSubmit;
import com.rzdata.framework.constant.Constants;
import com.rzdata.framework.core.controller.BaseController;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import com.rzdata.framework.core.validate.QueryGroup;
import com.rzdata.framework.enums.BusinessType;
import com.rzdata.framework.utils.SecurityUtils;
import com.rzdata.framework.utils.poi.ExcelUtil;
import com.rzdata.process.domain.bo.VersionBo;
import com.rzdata.process.domain.vo.VersionExportInvalidVo;
import com.rzdata.process.domain.vo.VersionExportVo;
import com.rzdata.process.domain.vo.VersionVo;
import com.rzdata.process.service.IVersionService;
import com.rzdata.setting.service.IDocClassService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 文件版本记录Controller
 *
 * <AUTHOR>
 * @date 2021-12-30
 */
@Validated
@Api(value = "文件版本记录控制器", tags = {"文件版本记录管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/process/version")
public class VersionController extends BaseController {

    private final IVersionService iVersionService;

    private final IDocClassService iDocClassService;

    /**
     * 查询文件版本记录列表
     */
    @ApiOperation("查询文件版本记录列表")
    @PostMapping("/list")
    public TableDataInfo<VersionVo> list(@RequestBody VersionBo bo) {
        List<String> docClassList = bo.getDocClassList();
        if (docClassList!=null) {
            docClassList.add(bo.getDocClass());
            bo.setDocClass(null);
            bo.setDocClassList(docClassList);
        }
        return iVersionService.queryPageList(bo);
    }


    /**
     * 查询文件版本记录列表(原始版本)
     */
    @ApiOperation("查询文件版本记录列表(原始版本)")
    @GetMapping("/listVersion")
    public List<VersionVo> listVersion(@Validated(QueryGroup.class) VersionBo bo) {
        return iVersionService.queryList(bo);
    }


    /**
     * 查询文件版本记录列表
     */
    @ApiOperation("查询文件版本外部门列表")
    @PostMapping("/list/other/dept")
    public TableDataInfo<VersionVo> listOtherDept(@RequestBody VersionBo bo) {
        List<String> docClassList = bo.getDocClassList();
        if (docClassList!=null) {
            docClassList.add(bo.getDocClass());
            bo.setDocClass(null);
            bo.setDocClassList(docClassList);
        }
        // 对应的二级部门
        bo.setDeptId(SecurityUtils.getLoginUser().getDeptId());
        bo.setUserName(SecurityUtils.getUsername());
        return iVersionService.queryPageOtherDeptList(bo);
    }


    /**
     * 体系文件台账-查询文件版本记录列表
     */
    @ApiOperation("体系文件台账-查询文件版本记录列表")
    @GetMapping("/listStdd")
    public TableDataInfo<VersionVo> listStdd(@Validated(QueryGroup.class) VersionBo bo) {
        //记录文件查询
        if("STDD-R".equals(bo.getDocClass())) {
            return iVersionService.selectRecordFileCompany(bo);
        }else {
            bo.setDataType("stdd");
            return iVersionService.queryPageList(bo);
        }
    }

    /**
     * 项目文件台账-查询文件版本记录列表
     */
    @ApiOperation("项目文件台账-查询文件版本记录列表")
    @GetMapping("/listProject")
    public TableDataInfo<VersionVo> listProject(@Validated(QueryGroup.class) VersionBo bo) {
        //记录文件查询
        if("PROJECT-R".equals(bo.getDocClass())) {
            return iVersionService.selectRecordFileCompany(bo);
        }else {
            bo.setDataType("project");
            return iVersionService.queryPageList(bo);
        }
    }

    /**
     * 获取版本详情（包含关联信息）
     */
    @ApiOperation("获取版本详情")
    @GetMapping("/detail")
    public AjaxResult<VersionVo> getDetail(@RequestParam String docId, @RequestParam String versionValue) {
        VersionVo versionDetail = iVersionService.getDetail(docId, versionValue);
        return AjaxResult.success(versionDetail);
    }

    /**
     * 查询记录文件列表
     */
    @ApiOperation("查询记录文件列表")
    @GetMapping("/page/record")
    public TableDataInfo<VersionVo> selectRecordFile(@Validated(QueryGroup.class) VersionBo bo) {
        return iVersionService.selectRecordFile(bo);
    }


    /**
     * 校验版本、申请单中，文件分类+物料编码+物料描述是否唯一
     */
    @ApiOperation("校验版本、申请单中，文件分类+物料编码+物料描述是否唯一")
    @GetMapping("/validateUniqueness")
    public Boolean validateUniqueness(VersionBo bo) {
        return iVersionService.validateUniqueness(bo);
    }

    /**
     * 查询部门文件列表
     */
    @ApiOperation("查询部门文件列表")
    @GetMapping("/page/dept")
    public TableDataInfo<VersionVo> selectDeptFile(@Validated(QueryGroup.class) VersionBo bo) {
        return iVersionService.selectDeptFile(bo);
    }

    /**
     * 导出文件版本记录列表
     */
    @ApiOperation("导出公司文件数据")
    @Log(title = "导出公司文件数据", businessType = BusinessType.EXPORT)
    @PostMapping("/exportForList")
    public void exportForList(@Validated VersionBo bo, HttpServletResponse response) {
        bo.setPageSize(Integer.MAX_VALUE);
        iVersionService.exportForList(bo, response, bo.getExportName(), Constants.VERSION_QUERY_LIST);
    }


    /**
     * 导出外来文件
     */
    @ApiOperation("导出外来文件")
    @Log(title = "导出外来文件", businessType = BusinessType.EXPORT)
    @PostMapping("/export/foreign")
    public void exportForeign(@Validated VersionBo bo, HttpServletResponse response) {
        bo.setPageSize(Integer.MAX_VALUE);
        iVersionService.exportForList(bo, response, bo.getExportName(), Constants.VERSION_QUERY_FOREIGN_LIST);
    }



    /**
     * 导出文件版本记录列表
     */
    @ApiOperation("导出失效公司文件")
    @Log(title = "导出失效公司文件", businessType = BusinessType.EXPORT)
    @PostMapping("/export/invalid/list")
    public void exportInvalidList(@Validated VersionBo bo, HttpServletResponse response) {
        bo.setPageSize(Integer.MAX_VALUE);
        List<VersionExportVo> listVo = BeanUtil.copyToList(list(bo).getRows(),VersionExportVo.class);
        listVo = iVersionService.transformData(listVo);
        /*List<VersionExportInvalidVo> invalidList = new ArrayList<>();
        for (VersionExportVo versionExportVo : listVo) {
            VersionExportInvalidVo exportInvalidVo = new VersionExportInvalidVo();
            BeanUtil.copyProperties(versionExportVo, exportInvalidVo);
            invalidList.add(exportInvalidVo);
        }*/
        ExcelUtil.exportExcel(listVo,"导出失效公司文件", VersionExportVo.class, response);
    }



    /**
     * 导出文件版本记录列表
     */
    @ApiOperation("导出外部门文件列表数据")
    @PreAuthorize("@ss.hasPermi('process:wbm:standard:export')")
    @Log(title = "导出外部门文件列表数据", businessType = BusinessType.EXPORT)
    @PostMapping("/export/other/dept")
    public void exportOtherDeptList(@Validated VersionBo bo, HttpServletResponse response) {
        bo.setPageSize(Integer.MAX_VALUE);
        iVersionService.exportForList(bo, response, bo.getExportName(), Constants.VERSION_QUERY_OTHER_DEPT);
    }

    /**
     * 导出文件版本记录列表
     */
    @ApiOperation("导出文件版本记录列表")
    @Log(title = "文件版本记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(@Validated VersionBo bo, HttpServletResponse response) {
        List<VersionVo> list = iVersionService.queryList(bo);
        ExcelUtil.exportExcel(list, "文件版本记录", VersionVo.class, response);
    }

    /**
     * 获取文件版本记录详细信息
     */
    @ApiOperation("获取文件版本记录详细信息")
    @GetMapping("/{id}")
    public AjaxResult<VersionVo> getInfo(@ApiParam("主键")
                                                  @NotNull(message = "主键不能为空")
                                                  @PathVariable("id") String id) {
        return AjaxResult.success(iVersionService.queryById(id));
    }

    /**
     * 新增文件版本记录
     */
    @ApiOperation("新增文件版本记录")
    @Log(title = "文件版本记录", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public AjaxResult<Void> add(@Validated(AddGroup.class) @RequestBody VersionBo bo) {
        return toAjax(iVersionService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改文件版本记录
     */
    @ApiOperation("修改文件版本记录")
    @Log(title = "文件版本记录", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public AjaxResult<Void> edit(@Validated(EditGroup.class) @RequestBody VersionBo bo) {
        return toAjax(iVersionService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除文件版本记录
     */
    @ApiOperation("删除文件版本记录")
    @Log(title = "文件版本记录" , businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult<Void> remove(@ApiParam("主键串")
                                       @NotEmpty(message = "主键不能为空")
                                       @PathVariable String[] ids) {
        return toAjax(iVersionService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }

    /**
     * 根据文件编号、文件版本号获取文件版本记录详情
     */
    @ApiOperation("根据文件编号、文件版本号获取文件版本记录详情")
    @GetMapping("/detail")
    public AjaxResult<VersionVo> getDetail(
            @ApiParam("文件编号") @RequestParam("docId") String docId,
            @ApiParam("版本号") @RequestParam("versionValue") String versionValue) {
        return AjaxResult.success(iVersionService.getDetail(docId, versionValue));
    }
}

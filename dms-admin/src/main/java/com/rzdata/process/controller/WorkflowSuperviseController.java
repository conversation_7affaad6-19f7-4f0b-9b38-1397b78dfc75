package com.rzdata.process.controller;

import com.rzdata.framework.annotation.Log;
import com.rzdata.framework.annotation.RepeatSubmit;
import com.rzdata.framework.core.controller.BaseController;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import com.rzdata.framework.core.validate.QueryGroup;
import com.rzdata.framework.enums.BusinessType;
import com.rzdata.process.domain.bo.WorkflowSuperviseBo;
import com.rzdata.process.domain.vo.WorkflowSuperviseVo;
import com.rzdata.process.service.IWorkflowSuperviseService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;

/**
 * 流程督办Controller
 *
 * <AUTHOR>
 * @date 2024-11-14
 */
@Validated
@Api(value = "流程督办控制器", tags = { "流程督办管理" })
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/process/workflow/supervise")
public class WorkflowSuperviseController extends BaseController {

    private final IWorkflowSuperviseService workflowSuperviseService;

    @ApiOperation("查询流程督办配置列表")
    @GetMapping("/list")
    public TableDataInfo<WorkflowSuperviseVo> list(@Validated(QueryGroup.class) WorkflowSuperviseBo bo) {
        return workflowSuperviseService.queryPageList(bo);
    }

    @ApiOperation("获取流程督办配置详细信息")
    @GetMapping("/{id}")
    public AjaxResult<WorkflowSuperviseVo> getInfo(
            @ApiParam("主键") @NotNull(message = "主键不能为空") @PathVariable("id") String id) {
        return AjaxResult.success(workflowSuperviseService.queryById(id));
    }

    @ApiOperation("流程督办配置同步")
    @Log(title = "流程督办配置", businessType = BusinessType.OTHER)
    @RepeatSubmit()
    @PostMapping("/sync")
    public AjaxResult<Void> sync() {
        try {
            workflowSuperviseService.sync();
            return AjaxResult.success();
        } catch (Exception e) {
            return AjaxResult.error();
        }
    }

    @ApiOperation("新增流程流程督办配置")
    @Log(title = "流程督办配置", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public AjaxResult<Void> add(@Validated(AddGroup.class) @RequestBody WorkflowSuperviseBo bo) {
        return toAjax(workflowSuperviseService.insertByBo(bo) ? 1 : 0);
    }

    @ApiOperation("修改流程流程督办配置")
    @Log(title = "流程督办配置", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public AjaxResult<Void> edit(@Validated(EditGroup.class) @RequestBody WorkflowSuperviseBo bo) {
        return toAjax(workflowSuperviseService.updateByBo(bo) ? 1 : 0);
    }

    @ApiOperation("删除流程督办配置")
    @Log(title = "流程督办配置", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult<Void> remove(@ApiParam("主键串") @NotEmpty(message = "主键不能为空") @PathVariable String[] ids) {
        return toAjax(workflowSuperviseService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }

}

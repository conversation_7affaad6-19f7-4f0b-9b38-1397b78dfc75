package com.rzdata.process.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;
import com.rzdata.framework.core.domain.BaseEntity;


/**
 * 编辑明细日志对象 basic_file_editing_detail_log
 *
 * <AUTHOR>
 * @date 2025-04-30
 */
@Data
@Accessors(chain = true)
@TableName("basic_file_editing_detail_log")
public class BasicFileEditingDetailLog extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private String id;
    /**
     * 编辑的文件
     */
    private String fileId;
    /**
     * 状态：E-编辑中，Y-编辑完成，C-编辑取消
     */
    private String status;
    /**
     * 业务ID
     */
    private String bizId;

    /**
     * 上级文件ID
     */
    private String protoFileId;

}

package com.rzdata.process.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.rzdata.framework.core.domain.BaseEntity;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 文件借阅申请对象 doc_borrow_apply
 *
 * <AUTHOR>
 * @date 2021-12-31
 */
@Data
@Accessors(chain = true)
@TableName("doc_borrow_apply")
public class BorrowApply extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;
    /**
     * 申请标题
     */
    private String applyTitle;

    /**
     * 文件类型
     */
    private String docClass;

    /**
     * 申请部门Id
     */
    private String deptId;
    /**
     * 申请部门Id
     */
    private String deptName;
    /**
     * 申请人
     */
    private String userName;
    /**
     * 申请人名称
     */
    private String nickName;
    /**
     * 申请时间
     */
    private Date applyTime;
    /**
     * 申请原因
     */
    private String reason;
    /**
     * 申请状态
     */
    private String status;

    /**
     * 租户id
     */
    private String tenantId;

    private String classType;
}

package com.rzdata.process.domain;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import com.rzdata.framework.core.domain.BaseEntity;

import java.util.Date;


/**
 * 文件变更申请对象 doc_change_apply
 *
 * <AUTHOR>
 * @date 2021-12-31
 */
@Data
@Accessors(chain = true)
@TableName("doc_change_apply")
public class DocChangeApply extends BaseEntity{

    private static final long serialVersionUID=1L;

    /**
     * 变更编号
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;
    /**
     * 申请标题
     */
    private String applyTitle;
    /**
     * 文件编号
     */
    private String docId;
    /**
     * 变更类型
     */
    private String changeType;
    /**
     * 文件类型
     */
    private String docClass;
    /**
     * 文件名称
     */
    private String docName;
    /**
     * 编制部门编号
     */
    private String deptId;
    /**
     * 编制人
     */
    private String userName;
    /**
     * 变更原因
     */
    private String changeReason;
    /**
     * 内容概述
     */
    private String content;
    /**
     * 备注
     */
    private String remark;
    /**
     * 状态
     */
    private String status;
    /**
     * 版本号
     */
    private String versionValue;
    /**
     * 文件版本ID
     */
    private String versionId;

    @ApiModelProperty(value = "变更文件的id", required = true)
    private String applyId;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 下一流程的发起人
     */
    private String editUserName;
    /**
     * 下一流程的发起人部门
     */
    private String editDeptId;
    /**
     * 附件，多个以,隔开
     */
    private String appendixs;
    /**
     * 变更要素，多个以,隔开
     */
    private String changeFactor;

    private String dataType;

    /**
     * 流程截止时间
     */
    private Date deadline;

    /**
     * 分类所属类型
     * DOC文件台账、RECORD记录台账、FOREIGN外来文件
     */
    private String classType;

    /**
     * 申请id临时使用，用来更新站内消息的请求地址
     */
    @TableField(exist = false)
    private String applyIdTemp;
}

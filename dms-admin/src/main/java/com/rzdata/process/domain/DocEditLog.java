package com.rzdata.process.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;
import com.rzdata.framework.core.domain.BaseEntity;


/**
 * 文件编辑日志对象 doc_edit_log
 *
 * <AUTHOR>
 * @date 2025-03-03
 */
@Data
@Accessors(chain = true)
@TableName("doc_edit_log")
public class DocEditLog extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 主键
     */
    @TableId(value = "id",type = IdType.ASSIGN_ID)
    private String id;
    /**
     * 流程id
     */
    private String applyId;
    /**
     * 版本id
     */
    private String versionId;
    /**
     * 附件id
     */
    private String fileId;
    /**
     * 附件名称
     */
    private String fileName;
    /**
     * 原始附件id
     */
    private String protoFileId;
    /**
     * 操作类型 编辑 修订 批准
     */
    private String type;
    /**
     * 环节名称
     */
    private String actDefName;
    /**
     * 创建名称
     */
    private String createName;

}

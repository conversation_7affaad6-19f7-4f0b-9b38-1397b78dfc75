package com.rzdata.process.domain;

import com.alibaba.excel.annotation.ExcelProperty;
import com.rzdata.framework.core.domain.BaseEntity;
import lombok.Data;

@Data
public class DocExt extends BaseEntity {
    @ExcelProperty(value = "所属部门",index = 17)
    private String ext1;
    @ExcelProperty(value = "订单号",index = 18)
    private String ext2;
    @ExcelProperty(value = "文件状态",index = 19)
    private String ext3;
    @ExcelProperty(value = "计划单号",index = 20)
    private String ext4;
    @ExcelProperty(value = "文件类别",index = 21)
    private String ext5;
    @ExcelProperty(value = "体系代号",index = 22)
    private String ext6;
    @ExcelProperty(value = "封面",index = 23)
    private String ext7;
    @ExcelProperty(value = "合同编号",index = 24)
    private String ext8;
    @ExcelProperty(value = "文件细分代码",index = 25)
    private String ext9;
    @ExcelProperty(value = "组织",index = 26)
    private String ext10;
    @ExcelProperty(value = "文件名称(英)",index = 27)
    private String ext11;
    @ExcelProperty(value = "外部编号",index = 28)
    private String ext12;
    @ExcelProperty(value = "项目/客户代码",index = 29)
    private String ext13;
    @ExcelProperty(value = "修订码",index = 30)
    private String ext14;
    @ExcelProperty(value = "事业部",index = 31)
    private String ext15;
    private String ext16;
    private String ext17;
    private String ext18;
    private String ext19;
    private String ext20;
}

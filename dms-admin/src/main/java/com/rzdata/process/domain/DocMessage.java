package com.rzdata.process.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2022/2/11 10:24
 * @Version 1.0
 * @Description doc_message表
 */
@Data
@Accessors(chain = true)
@TableName("doc_message")
public class DocMessage {

    /**
     * 主键id
     */
    @TableId(value = "id",type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 业务id
     */
    private String applyId;

    /**
     * 文档id
     */
    private String docId;

    /**
     * 文档名称
     */
    private String docName;

    /**
     * 版本id
     */
    private String versionId;
    /**
     * 文档版本号
     */
    private String versionValue;

    /**
     * 文档类型
     */
    private String docClass;

    /**
     * 文档所属部门
     */
    private String deptId;

    /**
     * 文档所属部门名称
     */
    private String deptName;

    /**
     * 消息状态 0=未读 1=已读
     */
    private int msgStatus;

    /**
     * 消息内容 用于显示 通过/未通过
     */
    private String msgInfo;

    /**
     * html内容不为空时，则企业微信去该地址进行展示
     */
    @TableField(exist = false)
    private String htmlContent;

    /**
     * 消息类型 stdd文件相关、msg站内消息
     */
    private String msgType;

    /**
     * 消息类型 1=文件变更申请 2=文件补发申请 3=文件增发申请 4=文件借阅申请 5=文件复审申请 6=文件签收 7=文件回收 8=文件生效 9=文件失效
     * 10=文件复审期提前 11=文件有效期提前 102=上传培训记录 106=文件作废 103=文件变更 1000=文件复审
     */
    private Integer msgClass;

    /**
     * 消息发送时间
     */
    private Date createTime;

    /**
     * 消息创建人
     */
    private String createUser;

    /**
     * 消息创建人id
     */
    private String createUserId;

    /**
     * 消息接受(已读)时间
     */
    private Date recoveryTime;

    /**
     * 消息接受人
     */
    private String recoveryUser;

    /**
     * 消息接受人id
     */
    private String recoveryUserId;

    /**
     * 租户id
     */
    private String tenantId;

    /**
     * pc端连接地址
     */
    private String pcUrl;

    /**
     * 移动端链接地址
     */
    private String mobileUrl;
}

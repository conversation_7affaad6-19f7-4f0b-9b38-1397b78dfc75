package com.rzdata.process.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;
import com.rzdata.framework.core.domain.BaseEntity;

import java.util.Date;


/**
 * 文件关联勘误记录对象 doc_errata_link
 *
 * <AUTHOR>
 * @date 2025-01-13
 */
@Data
@Accessors(chain = true)
@TableName("doc_errata_link")
public class ErrataLink extends DocLinkLog {

    private static final long serialVersionUID=1L;

    /**
     * 父id
     */
    private String parentId;
}

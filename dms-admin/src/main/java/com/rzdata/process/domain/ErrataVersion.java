package com.rzdata.process.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;
import com.rzdata.framework.core.domain.BaseEntity;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 勘误文件版本记录对象 doc_errata_version
 *
 * <AUTHOR>
 * @date 2025-01-08
 */
@Data
@Accessors(chain = true)
@TableName("doc_errata_version")
public class ErrataVersion extends Version {

    private static final long serialVersionUID=1L;

    /**
     * 版本主键
     */
    private String versionId;
    /**
     * 排序 从0开始 0是原版
     */
    private Integer sort;
    /**
     * 文件所属分类
     */
    private String docClass;
    /**
     * 创建人名称
     */
    private String createName;
}

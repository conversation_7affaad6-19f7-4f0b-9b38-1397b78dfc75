package com.rzdata.process.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;
import com.rzdata.framework.core.domain.BaseEntity;


/**
 * 文件推送对象 doc_file_push
 *
 * <AUTHOR>
 * @date 2025-08-05
 */
@Data
@Accessors(chain = true)
@TableName("doc_file_push")
public class FilePush extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private String id;
    /**
     * 流程id
     */
    private String applyId;
    /**
     * 推送公司Id
     */
    private String pushCompanyId;
    /**
     * 推送公司名称
     */
    private String pushCompanyName;
    /**
     * 推送分类Id
     */
    private String pashClassId;
    /**
     * 推送分类名称
     */
    private String pashClassName;
    /**
     * 接收人
     */
    private String receiveUserName;
    /**
     * 接收名称
     */
    private String receiveNickName;
    /**
     * 接收人部门id
     */
    private String receiveDeptId;
    /**
     * 接收人部门名称
     */
    private String receiveDeptName;
    /**
     * 文件Id
     */
    private String fileId;
    /**
     * 推送文件Id
     */
    private String pashVersionId;
    /**
     * 推送文件编号
     */
    private String pashDocId;
    /**
     * 推送文件名称
     */
    private String pashDocName;
    /**
     * 推送文件版本号
     */
    private String pashVersionValue;
    /**
     * 文件版本Id
     */
    private String versionId;
    /**
     * 推送状态
     */
    private String status;

}

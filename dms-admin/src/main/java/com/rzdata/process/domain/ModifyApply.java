package com.rzdata.process.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.rzdata.framework.core.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.util.Date;

/**
 * 文件变更操作申请对象 doc_modify_apply
 *
 * <AUTHOR>
 * @date 2021-12-31
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Getter
@TableName("doc_modify_apply")
public class ModifyApply  extends DocExt {

    private static final long serialVersionUID=1L;

    /**
     * 主键，根据编码规则生成
     */
    @TableId(value = "id",type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 触发来源类型 体系文件台账stdd、项目文件台账project、ECN流程pre_change_apply
     */
    private String invokeType;

    /**
     * 触发来源ID 触发来源类型为stdd则为空、project为项目的主键、pre_change_apply为其表的主键
     */
    private String invokeId;

    /**
     * 所属项目ID
     */
    private String projectId;

    /**
     * 所属项目名字
     */
    private String projectName;

    /**
     * ECN流程编号
     */
    private String preChangeCode;

    /**
     * 变更类型
     */
    private String changeType;
    /**
     * 文件类型
     */
    private String docClass;
    /**
     * 文件名称
     */
    private String docName;
    /**
     * 文件编号
     */
    private String docId;
    /**
     * 版本号
     */
    private String versionValue;
    /**
     * 文件版本ID
     */
    private String versionId;
    /**
     * 编制部门编号
     */
    private String deptId;
    /**
     * 编制人
     */
    private String userName;

    /**
     * 编制人
     */
    @TableField(exist = false)
    private String nickName;
    /**
     * 申请时间
     */
    private Date applyTime;
    /**
     * 文件有效期
     */
    private Integer expiration;
    /**
     * 是否培训
     */
    private String yNTrain;
    /**
     * 培训部门
     */
    private String trainDept;
    /**
     * 是否已合稿
     */
    private String yNMergeDraft;

    /**
     * 是否已签章
     */
    private String yNEncrypt;

    /**
     * 是否已分发
     */
    private String yNDistribute;

    /**
     * 编制文件编号
     */
    private String fileId;

    /**
     * 主文档PDF的file_id
     */
    private String pdfFileId;

    /**
     * 加密(签章)文件basic_file表id
     */
    private String encryptFileId;

    /**
     * 记录状态
     */
    private String recordStatus;
    /**
     * 变更原因
     */
    private String changeReason;
    /**
     * 内容概述
     */
    private String content;
    /**
     * 备注
     */
    private String remark;

    /**
     * 变更要素，多个以,隔开
     */
    private String changeFactor;

    private String changeId;


    /**
     * 是否已签章 Y=已 N=未 默认N
     */
    private String isSignature;

    /**
     * 设置生效时间
     */
    private Date setupTime;
    /**
     * 体系文件台账stdd、项目文件台账project
     */
    private String dataType;

    /**
     * 上级文件版本ID
     */
    private String upVersionId;
    /**
     * 上级文件编号
     */
    private String parentDocId;
    /**
     * 分类所属类型
     * DOC文件台账、RECORD记录台账、FOREIGN外来文件
     */
    private String classType;

    /**
     * 步骤进度
     */
    private Integer step;

    /**
     * 批次
     */
    private String batch;

    /**
     * 发布时间
     */
    private Date releaseTime;

    /**
     * 分发类型 公司 company、部门 dept、个人 person、部门和个人 dept_person
     */
    private String distributeType;

    /**
     * 分发类型 公司 company、部门 dept、个人 person、部门和个人 dept_person
     */
    private String trainType;


    /**
     * 合稿后文件PdfId
     */
    private String coverFileId;

    /**
     * 是否上传客户记录 Y是 N否
     */
    private String whetherCustomer;

    /**
     * 文件状态
     */
    private String docStatus;

    /**
     * 是否留用 Y是 N否
     */
    private String whetherRetain;

    /**
     * 流程截止日期
     */
    private Date deadline;
    /**
     * 合规性
     */
    private String compliance;

    /**
     * 留用截止日期
     */
    private Date retainDeadline;


    /**
     * 批次Id
     */
    private String batchId;


    /**
     * 内部文件编号
     */
    private String internalDocId;
}

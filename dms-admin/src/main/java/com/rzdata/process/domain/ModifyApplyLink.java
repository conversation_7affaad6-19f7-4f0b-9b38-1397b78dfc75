package com.rzdata.process.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;


/**
 * 文件变更操作申请引用对象 doc_modify_apply_link
 *
 * <AUTHOR>
 * @date 2021-12-31
 */
@Data
@Accessors(chain = true)
@TableName("doc_modify_apply_link")
public class ModifyApplyLink {

    private static final long serialVersionUID=1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * basic_file主键
     */
    private String fileId;

    /**
     * 流程编号
     */
    private String applyId;
    /**
     * 关联编号
     */
    private String linkId;
    /**
     * 关联类别: doc file
     */
    private String linkClass;
    /**
     * 关联类型:正文 附件 关联文件 关联记录
     */
    private String linkType;
    /**
     * 文件名称
     */
    private String docName;
    /**
     * 文件类型
     */
    private String docClass;
    /**
     * 版本号
     */
    private String versionValue;
    /**
     * 文件版本ID
     */
    private String versionId;
    /**
     * 是否删除 1 是
     */
    private Integer isDeleted;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 文件编号
     */
    private String docId;

    /**
     * 生效后状态 -1 不变  0 生效 1 删除 2 作废
     */
    private Integer status;

    @TableField(exist = false)
    private String parentDocId;

    /**
     * 生效时间
     */
    private Date startDate;

    /**
     * 发布时间
     */
    private Date releaseTime;

    /**
     * 原始附件id
     */
    private String protoFileId;
}

package com.rzdata.process.domain;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rzdata.framework.annotation.ExcelDictFormat;
import com.rzdata.framework.convert.ExcelDictConvert;
import com.rzdata.framework.core.domain.BaseEntity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 打印任务对象 doc_print_task
 *
 * <AUTHOR>
 * @date 2024-01-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("doc_print_task")
public class PrintTask extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 文件编号
     */
    private String docId;

    /**
     * 文件名称
     */
    private String docName;

    /**
     * 文件版本
     */
    private String versionId;

    /**
     * 打印份数
     */
    private Integer printCount;

    /**
     * 打印状态（pending待打印 printing 打印中 completed已打印 failure打印失败）
     */
    private String status;

    /**
     * 已打印份数
     */
    private Integer printedCount;

    /**
     * 打印机名称
     */
    private String printerName;

    /**
     * 打印描述
     */
    private String printDesc;
    
    /**
     * 文件类型
     */
    @ApiModelProperty("文件类型")
    private String docType;

    /**
     * 电子文件名称
     */
    private String electronicFileName;

    /**
     * 分类号
     */
    private String classificationNo;

    /**
     * 创建人
     */
    private String createName;

    /**
     * 分发明细的ID
     */
    private String docDistributeId;

    /**
     * 文件的ID
     */
    private String fileId;
} 
package com.rzdata.process.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.rzdata.framework.core.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 文件复审申请对象 doc_review_apply
 *
 * <AUTHOR>
 * @date 2021-12-30
 */
@Data
@Accessors(chain = true)
@TableName("doc_review_apply")
public class ReviewApply extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;
    /**
     * 申请标题
     */
    private String applyTitle;
    /**
     * 申请类型
     */
    private String applyType;
    /**
     * 申请部门
     */
    private String deptId;
    /**
     * 申请人
     */
    private String userName;
    /**
     * 申请原因
     */
    private String reason;
    /**
     * 申请状态
     */
    private String status;

    /**
     * 体系文件台账stdd、项目文件台账project
     */
    private String dataType;

    private String tenantId;

    private String classType;
    /**
     *  批次
     */
    private String batch;
}

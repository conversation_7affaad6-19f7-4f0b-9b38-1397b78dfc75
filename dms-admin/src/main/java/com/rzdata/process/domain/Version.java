package com.rzdata.process.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.Year;
import java.util.Date;

/**
 * 文件版本记录对象 doc_version
 *
 * <AUTHOR>
 * @date 2021-12-30
 */
@Data
@Accessors(chain = true)
@TableName("doc_version")
public class Version extends DocExt{

    private static final long serialVersionUID=1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 所属租户
     */
    private String tenantId;

    /**
     * 体系文件stdd、项目文件project
     */
    private String dataType;

    /**
     * 标准文件主键
     */
    private String standardId;
    /**
     * 文件编号
     */
    private String docId;
    /**
     * 文件名称
     */
    private String docName;
    /**
     * 变更申请流水号
     */
    private String applyId;
    /**
     * 编制部门编号
     */
    private String deptId;
    /**
     * 编制人
     */
    private String userName;

    /**
     * 变更类型
     */
    private String changeType;

    /**
     * 触发来源类型 体系文件台账stdd、项目文件台账project、ECN流程pre_change_apply
     */
    private String invokeType;

    /**
     * 触发来源ID 触发来源类型为stdd则为空、project为项目的主键、pre_change_apply为其表的主键
     */
    private String invokeId;
    /**
     * 编制时间
     */
    private Date applyTime;
    /**
     * 文件版本号
     */
    private String versionValue;
    /**
     * 版本生效时间
     */
    private Date startDate;
    /**
     * 版本生效截止时间
     */
    private Date endDate;
    /**
     * 版本状态 1=有效 2=失效 0=未知
     */
    private String status;
    /**
     * 变更原因
     */
    private String reason;
    /**
     * 上次复审时间
     */
    private Date reviewTime;

    /**
     * 内容概述
     */
    private String content;

    /**
     * 备注
     */
    private String remark;

    /**
     * 变更要素，多个以,隔开
     */
    private String changeFactor;

    /**
     * 变更原因
     */
    private String changeReason;

    /**
     * 是否永久有效 0=否 1=是
     */
    private String forever;
    /**
     * 培训部门
     */
    private String trainDept;
    private String fileId;
    private String mergeFileId;
    private String encryptFileId;

    private String pdfFileId;

    /**
     * 发布时间
     */
    private Date releaseTime;


    /**
     * 分发类型 公司 company、部门 dept、个人 person、部门和个人 dept_person
     */
    private String distributeType;

    /**
     * 上级文件版本ID
     */
    private String upVersionId;

    /**
     * 父级编号ID
     */
    private String parentDocId;

    /**
     * 数据类型子分类
     */
    private String classType;

    /**
     * 是否上传客户记录 Y是 N否
     */
    private String whetherCustomer;
    /**
     * 合规性
     */
    private String compliance;

    /**
     * 内部文件编号
     */
    private String internalDocId;
}

package com.rzdata.process.domain.bo;

import com.rzdata.framework.core.domain.BaseEntity;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;


/**
 * 文件申请流程关联业务对象 doc_apply_relation
 *
 * <AUTHOR>
 * @date 2021-12-30
 */

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("文件申请流程关联业务对象")
public class ApplyRelationBo extends BaseEntity {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键", required = true)
    @NotBlank(message = "主键不能为空", groups = { EditGroup.class })
    private String id;

    /**
     * 流程编号
     */
    @ApiModelProperty(value = "流程编号", required = true)
    @NotBlank(message = "流程编号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String applyId;

    /**
     * 流程类型 修订，作废
     */
    @ApiModelProperty(value = "流程类型 修订，作废", required = true)
    @NotBlank(message = "流程类型 修订，作废不能为空", groups = { AddGroup.class, EditGroup.class })
    private String applyType;

    /**
     * 流程编号
     */
    @ApiModelProperty(value = "流程编号", required = true)
    @NotBlank(message = "流程编号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String relationApplyId;

    /**
     * 流程类型 复审
     */
    @ApiModelProperty(value = "流程类型 复审", required = true)
    @NotBlank(message = "流程类型 复审不能为空", groups = { AddGroup.class, EditGroup.class })
    private String relationApplyType;

    /**
     * 是否删除 1 是
     */
    @ApiModelProperty(value = "是否删除 1 是", required = true)
    @NotNull(message = "是否删除 1 是不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long isDeleted;


    /**
     * 分页大小
     */
    @ApiModelProperty("分页大小")
    private Integer pageSize;

    /**
     * 当前页数
     */
    @ApiModelProperty("当前页数")
    private Integer pageNum;

    /**
     * 排序列
     */
    @ApiModelProperty("排序列")
    private String orderByColumn;

    /**
     * 排序的方向desc或者asc
     */
    @ApiModelProperty(value = "排序的方向", example = "asc,desc")
    private String isAsc;

}

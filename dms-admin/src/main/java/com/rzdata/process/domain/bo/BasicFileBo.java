package com.rzdata.process.domain.bo;

import com.rzdata.framework.core.domain.BaseEntity;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;


/**
 * 附件业务对象 basic_file
 *
 * <AUTHOR>
 * @date 2022-01-04
 */

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("附件业务对象")
public class BasicFileBo extends BaseEntity {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键", required = true)
    @NotBlank(message = "主键不能为空", groups = { EditGroup.class })
    private String id;

    /**
     * 文件名称
     */
    @ApiModelProperty(value = "文件名称", required = true)
    @NotBlank(message = "文件名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String fileName;

    /**
     * 文件大小
     */
    @ApiModelProperty(value = "文件大小", required = true)
    @NotNull(message = "文件大小不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long fileSize;

    /**
     * 文件类型
     */
    @ApiModelProperty(value = "文件类型", required = true)
    @NotBlank(message = "文件类型不能为空", groups = { AddGroup.class, EditGroup.class })
    private String fileType;

    /**
     * 文件类别
     */
    @ApiModelProperty(value = "文件类别", required = true)
    @NotBlank(message = "文件类别不能为空", groups = { AddGroup.class, EditGroup.class })
    private String fileClass;

    /**
     * 文件来源业务主键
     */
    @ApiModelProperty(value = "文件来源业务主键", required = true)
    @NotBlank(message = "文件来源业务主键不能为空", groups = { AddGroup.class, EditGroup.class })
    private String businessId;

    /**
     * 文件路径
     */
    @ApiModelProperty(value = "文件路径")
    private String filePath;

    /**
     * 文件外部存储ID
     */
    @ApiModelProperty(value = "文件外部存储ID")
    private String externalFileId;

    private String externalFilePath;

    private String externalRev;

    private Integer status;

    /**
     * 分页大小
     */
    @ApiModelProperty("分页大小")
    private Integer pageSize;

    /**
     * 当前页数
     */
    @ApiModelProperty("当前页数")
    private Integer pageNum;

    /**
     * 排序列
     */
    @ApiModelProperty("排序列")
    private String orderByColumn;

    /**
     * 排序的方向desc或者asc
     */
    @ApiModelProperty(value = "排序的方向", example = "asc,desc")
    private String isAsc;

    /**
     * 文件摘要
     */
    private String messageDigest;

}

package com.rzdata.process.domain.bo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.rzdata.framework.core.domain.BaseEntity;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;


/**
 * 编辑明细日志业务对象 basic_file_editing_detail_log
 *
 * <AUTHOR>
 * @date 2025-04-30
 */

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("编辑明细日志业务对象")
public class BasicFileEditingDetailLogBo extends BaseEntity {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键", required = true)
    @NotBlank(message = "主键不能为空", groups = { EditGroup.class })
    private String id;

    /**
     * 编辑的文件
     */
    @ApiModelProperty(value = "编辑的文件", required = true)
    @NotBlank(message = "编辑的文件不能为空", groups = { AddGroup.class, EditGroup.class })
    private String fileId;

    /**
     * 状态：E-编辑中，Y-编辑完成，C-编辑取消
     */
    @ApiModelProperty(value = "状态：E-编辑中，Y-编辑完成，C-编辑取消", required = true)
    @NotBlank(message = "状态：E-编辑中，Y-编辑完成，C-编辑取消不能为空", groups = { AddGroup.class, EditGroup.class })
    private String status;

    /**
     * 业务ID
     */
    @ApiModelProperty(value = "业务ID", required = true)
    @NotBlank(message = "业务ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private String bizId;


    /**
     * 分页大小
     */
    @ApiModelProperty("分页大小")
    private Integer pageSize;

    /**
     * 当前页数
     */
    @ApiModelProperty("当前页数")
    private Integer pageNum;

    /**
     * 排序列
     */
    @ApiModelProperty("排序列")
    private String orderByColumn;

    /**
     * 排序的方向desc或者asc
     */
    @ApiModelProperty(value = "排序的方向", example = "asc,desc")
    private String isAsc;

    /**
     * 上级文件ID
     */
    @ApiModelProperty("上级文件ID")
    private String protoFileId;
}

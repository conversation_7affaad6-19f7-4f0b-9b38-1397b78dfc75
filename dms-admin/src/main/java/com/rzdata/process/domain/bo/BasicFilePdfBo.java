package com.rzdata.process.domain.bo;

import com.rzdata.framework.core.domain.BaseEntity;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;
import java.util.List;


/**
 * 附件转PDF业务对象 basic_file_pdf
 *
 * <AUTHOR>
 * @date 2023-07-31
 */

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("附件转PDF业务对象")
public class BasicFilePdfBo extends BaseEntity {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键", required = true)
    @NotBlank(message = "主键不能为空", groups = { AddGroup.class, EditGroup.class })
    private String id;

    /**
     * 源文件ID
     */
    @ApiModelProperty(value = "源文件ID", required = true)
    @NotBlank(message = "源文件ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private String fileId;

    /**
     * pdf文件ID
     */
    @ApiModelProperty(value = "pdf文件ID", required = true)
    @NotBlank(message = "pdf文件ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private String pdfId;

    /**
     * 转换类型;转换：transition、签章：signature
     */
    @ApiModelProperty(value = "转换类型;转换：transition、签章：signature", required = true)
    @NotBlank(message = "转换类型;转换：transition、签章：signature不能为空", groups = { AddGroup.class, EditGroup.class })
    private String pdfType;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态", required = true)
    @NotBlank(message = "状态不能为空", groups = { AddGroup.class, EditGroup.class })
    private String status;

    /**
     * 租户id
     */
    @ApiModelProperty(value = "租户id", required = true)
    @NotBlank(message = "租户id不能为空", groups = { AddGroup.class, EditGroup.class })
    private String tenantId;

    /**
     * 业务id
     */
    private String bizId;


    /**
     * 分页大小
     */
    @ApiModelProperty("分页大小")
    private Integer pageSize;

    /**
     * 当前页数
     */
    @ApiModelProperty("当前页数")
    private Integer pageNum;

    /**
     * 排序列
     */
    @ApiModelProperty("排序列")
    private String orderByColumn;

    /**
     * 排序的方向desc或者asc
     */
    @ApiModelProperty(value = "排序的方向", example = "asc,desc")
    private String isAsc;

    /**
     * 源文件ID集
     */
    @ApiModelProperty(value = "源文件ID集", required = true)
    private List<String> fileIdList;
}

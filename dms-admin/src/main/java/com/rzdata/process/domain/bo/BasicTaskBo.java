package com.rzdata.process.domain.bo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.rzdata.framework.core.domain.BaseEntity;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 任务计划与执行业务对象 basic_task
 *
 * <AUTHOR>
 * @date 2023-06-21
 */

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("任务计划与执行业务对象")
public class BasicTaskBo extends BaseEntity {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键", required = true)
    private String id;

    /**
     * 来源类型;pre_change_apply前置变更申请流程、capa_apply纠正预防措施CAPA流程
     */
    @ApiModelProperty(value = "来源类型;pre_change_apply前置变更申请流程、capa_apply纠正预防措施CAPA流程", required = true)
    private String sourceType;

    /**
     * 来源ID;来源类型的主键
     */
    @ApiModelProperty(value = "来源ID;来源类型的主键", required = true)
    private String sourceId;

    /**
     * 来源业务模块;在同一个来源类型中，可区分多个业务模块
     */
    @ApiModelProperty(value = "来源业务模块;在同一个来源类型中，可区分多个业务模块", required = true)
    private String sourceBiz;

    /**
     * 任务方式;doc_task文件任务、other_task其他任务
     */
    @ApiModelProperty(value = "任务方式;doc_task文件任务、other_task其他任务", required = true)
    private String taskModel;

    /**
     * 文件类型;区分体系文件stdd、项目文件project
     */
    @ApiModelProperty(value = "文件类型;区分体系文件stdd、项目文件project", required = true)
    private String fileType;

    /**
     * CAPA类型
     */
    @ApiModelProperty("CAPA类型")
    private String capaType;

    /**
     * 文件名称;product生产、store库存、road在途
     */
    @ApiModelProperty(value = "文件名称;product生产、store库存、road在途", required = true)
    private String name;


    /**
     * 文件版本Id
     */
    @ApiModelProperty(value = "文件版本Id")
    private String versionId;

    /**
     * 文件版本
     */
    @ApiModelProperty(value = "文件版本")
    private String version;


    /**
     * 变更方式;add新增、update修订、disuse作废
     */
    @ApiModelProperty(value = "变更方式;add新增、update修订、disuse作废", required = true)
    private String changeType;

    /**
     * ECN流程编号
     */
    @ApiModelProperty(value = "ECN流程编号", required = true)
    private String flowCode;

    /**
     * 执行人ID
     */
    @ApiModelProperty(value = "执行人ID", required = true)
    private String userId;

    /**
     * 执行人名称
     */
    @ApiModelProperty(value = "执行人名称", required = true)
    private String userName;

    /**
     * 任务描述
     */
    @ApiModelProperty(value = "任务描述", required = true)
    private String description;

    /**
     * 实际完成日期
     */
    @ApiModelProperty(value = "实际完成日期", required = true)
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date factFinishDate;

    /**
     * 计划完成日期
     */
    @ApiModelProperty(value = "计划完成日期", required = true)
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date planFinishDate;

    /**
     * 流程ID
     */
    @ApiModelProperty(value = "流程ID", required = true)
    private String procInstId;

    /**
     * 流程状态
     */
    @ApiModelProperty(value = "流程状态", required = true)
    private String bpmnStatus;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注", required = true)
    private String remark;


    /**
     * 分页大小
     */
    @ApiModelProperty("分页大小")
    private Integer pageSize;

    /**
     * 当前页数
     */
    @ApiModelProperty("当前页数")
    private Integer pageNum;

    /**
     * 排序列
     */
    @ApiModelProperty("排序列")
    private String orderByColumn;

    /**
     * 排序的方向desc或者asc
     */
    @ApiModelProperty(value = "排序的方向", example = "asc,desc")
    private String isAsc;
}

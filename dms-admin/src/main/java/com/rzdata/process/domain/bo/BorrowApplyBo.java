package com.rzdata.process.domain.bo;

import com.rzdata.framework.core.domain.BaseEntity;
import com.rzdata.process.domain.BorrowApplyItem;
import com.rzdata.setting.domain.DocPresetUser;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * 文件借阅申请业务对象 doc_borrow_apply
 *
 * <AUTHOR>
 * @date 2021-12-31
 */

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("文件借阅申请业务对象")
public class BorrowApplyBo extends BaseEntity {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键", required = true)
    private String id;
    /**
     * 申请标题
     */
    @ApiModelProperty(value = "申请标题")
    private String applyTitle;
    /**
     * 申请部门Id
     */
    @ApiModelProperty("申请部门Id")
    private String deptId;

    /**
     * 文件类型
     */
    private String docClass;
    /**
     * 申请部门名称
     */
    @ApiModelProperty("申请部门名称")
    private String deptName;
    /**
     * 申请人
     */
    @ApiModelProperty("申请人")
    private String userName;
    /**
     * 申请人名称
     */
    @ApiModelProperty("申请人名称")
    private String nickName;
    /**
     * 申请时间
     */
    @ApiModelProperty("申请时间")
    private Date applyTime;
    /**
     * 申请原因
     */
    @ApiModelProperty("申请原因")
    private String reason;
    /**
     * 申请状态
     */
    @ApiModelProperty("申请状态")
    private String status;

    /**
     * 租户id
     */
    @ApiModelProperty("租户id")
    private String tenantId;

    /**
     * 分页大小
     */
    @ApiModelProperty("分页大小")
    private Integer pageSize;

    /**
     * 当前页数
     */
    @ApiModelProperty("当前页数")
    private Integer pageNum;

    /**
     * 排序列
     */
    @ApiModelProperty("排序列")
    private String orderByColumn;

    /**
     * 排序的方向desc或者asc
     */
    @ApiModelProperty(value = "排序的方向", example = "asc,desc")
    private String isAsc;

    private BpmClientInputModelBo bpmClientInputModel;

    private List<BorrowApplyItem> itemList;

    private String recordStatus;

    private Boolean editStatus;

    private String classType;

    private Boolean presetUserEdit;

    private List<DocPresetUser> presetUserList;

}

package com.rzdata.process.domain.bo;

import com.rzdata.framework.core.domain.BaseEntity;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;
import java.util.Date;


/**
 * 文件借阅选择借阅人业务对象 doc_borrow_apply_user
 *
 * <AUTHOR>
 * @date 2021-12-31
 */

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("文件借阅选择借阅人业务对象")
public class BorrowApplyItemBo extends BaseEntity {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键", required = true)
    @NotBlank(message = "主键不能为空", groups = { EditGroup.class })
    private String id;
    /**
     * 流程编号
     */
    private String applyId;
    /**
     * 文档编号
     */
    private String docId;
    /**
     * 文档名称
     */
    private String docName;
    /**
     * 文档类型
     */
    private String docClass;
    /**
     * 版本id
     */
    private String versionId;

    /**
     * 版本号
     */
    private String versionValue;
    /**
     * 编制部门Id
     */
    private String deptId;
    /**
     * 编制部门名称
     */
    private String deptName;
    /**
     * 编制人
     */
    private String userName;
    /**
     * 编制人名称
     */
    private String nickName;
    /**
     * 借阅人
     */
    private String borrowUserName;
    /**
     * 借阅人昵称
     */
    private String borrowNickName;
    /**
     * 是否长期有效 Y:是 N:否
     */
    private String isForever;
    /**
     * 借阅期限
     */
    private Integer timeLimit;
    /**
     * 借阅开始时间
     */
    private Date startTime;
    /**
     * 借阅结束时间
     */
    private Integer endTime;
    /**
     * 借阅结论
     */
    private String borrowAction;

    /**
     * 原由
     */
    private String reason;
    /**
     * 租户id
     */
    private Integer tenantId;


    /**
     * 分页大小
     */
    @ApiModelProperty("分页大小")
    private Integer pageSize;

    /**
     * 当前页数
     */
    @ApiModelProperty("当前页数")
    private Integer pageNum;

    /**
     * 排序列
     */
    @ApiModelProperty("排序列")
    private String orderByColumn;

    /**
     * 排序的方向desc或者asc
     */
    @ApiModelProperty(value = "排序的方向", example = "asc,desc")
    private String isAsc;

    private String borrowStatus;

    private String status;

    private String presetUser;
}

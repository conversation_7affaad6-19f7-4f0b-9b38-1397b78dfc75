package com.rzdata.process.domain.bo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.blueland.bpmclient.model.BpmClientInputModel;
import com.blueland.bpmclient.model.ProcessInstanceModel;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2022/2/11 15:12
 * @Version 1.0
 * @Description
 */
@Data
public class BpmClientInputModelBo implements Serializable {

    /**
     * 申请结果
     */
    private Boolean applyStatus;

    private Date setupTime;

    private String type;

    private BpmClientInputModel model;

    private String status;
    /**
     * 步骤
     */
    private Integer step;
    /**
     * 是否有审核
     */
    private Boolean review;
    /**
     * 下个环节是否会审
     */
    private Boolean jointReview;
    /**
     * 会审批次 批次相同为同一次会审
     */
    private String batch;

    private String redirectDefId;

    private String title;

    private List<Map<String, Object>> redirectReceivers;

    private Boolean isLast;

    /**
     * 流程实例对象
     */
    private ProcessInstanceModel processInst;
    /**
     * 流程对象
     */
    private BpmClientInputModel bpmClientInputModel;


    private Boolean transfer;

    private Boolean transferStatus;
    /**
     * 环节序号
     */
    private String order;

    private String bizType;

    /**
     * 标记
     */
    private String mark;
    /**
     * 方向 true正 false反
     */
    private Boolean direction;

    /**
     * 申请id临时使用，用来更新站内消息的请求地址
     */
    @TableField(exist = false)
    private String applyIdTemp;

    private String onlineType;
}

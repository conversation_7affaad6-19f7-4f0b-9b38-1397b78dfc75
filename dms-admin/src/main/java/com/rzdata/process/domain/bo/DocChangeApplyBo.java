package com.rzdata.process.domain.bo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.rzdata.framework.core.domain.BaseEntity;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import com.rzdata.setting.domain.DocPresetUser;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;
import java.util.Date;
import java.util.List;


/**
 * 文件变更申请业务对象 doc_change_apply
 *
 * <AUTHOR>
 * @date 2021-12-31
 */

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("文件变更申请业务对象")
public class DocChangeApplyBo extends BaseEntity {

    /**
     * 变更编号
     */
    @ApiModelProperty(value = "变更编号", required = true)
    private String id;

    /**
     * 申请标题
     */
    @ApiModelProperty(value = "申请标题", required = true)
    private String applyTitle;

    /**
     * 文件编号
     */
    @ApiModelProperty(value = "文件编号", required = true)
    private String docId;

    /**
     * 变更类型
     */
    @ApiModelProperty(value = "变更类型", required = true)
    private String changeType;

    /**
     * 文件类型
     */
    @ApiModelProperty(value = "文件类型", required = true)
    private String docClass;

    /**
     * 文件名称
     */
    @ApiModelProperty(value = "文件名称", required = true)
    private String docName;

    /**
     * 编制部门编号
     */
    @ApiModelProperty(value = "编制部门编号", required = true)
    private String deptId;

    /**
     * 编制人
     */
    @ApiModelProperty(value = "编制人", required = true)
    private String userName;

    /**
     * 变更原因
     */
    @ApiModelProperty(value = "变更原因", required = true)
    private String changeReason;

    /**
     * 内容概述
     */
    @ApiModelProperty(value = "内容概述", required = true)
    private String content;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态", required = true)
    private String status;

    /**
     * 版本号
     */
    @ExcelProperty(value = "版本号")
    @ApiModelProperty("版本号")
    private String versionValue;

    /**
     * 文件版本ID
     */
    @ExcelProperty(value = "版本ID")
    @ApiModelProperty("版本ID")
    private String versionId;

    /**
     * 分页大小
     */
    @ApiModelProperty("分页大小")
    private Integer pageSize;

    /**
     * 当前页数
     */
    @ApiModelProperty("当前页数")
    private Integer pageNum;

    /**
     * 排序列
     */
    @ApiModelProperty("排序列")
    private String orderByColumn;

    /**
     * 排序的方向desc或者asc
     */
    @ApiModelProperty(value = "排序的方向", example = "asc,desc")
    private String isAsc;

    /**
     * 下一流程的发起人
     */
    @ApiModelProperty(value = "下一流程的发起人", required = true)
    private String editUserName;

    /**
     * 下一流程的发起人部门
     */
    @ApiModelProperty(value = "下一流程的发起人部门", required = true)
    private String editDeptId;

    /**
     * 附件，多个以,隔开
     */
    @ApiModelProperty(value = "附件，多个以,隔开", required = true)
    private String appendixs;

    /**
     * 备注附件
     */
    @ApiModelProperty(value = "备注附件")
    private List<ModifyApplyLinkBo> remarkDoc;

    /**
     * 变更要素，多个以,隔开
     */
    @ApiModelProperty(value = "变更要素，多个以,隔开", required = true)
    private String changeFactor;

    @ApiModelProperty(value = "变更文件的id", required = true)
    private String applyId;

    private BpmClientInputModelBo bpmClientInputModel;

    private String recordStatus;

    private String dataType;

    /**
     * 流程截止时间
     */
    private Date deadline;

    /**
     * 分类所属类型
     * DOC文件台账、RECORD记录台账、FOREIGN外来文件
     */
    private String classType;

    private Boolean presetUserEdit;

    private List<DocPresetUser> presetUserList;

    private Boolean editStatus;
}

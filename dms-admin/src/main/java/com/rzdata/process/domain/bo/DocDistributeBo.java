package com.rzdata.process.domain.bo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.rzdata.framework.core.domain.BaseEntity;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;

import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 文件分发明细业务对象 doc_distribute
 *
 * <AUTHOR>
 * @date 2023-07-20
 */

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("文件分发明细业务对象")
public class DocDistributeBo extends BaseEntity {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键", required = true)
    private String id;

    /**
     * 租户id
     */
    @ApiModelProperty(value = "租户id", required = true)
    private String tenantId;

    /**
     * 文件版本ID
     */
    @ApiModelProperty(value = "文件版本ID", required = true)
    private String versionId;

    /**
     * 分发号;例如01、02
     */
    @ApiModelProperty(value = "分发号;例如01、02", required = true)
    private Integer code;

    /**
     * 文件编号
     */
    @ApiModelProperty(value = "文件编号", required = true)
    private String docId;

    /**
     * 文件类型
     */
    @ApiModelProperty(value = "文件类型", required = true)
    private String docClass;

    /**
     * 文件名称
     */
    @ApiModelProperty(value = "文件名称", required = true)
    private String docName;

    /**
     * 分发数量
     */
    @ApiModelProperty(value = "分发数量", required = true)
    private Integer nums;

    /**
     * 打印数量
     */
    @ApiModelProperty(value = "打印数量", required = true)
    private Integer printNums;

    /**
     * Y可打印 N不可打印
     */
    @ApiModelProperty(value = "Y可打印 N不可打印", required = true)
    private String printFlag;

    /**
     * 签收人
     */
    @ApiModelProperty(value = "签收人", required = true)
    private String receiveUserName;

    /**
     * 签收人昵称
     */
    @ApiModelProperty(value = "签收人昵称", required = true)
    private String receiveNickName;

    /**
     * 签收人部门id
     */
    @ApiModelProperty("签收人部门id")
    private String receiveUserDeptId;
    /**
     * 签收人部门名称
     */
    @ApiModelProperty(value = "签收人部门名称", required = true)
    private String receiveUserDept;

    /**
     * 签收时间
     */
    @ApiModelProperty(value = "签收时间", required = true)
    private Date receiveTime;

    /**
     * 丢失时间
     */
    @ApiModelProperty(value = "丢失时间", required = true)
    private Date lostTime;

    /**
     * 回收时间
     */
    @ApiModelProperty(value = "回收时间", required = true)
    private Date recoveryTime;

    /**
     * 是否签收 0否 1是
     */
    @ApiModelProperty(value = "是否签收 0否 1是", required = true)
    private Boolean receive;

    /**
     * 是否丢失 0否 1是
     */
    @ApiModelProperty(value = "是否丢失 0否 1是", required = true)
    private Boolean lost;

    /**
     * 是否回收 0否 1是
     */
    @ApiModelProperty(value = "是否回收 0否 1是", required = true)
    private Boolean recovery;

    /**
     * 状态;未签收、已签收、已打印、已回收、已丢失
     */
    @ApiModelProperty(value = "状态;未签收、已签收、已打印、已回收、已丢失", required = true)
    private String status;

    /**
     * 类型 部门 dept、个人 person
     */
    private String type;

    /**
     * 分页大小
     */
    @ApiModelProperty("分页大小")
    private Integer pageSize;

    /**
     * 当前页数
     */
    @ApiModelProperty("当前页数")
    private Integer pageNum;

    /**
     * 排序列
     */
    @ApiModelProperty("排序列")
    private String orderByColumn;

    /**
     * 排序的方向desc或者asc
     */
    @ApiModelProperty(value = "排序的方向", example = "asc,desc")
    private String isAsc;

    private String neType;

    @TableField(exist=false)
    @ApiModelProperty(value = "文件类型集合")
    private List<String> docClassList;

    @TableField(exist=false)
    private List<String> deptIds;

    private String deptId;

    @TableField(exist=false)
    private String startTime;

    @TableField(exist=false)
    private String endTime;

    @TableField(exist=false)
    private List<String> ids;

    /** 培训状态 **/
    @TableField(exist=false)
    private String tranStatus;
    /** 查询名称 **/
    @TableField(exist=false)
    private String queryName;

    @TableField(exist=false)
    private String classType;

    @TableField(exist=false)
    private String recordFilePrintPermission;

    private String printPaperType;

    private String fileId;

    private List<String> fileIds;
}

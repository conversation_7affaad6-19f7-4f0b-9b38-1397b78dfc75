package com.rzdata.process.domain.bo;

import com.rzdata.framework.core.domain.BaseEntity;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;


/**
 * 文件编辑日志业务对象 doc_edit_log
 *
 * <AUTHOR>
 * @date 2025-03-03
 */

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("文件编辑日志业务对象")
public class DocEditLogBo extends BaseEntity {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键", required = true)
    @NotBlank(message = "主键不能为空", groups = { EditGroup.class })
    private String id;

    /**
     * 流程id
     */
    @ApiModelProperty(value = "流程id", required = true)
    @NotBlank(message = "流程id不能为空", groups = { AddGroup.class, EditGroup.class })
    private String applyId;

    /**
     * 版本id
     */
    @ApiModelProperty(value = "版本id", required = true)
    @NotBlank(message = "版本id不能为空", groups = { AddGroup.class, EditGroup.class })
    private String versionId;

    /**
     * 附件id
     */
    @ApiModelProperty(value = "附件id", required = true)
    @NotBlank(message = "附件id不能为空", groups = { AddGroup.class, EditGroup.class })
    private String fileId;

    /**
     * 附件名称
     */
    private String fileName;

    /**
     * 原始附件id
     */
    @ApiModelProperty(value = "原始附件id", required = true)
    @NotBlank(message = "原始附件id不能为空", groups = { AddGroup.class, EditGroup.class })
    private String protoFileId;

    /**
     * 操作类型 编辑 修订 批准
     */
    @ApiModelProperty(value = "操作类型 编辑 修订 批准", required = true)
    @NotBlank(message = "操作类型 编辑 修订 批准不能为空", groups = { AddGroup.class, EditGroup.class })
    private String type;

    /**
     * 环节名称
     */
    @ApiModelProperty(value = "环节名称", required = true)
    @NotBlank(message = "环节名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String actDefName;

    /**
     * 创建名称
     */
    @ApiModelProperty(value = "创建名称", required = true)
    @NotBlank(message = "创建名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String createName;


    /**
     * 分页大小
     */
    @ApiModelProperty("分页大小")
    private Integer pageSize;

    /**
     * 当前页数
     */
    @ApiModelProperty("当前页数")
    private Integer pageNum;

    /**
     * 排序列
     */
    @ApiModelProperty("排序列")
    private String orderByColumn;

    /**
     * 排序的方向desc或者asc
     */
    @ApiModelProperty(value = "排序的方向", example = "asc,desc")
    private String isAsc;

}

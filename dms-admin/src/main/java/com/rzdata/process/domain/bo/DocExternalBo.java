package com.rzdata.process.domain.bo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.rzdata.framework.core.domain.BaseEntity;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;

import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 外来文件业务对象 doc_external
 *
 * <AUTHOR>
 * @date 2023-07-19
 */

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("外来文件业务对象")
public class DocExternalBo extends BaseEntity {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键", required = true)
    @NotBlank(message = "主键不能为空", groups = { EditGroup.class })
    private String id;

    /**
     * 租户id
     */
    @ApiModelProperty(value = "租户id", required = true)
//    @NotBlank(message = "租户id不能为空", groups = { AddGroup.class, EditGroup.class })
    private String tenantId;

    /**
     * 文件类型
     */
    @ApiModelProperty(value = "文件类型", required = true)
    @NotBlank(message = "文件类型不能为空", groups = { AddGroup.class, EditGroup.class })
    private String docClass;

    /**
     * 文件名称
     */
    @ApiModelProperty(value = "文件名称", required = true)
    @ExcelProperty(value = "文件名称",index = 3)
    @NotBlank(message = "文件名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String docName;

    /**
     * 文件编码
     */
    @ApiModelProperty(value = "文件编码", required = true)
    @ExcelProperty(value = "文件编码",index = 1)
    @NotBlank(message = "文件编码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String docCode;


    @ExcelProperty(value = "文件版本",index = 2)
    private String version;

    /**
     * 文件状态
;有效=1
失效=2
草稿=0
     */
    @ApiModelProperty(value = "文件状态", required = true)
    @NotBlank(message = "文件状态", groups = { AddGroup.class, EditGroup.class })
    private String status;

    /**
     * 部门Id
     */
    @ApiModelProperty(value = "部门Id", required = true)
    @NotBlank(message = "部门Id不能为空", groups = { AddGroup.class, EditGroup.class })
    private String deptId;

    /**
     * 部门名称
     */
    @ApiModelProperty(value = "部门名称", required = true)
    @NotBlank(message = "部门名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String deptName;

    /**
     * 发布时间
     */
    @ApiModelProperty(value = "发布时间", required = true)
    @ExcelProperty(value = "发布时间",index = 4)
    @NotNull(message = "发布时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date startTime;

    /**
     * 作废时间
     */
    @ApiModelProperty(value = "作废时间", required = true)
    @ExcelProperty(value = "作废时间",index = 5)
//    @NotNull(message = "作废时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date endTime;

    /**
     * 主文件basic_file表id
     */
    @ApiModelProperty(value = "主文件basic_file表id", required = true)
//    @NotBlank(message = "主文件basic_file表id不能为空", groups = { AddGroup.class, EditGroup.class })
    private String fileId;
    private String createUserId;

    private List<String> docClassList;
    /**
     * 分页大小
     */
    @ApiModelProperty("分页大小")
    private Integer pageSize;

    /**
     * 当前页数
     */
    @ApiModelProperty("当前页数")
    private Integer pageNum;

    /**
     * 排序列
     */
    @ApiModelProperty("排序列")
    private String orderByColumn;

    /**
     * 排序的方向desc或者asc
     */
    @ApiModelProperty(value = "排序的方向", example = "asc,desc")
    private String isAsc;

    /** 内部编号 **/
    @ApiModelProperty("内部编号")
    private String internalNumber;
}

package com.rzdata.process.domain.bo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.rzdata.framework.core.domain.BaseEntity;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;
import java.util.Date;


/**
 * 文件关联记录业务对象 doc_link_log
 *
 * <AUTHOR>
 * @date 2022-01-06
 */

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("文件关联记录业务对象")
public class DocLinkLogBo extends BaseEntity {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 主文件主键
     */
    @ApiModelProperty(value = "主文件主键")
    private String standardId;
    /**
     * 文件编号
     */
    @ApiModelProperty(value = "主文件编号")
    private String docId;

    /**
     * 关联记录编号
     */
    @ApiModelProperty(value = "关联记录编号")
    private String linkCode;

    /**
     * 文件名称
     */
    @ApiModelProperty(value = "文件名称")
    private String fileName;

    /**
     * 文件id
     */
    @ApiModelProperty(value = "文件id")
    private String fileId;

    /**
     * 版本号
     */
    @ApiModelProperty(value = "版本号")
    private String versionValue;


    /**
     * 文件版本ID
     */
    @ApiModelProperty("版本ID")
    private String versionId;

    /**
     * 关联类型: DOC正文 APPENDIX附件 RECORD记录 REF_DOC关联其他文件
     */
    @ApiModelProperty("关联类型")
    private String linkType;

    private String neLinkType;

    private String[] linkTypeList;

    /**
     * 文件类型
     */
    @ApiModelProperty("文件类型")
    private String docClass;

    /**
     * 分页大小
     */
    @ApiModelProperty("分页大小")
    private Integer pageSize;

    /**
     * 当前页数
     */
    @ApiModelProperty("当前页数")
    private Integer pageNum;

    /**
     * 排序列
     */
    @ApiModelProperty("排序列")
    private String orderByColumn;

    /**
     * 排序的方向desc或者asc
     */
    @ApiModelProperty(value = "排序的方向", example = "asc,desc")
    private String isAsc;

    private String status;

    /**
     * 生效时间
     */
    private Date startDate;

    /**
     * 发布时间
     */
    private Date releaseTime;

    /**
     * 失效时间
     */
    private Date endDate;

    /**
     * 关联版本Id
     */
    @ApiModelProperty("关联版本Id")
    private String pVersionId;

    private String keyword;

    private String searchType;

    public void setLinkTypeList(String ...linkTypeList) {
        this.linkTypeList = linkTypeList;
    }

    /**
     * 原始附件id
     */
    private String protoFileId;
}

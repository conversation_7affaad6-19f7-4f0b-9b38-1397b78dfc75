package com.rzdata.process.domain.bo;

import com.rzdata.framework.core.domain.BaseEntity;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import com.rzdata.process.domain.vo.DocDistributeVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;
import java.util.List;


/**
 * 文件打印数据权限业务对象 doc_print_data_auth
 *
 * <AUTHOR>
 * @date 2024-04-29
 */

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("文件打印数据权限业务对象")
public class DocPrintDataAuthBo extends BaseEntity {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键", required = true)
    private String id;

    /** 文件分发明细对象Vo **/
    private List<DocDistributeVo> docDistributeVoList;

    /**
     * 用户id;所属用户权限数据
     */
    @ApiModelProperty(value = "用户id;所属用户权限数据", required = true)
    private String userId;

    /**
     * 用户名称;所属用户权限数据
     */
    @ApiModelProperty(value = "用户名称;所属用户权限数据", required = true)
    private String userName;

    /**
     * 文件分发明细id;关联的数据id
     */
    @ApiModelProperty(value = "文件分发明细id;关联的数据id", required = true)
    private String docDistributeId;


    /**
     * 分页大小
     */
    @ApiModelProperty("分页大小")
    private Integer pageSize;

    /**
     * 当前页数
     */
    @ApiModelProperty("当前页数")
    private Integer pageNum;

    /**
     * 排序列
     */
    @ApiModelProperty("排序列")
    private String orderByColumn;

    /**
     * 排序的方向desc或者asc
     */
    @ApiModelProperty(value = "排序的方向", example = "asc,desc")
    private String isAsc;

}

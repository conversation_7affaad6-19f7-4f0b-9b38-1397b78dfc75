package com.rzdata.process.domain.bo;

import com.rzdata.framework.core.domain.BaseEntity;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 文件关联勘误记录业务对象 doc_errata_link
 *
 * <AUTHOR>
 * @date 2025-01-13
 */

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("文件关联勘误记录业务对象")
public class ErrataLinkBo extends BaseEntity {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键", required = true)
    @NotBlank(message = "主键不能为空", groups = { EditGroup.class })
    private String id;

    /**
     * 父id
     */
    @ApiModelProperty(value = "父id", required = true)
    @NotBlank(message = "父id不能为空", groups = { AddGroup.class, EditGroup.class })
    private String parentId;

    /**
     * 关联记录文件编号
     */
    @ApiModelProperty(value = "关联记录文件编号", required = true)
    @NotBlank(message = "关联记录文件编号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String linkCode;

    /**
     * 关联文件名称
     */
    @ApiModelProperty(value = "关联文件名称", required = true)
    @NotBlank(message = "关联文件名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String fileName;

    /**
     * 关联记录文件id
     */
    @ApiModelProperty(value = "关联记录文件id", required = true)
    @NotBlank(message = "关联记录文件id不能为空", groups = { AddGroup.class, EditGroup.class })
    private String fileId;

    /**
     * 关联记录版本ID
     */
    @ApiModelProperty(value = "关联记录版本ID", required = true)
    @NotBlank(message = "关联记录版本ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private String versionId;

    /**
     * 关联记录版本号
     */
    @ApiModelProperty(value = "关联记录版本号", required = true)
    @NotBlank(message = "关联记录版本号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String versionValue;

    /**
     * 关联类型: DOC正文 APPENDIX附件 RECORD记录 REF_DOC关联其他文件 APPENDIX_REMARK备注附件
     */
    @ApiModelProperty(value = "关联类型: DOC正文 APPENDIX附件 RECORD记录 REF_DOC关联其他文件 APPENDIX_REMARK备注附件", required = true)
    @NotBlank(message = "关联类型: DOC正文 APPENDIX附件 RECORD记录 REF_DOC关联其他文件 APPENDIX_REMARK备注附件不能为空", groups = { AddGroup.class, EditGroup.class })
    private String linkType;

    /**
     * 租户id
     */
    @ApiModelProperty(value = "租户id", required = true)
    @NotBlank(message = "租户id不能为空", groups = { AddGroup.class, EditGroup.class })
    private String tenantId;

    /**
     * 关联记录文件类型
     */
    @ApiModelProperty(value = "关联记录文件类型", required = true)
    @NotBlank(message = "关联记录文件类型不能为空", groups = { AddGroup.class, EditGroup.class })
    private String docClass;

    /**
     * 1：有效  2：失效
     */
    @ApiModelProperty(value = "1：有效  2：失效", required = true)
    @NotBlank(message = "1：有效  2：失效不能为空", groups = { AddGroup.class, EditGroup.class })
    private String status;

    /**
     * 生效时间
     */
    @ApiModelProperty(value = "生效时间", required = true)
    @NotNull(message = "生效时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date startDate;

    /**
     * 发布时间
     */
    @ApiModelProperty(value = "发布时间", required = true)
    @NotNull(message = "发布时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date releaseTime;

    /**
     * 失效时间
     */
    @ApiModelProperty(value = "失效时间", required = true)
    @NotNull(message = "失效时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date endDate;


    /**
     * 分页大小
     */
    @ApiModelProperty("分页大小")
    private Integer pageSize;

    /**
     * 当前页数
     */
    @ApiModelProperty("当前页数")
    private Integer pageNum;

    /**
     * 排序列
     */
    @ApiModelProperty("排序列")
    private String orderByColumn;

    /**
     * 排序的方向desc或者asc
     */
    @ApiModelProperty(value = "排序的方向", example = "asc,desc")
    private String isAsc;

}

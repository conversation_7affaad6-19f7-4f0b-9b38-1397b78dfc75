package com.rzdata.process.domain.bo;

import com.rzdata.framework.core.domain.BaseEntity;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import com.rzdata.process.domain.DocExt;
import com.rzdata.process.domain.ErrataLink;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;

import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 勘误文件版本记录业务对象 doc_errata_version
 *
 * <AUTHOR>
 * @date 2025-01-08
 */

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("勘误文件版本记录业务对象")
public class ErrataVersionBo extends DocExt {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键", required = true)
    @NotBlank(message = "主键不能为空", groups = { EditGroup.class })
    private String id;

    /**
     * 版本主键
     */
    @ApiModelProperty(value = "版本主键", required = true)
    private String versionId;

    /**
     * 排序 从0开始 0是原版
     */
    @ApiModelProperty(value = "排序 从0开始 0是原版", required = true)
    private Integer sort;

    private Integer maxSort;

    private Integer neSort;

    /**
     * 文件所属分类
     */
    @ApiModelProperty(value = "文件所属分类", required = true)
    private String docClass;

    /**
     * 标准文件主键
     */
    @ApiModelProperty(value = "标准文件主键", required = true)
    private String standardId;

    /**
     * 文件编号
     */
    @ApiModelProperty(value = "文件编号", required = true)
    private String docId;

    /**
     * 文件名称
     */
    @ApiModelProperty(value = "文件名称", required = true)
    private String docName;

    /**
     * 变更申请流水号
     */
    @ApiModelProperty(value = "变更申请流水号", required = true)
    private String applyId;

    /**
     * 文件版本号
     */
    @ApiModelProperty(value = "文件版本号", required = true)
    private String versionValue;

    /**
     * 版本生效时间
     */
    @ApiModelProperty(value = "版本生效时间", required = true)
    private Date startDate;

    /**
     * 版本生效截止时间
     */
    @ApiModelProperty(value = "版本生效截止时间", required = true)
    private Date endDate;

    /**
     * 版本状态 0=未知 1=有效 2=失效 3=留用
     */
    @ApiModelProperty(value = "版本状态 0=未知 1=有效 2=失效 3=留用", required = true)
    private String status;

    /**
     * 变更原因
     */
    @ApiModelProperty(value = "变更原因", required = true)
    private String reason;

    /**
     * 复审时间
     */
    @ApiModelProperty(value = "复审时间", required = true)
    private Date reviewTime;

    /**
     * 内容概述
     */
    @ApiModelProperty(value = "内容概述", required = true)
    private String content;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注", required = true)
    private String remark;

    /**
     * 变更要素，多个以,隔开
     */
    @ApiModelProperty(value = "变更要素，多个以,隔开", required = true)
    private String changeFactor;

    /**
     * 变更原因
     */
    @ApiModelProperty(value = "变更原因", required = true)
    private String changeReason;

    /**
     * 培训部门
     */
    @ApiModelProperty(value = "培训部门", required = true)
    private String trainDept;

    /**
     * 主文件basic_file表id
     */
    @ApiModelProperty(value = "主文件basic_file表id", required = true)
    private String fileId;

    /**
     * 合稿文件basic_file表id
     */
    @ApiModelProperty(value = "合稿文件basic_file表id", required = true)
    private String mergeFileId;

    /**
     * 加密(签章)文件basic_file表id
     */
    @ApiModelProperty(value = "加密(签章)文件basic_file表id", required = true)
    private String encryptFileId;

    /**
     * 租户id
     */
    @ApiModelProperty(value = "租户id", required = true)
    private String tenantId;

    /**
     * 是否永久有效  0=否  1=是
     */
    @ApiModelProperty(value = "是否永久有效  0=否  1=是", required = true)
    private String forever;

    /**
     * 体系文件stdd、项目文件project
     */
    @ApiModelProperty(value = "体系文件stdd、项目文件project", required = true)
    private String dataType;

    /**
     * file_id的PDF版本
     */
    @ApiModelProperty(value = "file_id的PDF版本", required = true)
    private String pdfFileId;

    /**
     * 发布时间
     */
    @ApiModelProperty(value = "发布时间", required = true)
    private Date releaseTime;

    /**
     * 是否是bom文件 0=否  1=是
     */
    @ApiModelProperty(value = "是否是bom文件 0=否  1=是", required = true)
    private Long isBom;

    /**
     * 上级文件版本ID
     */
    @ApiModelProperty(value = "上级文件版本ID", required = true)
    private String upVersionId;

    /**
     * 上级文件编号
     */
    @ApiModelProperty(value = "上级文件编号", required = true)
    private String parentDocId;

    /**
     * 分类所属类型
DOC文件台账、RECORD记录台账、FOREIGN外来文件
     */
    @ApiModelProperty(value = "分类所属类型", required = true)
    private String classType;

    /**
     *
     */
    @ApiModelProperty(value = "", required = true)
    private String deptId;

    /**
     * 编制人
     */
    @ApiModelProperty(value = "编制人", required = true)
    private String userName;

    /**
     * 变更类型 ADD=新增 UPDATE=修订 DISUSE = 作废
     */
    @ApiModelProperty(value = "变更类型 ADD=新增 UPDATE=修订 DISUSE = 作废", required = true)
    private String changeType;

    /**
     * 触发来源类型 体系文件台账stdd、项目文件台账project、ECN流程pre_change_apply
     */
    @ApiModelProperty(value = "触发来源类型 体系文件台账stdd、项目文件台账project、ECN流程pre_change_apply", required = true)
    private String invokeType;

    /**
     * 触发来源ID 触发来源类型为stdd则为空、project为项目的主键、pre_change_apply为其表的主键
     */
    @ApiModelProperty(value = "触发来源ID 触发来源类型为stdd则为空、project为项目的主键、pre_change_apply为其表的主键", required = true)
    private String invokeId;

    /**
     * 分发类型 公司 company、部门 dept、个人 person、部门和个人 dept_person
     */
    @ApiModelProperty(value = "分发类型 公司 company、部门 dept、个人 person、部门和个人 dept_person", required = true)
    private String distributeType;

    /**
     * 是否上传客户记录 Y是 N否
     */
    @ApiModelProperty(value = "是否上传客户记录 Y是 N否", required = true)
    private String whetherCustomer;

    /**
     *
     */
    @ApiModelProperty(value = "", required = true)
    private Date applyTime;

    /**
     * 产品线
     */
    @ApiModelProperty(value = "产品线", required = true)
    private String productLine;

    /**
     * 工序
     */
    @ApiModelProperty(value = "工序", required = true)
    private String process;

    /**
     * 产品类别 民品 civil  汽车部品 car
     */
    @ApiModelProperty(value = "产品类别 民品 civil  汽车部品 car", required = true)
    private String productType;

    /**
     * 是否有关联文件
     */
    @ApiModelProperty(value = "是否有关联文件", required = true)
    private String haveLinkFile;

    /**
     * 关联程序文件版本ID
     */
    @ApiModelProperty(value = "关联程序文件版本ID", required = true)
    private String programVersionId;

    /**
     * 关联程序文件编号
     */
    @ApiModelProperty(value = "关联程序文件编号", required = true)
    private String programDocId;

    /**
     * 物料编码
     */
    @ApiModelProperty(value = "物料编码", required = true)
    private String partNumber;

    /**
     * 物料描述
     */
    @ApiModelProperty(value = "物料描述", required = true)
    private String partRemark;

    /**
     * 保管部门
     */
    @ApiModelProperty(value = "保管部门", required = true)
    private String custodyDeptId;

    /**
     * 保存期限
     */
    @ApiModelProperty(value = "保存期限", required = true)
    private String shelfLife;

    /**
     * 文件生效日期
     */
    @ApiModelProperty(value = "文件生效日期", required = true)
    private Date fileEffectiveDate;

    /**
     * 修订日期
     */
    @ApiModelProperty(value = "修订日期", required = true)
    private Date revisionDate;

    /**
     * 合规性
     */
    @ApiModelProperty(value = "合规性", required = true)
    private String compliance;

    /**
     * 工厂
     */
    @ApiModelProperty(value = "工厂", required = true)
    private String factorys;

    /**
     * 客户编码
     */
    @ApiModelProperty(value = "客户编码", required = true)
    private String customerCode;

    /**
     * 设备编码
     */
    @ApiModelProperty(value = "设备编码", required = true)
    private String deviceCode;

    /**
     * 设备名称
     */
    @ApiModelProperty(value = "设备名称", required = true)
    private String deviceName;

    /**
     * 产品版本
     */
    @ApiModelProperty(value = "产品版本", required = true)
    private String productVersion;

    /**
     * 内部文件编号
     */
    @ApiModelProperty(value = "内部文件编号", required = true)
    private String internalDocId;

    /**
     * 分页大小
     */
    @ApiModelProperty("分页大小")
    private Integer pageSize;

    /**
     * 当前页数
     */
    @ApiModelProperty("当前页数")
    private Integer pageNum;

    /**
     * 排序列
     */
    @ApiModelProperty("排序列")
    private String orderByColumn;

    /**
     * 排序的方向desc或者asc
     */
    @ApiModelProperty(value = "排序的方向", example = "asc,desc")
    private String isAsc;

    private List<ErrataLink> linkList;

    /**
     * 创建人名称
     */
    private String createName;
}

package com.rzdata.process.domain.bo;

import com.rzdata.framework.core.domain.BaseEntity;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;


/**
 * 文件增发申请文件份数业务对象 doc_extra_apply_doc_number
 *
 * <AUTHOR>
 * @date 2021-12-31
 */

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("文件增发申请文件份数业务对象")
public class ExtraApplyItemBo extends BaseEntity {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键", required = true)
    @NotBlank(message = "主键不能为空", groups = { EditGroup.class })
    private String id;

    /**
     * 流程编号
     */
    @ApiModelProperty(value = "流程编号", required = true)
    @NotBlank(message = "流程编号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String applyId;

    /**
     * 文件编号
     */
    @ApiModelProperty(value = "文件编号", required = true)
    @NotBlank(message = "文件编号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String docId;

    /**
     * 文件类型
     */
    private String docClass;
    /**
     * 文件名称
     */
    private String docName;
    /**
     * 编制部门Id
     */
    private String deptId;

    /**
     * 编制部门名称
     */
    private String deptName;
    /**
     * 版本号
     */
    @ApiModelProperty("版本号")
    private String versionValue;

    /**
     * 文件版本ID
     */
    @ApiModelProperty("版本ID")
    private String versionId;

    /**
     * 租户
     */
    private String tenantId;
    /**
     * 分发类型
     */
    private String distributeType;
    /**
     * 分页大小
     */
    @ApiModelProperty("分页大小")
    private Integer pageSize;

    /**
     * 当前页数
     */
    @ApiModelProperty("当前页数")
    private Integer pageNum;

    /**
     * 排序列
     */
    @ApiModelProperty("排序列")
    private String orderByColumn;

    /**
     * 排序的方向desc或者asc
     */
    @ApiModelProperty(value = "排序的方向", example = "asc,desc")
    private String isAsc;


}

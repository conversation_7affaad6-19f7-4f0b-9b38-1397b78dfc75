package com.rzdata.process.domain.bo;

import com.rzdata.framework.core.domain.BaseEntity;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;


/**
 * 文件推送业务对象 doc_file_push
 *
 * <AUTHOR>
 * @date 2025-08-05
 */

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("文件推送业务对象")
public class FilePushBo extends BaseEntity {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键", required = true)
    @NotBlank(message = "主键不能为空", groups = { EditGroup.class })
    private String id;

    /**
     * 流程id
     */
    @ApiModelProperty(value = "流程id", required = true)
    @NotBlank(message = "流程id不能为空", groups = { AddGroup.class, EditGroup.class })
    private String applyId;

    /**
     * 推送公司Id
     */
    @ApiModelProperty(value = "推送公司Id", required = true)
    @NotBlank(message = "推送公司Id不能为空", groups = { AddGroup.class, EditGroup.class })
    private String pushCompanyId;

    /**
     * 推送公司名称
     */
    @ApiModelProperty(value = "推送公司名称", required = true)
    @NotBlank(message = "推送公司名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String pushCompanyName;

    /**
     * 推送分类Id
     */
    @ApiModelProperty(value = "推送分类Id", required = true)
    @NotBlank(message = "推送分类Id不能为空", groups = { AddGroup.class, EditGroup.class })
    private String pashClassId;

    /**
     * 推送分类名称
     */
    @ApiModelProperty(value = "推送分类名称", required = true)
    @NotBlank(message = "推送分类名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String pashClassName;

    /**
     * 接收人
     */
    @ApiModelProperty(value = "接收人", required = true)
    @NotBlank(message = "接收人不能为空", groups = { AddGroup.class, EditGroup.class })
    private String receiveUserName;

    /**
     * 接收名称
     */
    @ApiModelProperty(value = "接收名称", required = true)
    @NotBlank(message = "接收名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String receiveNickName;

    /**
     * 接收人部门id
     */
    @ApiModelProperty(value = "接收人部门id", required = true)
    @NotBlank(message = "接收人部门id不能为空", groups = { AddGroup.class, EditGroup.class })
    private String receiveDeptId;

    /**
     * 接收人部门名称
     */
    @ApiModelProperty(value = "接收人部门名称", required = true)
    @NotBlank(message = "接收人部门名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String receiveDeptName;

    /**
     * 文件Id
     */
    @ApiModelProperty(value = "文件Id", required = true)
    @NotBlank(message = "文件Id不能为空", groups = { AddGroup.class, EditGroup.class })
    private String fileId;

    /**
     * 推送文件Id
     */
    @ApiModelProperty(value = "推送文件Id", required = true)
    @NotBlank(message = "推送文件Id不能为空", groups = { AddGroup.class, EditGroup.class })
    private String pashVersionId;

    /**
     * 推送文件编号
     */
    @ApiModelProperty(value = "推送文件编号", required = true)
    @NotBlank(message = "推送文件编号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String pashDocId;

    /**
     * 推送文件名称
     */
    @ApiModelProperty(value = "推送文件名称", required = true)
    @NotBlank(message = "推送文件名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String pashDocName;

    /**
     * 推送文件版本号
     */
    @ApiModelProperty(value = "推送文件版本号", required = true)
    @NotBlank(message = "推送文件版本号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String pashVersionValue;

    /**
     * 文件版本Id
     */
    @ApiModelProperty(value = "文件版本Id", required = true)
    @NotBlank(message = "文件版本Id不能为空", groups = { AddGroup.class, EditGroup.class })
    private String versionId;

    /**
     * 推送状态
     */
    @ApiModelProperty(value = "推送状态", required = true)
    @NotBlank(message = "推送状态不能为空", groups = { AddGroup.class, EditGroup.class })
    private String status;


    /**
     * 分页大小
     */
    @ApiModelProperty("分页大小")
    private Integer pageSize;

    /**
     * 当前页数
     */
    @ApiModelProperty("当前页数")
    private Integer pageNum;

    /**
     * 排序列
     */
    @ApiModelProperty("排序列")
    private String orderByColumn;

    /**
     * 排序的方向desc或者asc
     */
    @ApiModelProperty(value = "排序的方向", example = "asc,desc")
    private String isAsc;

}

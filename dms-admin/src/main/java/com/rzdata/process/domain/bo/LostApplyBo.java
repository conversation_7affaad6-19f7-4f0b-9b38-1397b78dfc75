package com.rzdata.process.domain.bo;

import com.rzdata.framework.core.domain.BaseEntity;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import com.rzdata.process.domain.LostApplyItem;
import com.rzdata.process.domain.ReissueApplyItem;
import com.rzdata.setting.domain.DocPresetUser;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;

import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 文件丢失申请业务对象 doc_lost_apply
 *
 * <AUTHOR>
 * @date 2024-04-23
 */

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("文件丢失申请业务对象")
public class LostApplyBo extends BaseEntity {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键", required = true)
    @NotBlank(message = "主键不能为空", groups = { EditGroup.class })
    private String id;

    /**
     * 申请标题
     */
    @ApiModelProperty(value = "申请标题", required = true)
    private String applyTitle;

    /**
     * 申请类型(默认REISSUE)
     */
    @ApiModelProperty(value = "申请类型(默认REISSUE) ", required = true)
    private String applyType;

    /**
     * 申请部门Id
     */
    @ApiModelProperty(value = "申请部门Id", required = true)
    private String deptId;

    /**
     * 申请部门名称
     */
    @ApiModelProperty(value = "申请部门名称", required = true)
    private String deptName;

    /**
     * 申请人
     */
    @ApiModelProperty(value = "申请人", required = true)
    private String userName;

    /**
     * 申请人名称
     */
    @ApiModelProperty(value = "申请人名称", required = true)
    private String nickName;

    /**
     * 文件版本ID
     */
    @ApiModelProperty(value = "文件版本ID", required = true)
    private String versionId;

    /**
     * 文件编号
     */
    @ApiModelProperty(value = "文件编号", required = true)
    private String docId;

    /**
     * 文件名称
     */
    @ApiModelProperty(value = "文件名称", required = true)
    private String docName;

    /**
     * 申请时间
     */
    @ApiModelProperty(value = "申请时间", required = true)
    private Date applyTime;

    /**
     * 版本号
     */
    @ApiModelProperty(value = "版本号", required = true)
    private String versionValue;

    /**
     * 文件类型
     */
    @ApiModelProperty(value = "文件类型", required = true)
    private String docClass;

    /**
     * 是否外发
     */
    @ApiModelProperty(value = "是否外发", required = true)
    private String isPuttingOut;

    /**
     * 申请原因
     */
    @ApiModelProperty(value = "申请原因", required = true)
    private String reason;

    /**
     * 丢失后影响
     */
    @ApiModelProperty(value = "丢失后影响", required = true)
    private String effect;

    /**
     * 后期预防措施
     */
    @ApiModelProperty(value = "后期预防措施", required = true)
    private String precaution;

    /**
     * 申请状态
     */
    @ApiModelProperty(value = "申请状态", required = true)
    private String status;

    /**
     * 租户id
     */
    @ApiModelProperty(value = "租户id", required = true)
    private String tenantId;


    /**
     * 分页大小
     */
    @ApiModelProperty("分页大小")
    private Integer pageSize;

    /**
     * 当前页数
     */
    @ApiModelProperty("当前页数")
    private Integer pageNum;

    /**
     * 排序列
     */
    @ApiModelProperty("排序列")
    private String orderByColumn;

    /**
     * 排序的方向desc或者asc
     */
    @ApiModelProperty(value = "排序的方向", example = "asc,desc")
    private String isAsc;

    private String recordStatus;

    private Boolean editStatus;

    private BpmClientInputModelBo bpmClientInputModel;

    private List<LostApplyItem> itemList;

    private Boolean presetUserEdit;

    private List<DocPresetUser> presetUserList;
}

package com.rzdata.process.domain.bo;

import com.rzdata.framework.core.domain.BaseEntity;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 文件丢失申请详情业务对象 doc_lost_apply_item
 *
 * <AUTHOR>
 * @date 2024-04-23
 */

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("文件丢失申请详情业务对象")
public class LostApplyItemBo extends BaseEntity {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键", required = true)
    @NotBlank(message = "主键不能为空", groups = { EditGroup.class })
    private String id;

    /**
     * 流程编号
     */
    @ApiModelProperty(value = "流程编号", required = true)
    @NotBlank(message = "流程编号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String applyId;

    /**
     * 分发表主键
     */
    private String distributeId;

    /**
     * 分发号;例如01、02
     */
    @ApiModelProperty(value = "分发号;例如01、02", required = true)
    @NotNull(message = "分发号;例如01、02不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long code;

    /**
     * 签收人
     */
    @ApiModelProperty(value = "签收人", required = true)
    @NotBlank(message = "签收人不能为空", groups = { AddGroup.class, EditGroup.class })
    private String receiveUserName;

    /**
     * 签收人昵称
     */
    @ApiModelProperty(value = "签收人昵称", required = true)
    @NotBlank(message = "签收人昵称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String receiveNickName;

    /**
     * 签收人部门id
     */
    @ApiModelProperty(value = "签收人部门id", required = true)
    @NotBlank(message = "签收人部门id不能为空", groups = { AddGroup.class, EditGroup.class })
    private String receiveUserDeptId;

    /**
     * 签收人部门名称
     */
    @ApiModelProperty(value = "签收人部门名称", required = true)
    @NotBlank(message = "签收人部门名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String receiveUserDept;

    /**
     * 签收时间
     */
    @ApiModelProperty(value = "签收时间", required = true)
    @NotNull(message = "签收时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date receiveTime;

    /**
     * 状态;未签收、已签收、已打印、已回收、已丢失
     */
    @ApiModelProperty(value = "状态;未签收、已签收、已打印、已回收、已丢失", required = true)
    @NotBlank(message = "状态;未签收、已签收、已打印、已回收、已丢失不能为空", groups = { AddGroup.class, EditGroup.class })
    private String status;


    /**
     * 分页大小
     */
    @ApiModelProperty("分页大小")
    private Integer pageSize;

    /**
     * 当前页数
     */
    @ApiModelProperty("当前页数")
    private Integer pageNum;

    /**
     * 排序列
     */
    @ApiModelProperty("排序列")
    private String orderByColumn;

    /**
     * 排序的方向desc或者asc
     */
    @ApiModelProperty(value = "排序的方向", example = "asc,desc")
    private String isAsc;

}

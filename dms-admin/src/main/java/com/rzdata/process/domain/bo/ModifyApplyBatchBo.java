package com.rzdata.process.domain.bo;

import com.rzdata.framework.core.domain.BaseEntity;
import com.rzdata.setting.domain.DocPresetUser;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 文件变更操作申请业务对象 doc_modify_apply
 *
 * <AUTHOR>
 * @date 2021-12-31
 */

@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel("文件变更操作申请业务对象")
public class ModifyApplyBatchBo extends BaseEntity {

    private String batchId;

    private String changeType;

    private Boolean editStatus;

    private Boolean presetUserEdit;

    private Boolean customerEdit;

    /**
     * 只修改基础数据 Y是 N否
     */
    private Boolean onlyEdit;

    private String procTitle;
    /**
     * 记录状态
     */
    @ApiModelProperty(value = "记录状态", required = true)
    private String recordStatus;

    private BpmClientInputModelBo bpmClientInputModel;

    private List<ModifyApplyBo> dataList;

    private List<DocPresetUser> presetUserList;
}

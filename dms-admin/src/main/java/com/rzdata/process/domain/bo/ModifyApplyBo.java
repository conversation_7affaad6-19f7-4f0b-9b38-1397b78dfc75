package com.rzdata.process.domain.bo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.rzdata.framework.core.domain.BaseEntity;
import com.rzdata.setting.domain.DocPresetUser;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.Year;
import java.util.Date;
import java.util.List;

/**
 * 文件变更操作申请业务对象 doc_modify_apply
 *
 * <AUTHOR>
 * @date 2021-12-31
 */

@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel("文件变更操作申请业务对象")
public class ModifyApplyBo extends BaseEntity {

    /**
     * 主键，根据编码规则生成
     */
    @ApiModelProperty(value = "主键，根据编码规则生成", required = true)
    private String id;

    /**
     * 触发来源类型 体系文件台账stdd、项目文件台账project、ECN流程pre_change_apply
     */
    private String invokeType;

    /**
     * 触发来源ID 触发来源类型为stdd则为空、project为项目的主键、pre_change_apply为其表的主键
     */
    private String invokeId;

    /**
     * 所属项目ID
     */
    private String projectId;

    /**
     * 所属项目名字
     */
    private String projectName;

    /**
     * ECN流程编号
     */
    private String preChangeCode;

    /**
     * 变更类型
     */
    @ApiModelProperty(value = "变更类型", required = true)
    private String changeType;

    /**
     * 文件类型
     */
    @ApiModelProperty(value = "文件类型", required = true)
    private String docClass;

    /**
     * 文件名称
     */
    @ApiModelProperty(value = "文件名称", required = true)
    private String docName;

    /**
     * 文件编号
     */
    @ApiModelProperty(value = "文件编号", required = true)
    private String docId;

    /**
     * 编制部门编号
     */
    @ApiModelProperty(value = "编制部门编号", required = true)
    private String deptId;

    /**
     * 编制人
     */
    @ApiModelProperty(value = "编制人", required = true)
    private String userName;

    /**
     * 申请时间
     */
    @ApiModelProperty(value = "申请时间", required = true)
    private Date applyTime;

    /**
     * 文件有效期
     */
    @ApiModelProperty(value = "文件有效期", required = true)
    private Long expiration;

    /**
     * 版本号
     */
    @ApiModelProperty(value = "版本号")
    private String versionValue;

    /**
     * 文件版本ID
     */
    @ApiModelProperty("版本ID")
    private String versionId;

    /**
     * 编制正文
     */
    @ApiModelProperty(value = "编制正文")
    private ModifyApplyLinkBo standardDoc;

    /**
     * 备注附件
     */
    @ApiModelProperty(value = "备注附件")
    private List<ModifyApplyLinkBo> remarkDoc;

    /**
     * 附件
     */
    @ApiModelProperty(value = "附件文件")
    private List<ModifyApplyLinkBo> appendixes;

    /**
     * 是否培训
     */
    @ApiModelProperty(value = "是否培训", required = true)
    @JsonProperty("yNTrain")
    private String yNTrain;

    /**
     * 培训部门
     */
    @ApiModelProperty(value = "培训部门", required = true)
    private String trainDept;

    /**
     * 分发列表
     */
    @ApiModelProperty(value = "分发列表", required = true)
    private List<ModifyApplyDistributeBo> distributeList;

    /**
     * 关联文件
     */
    @ApiModelProperty(value = "关联文件")
    private List<ModifyApplyLinkBo> docLinks;

    /**
     * 关联或新增记录
     */
    @ApiModelProperty(value = "关联或新增记录")
    private List<ModifyApplyLinkBo> recordLinks;

    /**
     * 关联文件 多对多
     */
    @ApiModelProperty(value = "关联文件 多对多")
    private List<ModifyApplyLinkBo> noteLinks;

    /**
     * 主文件
     */
    @ApiModelProperty(value = "主文件")
    private List<ModifyApplyLinkBo> noteDocLinks;

    /**
     * 培训记录
     */
    @ApiModelProperty(value = "培训记录")
    private List<ModifyApplyTrainBo> trains;

    /**
     * 是否已合稿
     */
    @ApiModelProperty(value = "是否已合稿", required = true)
    @JsonProperty("yNMergeDraft")
    private String yNMergeDraft;

    /**
     * 是否已签章
     */
    @ApiModelProperty(value = "是否已签章", required = true)
    @JsonProperty("yNEncrypt")
    private String yNEncrypt;

    /**
     * 是否分发
     */
    @ApiModelProperty(value = "是否分发", required = true)
    @JsonProperty("yNDistribute")
    private String yNDistribute;

    /**
     * 编制文件编号
     */
    @ApiModelProperty(value = "编制文件编号", required = true)
    private String fileId;

    /**
     * 主文档PDF的file_id
     */
    private String pdfFileId;

    /**
     * 加密(签章)文件basic_file表id
     */
    private String encryptFileId;

    /**
     * 记录状态
     */
    @ApiModelProperty(value = "记录状态", required = true)
    private String recordStatus;

    /**
     * 变更原因
     */
    @ApiModelProperty(value = "变更原因", required = true)
    private String changeReason;

    /**
     * 内容概述
     */
    @ApiModelProperty(value = "内容概述", required = true)
    private String content;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注", required = true)
    private String remark;

    /**
     * 变更要素，多个以,隔开
     */
    @ApiModelProperty(value = "变更要素，多个以,隔开", required = true)
    private String changeFactor;

    private String changeId;

    /**
     * 分页大小
     */
    @ApiModelProperty("分页大小")
    private Integer pageSize;

    /**
     * 当前页数
     */
    @ApiModelProperty("当前页数")
    private Integer pageNum;

    /**
     * 排序列
     */
    @ApiModelProperty("排序列")
    private String orderByColumn;

    /**
     * 排序的方向desc或者asc
     */
    @ApiModelProperty(value = "排序的方向", example = "asc,desc")
    private String isAsc;

    /**
     * 流程状态
     */
    @ApiModelProperty("流程状态")
    private String processStatus;

    private BpmClientInputModelBo bpmClientInputModel;
    /**
     * 是否已签章 Y=已 N=未 默认N
     */
    private String isSignature;
    /**
     * 设置生效时间
     */
    private Date setupTime;

    private String dataType;

    private Boolean editStatus;

    private Boolean presetUserEdit;
    /**
     * 上级文件版本ID
     */
    private String upVersionId;

    /**
     * 上级文件编号
     */
    private String parentDocId;

    /**
     * 分类所属类型
     * DOC文件台账、RECORD记录台账、FOREIGN外来文件
     */
    private String classType;

    private List<DocPresetUser> presetUserList;

    /**
     * 步骤进度
     */
    private Integer step;

    /**
     * 批次
     */
    private String batch;

    /**
     * 发布时间
     */
    private Date releaseTime;

    /**
     * 分发类型 公司 company、部门 dept、个人 person、部门和个人 dept_person
     */
    private String distributeType;

    /**
     * 分发类型 公司 company、部门 dept、个人 person、部门和个人 dept_person
     */
    private String trainType;

    /**
     * 合稿后文件PdfId
     */
    private String coverFileId;


    /**
     * 是否上传客户记录 Y是 N否
     */
    private String whetherCustomer;

    /**
     * 文件状态
     */
    private String docStatus;

    /**
     * 是否留用 Y是 N否
     */
    private String whetherRetain;

    /**
     * 流程截止日期
     */
    private Date deadline;

    /**
     * 留用截止日期
     */
    private Date retainDeadline;

    private Boolean customerEdit;

    /**
     * 只修改基础数据 Y是 N否
     */
    private Boolean onlyEdit;

    /**
     * 合规性
     */
    private String compliance;

    /**
     * 批次Id
     */
    private String batchId;

    private String secDeptId;

    /**
     * 内部文件编号
     */
    private String internalDocId;

    @TableField(exist = false)
    private List<String> neApplyClass;

    /***********扩展字段**************/
    private String ext1;
    private String ext2;
    private String ext3;
    private String ext4;
    private String ext5;
    private String ext6;
    private String ext7;
    private String ext8;
    private String ext9;
    private String ext10;
    private String ext11;
    private String ext12;
    private String ext13;
    private String ext14;
    private String ext15;
    private String ext16;
    private String ext17;
    private String ext18;
    private String ext19;
    private String ext20;

    private Boolean notUpdateTitle;

    /**
     * 申请id临时使用，用来更新站内消息的请求地址
     */
    @TableField(exist = false)
    private String applyIdTemp;
}

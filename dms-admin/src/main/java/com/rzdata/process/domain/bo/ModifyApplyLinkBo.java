package com.rzdata.process.domain.bo;

import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.*;
import java.util.Date;


/**
 * 文件变更操作申请引用业务对象 doc_modify_apply_link
 *
 * <AUTHOR>
 * @date 2021-12-31
 */

@Data
@ApiModel("文件变更操作申请引用业务对象")
public class ModifyApplyLinkBo {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键", required = false)
    private String id;

    /**
     * 流程编号
     */
    @ApiModelProperty(value = "流程编号", required = false)
    private String applyId;

    /**
     * basic_file主键
     */
    private String fileId;

    /**
     * 关联编号
     */
    @ApiModelProperty(value = "关联编号", required = true)
    @NotBlank(message = "关联编号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String linkId;

    /**
     * 关联类别: doc file
     */
    @ApiModelProperty(value = "关联类别: doc file", required = true)
    private String linkClass;

    /**
     * 关联类型:正文 附件 关联文件 关联记录
     */
    @ApiModelProperty(value = "关联类型:正文 附件 关联文件 关联记录", required = false)
    private String linkType;

    /**
     * 文件名称
     */
    @ApiModelProperty(value = "文件名称", required = false)
    private String docName;
    /**
     * 文件类型
     */
    @ApiModelProperty(value = "文件类型", required = false)
    private String docClass;
    /**
     * 版本号
     */
    @ApiModelProperty(value = "版本号")
    private String versionValue;


    /**
     * 文件版本ID
     */
    @ApiModelProperty("版本ID")
    private String versionId;

    /**
     * 是否删除 1 是
     */
    @ApiModelProperty(value = "是否删除 1 是", required = false)
    private Integer isDeleted;

    private Integer status;

    private String docId;

    /**
     * 生效时间
     */
    private Date startDate;

    /**
     * 发布时间
     */
    private Date releaseTime;

    /**
     * 原始附件id
     */
    private String protoFileId;

    private String fileName;
}

package com.rzdata.process.domain.bo;

import com.rzdata.framework.core.domain.BaseEntity;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 同步文档日志业务对象 oss_file_log
 *
 * <AUTHOR>
 * @date 2023-09-01
 */

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("同步文档日志业务对象")
public class OssFileLogBo extends BaseEntity {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键", required = true)
    @NotBlank(message = "主键不能为空", groups = { EditGroup.class })
    private String id;

    /**
     * 租户id
     */
    @ApiModelProperty(value = "租户id", required = true)
    @NotBlank(message = "租户id不能为空", groups = { AddGroup.class, EditGroup.class })
    private String tenantId;

    /**
     * 文件台账ID
     */
    @ApiModelProperty(value = "文件台账ID", required = true)
    @NotBlank(message = "文件台账ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private String docStandardId;

    /**
     * 文件版本ID
     */
    @ApiModelProperty(value = "文件版本ID", required = true)
    @NotBlank(message = "文件版本ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private String docVersionId;

    /**
     * 文件名称
     */
    @ApiModelProperty(value = "文件名称", required = true)
    @NotBlank(message = "文件名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String docName;

    /**
     * 触发类型;触发类型：ADD新增/DISUSE作废/INIT初始化/REBUILD重新生成（根据doc_standard_id）
     */
    @ApiModelProperty(value = "触发类型;触发类型：ADD新增/DISUSE作废/INIT初始化/REBUILD重新生成（根据doc_standard_id）", required = true)
    @NotBlank(message = "触发类型;触发类型：ADD新增/DISUSE作废/INIT初始化/REBUILD重新生成（根据doc_standard_id）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String invokeType;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间", required = true)
    @NotNull(message = "开始时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date beginTime;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间", required = true)
    @NotNull(message = "结束时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date finishTime;

    /**
     * 耗时秒
     */
    @ApiModelProperty(value = "耗时秒", required = true)
    @NotNull(message = "耗时秒不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long costTime;

    /**
     * 消息;异常消息
     */
    @ApiModelProperty(value = "消息;异常消息", required = true)
    @NotBlank(message = "消息;异常消息不能为空", groups = { AddGroup.class, EditGroup.class })
    private String message;

    /**
     * 状态;Y正常 N异常
     */
    @ApiModelProperty(value = "状态;Y正常 N异常", required = true)
    @NotBlank(message = "状态;Y正常 N异常不能为空", groups = { AddGroup.class, EditGroup.class })
    private String status;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注", required = true)
    @NotBlank(message = "备注不能为空", groups = { AddGroup.class, EditGroup.class })
    private String remark;


    /**
     * 分页大小
     */
    @ApiModelProperty("分页大小")
    private Integer pageSize;

    /**
     * 当前页数
     */
    @ApiModelProperty("当前页数")
    private Integer pageNum;

    /**
     * 排序列
     */
    @ApiModelProperty("排序列")
    private String orderByColumn;

    /**
     * 排序的方向desc或者asc
     */
    @ApiModelProperty(value = "排序的方向", example = "asc,desc")
    private String isAsc;

}

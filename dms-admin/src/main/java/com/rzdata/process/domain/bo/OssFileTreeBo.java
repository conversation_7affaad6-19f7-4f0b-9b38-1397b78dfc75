package com.rzdata.process.domain.bo;

import com.rzdata.framework.core.domain.BaseEntity;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;


/**
 * 同步文档结构主业务对象 oss_file_tree
 *
 * <AUTHOR>
 * @date 2023-09-01
 */

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("同步文档结构主业务对象")
public class OssFileTreeBo extends BaseEntity {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键", required = true)
    @NotBlank(message = "主键不能为空", groups = { EditGroup.class })
    private String id;

    /**
     * 租户id
     */
    @ApiModelProperty(value = "租户id", required = true)
    @NotBlank(message = "租户id不能为空", groups = { AddGroup.class, EditGroup.class })
    private String tenantId;

    /**
     * 文档库;AS7文档库
     */
    @ApiModelProperty(value = "文档库;AS7文档库", required = true)
    @NotBlank(message = "文档库;AS7文档库不能为空", groups = { AddGroup.class, EditGroup.class })
    private String library;

    /**
     * 名称;目录名称或文件名称
     */
    @ApiModelProperty(value = "名称;目录名称或文件名称", required = true)
    @NotBlank(message = "名称;目录名称或文件名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String name;

    /**
     * 父级ID
     */
    @ApiModelProperty(value = "父级ID", required = true)
    @NotBlank(message = "父级ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private String parentId;

    /**
     * 节点类型;数据类型：DIR目录/FILE文件
     */
    @ApiModelProperty(value = "节点类型;数据类型：DIR目录/FILE文件", required = true)
    @NotBlank(message = "节点类型;数据类型：DIR目录/FILE文件不能为空", groups = { AddGroup.class, EditGroup.class })
    private String nodeType;

    /**
     * 文件类型;文件类型：DOC主文件/RECOD关联记录/REF_DOC关联文件
     */
    @ApiModelProperty(value = "文件类型;文件类型：DOC主文件/RECOD关联记录/REF_DOC关联文件", required = true)
    @NotBlank(message = "文件类型;文件类型：DOC主文件/RECOD关联记录/REF_DOC关联文件不能为空", groups = { AddGroup.class, EditGroup.class })
    private String fileType;

    /**
     * 层级
     */
    @ApiModelProperty(value = "层级", required = true)
    @NotNull(message = "层级不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long level;

    /**
     * 排序;数字越小，靠前
     */
    @ApiModelProperty(value = "排序;数字越小，靠前", required = true)
    @NotBlank(message = "排序;数字越小，靠前不能为空", groups = { AddGroup.class, EditGroup.class })
    private String sort;

    /**
     * 标准台账ID
     */
    @ApiModelProperty(value = "标准台账ID", required = true)
    @NotBlank(message = "标准台账ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private String docStandardId;

    /**
     * 标准版本ID
     */
    @ApiModelProperty(value = "标准版本ID", required = true)
    @NotBlank(message = "标准版本ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private String docVersionId;

    /**
     * 基础文件ID
     */
    @ApiModelProperty(value = "基础文件ID", required = true)
    @NotBlank(message = "基础文件ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private String basicFileId;

    /**
     * 状态;Y有效 N失效 E错误
     */
    @ApiModelProperty(value = "状态;Y有效 N失效 E错误", required = true)
    @NotBlank(message = "状态;Y有效 N失效 E错误不能为空", groups = { AddGroup.class, EditGroup.class })
    private String status;

    /**
     * 消息
     */
    @ApiModelProperty(value = "消息", required = true)
    @NotBlank(message = "消息不能为空", groups = { AddGroup.class, EditGroup.class })
    private String message;

    /**
     * 文件外部存储ID
     */
    @ApiModelProperty(value = "文件外部存储ID", required = true)
    @NotBlank(message = "文件外部存储ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private String externalFileId;

    /**
     * 文件外部存储路径
     */
    @ApiModelProperty(value = "文件外部存储路径", required = true)
    @NotBlank(message = "文件外部存储路径不能为空", groups = { AddGroup.class, EditGroup.class })
    private String externalFilePath;

    /**
     * 
     */
    @ApiModelProperty(value = "", required = true)
    @NotBlank(message = "不能为空", groups = { AddGroup.class, EditGroup.class })
    private String externalRev;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注", required = true)
    @NotBlank(message = "备注不能为空", groups = { AddGroup.class, EditGroup.class })
    private String remark;


    /**
     * 分页大小
     */
    @ApiModelProperty("分页大小")
    private Integer pageSize;

    /**
     * 当前页数
     */
    @ApiModelProperty("当前页数")
    private Integer pageNum;

    /**
     * 排序列
     */
    @ApiModelProperty("排序列")
    private String orderByColumn;

    /**
     * 排序的方向desc或者asc
     */
    @ApiModelProperty(value = "排序的方向", example = "asc,desc")
    private String isAsc;

}

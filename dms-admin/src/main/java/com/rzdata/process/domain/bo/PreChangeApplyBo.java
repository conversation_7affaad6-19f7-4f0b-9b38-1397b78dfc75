package com.rzdata.process.domain.bo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.rzdata.framework.core.domain.BaseEntity;
import com.rzdata.framework.core.validate.EditGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import java.util.Date;
import java.util.List;


/**
 * 前置变更申请业务对象 pre_change_apply
 *
 * <AUTHOR>
 * @date 2023-06-25
 */

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("前置变更申请业务对象")
public class PreChangeApplyBo extends BaseEntity {

    /**
     * 变更唯一编号
     */
    @ApiModelProperty(value = "变更唯一编号", required = true)
    @NotBlank(message = "变更唯一编号不能为空", groups = { EditGroup.class })
    private String id;

    /**
     * 租户id
     */
    @ApiModelProperty(value = "租户id", required = true)
    private String tenantId;

    /**
     * 变更业务编号
     */
    @ApiModelProperty(value = "变更业务编号", required = true)
    private String bizCode;

    /**
     * 标题
     */
    @ApiModelProperty(value = "标题", required = true)
    private String title;

    /**
     * 所属项目ID
     */
    @ApiModelProperty(value = "所属项目ID", required = true)
    private String projectId;

    /**
     * 所属项目名称
     */
    @ApiModelProperty(value = "所属项目名称", required = true)
    private String projectName;

    /**
     * 变更类型;可多选，json格式
     */
    @ApiModelProperty(value = "变更类型;可多选，json格式", required = true)
    private String changeType;

    /**
     * 变更原因
     */
    @ApiModelProperty(value = "变更原因", required = true)
    private String changeReason;

    /**
     * 变更描述
     */
    @ApiModelProperty(value = "变更描述", required = true)
    private String description;

    /**
     * 对原材料影响标识;Y有影响 N无影响
     */
    @ApiModelProperty(value = "对原材料影响标识;Y有影响 N无影响", required = true)
    private String effectRawFlag;

    /**
     * 对半成品影响标识;Y有影响 N无影响
     */
    @ApiModelProperty(value = "对半成品影响标识;Y有影响 N无影响", required = true)
    private String effectHalfFlag;

    /**
     * 对成品影响标识;Y有影响 N无影响
     */
    @ApiModelProperty(value = "对成品影响标识;Y有影响 N无影响", required = true)
    private String effectProdFlag;

    /**
     * 影响内容标识;Y有影响 N无影响
     */
    @ApiModelProperty(value = "影响内容标识;Y有影响 N无影响", required = true)
    private String effectContentFlag;

    /**
     * 影响内容;可多选，json格式
     */
    @ApiModelProperty(value = "影响内容;可多选，json格式", required = true)
    private String effectContent;

    /**
     * 影响文件标识;Y有影响 N无影响
     */
    @ApiModelProperty(value = "影响文件标识;Y有影响 N无影响", required = true)
    private String effectFileFlag;

    /**
     * 影响文件;可多选，json格式
     */
    @ApiModelProperty(value = "影响文件;可多选，json格式", required = true)
    private String effectFile;

    /**
     * 影响市场/用户标识;Y有影响 N无影响
     */
    @ApiModelProperty(value = "影响市场/用户标识;Y有影响 N无影响", required = true)
    private String effectUserFlag;

    /**
     * 影响市场/用户措施
     */
    @ApiModelProperty(value = "影响市场/用户措施", required = true)
    private String effectUserCure;

    /**
     * 影响销售产品标识;Y有影响 N无影响
     */
    @ApiModelProperty(value = "影响销售产品标识;Y有影响 N无影响", required = true)
    private String effectProductFlag;

    /**
     * 影响销售产品措施
     */
    @ApiModelProperty(value = "影响销售产品措施", required = true)
    private String effectProductCure;

    /**
     * 影响供应商标识;Y有影响 N无影响
     */
    @ApiModelProperty(value = "影响供应商标识;Y有影响 N无影响", required = true)
    private String effectSupplierFlag;

    /**
     * 影响供应商措施
     */
    @ApiModelProperty(value = "影响供应商措施", required = true)
    private String effectSupplierCure;

    /**
     * 影响其他描述
     */
    @ApiModelProperty(value = "影响其他描述", required = true)
    private String effectOtherDesc;

    /**
     * 影响其他措施
     */
    @ApiModelProperty(value = "影响其他措施", required = true)
    private String effectOtherCure;

    /**
     * 是否需要试生产;Y是 N否
     */
    @ApiModelProperty(value = "是否需要试生产;Y是 N否", required = true)
    private String trialProdFlag;

    /**
     * 产品名称
     */
    @ApiModelProperty(value = "产品名称", required = true)
    private String productName;

    /**
     * 批次号
     */
    @ApiModelProperty(value = "批次号", required = true)
    private String lot;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态", required = true)
    private String status;

    /**
     * 流程实例ID
     */
    @ApiModelProperty(value = "流程实例ID", required = true)
    private String procInstId;

    /**
     * 流程实例状态
     */
    @ApiModelProperty(value = "流程实例状态", required = true)
    private String bpmnStatus;

    /**
     * 发布时间
     */
    @ApiModelProperty(value = "发布时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date releaseTime;

    /**
     * 创建人姓名
     */
    @ApiModelProperty(value = "流程实例状态", required = true)
    private String createName;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注", required = true)
    private String remark;


    /**
     * 分页大小
     */
    @ApiModelProperty("分页大小")
    private Integer pageSize;

    /**
     * 当前页数
     */
    @ApiModelProperty("当前页数")
    private Integer pageNum;

    /**
     * 排序列
     */
    @ApiModelProperty("排序列")
    private String orderByColumn;

    /**
     * 排序的方向desc或者asc
     */
    @ApiModelProperty(value = "排序的方向", example = "asc,desc")
    private String isAsc;


    /**
     * 文件任务列表
     */
    @ApiModelProperty(value = "文件任务列表")
    private List<BasicTaskBo> fileTaskList;

    /**
     * 空白任务列表
     */
    @ApiModelProperty(value = "文件任务列表")
    private List<BasicTaskBo> blankTaskList;

}

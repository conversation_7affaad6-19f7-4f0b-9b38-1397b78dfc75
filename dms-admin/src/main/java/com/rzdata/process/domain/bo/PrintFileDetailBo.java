package com.rzdata.process.domain.bo;

import com.rzdata.framework.core.domain.BaseEntity;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;


/**
 * 文件打印明细业务对象 doc_print_file_detail
 *
 * <AUTHOR>
 * @date 2024-04-28
 */

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("文件打印明细业务对象")
public class PrintFileDetailBo extends BaseEntity {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键", required = true)
    @NotBlank(message = "主键不能为空", groups = { EditGroup.class })
    private String id;

    /**
     * 关联id（basic_file_pdf表id）
     */
    @ApiModelProperty(value = "关联id（basic_file_pdf表id）", required = true)
    @NotBlank(message = "关联id（basic_file_pdf表id）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String bizId;

    /**
     * 文件名称
     */
    @ApiModelProperty(value = "文件名称", required = true)
    @NotBlank(message = "文件名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String fileName;

    /**
     * 打印人
     */
    @ApiModelProperty(value = "打印人", required = true)
    @NotBlank(message = "打印人不能为空", groups = { AddGroup.class, EditGroup.class })
    private String printUserName;

    /**
     * 打印人名称
     */
    @ApiModelProperty(value = "打印人名称", required = true)
    @NotBlank(message = "打印人名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String printUserNickName;


    /**
     * 分页大小
     */
    @ApiModelProperty("分页大小")
    private Integer pageSize;

    /**
     * 当前页数
     */
    @ApiModelProperty("当前页数")
    private Integer pageNum;

    /**
     * 排序列
     */
    @ApiModelProperty("排序列")
    private String orderByColumn;

    /**
     * 排序的方向desc或者asc
     */
    @ApiModelProperty(value = "排序的方向", example = "asc,desc")
    private String isAsc;

}

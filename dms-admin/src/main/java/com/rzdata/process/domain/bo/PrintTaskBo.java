package com.rzdata.process.domain.bo;

import com.rzdata.framework.core.domain.BaseEntity;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;

/**
 * 打印任务业务对象 doc_print_task
 *
 * <AUTHOR>
 * @date 2024-01-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("打印任务业务对象")
public class PrintTaskBo extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private String id;

    public void setTaskId(String taskId) {
        this.id = taskId;
    }
    public String getTaskId() {
        return this.id;
    }

    /**
     * 文件编号
     */
    @ApiModelProperty(value = "文件编号")
    private String docId;

    /**
     * 文件名称
     */
    @ApiModelProperty(value = "文件名称")
    private String docName;

    /**
     * 文件类型
     */
    @ApiModelProperty(value = "文件类型")
    private String docType;

    /**
     * 电子文件名称
     */
    @ApiModelProperty(value = "电子文件名称")
    private String electronicFileName;

    /**
     * 分类号
     */
    @ApiModelProperty(value = "分类号")
    private String classificationNo;

    /**
     * 文件版本
     */
    @ApiModelProperty(value = "文件版本", required = true)
    @NotBlank(message = "文件版本不能为空", groups = { AddGroup.class, EditGroup.class })
    private String versionId;

    /**
     * 打印份数
     */
    @ApiModelProperty(value = "打印份数", required = true)
    @NotNull(message = "打印份数不能为空", groups = { AddGroup.class, EditGroup.class })
    @Min(value = 1, message = "打印份数必须大于0")
    private Integer printCount;
    /**
     * 已打印份数
     */
    @ApiModelProperty(value = "已打印份数", required = true)
    private Integer printedCount;

    /**
     * 打印状态
     */
    @ApiModelProperty(value = "打印状态", required = true)
    private String status;
    public void setTaskStatus(String status) {
        this.status = status;
    }
    public String getTaskStatus() {
        return this.status;
    }

    /**
     * 打印机名称
     */
    @ApiModelProperty(value = "打印机名称", required = true)
    @NotBlank(message = "打印机名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String printerName;

    /**
     * 打印描述
     */
    @ApiModelProperty(value = "打印描述")
    private String printDesc;

    private void setTaskDesc(String taskDesc) {
        this.printDesc = taskDesc;
    }
    private String getTaskDesc() {
        return this.printDesc;
    }

    /**
     * 分页大小
     */
    @ApiModelProperty("分页大小")
    private Integer pageSize;

    /**
     * 当前页数
     */
    @ApiModelProperty("当前页数")
    private Integer pageNum;

    /**
     * 排序列
     */
    @ApiModelProperty("排序列")
    private String orderByColumn;

    /**
     * 排序的方向desc或者asc
     */
    @ApiModelProperty(value = "排序的方向", example = "asc,desc")
    private String isAsc;

    /**
     * 分发明细的IDa
     */
    private String docDistributeId;

    /**
     * 文件的ID
     */
    private String fileId;

    private String keyword;

    private String fileNameOrPrinter;

    /**
     * 开始时间
     */
    @ApiModelProperty("开始时间")
    private String startTime;

    /**
     * 结束时间
     */
    @ApiModelProperty("结束时间")
    private String endTime;

    //打印后回调的地址
    private String printReturnUrl;
} 
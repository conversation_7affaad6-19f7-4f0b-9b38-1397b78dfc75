package com.rzdata.process.domain.bo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;


@Data
public class ProBomDetailBo {
    /**
     * 主键id
     */
    private String id;

    /**
     * BOM分组Id
     */
    private String groupId;

    /**
     * BOM分组编码
     */
    private String groupCode;

    /**
     * BOM分组名称
     */
    private String groupName;

    /**
     * 物料编码
     */
    private String parentMaterialCode;

    /**
     * 物料名称
     */
    private String parentMaterialName;

    /**
     * 物料规格型号
     */
    private String parentMaterialSpecification;

    /**
     * 物料计量单位
     */
    private String parentMaterialUnit;

    /**
     * BOM版本
     */
    private String version;

    /**
     * 版本说明
     */
    private String versionRemark;

    /**
     * 版本日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private Date versionDate;

    private String fileVersion;

    /**
     * 损耗率
     */
    private BigDecimal attritionRate;

    private String workflowStatus;
    /**
     * 关联BOM主键
     */
    private String relateId;

    /**
     * ECN编号
     */
    private String ecn;

    /**
     * 是否已同步到ERP
     */
    private Boolean erpStatus;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createTime;
}

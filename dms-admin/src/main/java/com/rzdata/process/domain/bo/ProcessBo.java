package com.rzdata.process.domain.bo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.blueland.bpmclient.model.SearchQuery;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @auther xcy
 * @create 2021-12-31 9:58
 * 待办数据查询bo
 */
@Data
public class ProcessBo extends SearchQuery {

    /**
     * 原始待办original_bpmn/业务组合biz_bpmn
     */
    @ApiModelProperty(value = "查询类型")
    @TableField(exist = false)
    private String searchType;

    /**
     * 搜索值
     */
    @ApiModelProperty(value = "搜索值")
    @TableField(exist = false)
    private String searchValue;

    @ApiModelProperty(value = "文件名称")
    private String docName;

    @ApiModelProperty(value = "变更类型")
    private String changeType;

    @ApiModelProperty(value = "文件类型")
    private String docClass;

    @ApiModelProperty(value = "文件编号")
    private String docId;

    @ApiModelProperty(value = "编制部门编号")
    private Integer deptId;

    @ApiModelProperty(value = "编制人")
    private String userName;

    @ApiModelProperty(value = "版本号")
    private String versionValue;

    @ApiModelProperty(value = "文件版本ID")
    private String versionId;

    @ApiModelProperty(value = "申请开始时间")
    private Date applyStartTime;

    @ApiModelProperty(value = "申请结束时间")
    private Date applyEndTime;

    @ApiModelProperty(value = "发起人")
    private String startUserId;

    /**
     * 接收人
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "接收人")
    private String receiverMan;

    /**
     * >0: 大于0有数据
     * 0：没有数据
     */
    private Integer haveData = 0;

    @Override
    public String getProcDefKey() {
        return this.getString("procDefKey");
    }
}

package com.rzdata.process.domain.bo;

import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2022/1/26 18:46
 * @Version 1.0
 * @Description
 */
@Data
public class QysCallbackBo {

    /**
     * 文件编号
     */
    private long contractId;

    /**
     * 签署方编号
     */
    private String tenantId;

    /**
     * 签署方名称
     */
    private String tenantName;

    /**
     * 文件自定义编号
     */
    private String sn;

    /**
     * 文件状态 （DRAFT：草稿，FILLING：拟定中，SIGNING：签署中，COMPLETE：已完成，REJECTED：已退回，
     * RECALLED：已撤回，EXPIRED：已过期，TERMINATING：作废中，TERMINATED：已作废，DELETE：已删除，
     * FINISHED：强制完成）
     */
    private String status;

    /**
     * 回调类型
     */
    private String type;

    /**
     * 联系方式
     */
    private String contact;

    /**
     * 操作人姓名
     */
    private String operatorName;

    /**
     * 操作人手机号
     */
    private String operatorMobile;

}

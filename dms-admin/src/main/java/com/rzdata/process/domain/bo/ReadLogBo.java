package com.rzdata.process.domain.bo;

import com.rzdata.framework.core.domain.BaseEntity;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;


/**
 * 文件阅览记录业务对象 doc_read_log
 *
 * <AUTHOR>
 * @date 2022-01-25
 */

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("文件阅览记录业务对象")
public class ReadLogBo extends BaseEntity {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键", required = true)
    @NotBlank(message = "主键不能为空", groups = { EditGroup.class })
    private String id;

    /**
     * 文件编号
     */
    @ApiModelProperty(value = "文件编号", required = true)
    @NotBlank(message = "文件编号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String docId;

    /**
     * 文件版本号
     */
    @ApiModelProperty(value = "文件版本号", required = true)
    @NotBlank(message = "文件版本号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String versionValue;

    /**
     * 阅读人员
     */
    @ApiModelProperty(value = "阅读人员", required = true)
    @NotBlank(message = "阅读人员不能为空", groups = { AddGroup.class, EditGroup.class })
    private String userName;

    /**
     * 所属部门
     */
    @ApiModelProperty(value = "所属部门", required = true)
    @NotNull(message = "所属部门不能为空", groups = { AddGroup.class, EditGroup.class })
    private String deptId;

    /**
     * 下载量
     */
    @ApiModelProperty(value = "下载量", required = true)
    @NotBlank(message = "下载量不能为空", groups = { AddGroup.class, EditGroup.class })
    private String downLoad;

    /**
     * 阅读量
     */
    @ApiModelProperty(value = "阅读量", required = true)
    @NotBlank(message = "阅读量不能为空", groups = { AddGroup.class, EditGroup.class })
    private String preview;

    /**
     * 
     */
    @ApiModelProperty(value = "", required = true)
    @NotBlank(message = "不能为空", groups = { AddGroup.class, EditGroup.class })
    private String externalFileId;

    private String docName;

    private String deptName;

    private String nickName;

    private String fileName;

    /**
     * 分页大小
     */
    @ApiModelProperty("分页大小")
    private Integer pageSize;

    /**
     * 当前页数
     */
    @ApiModelProperty("当前页数")
    private Integer pageNum;

    /**
     * 排序列
     */
    @ApiModelProperty("排序列")
    private String orderByColumn;

    /**
     * 排序的方向desc或者asc
     */
    @ApiModelProperty(value = "排序的方向", example = "asc,desc")
    private String isAsc;

}

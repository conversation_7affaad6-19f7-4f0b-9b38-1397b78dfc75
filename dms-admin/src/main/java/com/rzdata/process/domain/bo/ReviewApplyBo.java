package com.rzdata.process.domain.bo;

import com.rzdata.framework.core.domain.BaseEntity;
import com.rzdata.framework.core.validate.EditGroup;
import com.rzdata.setting.domain.DocPresetUser;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;

import java.util.List;


/**
 * 文件复审申请业务对象 doc_review_apply
 *
 * <AUTHOR>
 * @date 2021-12-30
 */

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("文件复审申请业务对象")
public class ReviewApplyBo extends BaseEntity {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键", required = true)
    @NotBlank(message = "主键不能为空", groups = { EditGroup.class })
    private String id;

    /**
     * 申请标题
     */
    @ApiModelProperty(value = "申请标题", required = true)
    private String applyTitle;

    /**
     * 申请类型
     */
    @ApiModelProperty(value = "申请类型", required = true)
    private String applyType;

    /**
     * 申请部门
     */
    @ApiModelProperty(value = "申请部门", required = true)
    private String deptId;

    /**
     * 申请人
     */
    @ApiModelProperty(value = "申请人", required = true)
    private String userName;

    /**
     * 申请原因
     */
    @ApiModelProperty(value = "申请原因", required = true)
    private String reason;

    /**
     * 申请状态
     */
    @ApiModelProperty(value = "申请状态", required = true)
    private String status;


    @ApiModelProperty("流程定义key")
    private String procDefKey;


    /**
     * 分页大小
     */
    @ApiModelProperty("分页大小")
    private Integer pageSize;

    /**
     * 当前页数
     */
    @ApiModelProperty("当前页数")
    private Integer pageNum;

    /**
     * 排序列
     */
    @ApiModelProperty("排序列")
    private String orderByColumn;

    /**
     * 排序的方向desc或者asc
     */
    @ApiModelProperty(value = "排序的方向", example = "asc,desc")
    private String isAsc;

    private String procStatus;

    private String recordStatus;

    private BpmClientInputModelBo bpmClientInputModel;

    private List<ReviewApplyItemBo> itemList;

    private String dataType;

    private String tenantId;

    private String classType;

    List<DocPresetUser> presetUserList;

    private Boolean presetUserEdit;

    private Boolean editStatus;
    /**
     *  批次
     */
    private String batch;
}

package com.rzdata.process.domain.bo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.rzdata.framework.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;


@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("复审清单对象")
public class ReviewApplyItemBo extends BaseEntity {
    /**
     * 主键
     */
    private String id;
    /**
     * 文件复审申请表主键
     */
    private String applyId;

    /**
     * 文件名称
     */
    private String docName;
    /**
     * 文件编号
     */
    private String docId;

    /**
     * 文件类型
     */
    private String docClass;

    /**
     * 版本号
     */
    private String versionValue;
    /**
     * 文件版本ID
     */
    private String versionId;

    /**
     * 租户id
     */
    private String tenantId;

    /**
     * 复审标准，EXTENSION=文件延长有效期，KEEP=文件保持现状，UPDATE=文件修订，DISUSE=文件作废
     */
    private String reviewAction;

    /**
     * 编制人
     */
    private String userName;

    /**
     * 编制部门
     */
    private String deptId;

    /**
     * 原因
     */
    private String reason;

    /**
     * 复审时间
     */
    private Date reviewTime;

    /**
     * 设置时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date setupTime;
}

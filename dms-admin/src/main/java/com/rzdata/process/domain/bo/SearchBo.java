package com.rzdata.process.domain.bo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.rzdata.framework.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Set;


/**
 * 全文搜索对象
 * <AUTHOR>
 * @Data 2023年9月22日17:46:39
 */
@Data
@ApiModel("全文搜索对象对象")
@ExcelIgnoreUnannotated
public class SearchBo extends BaseEntity {

	private static final long serialVersionUID = 1L;

	@ExcelProperty(value = "主键")
	@ApiModelProperty("主键")
	private String id;
	@ApiModelProperty("标准文件Id")
	private String standardId;
	@ApiModelProperty("版本")
	private String versionValue;
	@ApiModelProperty("文档id")
	private String docId;
	@ApiModelProperty("申请id")
	private String applyId;
	@ApiModelProperty("状态  1=有效 2=失效 ")
	private String status;
	@ApiModelProperty("类型 stdd：体系文件，project：项目文件，external：外来文件 ")
	private String dataType;
	@ApiModelProperty("文档类型")
	private String docClass;
	@ApiModelProperty("文档名称")
	private String docName;
	@ApiModelProperty("部门id")
	private String deptId;
	@ApiModelProperty("发布时间")
	private String releaseTime;
	@ApiModelProperty("查询名称或code")
	private String nameOrCode;
	@ApiModelProperty("查询-状态")
	private Set<String> statusSet;
	@ApiModelProperty("查询-类型")
	private Set<String> typeSet;
	@ApiModelProperty("是否有查询权限 true:是，false:否")
	private Boolean isQueryPermission;
	@ApiModelProperty("是否特殊字符 true:是，false:否")
	private Boolean isSpecialCharacters;
}

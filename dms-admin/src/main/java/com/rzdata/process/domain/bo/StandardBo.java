package com.rzdata.process.domain.bo;

import com.rzdata.framework.core.domain.BaseEntity;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import java.util.Date;
import java.util.List;

/**
 * 标准文件业务对象 doc_standard
 *
 * <AUTHOR>
 * @date 2021-12-30
 */

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("标准文件业务对象")
public class StandardBo extends BaseEntity {

    /**
     * 文件编号，根据编号规则自动生成
     */
    @ApiModelProperty(value = "文件编号，根据编号规则自动生成", required = true)
    @NotBlank(message = "文件编号，根据编号规则自动生成不能为空", groups = { EditGroup.class })
    private String id;

    /**
     * 体系文件stdd、项目文件project
     */
    private String dataType;

    //项目ID
    private String projectId;
    /**
     * 文件类型
     */
    @ApiModelProperty(value = "文件类型", required = true)
    @NotBlank(message = "文件类型不能为空", groups = { AddGroup.class, EditGroup.class })
    private String docClass;

    private List<String> docClassList;

    /**
     * 文件名称
     */
    @ApiModelProperty(value = "文件名称", required = true)
    @NotBlank(message = "文件名称不能为空", groups = { AddGroup.class, EditGroup.class })
    @Length(max = 200,message = "文件名称过长")
    private String docName;

    /**
     * 文件状态
     */
    @ApiModelProperty(value = "文件状态", required = true)
    @NotBlank(message = "文件状态不能为空", groups = { AddGroup.class, EditGroup.class })
    private String status;

    /**
     * 文件有效期
     */
    @ApiModelProperty(value = "文件有效期", required = true)
    private Long expiration;

    /**
     * 当前文件版本
     */
    @ApiModelProperty(value = "当前文件版本", required = true)
    @NotBlank(message = "当前文件版本不能为空", groups = { AddGroup.class, EditGroup.class })
    @Length(max = 20,message = "文件版本过大")
    private String currentVersion;

    /**
     * 编制部门编号
     */
    @ApiModelProperty(value = "编制部门编号", required = true)
//    @NotNull(message = "编制部门编号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String deptId;

    /**
     * 编制人
     */
    @ApiModelProperty(value = "编制人", required = true)
//    @NotBlank(message = "编制人不能为空", groups = { AddGroup.class, EditGroup.class })
    private String userName;

    /**
     * 申请时间
     */
    @ApiModelProperty(value = "申请时间", required = true)
//    @NotNull(message = "申请时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date applyTime;

    /**
     * 编制文件编号
     */
    @ApiModelProperty(value = "编制文件编号", required = true)
    @NotBlank(message = "编制文件编号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String fileId;

    /**
     * 加密文件编号
     */
    @ApiModelProperty(value = "加密文件编号", required = true)
    private String encryptFileId;

    /**
     * 内容概述
     */
    @ApiModelProperty(value = "内容概述", required = true)
    @NotBlank(message = "内容概述不能为空", groups = { AddGroup.class, EditGroup.class })
    private String content;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注", required = true)
    private String remark;

    /**
     * 变更要素，多个以,隔开
     */
    @ApiModelProperty(value = "变更要素，多个以,隔开", required = true)
    private String changeFactor;

    /**
     * 变更原因
     */
    @ApiModelProperty(value = "变更原因", required = true)
    private String changeReason;


    /**
     * 变更类型 ADD=新增 UPDATE=修订 DISUSE = 作废
     */
    private String changeType;

    /**
     * 分页大小
     */
    @ApiModelProperty("分页大小")
    private Integer pageSize;

    /**
     * 当前页数
     */
    @ApiModelProperty("当前页数")
    private Integer pageNum;

    /**
     * 排序列
     */
    @ApiModelProperty("排序列")
    private String orderByColumn;

    /**
     * 排序的方向desc或者asc
     */
    @ApiModelProperty(value = "排序的方向", example = "asc,desc")
    private String isAsc;

    /**
     * 是否初始化文件
     * 否 = 0
     * 是 = 1
     */
    @ApiModelProperty(value = "是否初始化文件")
    private String initFile;

    /**
     * 版本生效时间
     */
    private Date startDate;
    /**
     * 复审时间
     */
    private Date reviewTime;

    private String forever;
    /**
     * 关联记录编号
     */
    private String linkCode;
    /**
     * 文件编号
     */
    private String docId;

    private Date endDate;

    /**
     * 关联类型
     */
    private String linkType;

    private String versionValue;

    private String inside;

    private String outside;

    private Date releaseTime;

    private String versionId;

    /**
     * 文件id
     */
    private String standardId;

    /**
     * 是否完成基线申请 0=否  1=是
     */
    private String baseStatus;

    private String projectName;

    @ApiModelProperty(value = "附件文件")
    private List<DocLinkLogBo> appendixes;

    private List<String> ids;

    private String applyId;


    /**
     * 文件编号
     */
    private String docCode;


    /**
     * 编制人登录ID
     */
    private String editUserName;

    /**
     * 编制人姓名
     */
    private String editNickName;

    /**
     * 编制人所属组织ID
     */
    private String editDeptId;

    /**
     * 编制人所属组织名称
     */
    private String editDeptName;

    /**
     * 编制人所属二级部门ID
     */
    private String editSecDeptId;

    /**
     * 编制人所属二级部门名称
     */
    private String editSecDeptName;

    /**
     * 文件所属类型（是文件所属分类的顶级）
     */
    private String docClassType;

    /**
     * 是否关联上级文件版本ID Y是 N否
     */
    private String isHasUpVersionId;

    /**
     * 分类所属类型
     * DOC文件台账、RECORD记录台账、FOREIGN外来文件
     */
    private String classType;

}

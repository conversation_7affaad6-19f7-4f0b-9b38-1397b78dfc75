package com.rzdata.process.domain.bo;

import com.rzdata.framework.core.domain.BaseEntity;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;


/**
 * 客户业务对象 basic_Tenant
 *
 * <AUTHOR>
 * @date 2021-11-26
 */

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("客户业务对象")
public class TenantBo extends BaseEntity {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键", required = true)
    @NotBlank(message = "主键不能为空", groups = { EditGroup.class })
    private String id;

    /**
     * 客户名称
     */
    @ApiModelProperty(value = "客户名称", required = true)
    @NotBlank(message = "客户名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String tenantName;


    /**
     * 客户地址
     */
    @ApiModelProperty(value = "客户地址", required = true)
    //@NotBlank(message = "客户地址不能为空", groups = { AddGroup.class, EditGroup.class })
    private String address;

    /**
     * 联系人
     */
    @ApiModelProperty(value = "联系人", required = true)
    //@NotBlank(message = "联系人不能为空", groups = { AddGroup.class, EditGroup.class })
    private String contact;

    /**
     * 联系人电话
     */
    @ApiModelProperty(value = "联系人电话", required = true)
    //@NotBlank(message = "联系人电话不能为空", groups = { AddGroup.class, EditGroup.class })
    private String contactTel;

    /**
     * 租户访问地址
     */
    @ApiModelProperty(value = "租户访问地址", required = true)
    //@NotBlank(message = "联系人电话不能为空", groups = { AddGroup.class, EditGroup.class })
    private String url;

    /**
     * 分页大小
     */
    @ApiModelProperty("分页大小")
    private Integer pageSize;

    /**
     * 当前页数
     */
    @ApiModelProperty("当前页数")
    private Integer pageNum;

    /**
     * 排序列
     */
    @ApiModelProperty("排序列")
    private String orderByColumn;

    /**
     * 排序的方向desc或者asc
     */
    @ApiModelProperty(value = "排序的方向", example = "asc,desc")
    private String isAsc;

}

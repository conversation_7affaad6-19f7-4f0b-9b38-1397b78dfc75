package com.rzdata.process.domain.bo;

import com.rzdata.framework.core.domain.BaseEntity;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.Year;
import java.util.Date;
import java.util.List;

/**
 * 文件版本记录业务对象 doc_version
 *
 * <AUTHOR>
 * @date 2021-12-30
 */

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("文件版本记录业务对象")
public class VersionBo extends BaseEntity {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键", required = true)
    @NotBlank(message = "主键不能为空", groups = { EditGroup.class })
    private String id;


    /**
     * 体系文件stdd、项目文件project
     */
    private String dataType;

    /**
     * 标准文件主键
     */

    @ApiModelProperty(value = "标准文件主键", required = true)
    private String standardId;

    /**
     * 文件编号
     */
    @ApiModelProperty(value = "文件编号", required = true)
    @NotBlank(message = "文件编号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String docId;

    /**
     * 文件名称
     */
    @ApiModelProperty("文件名称")
    private String docName;

    /**
     * 变更申请流水号
     */
    @ApiModelProperty(value = "变更申请流水号", required = true)
    @NotBlank(message = "变更申请流水号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String applyId;

    /**
     * 文件版本号
     */
    @ApiModelProperty(value = "文件版本号", required = true)
    @NotBlank(message = "文件版本号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String versionValue;

    /**
     * 版本生效时间
     */
    @ApiModelProperty(value = "版本生效时间", required = true)
    @NotNull(message = "版本生效时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date startDate;

    /**
     * 版本生效截止时间
     */
    @ApiModelProperty(value = "版本生效截止时间", required = true)
    @NotNull(message = "版本生效截止时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date endDate;

    /**
     * 版本状态
     */
    @ApiModelProperty(value = "版本状态", required = true)
    @NotBlank(message = "版本状态不能为空", groups = { AddGroup.class, EditGroup.class })
    private String status;

    private String dbStatus;

    /**
     * 变更原因
     */
    @ApiModelProperty(value = "变更原因", required = true)
    @NotBlank(message = "变更原因不能为空", groups = { AddGroup.class, EditGroup.class })
    private String reason;


    /**
     * 文件类型
     */
    @ApiModelProperty("文件类型id")
    private String docClass;

    @ApiModelProperty("文件类型名称")
    private String className;

    private List<String> docClassList;
    /**
     * 工程文件子级id
     */
    private List<String> projectFileDocClassList;


    /**
     * 编制部门编号
     */
    @ApiModelProperty("编制部门编号")
    private String deptId;

    private String secDeptId;

    /**
     * 培训部门
     */
    @ApiModelProperty("培训部门")
    private String trainDept;

    /**
     * 上次复审时间
     */
    @ApiModelProperty(value = "上次复审时间", required = true)
    private Date reviewTime;

    private String forever;
    /**
     * 编制人
     */
    @ApiModelProperty("编制人")
    private String userName;

    /**
     * 变更类型
     */
    private String changeType;

    /**
     * 触发来源类型 体系文件台账stdd、项目文件台账project、ECN流程pre_change_apply
     */
    private String invokeType;

    /**
     * 触发来源ID 触发来源类型为stdd则为空、project为项目的主键、pre_change_apply为其表的主键
     */
    private String invokeId;

    /**
     * 编制人
     */
    @ApiModelProperty("编制时间")
    private String applyTime;

    /**
     * 文档状态
     */
    @ApiModelProperty(value = "文档状态")
    private String docStatus;
    /**
     * 分页大小
     */
    @ApiModelProperty("分页大小")
    private Integer pageSize;

    /**
     * 当前页数
     */
    @ApiModelProperty("当前页数")
    private Integer pageNum;

    /**
     * 排序列
     */
    @ApiModelProperty("排序列")
    private String orderByColumn;

    /**
     * 排序的方向desc或者asc
     */
    @ApiModelProperty(value = "排序的方向", example = "asc,desc")
    private String isAsc;

    private String deptIds;

    private String inside;

    private String outside;

    /**
     * 当前用户所属角色
     */
    private String roleKey;

    /**
     * 发布时间
     */
    private Date releaseTime;

    private Boolean isBom;

    /**
     * 上级文件版本ID
     */
    private String upVersionId;
    /**
     * 上级文件编号
     */
    private String parentDocId;
    /**
     * 分类所属类型
     * DOC文件台账、RECORD记录台账、FOREIGN外来文件
     */
    private String classType;
    /**
     * 分发类型 公司 company、部门 dept、个人 person、部门和个人 dept_person
     */
    private String distributeType;

    /**
     * 产品线
     */
    private String productLine;

    /**
     * 工序
     */
    private String process;

    /**
     * 产品类别 民品 civil  汽车部品 car
     */
    private String productType;

    /**
     * 安全级别 0-3
     */
    private String securityLevel;

    /**
     * 是否有关联文件
     */
    private String haveLinkFile;

    /**
     * 导出文件名称
     */
    private String exportName;

    /**
     * 关联程序文件版本id
     */
    private String programVersionId;

    /**
     * 关联程序文件编号
     */
    private String programDocId;

    /**
     * 物料编码
     */
    private String partNumber;
    /**
     * 物料描述
     */
    private String partRemark;
    /**
     * 工厂
     */
    private String factorys;
    /**
     * 产品版本
     */
    private String productVersion;
    /**
     * 保管部门
     */
    private String custodyDeptId;

    /**
     * 保存期限
     */
    private String shelfLife;

    /**
     * 文件生效日期
     */
    private Date fileEffectiveDate;

    /**
     * 修订日期
     */
    private Date revisionDate;

    /**
     * 合规性
     */
    private String compliance;

    /**
     * 需要过滤的版本id
     */
    private String neVersionId;

    /**
     * 查询物料编码不为空
     */
    private String partNumberIsNotNull;

    /**
     * 客户编码
     */
    private String customerCode;
    /**
     * 设备编码
     */
    private String deviceCode;

    /**
     * 设备名称
     */
    private String deviceName;

    private String nickName;

    /**
     * 项目编号
     */
    private String projectCode;

    /**
     * 体系条款
     */
    private String systemClause;

    /**
     * 密级
     */
    private String securityClass;

    /**
     * 关键字
     */
    private String keyword;

    /**
     * 字节数
     */
    private String docBytes;

    /**
     * 内部文件编号
     */
    private String internalDocId;

    /**
     * ECN编号
     */
    private String ecnCode;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 评审记录文件id
     */
    @ApiModelProperty(value = "评审记录文件id")
    private String reviewRecordFileIds;

    /**
     * 过期文件
     */
    private boolean expired;

    /**
     * 文件类型：N=无，Y=临时文件，G=工程样板
     */
    private String isFileType;

    private String type;
}

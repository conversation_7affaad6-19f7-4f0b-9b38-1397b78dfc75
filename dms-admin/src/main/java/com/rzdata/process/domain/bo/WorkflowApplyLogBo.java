package com.rzdata.process.domain.bo;

import com.rzdata.framework.core.domain.BaseEntity;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;

import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 流程申请记录业务对象 doc_workflow_apply_log
 *
 * <AUTHOR>
 * @date 2022-01-05
 */

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("流程申请记录业务对象")
public class WorkflowApplyLogBo extends BaseEntity {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键", required = true)
    @NotBlank(message = "主键不能为空", groups = { EditGroup.class })
    private String id;

    /**
     * 流程类别
     */
    @ApiModelProperty(value = "流程类别", required = true)
    @NotBlank(message = "流程类别不能为空", groups = { AddGroup.class, EditGroup.class })
    private String applyClass;

    /**
     * 流程处理状态 参考applyStatusEnum
     */
    @ApiModelProperty(value = "申请结果", required = true)
    @NotBlank(message = "申请结果不能为空", groups = { AddGroup.class, EditGroup.class })
    private String applyStatus;

    /**
     * 文件名称
     */
    @ApiModelProperty(value = "文件名称", required = true)
    @NotBlank(message = "文件名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String docName;

    /**
     * 文件编号
     */
    @ApiModelProperty(value = "文件编号", required = true)
    @NotBlank(message = "文件编号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String docId;

    /**
     * 文件类型
     */
    @ApiModelProperty(value = "文件类型", required = true)
    @NotBlank(message = "文件类型不能为空", groups = { AddGroup.class, EditGroup.class })
    private String docClass;

    /**
     * 文件版本ID
     */
    @ApiModelProperty(value = "文件版本ID", required = true)
    @NotBlank(message = "文件版本ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private String versionId;

    /**
     * 文件版本号
     */
    @ApiModelProperty(value = "文件版本号", required = true)
    @NotBlank(message = "文件版本号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String versionValue;

    /**
     * 发送人
     */
    @ApiModelProperty(value = "发送人", required = true)
    @NotBlank(message = "发送人不能为空", groups = { AddGroup.class, EditGroup.class })
    private String sender;

    /**
     * 编制部门编号
     */
    @ApiModelProperty(value = "编制部门编号", required = true)
    @NotNull(message = "编制部门编号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String deptId;

    /**
     * 流程实例ID
     */
    @ApiModelProperty(value = "流程实例ID", required = true)
    @NotBlank(message = "流程实例ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private String procInstId;

    /**
     * 流程实例key
     */
    @ApiModelProperty(value = "流程实例key", required = true)
    @NotBlank(message = "流程实例key不能为空", groups = { AddGroup.class, EditGroup.class })
    private String procDefKey;

    /**
     * 申请时间
     */
    @ApiModelProperty(value = "申请时间", required = true)
    @NotNull(message = "申请时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date applyTime;

    private String docIds;

    @ApiModelProperty("当前环节名称")
    private String actDefName;

    private String changeType;

    /**
     * 分页大小
     */
    @ApiModelProperty("分页大小")
    private Integer pageSize;

    /**
     * 当前页数
     */
    @ApiModelProperty("当前页数")
    private Integer pageNum;

    /**
     * 排序列
     */
    @ApiModelProperty("排序列")
    private String orderByColumn;

    /**
     * 排序的方向desc或者asc
     */
    @ApiModelProperty(value = "排序的方向", example = "asc,desc")
    private String isAsc;


    @ApiModelProperty(value = "流程状态")
    private String procStatus;

    private List<String> neApplyClass;
}

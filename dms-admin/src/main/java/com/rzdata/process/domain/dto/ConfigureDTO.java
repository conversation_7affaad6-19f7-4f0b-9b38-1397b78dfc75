package com.rzdata.process.domain.dto;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.rzdata.framework.constant.GenConstants;
import com.rzdata.framework.core.domain.entity.SysDept;
import com.rzdata.framework.core.domain.entity.SysDictData;
import com.rzdata.framework.core.domain.entity.SysUser;
import com.rzdata.setting.domain.BasicFormRule;
import com.rzdata.setting.domain.DocClass;
import com.rzdata.setting.domain.DocClassSetting;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;


/**
 * 文件版本记录视图对象 doc_version
 *
 * <AUTHOR>
 * @date 2021-12-30
 */
@Data
@ApiModel("文件版本记录视图对象")
@ExcelIgnoreUnannotated
public class ConfigureDTO {

	private static final long serialVersionUID = 1L;

	private List<SysUser> userList;
	private List<SysDept> deptList;
	private List<DocClass> dcList;

	private Map<String,List<SysDictData>> dictList;

	private List<SysDictData> formList;

	public List<SysDept> findDept(String key){
		String [] deptNameList = key.split("/");
		List<SysDept> deptList = new ArrayList<>();
		for (int i=deptNameList.length-1;i>=0;i--) {
			String deptName = deptNameList[i];
			if (i==deptNameList.length-1) {
				deptList = BeanUtil.copyToList(this.deptList.stream()
						.filter(dept -> deptName.equals(dept.getDeptName()))
						.collect(Collectors.toList()),SysDept.class);
			}else {
				deptList = deptList.stream().filter(dept ->{
					Optional<SysDept> deptOptional = this.deptList.stream().filter(item->deptName.equals(item.getDeptName())
							&& dept.getAncestors().contains(item.getDeptId())
							&& item.getDeptLevel()<dept.getDeptLevel()).findFirst();
					if (deptOptional.isPresent()) {
						dept.setDeptLevel(deptOptional.get().getDeptLevel());
						return true;
					}else {
						return false;
					}
				}).collect(Collectors.toList());
			}
			if (deptList.size()<=1){
				break;
			}
		}
		return deptList;
	}
}

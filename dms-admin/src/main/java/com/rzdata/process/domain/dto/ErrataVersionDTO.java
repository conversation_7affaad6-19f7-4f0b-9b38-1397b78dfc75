package com.rzdata.process.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.rzdata.process.domain.DocExt;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;


/**
 * 勘误文件版本记录视图对象 doc_errata_version
 *
 * <AUTHOR>
 * @date 2025-01-08
 */
@Data
@ApiModel("勘误文件版本记录视图对象")
@ExcelIgnoreUnannotated
public class ErrataVersionDTO {

	private static final long serialVersionUID = 1L;

	@ExcelProperty(value = "文件类型")
	private String className;

	/**
	 * 文件名称
	 */
	@ExcelProperty(value = "文件名称")
	@ApiModelProperty("文件名称")
	private String docName;

	/**
	 * 文件编号
	 */
	@ExcelProperty(value = "文件编号")
	@ApiModelProperty("文件编号")
	private String docId;

    /**
     * 文件版本号
     */
	@ExcelProperty(value = "文件版本")
	@ApiModelProperty("文件版本")
	private String versionValue;

	/**
	 * 创建人名称
	 */
	@ExcelProperty(value = "勘误人")
	private String createName;

	/**
	 * 创建人名称
	 */
	@ExcelProperty(value = "勘误时间")
	private Date createTime;
}

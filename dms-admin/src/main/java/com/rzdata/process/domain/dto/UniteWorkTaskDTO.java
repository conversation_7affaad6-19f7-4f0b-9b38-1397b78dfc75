package com.rzdata.process.domain.dto;

import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel("流程待办视图")
public class UniteWorkTaskDTO {
    private String id;
    private String title;
    private String recUserId;
    private String recUserName;
    private String recOrgId;
    private String recOrgName;
    private String sendUserId;
    private String sendUserName;
    private String sendOrgId;
    private String sendOrgName;
    private String status;
    private String url;
    private String tenantId;
    private String tenantName;
    private Date sendTime;
    private String endSendTime;
    private String startUserId;
    private String startUserName;
    private String startOrgId;
    private String startOrgName;
    private Date startTime;
    private String topProcInstId;
    private String parentProcInstId;
    private String procInstId;
    private String procDefId;
    private String procDefKey;
    private String procDefName;
    private String curActInstId;
    private String curActDefId;
    private String curActDefName;
    private String prevActInstId;
    private String prevActDefId;
    private String prevActDefName;
    private String dataSource;
}

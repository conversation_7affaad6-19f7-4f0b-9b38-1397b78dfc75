package com.rzdata.process.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.rzdata.framework.annotation.ExcelDictFormat;
import com.rzdata.framework.convert.ExcelDictConvert;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 编辑明细日志视图对象 basic_file_editing_detail_log
 *
 * <AUTHOR>
 * @date 2025-04-30
 */
@Data
@ApiModel("编辑明细日志视图对象")
@ExcelIgnoreUnannotated
public class BasicFileEditingDetailLogVo {

	private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
	@ExcelProperty(value = "主键")
	@ApiModelProperty("主键")
	private String id;

    /**
     * 编辑的文件
     */
	@ExcelProperty(value = "编辑的文件")
	@ApiModelProperty("编辑的文件")
	private String fileId;

    /**
     * 状态：E-编辑中，Y-编辑完成，C-编辑取消
     */
    @ExcelProperty(value = "状态：E-编辑中，Y-编辑完成，C-编辑取消", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "ediiting_statuc")
	@ApiModelProperty("状态：E-编辑中，Y-编辑完成，C-编辑取消")
	private String status;

    /**
     * 业务ID
     */
	@ExcelProperty(value = "业务ID")
	@ApiModelProperty("业务ID")
	private String bizId;

	/**
	 * 上级文件ID
	 */
	@ExcelProperty(value = "上级文件ID")
	@ApiModelProperty("上级文件ID")
	private String protoFileId;
}

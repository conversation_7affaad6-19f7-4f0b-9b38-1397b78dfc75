package com.rzdata.process.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;


/**
 * 附件转PDF视图对象 basic_file_pdf
 *
 * <AUTHOR>
 * @date 2023-07-31
 */
@Data
@ApiModel("附件转PDF视图对象")
@ExcelIgnoreUnannotated
public class BasicFilePdfVo {

	private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
	@ExcelProperty(value = "主键")
	@ApiModelProperty("主键")
	private String id;

    /**
     * 源文件ID
     */
	@ExcelProperty(value = "源文件ID")
	@ApiModelProperty("源文件ID")
	private String fileId;

    /**
     * pdf文件ID
     */
	@ExcelProperty(value = "pdf文件ID")
	@ApiModelProperty("pdf文件ID")
	private String pdfId;

    /**
     * 转换类型;转换：transition、签章：signature
     */
	@ExcelProperty(value = "转换类型;转换：transition、签章：signature")
	@ApiModelProperty("转换类型;转换：transition、签章：signature")
	private String pdfType;

    /**
     * 状态
     */
	@ExcelProperty(value = "状态")
	@ApiModelProperty("状态")
	private String status;

    /**
     * 租户id
     */
	@ExcelProperty(value = "租户id")
	@ApiModelProperty("租户id")
	private String tenantId;

	/**
	 * 业务id
	 */
	private String bizId;

	private Date createTime;

	/**
	 * 源文件对象
	 */
	private BasicFileVo fileObj;

	/**
	 * 文件关联记录
	 */
	private DocLinkLogVo docLinkLogVo;


	private String printStatus;
}

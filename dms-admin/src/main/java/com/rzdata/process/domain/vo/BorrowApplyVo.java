package com.rzdata.process.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;


/**
 * 文件借阅申请视图对象 doc_borrow_apply
 *
 * <AUTHOR>
 * @date 2021-12-31
 */
@Data
@ApiModel("文件借阅申请视图对象")
@ExcelIgnoreUnannotated
public class BorrowApplyVo {

	private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
	@ExcelProperty(value = "主键")
	@ApiModelProperty("主键")
	private String id;
	/**
	 * 申请标题
	 */
	@ApiModelProperty("申请标题")
	private String applyTitle;
	/**
	 * 申请部门Id
	 */
	@ApiModelProperty("申请部门Id")
	private String deptId;
	/**
	 * 申请部门Id
	 */
	@ApiModelProperty("申请部门Id")
	private String deptName;
	/**
	 * 申请人
	 */
	@ApiModelProperty("申请人")
	private String userName;
	/**
	 * 申请人名称
	 */
	@ApiModelProperty("申请人名称")
	private String nickName;
	/**
	 * 申请时间
	 */
	@ApiModelProperty("申请时间")
	private Date applyTime;
	/**
	 * 申请原因
	 */
	@ApiModelProperty("申请原因")
	private String reason;
	/**
	 * 申请状态
	 */
	@ApiModelProperty("申请状态")
	private String status;

	/**
	 * 文件类型
	 */
	private String docClass;

	/**
	 * 租户id
	 */
	@ApiModelProperty("租户id")
	private String tenantId;

	private String classType;
}

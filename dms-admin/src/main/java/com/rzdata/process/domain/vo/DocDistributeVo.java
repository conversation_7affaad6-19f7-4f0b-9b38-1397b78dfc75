package com.rzdata.process.domain.vo;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.rzdata.framework.annotation.ExcelDictFormat;
import com.rzdata.framework.convert.ExcelDictConvert;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 文件分发明细视图对象 doc_distribute
 *
 * <AUTHOR>
 * @date 2023-07-20
 */
@Data
@ApiModel("文件分发明细视图对象")
@ExcelIgnoreUnannotated
public class DocDistributeVo {

	private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
	//@ExcelProperty(value = "主键")
	@ApiModelProperty("主键")
	private String id;

    /**
     * 租户id
     */
	//@ExcelProperty(value = "租户id")
	@ApiModelProperty("租户id")
	private String tenantId;

    /**
     * 文件版本ID
     */
	//@ExcelProperty(value = "文件版本ID")
	@ApiModelProperty("文件版本ID")
	private String versionId;

	private String versionValue;

    /**
     * 分发号;例如01、02
     */
	//@ExcelProperty(value = "分发号;例如01、02")
	@ApiModelProperty("分发号;例如01、02")
	private Integer code;

    /**
     * 文件编号
     */
	//@ExcelProperty(value = "文件编号")
	@ApiModelProperty("文件编号")
	private String docId;

    /**
     * 文件类型
     */
	//@ExcelProperty(value = "文件类型")
	@ApiModelProperty("文件类型")
	private String docClass;

	private String className;

    /**
     * 文件名称
     */
	//@ExcelProperty(value = "文件名称")
	@ApiModelProperty("文件名称")
	private String docName;

    /**
     * 分发数量
     */
	//@ExcelProperty(value = "分发数量")
	@ApiModelProperty("分发数量")
	private Integer nums;

    /**
     * 打印数量
     */
	//@ExcelProperty(value = "打印数量")
	@ApiModelProperty("打印数量")
	private Integer printNums;

    /**
     * Y可打印 N不可打印
     */
	//@ExcelProperty(value = "Y可打印 N不可打印")
	@ApiModelProperty("Y可打印 N不可打印")
	private String printFlag;

    /**
     * 签收人
     */
	//@ExcelProperty(value = "签收人")
	@ApiModelProperty("签收人")
	private String receiveUserName;

    /**
     * 签收人昵称
     */
	//@ExcelProperty(value = "签收人昵称")
	@ExcelProperty(value = "签收人", index = 1)
	@ApiModelProperty("签收人昵称")
	private String receiveNickName;

	/**
	 * 签收人部门id
	 */
	//@ExcelProperty(value = "签收人部门id")
	@ApiModelProperty("签收人部门id")
	private String receiveUserDeptId;

    /**
     * 签收人部门名称
     */
	@ExcelProperty(value = "签收部门", index = 0)
	@ApiModelProperty("签收人部门名称")
	private String receiveUserDept;

    /**
     * 签收时间
     */
	@ExcelProperty(value = "签收时间", index = 3)
	@ApiModelProperty("签收时间")
	private Date receiveTime;

    /**
     * 丢失时间
     */
	//@ExcelProperty(value = "丢失时间")
	@ApiModelProperty("丢失时间")
	private Date lostTime;

    /**
     * 回收时间
     */
	@ExcelProperty(value = "回收时间", index = 6)
	@ApiModelProperty("回收时间")
	private Date recoveryTime;

	/**
	 * 分发时间
	 */
	//@ExcelProperty(value = "分发时间")
	@ApiModelProperty("分发时间")
	private Date createTime;


	/**
     * 是否签收 0否 1是
     */
	//@ExcelProperty(value = "是否签收 0否 1是")
	@ApiModelProperty("是否签收 0否 1是")
	private Boolean receive;

    /**
     * 是否丢失 0否 1是
     */
	//@ExcelProperty(value = "是否丢失 0否 1是")
	@ApiModelProperty("是否丢失 0否 1是")
	private Boolean lost;

    /**
     * 是否回收 0否 1是
     */
	@ExcelProperty(value = "回收状态", converter = ExcelDictConvert.class, index = 4)
	@ExcelDictFormat(readConverterExp = "false=未回收,true=已回收")
	@ApiModelProperty("是否回收 0否 1是")
	private Boolean recovery;

    /**
     * 状态;未签收、已签收、已打印、已回收、已丢失
     */
	//@ExcelProperty(value = "状态;未签收、已签收、已打印、已回收、已丢失")
	@ApiModelProperty("状态;未签收、已签收、已打印、已回收、已丢失")
	private String status;

	@ExcelProperty(value = "分发号", index = 2)
	private String codeNum;

	/**
	 * 类型 部门 dept、个人 person
	 */
	private String type;

	private Date releaseTime;

	/**
	 * 打印总数
	 */
	@ExcelProperty(value = "打印次数", index = 5)
	private Integer printTotal;

	/** 生效时间 **/
	private Date startDate;
	/** 已学习人数 **/
	private Integer num;
	/** 总学习人数 **/
	private Integer total;
	/** 编制部门 **/
	private String deptId;
	/** 编制部门 **/
	private String deptName;

	private Date times;

	private String printPaperType;

	private String fileId;
}

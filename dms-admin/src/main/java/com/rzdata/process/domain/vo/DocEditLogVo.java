package com.rzdata.process.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.rzdata.framework.annotation.ExcelDictFormat;
import com.rzdata.framework.convert.ExcelDictConvert;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;


/**
 * 文件编辑日志视图对象 doc_edit_log
 *
 * <AUTHOR>
 * @date 2025-03-03
 */
@Data
@ApiModel("文件编辑日志视图对象")
@ExcelIgnoreUnannotated
public class DocEditLogVo {

	private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
	@ExcelProperty(value = "主键")
	@ApiModelProperty("主键")
	private String id;

    /**
     * 流程id
     */
	@ExcelProperty(value = "流程id")
	@ApiModelProperty("流程id")
	private String applyId;

    /**
     * 版本id
     */
	@ExcelProperty(value = "版本id")
	@ApiModelProperty("版本id")
	private String versionId;

    /**
     * 附件id
     */
	@ExcelProperty(value = "附件id")
	@ApiModelProperty("附件id")
	private String fileId;

	/**
	 * 附件名称
	 */
	private String fileName;

    /**
     * 原始附件id
     */
	@ExcelProperty(value = "原始附件id")
	@ApiModelProperty("原始附件id")
	private String protoFileId;

    /**
     * 操作类型 编辑 修订 批准
     */
	@ExcelProperty(value = "操作类型 编辑 修订 批准")
	@ApiModelProperty("操作类型 编辑 修订 批准")
	private String type;

    /**
     * 环节名称
     */
	@ExcelProperty(value = "环节名称")
	@ApiModelProperty("环节名称")
	private String actDefName;

    /**
     * 创建名称
     */
	@ExcelProperty(value = "创建名称")
	@ApiModelProperty("创建名称")
	private String createName;

	private Date createTime;
}

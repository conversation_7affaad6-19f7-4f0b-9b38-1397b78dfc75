package com.rzdata.process.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;


/**
 * 文件关联记录视图对象 doc_link_log
 *
 * <AUTHOR>
 * @date 2022-01-06
 */
@Data
@ApiModel("文件关联记录视图对象")
@ExcelIgnoreUnannotated
public class DocLinkLogVo {

	private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
	@ExcelProperty(value = "主键")
	@ApiModelProperty("主键")
	private String id;

    /**
     * 关联记录编号
     */
	@ExcelProperty(value = "关联记录编号")
	@ApiModelProperty("关联记录编号")
	private String linkCode;

    /**
     * 文件名称
     */
	@ExcelProperty(value = "文件名称")
	@ApiModelProperty("文件名称")
	private String fileName;

    /**
     * 文件id
     */
	@ExcelProperty(value = "文件id")
	@ApiModelProperty("文件id")
	private String fileId;

    /**
     * 版本号
     */
	@ExcelProperty(value = "版本号")
	@ApiModelProperty("版本号")
	private String versionValue;

	/**
	 * 文件版本ID
	 */
	@ExcelProperty(value = "版本ID")
	@ApiModelProperty("版本ID")
	private String versionId;

	/**
	 * 关联类型: DOC正文 APPENDIX附件 RECORD记录 REF_DOC关联其他文件
	 */
	@ExcelProperty(value = "关联类型")
	@ApiModelProperty("关联类型")
	private String linkType;

	/**
	 * 文件类型
	 */
	@ExcelProperty(value = "关联类型")
	@ApiModelProperty("关联类型")
	private String docClass;

	/**
	 * 创建时间
	 */
	@ExcelProperty(value = "创建时间")
	@ApiModelProperty("创建时间")
	private Date createTime;

	/**
	 * 更新时间
	 */
	@ExcelProperty(value = "更新时间")
	@ApiModelProperty("更新时间")
	private Date updateTime;

	/**
	 * 当前版本
	 */
	@ApiModelProperty("当前版本")
	private String currentVersion;

	/**
	 *
	 */
	private String dsDocClass;

	private String status;

	/**
	 * 生效时间
	 */
	private Date startDate;

	/**
	 * 发布时间
	 */
	private Date releaseTime;

	/**
	 * 失效时间
	 */
	private Date endDate;

	/**
	 * 原始附件id
	 */
	private String protoFileId;
}

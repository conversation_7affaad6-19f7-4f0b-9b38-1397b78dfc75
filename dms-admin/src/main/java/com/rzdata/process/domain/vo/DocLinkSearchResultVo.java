package com.rzdata.process.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.rzdata.es.bean.AppendixDocument;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;


/**
 * 首页搜索文件关联记录视图对象
 *
 * <AUTHOR>
 * @date 2024-11-18
 */
@Data
@ApiModel("首页搜索文件关联记录视图对象")
@ExcelIgnoreUnannotated
public class DocLinkSearchResultVo {

	private static final long serialVersionUID = 1L;

	/**
	 * versionId
	 */
	@ApiModelProperty("versionId")
	private String versionId;

	/**
	 * 文档名称
	 */
	@ApiModelProperty("文档名称")
	private String docName;

    /**
     * 文件编号
     */
	@ApiModelProperty("文件编号")
	private String docId;

	/**
	 * 文件ID
	 */
	@ApiModelProperty("文件ID")
	private String fileId;

    /**
     * 文件类型
     */
	@ApiModelProperty("文件类型")
	private String docClass;

	/**
	 * 文件类型名称
	 */
	@ApiModelProperty("文件类型名称")
	private String className;

    /**
     * 发布时间
     */
	@ApiModelProperty("发布时间")
	private Date releaseTime;

	private String releaseTimeStr;

    /**
     * 部门ID
     */
	@ApiModelProperty("部门ID")
	private String deptId;

	/**
	 * 部门名称
	 */
	@ApiModelProperty("部门名称")
	private String deptName;

    /**
     * 项目编号
     */
	@ApiModelProperty("项目编号")
	private String projectCode;

	/**
	 * 物料编码
	 */
	@ApiModelProperty("物料编码")
	private String partNumber;

	/**
	 * 物料描述
	 */
	@ApiModelProperty("物料描述")
	private String partRemark;

	/**
	 * 产品版本
	 */
	@ApiModelProperty("产品版本")
	private String productVersion;

	/**
	 * 关联类型
	 */
	@ApiModelProperty("关联类型")
	private String linkType;

	/**
	 * 生效时间
	 */
	private Date startDate;

	/**
	 * 是否需要显示子记录，0需要，1不需要
	 */
	private String hasChild;

	private Boolean hasPerms;

	private Boolean isBorrow;

	private String createBy;

	private String userName;

	//文件版本号
	private String versionValue;
	//文件状态
	private String status;
	//文件状态名称
	private String statusName;
	//文件类型
	private String dataType;
	//文件类型名称
	private String dataTypeName;
	//内部文件编号
	private String internalDocId;
	//发布时间
	private String pushDate;
	//文件内容
	private String textContent;

	//附件内容
	private List<AppendixDocument> appendixDocumentList;
}

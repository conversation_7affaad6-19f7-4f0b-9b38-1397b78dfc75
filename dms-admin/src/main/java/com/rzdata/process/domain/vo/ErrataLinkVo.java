package com.rzdata.process.domain.vo;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.rzdata.framework.annotation.ExcelDictFormat;
import com.rzdata.framework.convert.ExcelDictConvert;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 文件关联勘误记录视图对象 doc_errata_link
 *
 * <AUTHOR>
 * @date 2025-01-13
 */
@Data
@ApiModel("文件关联勘误记录视图对象")
@ExcelIgnoreUnannotated
public class ErrataLinkVo {

	private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
	@ExcelProperty(value = "主键")
	@ApiModelProperty("主键")
	private String id;

    /**
     * 父id
     */
	@ExcelProperty(value = "父id")
	@ApiModelProperty("父id")
	private String parentId;

    /**
     * 关联记录文件编号
     */
	@ExcelProperty(value = "关联记录文件编号")
	@ApiModelProperty("关联记录文件编号")
	private String linkCode;

    /**
     * 关联文件名称
     */
	@ExcelProperty(value = "关联文件名称")
	@ApiModelProperty("关联文件名称")
	private String fileName;

    /**
     * 关联记录文件id
     */
	@ExcelProperty(value = "关联记录文件id")
	@ApiModelProperty("关联记录文件id")
	private String fileId;

    /**
     * 关联记录版本ID
     */
	@ExcelProperty(value = "关联记录版本ID")
	@ApiModelProperty("关联记录版本ID")
	private String versionId;

    /**
     * 关联记录版本号
     */
	@ExcelProperty(value = "关联记录版本号")
	@ApiModelProperty("关联记录版本号")
	private String versionValue;

    /**
     * 关联类型: DOC正文 APPENDIX附件 RECORD记录 REF_DOC关联其他文件 APPENDIX_REMARK备注附件
     */
	@ExcelProperty(value = "关联类型: DOC正文 APPENDIX附件 RECORD记录 REF_DOC关联其他文件 APPENDIX_REMARK备注附件")
	@ApiModelProperty("关联类型: DOC正文 APPENDIX附件 RECORD记录 REF_DOC关联其他文件 APPENDIX_REMARK备注附件")
	private String linkType;

    /**
     * 租户id
     */
	@ExcelProperty(value = "租户id")
	@ApiModelProperty("租户id")
	private String tenantId;

    /**
     * 关联记录文件类型
     */
	@ExcelProperty(value = "关联记录文件类型")
	@ApiModelProperty("关联记录文件类型")
	private String docClass;

    /**
     * 1：有效  2：失效
     */
	@ExcelProperty(value = "1：有效  2：失效")
	@ApiModelProperty("1：有效  2：失效")
	private String status;

    /**
     * 生效时间
     */
	@ExcelProperty(value = "生效时间")
	@ApiModelProperty("生效时间")
	private Date startDate;

    /**
     * 发布时间
     */
	@ExcelProperty(value = "发布时间")
	@ApiModelProperty("发布时间")
	private Date releaseTime;

    /**
     * 失效时间
     */
	@ExcelProperty(value = "失效时间")
	@ApiModelProperty("失效时间")
	private Date endDate;


}

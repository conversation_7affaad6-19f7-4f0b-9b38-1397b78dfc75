package com.rzdata.process.domain.vo;

import java.util.Date;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.rzdata.process.domain.DocExt;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 勘误文件版本记录视图对象 doc_errata_version
 *
 * <AUTHOR>
 * @date 2025-01-08
 */
@Data
@ApiModel("勘误文件版本记录视图对象")
@ExcelIgnoreUnannotated
public class ErrataVersionVo extends DocExt {

	private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
	@ApiModelProperty("主键")
	private String id;

	@ExcelProperty(value = "文件类型")
	private String className;

	/**
	 * 文件名称
	 */
	@ExcelProperty(value = "文件名称")
	@ApiModelProperty("文件名称")
	private String docName;

	/**
	 * 文件编号
	 */
	@ExcelProperty(value = "文件编号")
	@ApiModelProperty("文件编号")
	private String docId;
    /**
     * 版本主键
     */
	@ApiModelProperty("版本主键")
	private String versionId;

    /**
     * 排序 从0开始 0是原版
     */
	@ApiModelProperty("排序 从0开始 0是原版")
	private Integer sort;

    /**
     * 文件所属分类
     */
	@ApiModelProperty("文件所属分类")
	private String docClass;

    /**
     * 标准文件主键
     */
	@ApiModelProperty("标准文件主键")
	private String standardId;



    /**
     * 变更申请流水号
     */
	@ApiModelProperty("变更申请流水号")
	private String applyId;

    /**
     * 文件版本号
     */
	@ExcelProperty(value = "文件版本")
	@ApiModelProperty("文件版本")
	private String versionValue;

    /**
     * 版本生效时间
     */
	@ApiModelProperty("版本生效时间")
	private Date startDate;

    /**
     * 版本生效截止时间
     */
	@ApiModelProperty("版本生效截止时间")
	private Date endDate;

    /**
     * 版本状态 0=未知 1=有效 2=失效 3=留用
     */
	@ApiModelProperty("版本状态 0=未知 1=有效 2=失效 3=留用")
	private String status;

    /**
     * 变更原因
     */
	@ApiModelProperty("变更原因")
	private String reason;

    /**
     * 复审时间
     */
	@ApiModelProperty("复审时间")
	private Date reviewTime;

    /**
     * 内容概述
     */
	@ApiModelProperty("内容概述")
	private String content;

    /**
     * 备注
     */
	@ApiModelProperty("备注")
	private String remark;

    /**
     * 变更要素，多个以,隔开
     */
	@ApiModelProperty("变更要素，多个以,隔开")
	private String changeFactor;

    /**
     * 变更原因
     */
	@ApiModelProperty("变更原因")
	private String changeReason;

    /**
     * 培训部门
     */
	@ApiModelProperty("培训部门")
	private String trainDept;

    /**
     * 主文件basic_file表id
     */
	@ApiModelProperty("主文件basic_file表id")
	private String fileId;

    /**
     * 合稿文件basic_file表id
     */
	@ApiModelProperty("合稿文件basic_file表id")
	private String mergeFileId;

    /**
     * 加密(签章)文件basic_file表id
     */
	@ApiModelProperty("加密(签章)文件basic_file表id")
	private String encryptFileId;

    /**
     * 租户id
     */
	@ApiModelProperty("租户id")
	private String tenantId;

    /**
     * 是否永久有效  0=否  1=是
     */
	@ApiModelProperty("是否永久有效  0=否  1=是")
	private String forever;

    /**
     * 体系文件stdd、项目文件project
     */
	@ApiModelProperty("体系文件stdd、项目文件project")
	private String dataType;

    /**
     * file_id的PDF版本
     */
	@ApiModelProperty("file_id的PDF版本")
	private String pdfFileId;

    /**
     * 发布时间
     */
	@ApiModelProperty("发布时间")
	private Date releaseTime;

    /**
     * 是否是bom文件 0=否  1=是
     */
	@ApiModelProperty("是否是bom文件 0=否  1=是")
	private Long isBom;

    /**
     * 上级文件版本ID
     */
	@ApiModelProperty("上级文件版本ID")
	private String upVersionId;

    /**
     * 上级文件编号
     */
	@ApiModelProperty("上级文件编号")
	private String parentDocId;

    /**
     * 分类所属类型
DOC文件台账、RECORD记录台账、FOREIGN外来文件
     */
	private String classType;

    /**
     *
     */
	@ApiModelProperty("")
	private String deptId;

    /**
     * 编制人
     */
	@ApiModelProperty("编制人")
	private String userName;

    /**
     * 变更类型 ADD=新增 UPDATE=修订 DISUSE = 作废
     */
	@ApiModelProperty("变更类型 ADD=新增 UPDATE=修订 DISUSE = 作废")
	private String changeType;

    /**
     * 触发来源类型 体系文件台账stdd、项目文件台账project、ECN流程pre_change_apply
     */
	@ApiModelProperty("触发来源类型 体系文件台账stdd、项目文件台账project、ECN流程pre_change_apply")
	private String invokeType;

    /**
     * 触发来源ID 触发来源类型为stdd则为空、project为项目的主键、pre_change_apply为其表的主键
     */
	@ApiModelProperty("触发来源ID 触发来源类型为stdd则为空、project为项目的主键、pre_change_apply为其表的主键")
	private String invokeId;

    /**
     * 分发类型 公司 company、部门 dept、个人 person、部门和个人 dept_person
     */
	@ApiModelProperty("分发类型 公司 company、部门 dept、个人 person、部门和个人 dept_person")
	private String distributeType;

    /**
     * 是否上传客户记录 Y是 N否
     */
	@ApiModelProperty("是否上传客户记录 Y是 N否")
	private String whetherCustomer;

    /**
     *
     */
	@ApiModelProperty("")
	private Date applyTime;

    /**
     * 产品线
     */
	@ApiModelProperty("产品线")
	private String productLine;

    /**
     * 工序
     */
	@ApiModelProperty("工序")
	private String process;

    /**
     * 产品类别 民品 civil  汽车部品 car
     */
	@ApiModelProperty("产品类别 民品 civil  汽车部品 car")
	private String productType;

    /**
     * 是否有关联文件
     */
	@ApiModelProperty("是否有关联文件")
	private String haveLinkFile;

    /**
     * 关联程序文件版本ID
     */
	@ApiModelProperty("关联程序文件版本ID")
	private String programVersionId;

    /**
     * 关联程序文件编号
     */
	@ApiModelProperty("关联程序文件编号")
	private String programDocId;

    /**
     * 物料编码
     */
	@ApiModelProperty("物料编码")
	private String partNumber;

    /**
     * 物料描述
     */
	@ApiModelProperty("物料描述")
	private String partRemark;

    /**
     * 保管部门
     */
	@ApiModelProperty("保管部门")
	private String custodyDeptId;

    /**
     * 保存期限
     */
	@ApiModelProperty("保存期限")
	private String shelfLife;

    /**
     * 文件生效日期
     */
	@ApiModelProperty("文件生效日期")
	private Date fileEffectiveDate;

    /**
     * 修订日期
     */
	@ApiModelProperty("修订日期")
	private Date revisionDate;

    /**
     * 合规性
     */
	@ApiModelProperty("合规性")
	private String compliance;

    /**
     * 工厂
     */
	@ApiModelProperty("工厂")
	private String factorys;

    /**
     * 客户编码
     */
	@ApiModelProperty("客户编码")
	private String customerCode;

    /**
     * 设备编码
     */
	@ApiModelProperty("设备编码")
	private String deviceCode;

    /**
     * 设备名称
     */
	@ApiModelProperty("设备名称")
	private String deviceName;

    /**
     * 产品版本
     */
	@ApiModelProperty("产品版本")
	private String productVersion;

    /**
     * 内部文件编号
     */
	@ApiModelProperty("内部文件编号")
	private String internalDocId;

	/**
	 * 创建人名称
	 */
	@ExcelProperty(value = "勘误人")
	private String createName;

	/**
	 * 创建人名称
	 */
	@ExcelProperty(value = "勘误时间")
	private Date errataTime;
}

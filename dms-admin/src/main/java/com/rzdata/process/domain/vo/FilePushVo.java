package com.rzdata.process.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.rzdata.framework.annotation.ExcelDictFormat;
import com.rzdata.framework.convert.ExcelDictConvert;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 文件推送视图对象 doc_file_push
 *
 * <AUTHOR>
 * @date 2025-08-05
 */
@Data
@ApiModel("文件推送视图对象")
@ExcelIgnoreUnannotated
public class FilePushVo {

	private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
	@ExcelProperty(value = "主键")
	@ApiModelProperty("主键")
	private String id;

    /**
     * 流程id
     */
	@ExcelProperty(value = "流程id")
	@ApiModelProperty("流程id")
	private String applyId;

    /**
     * 推送公司Id
     */
	@ExcelProperty(value = "推送公司Id")
	@ApiModelProperty("推送公司Id")
	private String pushCompanyId;

    /**
     * 推送公司名称
     */
	@ExcelProperty(value = "推送公司名称")
	@ApiModelProperty("推送公司名称")
	private String pushCompanyName;

    /**
     * 推送分类Id
     */
	@ExcelProperty(value = "推送分类Id")
	@ApiModelProperty("推送分类Id")
	private String pashClassId;

    /**
     * 推送分类名称
     */
	@ExcelProperty(value = "推送分类名称")
	@ApiModelProperty("推送分类名称")
	private String pashClassName;

    /**
     * 接收人
     */
	@ExcelProperty(value = "接收人")
	@ApiModelProperty("接收人")
	private String receiveUserName;

    /**
     * 接收名称
     */
	@ExcelProperty(value = "接收名称")
	@ApiModelProperty("接收名称")
	private String receiveNickName;

    /**
     * 接收人部门id
     */
	@ExcelProperty(value = "接收人部门id")
	@ApiModelProperty("接收人部门id")
	private String receiveDeptId;

    /**
     * 接收人部门名称
     */
	@ExcelProperty(value = "接收人部门名称")
	@ApiModelProperty("接收人部门名称")
	private String receiveDeptName;

    /**
     * 文件Id
     */
	@ExcelProperty(value = "文件Id")
	@ApiModelProperty("文件Id")
	private String fileId;

    /**
     * 推送文件Id
     */
	@ExcelProperty(value = "推送文件Id")
	@ApiModelProperty("推送文件Id")
	private String pashVersionId;

    /**
     * 推送文件编号
     */
	@ExcelProperty(value = "推送文件编号")
	@ApiModelProperty("推送文件编号")
	private String pashDocId;

    /**
     * 推送文件名称
     */
	@ExcelProperty(value = "推送文件名称")
	@ApiModelProperty("推送文件名称")
	private String pashDocName;

    /**
     * 推送文件版本号
     */
	@ExcelProperty(value = "推送文件版本号")
	@ApiModelProperty("推送文件版本号")
	private String pashVersionValue;

    /**
     * 文件版本Id
     */
	@ExcelProperty(value = "文件版本Id")
	@ApiModelProperty("文件版本Id")
	private String versionId;

    /**
     * 推送状态
     */
	@ExcelProperty(value = "推送状态")
	@ApiModelProperty("推送状态")
	private String status;


}

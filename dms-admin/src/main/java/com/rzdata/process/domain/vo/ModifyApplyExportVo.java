package com.rzdata.process.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.rzdata.framework.annotation.ExcelDictFormat;
import com.rzdata.framework.convert.ExcelDictConvert;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 文件变更操作申请视图对象 doc_modify_apply
 *
 * <AUTHOR>
 * @date 2021-12-31
 */
@Data
@ApiModel("文件变更操作申请视图对象")
@ExcelIgnoreUnannotated
public class ModifyApplyExportVo {

	private static final long serialVersionUID = 1L;

	/**
	 * 序号
	 */
	@ExcelProperty(value = "序号")
	@ApiModelProperty("序号")
	private Integer indexNum;

    /**
     * 变更类型
     */
	@ExcelProperty(value = "变更类型", converter = ExcelDictConvert.class)
	@ApiModelProperty("变更类型")
	@ExcelDictFormat(dictType = "apply_type")
	private String changeType;

	/**
	 * 文件类型
	 */
	@ExcelProperty(value = "文件类型")
	@ApiModelProperty("文件类型")
	private String docClass;

	/**
	 * 文件名称
	 */
	@ExcelProperty(value = "文件名称")
	@ApiModelProperty("文件名称")
	private String docName;

	/**
	 * 文件编号
	 */
	@ExcelProperty(value = "文件编号")
	@ApiModelProperty("文件编号")
	private String docId;

	/**
	 * 版本号
	 */
	@ExcelProperty(value = "文件版本")
	@ApiModelProperty("版本号")
	private String versionValue;

	@ApiModelProperty("主键，根据编码规则生成")
	private String id;

	/**
	 * 流程状态
	 */
	@ApiModelProperty("流程状态")
	private String processStatus;

    /**
     * 编制部门编号
     */
	@ApiModelProperty("编制部门编号")
	private String deptId;

	@ExcelProperty(value = "编制部门")
	private String deptName;

    /**
     * 编制人
     */
	@ApiModelProperty("编制人")
	private String userName;

	@ExcelProperty(value = "编制人")
	private String nickName;
    /**
     * 申请时间
     */
	@ExcelProperty(value = "编制时间")
	@ApiModelProperty("申请时间")
	private Date applyTime;

    /**
     * 文件有效期
     */
	@ApiModelProperty("文件有效期")
	private Integer expiration;

	/**
	 * 文件版本ID
	 */
	@ApiModelProperty("版本ID")
	private String versionId;
    /**
     * 是否培训
     */
	@ApiModelProperty("是否培训")
	private String yNTrain;

    /**
     * 培训部门
     */
	@ApiModelProperty("培训部门")
	private String trainDept;

    /**
     * 是否已合稿
     */
	@ApiModelProperty("是否已合稿")
	private String yNMergeDraft;

    /**
     * 是否已签章
     */
	@ApiModelProperty("是否已签章")
	private String yNEncrypt;

    /**
     * 是否已分发
     */
	@ApiModelProperty("是否已分发")
	private String yNDistribute;

    /**
     * 编制文件编号
     */
	@ApiModelProperty("编制文件编号")
	private String fileId;

	/**
	 * 更新时间
	 */
	@ApiModelProperty(value = "更新时间")
	@ExcelProperty(value = "结束时间")
	private Date updateTime;

    /**
     * 记录状态
     */
	@ExcelProperty(value = "流程状态", converter = ExcelDictConvert.class)
	@ExcelDictFormat(dictType = "process_status")
	@ApiModelProperty("记录状态")
	private String recordStatus;

    /**
     * 变更原因
     */
	@ApiModelProperty("变更原因")
	private String changeReason;

    /**
     * 内容概述
     */
	@ApiModelProperty("内容概述")
	private String content;

    /**
     * 备注
     */
	@ApiModelProperty("备注")
	private String remark;

	/**
	 * 变更要素，多个以,隔开
	 */
	@ApiModelProperty("变更要素，多个以,隔开")
	private String changeFactor;


	@ApiModelProperty(value = "当前生效版本")
	private StandardVo file;
	/**
	 * 编制正文
	 */
	@ApiModelProperty(value = "编制正文")
	private ModifyApplyLinkVo standardDoc;

	/**
	 * 备注附件
	 */
	@ApiModelProperty(value = "备注附件")
	private List<ModifyApplyLinkVo> remarkDoc;

	/**
	 * 编制正文
	 */
	@ApiModelProperty(value = "上个编制正文")
	private ModifyApplyLinkVo preStandardDoc;

	/**
	 * 附件
	 */
	@ApiModelProperty(value = "附件文件")
	private List<ModifyApplyLinkVo> appendixes;

	/**
	 * 附件
	 */
	@ApiModelProperty(value = "附件文件")
	private List<ModifyApplyLinkVo> preAppendixes;

	/**
	 * 分发部门
	 */
	@ApiModelProperty(value = "分发部门", required = true)
	private List<ModifyApplyDistributeVo> distributeDepths;

	/**
	 * 关联文件
	 */
	@ApiModelProperty(value = "关联文件")
	private List<ModifyApplyLinkVo> docLinks;

	/**
	 * 关联或新增记录
	 */
	@ApiModelProperty(value = "关联或新增记录")
	private List<ModifyApplyLinkVo> recordLinks;

	/**
	 * 历史版本
	 */
	@ApiModelProperty(value = "历史版本")
	private List<VersionVo> versions;

	/**
	 * 培训记录
	 */
	@ApiModelProperty(value = "培训记录")
	private List<ModifyApplyTrainVo> trains;

	private String changeId;
}

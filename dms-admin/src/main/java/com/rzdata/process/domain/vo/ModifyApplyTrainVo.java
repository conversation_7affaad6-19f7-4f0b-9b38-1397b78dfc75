package com.rzdata.process.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;


/**
 * 文件变更操作申请培训记录视图对象 doc_modify_apply_train
 *
 * <AUTHOR>
 * @date 2022-01-08
 */
@Data
@ApiModel("文件变更操作申请培训记录视图对象")
@ExcelIgnoreUnannotated
public class ModifyApplyTrainVo {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    @ApiModelProperty("主键")
    private String id;

    /**
     * 流程编号
     */
    @ExcelProperty(value = "流程编号")
    @ApiModelProperty("流程编号")
    private String applyId;

    /**
     * 文件ID,多条
     */
    @ExcelProperty(value = "文件ID,多条")
    @ApiModelProperty("文件ID,多条")
    private String fileIds;

    /**
     * 文件信息
     */
    @ExcelProperty(value = "文件信息")
    @ApiModelProperty("文件信息")
    private List<BasicFileVo> files;
    /**
     * 培训人
     */
    @ExcelProperty(value = "培训人")
    @ApiModelProperty("培训人")
    private String userName;
    /**
     * 培训人昵称
     */
    @ExcelProperty(value = "培训人昵称")
    @ApiModelProperty("培训人昵称")
    private String nickName;

    /**
     * 培训人部门编号
     */
    @ExcelProperty(value = "培训人部门编号")
    @ApiModelProperty("培训人部门编号")
    private String deptId;
    /**
     * 培训人部门名称
     */
    @ExcelProperty(value = "培训人部门名称")
    @ApiModelProperty("培训人部门名称")
    private String deptName;

    /**
     * 培训时间
     */
    @ExcelProperty(value = "培训时间")
    @ApiModelProperty("培训时间")
    private Date trainTime;

    /**
     * 是否删除 1 是
     */
    @ExcelProperty(value = "是否删除 1 是")
    @ApiModelProperty("是否删除 1 是")
    private Integer isDeleted;

    /**
     * 文件版本ID
     */
    @ExcelProperty(value = "版本ID")
    @ApiModelProperty("版本ID")
    private String versionId;

    /**
     * 文件编号
     */
    @ExcelProperty(value = "文件编号")
    @ApiModelProperty("文件编号")
    private String docId;

    /**
     * 类型 培训记录：train  客户记录：customer
     */
    private String type;

    /**
     * 备注说明
     */
    private String remark;

    /**
     * 是否通过
     */
    private Boolean pass;

    private Date createTime;

    /**
     * 文件版本号
     */
    private String versionValue;

    private Integer num;

    private String docName;
}

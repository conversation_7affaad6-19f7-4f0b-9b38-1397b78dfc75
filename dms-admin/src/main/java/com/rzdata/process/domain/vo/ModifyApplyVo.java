package com.rzdata.process.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.rzdata.process.domain.DocExt;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.Year;
import java.util.Date;
import java.util.List;

/**
 * 文件变更操作申请视图对象 doc_modify_apply
 *
 * <AUTHOR>
 * @date 2021-12-31
 */
@Data
@ApiModel("文件变更操作申请视图对象")
@ExcelIgnoreUnannotated
public class ModifyApplyVo extends DocExt implements java.io.Serializable{

	private static final long serialVersionUID = 1L;

	/**
	 * 序号
	 */
	@ExcelProperty(value = "序号")
	@ApiModelProperty("序号")
	private Integer indexNum;

    /**
     * 主键，根据编码规则生成
     */
	@ExcelProperty(value = "主键，根据编码规则生成")
	@ApiModelProperty("主键，根据编码规则生成")
	private String id;

	/**
	 * 触发来源类型 体系文件台账stdd、项目文件台账project、ECN流程pre_change_apply
	 */
	private String invokeType;

	/**
	 * 触发来源ID 触发来源类型为stdd则为空、project为项目的主键、pre_change_apply为其表的主键
	 */
	private String invokeId;

	/**
	 * 所属项目ID
	 */
	private String projectId;

	/**
	 * 所属项目名字
	 */
	private String projectName;
	/**
	 * ECN流程编号
	 */
	private String preChangeCode;

    /**
     * 变更类型
     */
	@ExcelProperty(value = "变更类型")
	@ApiModelProperty("变更类型")
	private String changeType;

    /**
     * 文件类型
     */
	@ExcelProperty(value = "文件类型")
	@ApiModelProperty("文件类型")
	private String docClass;

    /**
     * 文件名称
     */
	@ExcelProperty(value = "文件名称")
	@ApiModelProperty("文件名称")
	private String docName;

    /**
     * 文件编号
     */
	@ExcelProperty(value = "文件编号")
	@ApiModelProperty("文件编号")
	private String docId;

    /**
     * 编制部门编号
     */
	@ExcelProperty(value = "编制部门编号")
	@ApiModelProperty("编制部门编号")
	private String deptId;

	private String deptName;

    /**
     * 编制人
     */
	@ExcelProperty(value = "编制人")
	@ApiModelProperty("编制人")
	private String userName;

	@TableField(exist = false)
	private String nickName;
    /**
     * 申请时间
     */
	@ExcelProperty(value = "申请时间")
	@ApiModelProperty("申请时间")
	private Date applyTime;

    /**
     * 文件有效期
     */
	@ExcelProperty(value = "文件有效期")
	@ApiModelProperty("文件有效期")
	private Integer expiration;

	/**
	 * 版本号
	 */
	@ExcelProperty(value = "版本号")
	@ApiModelProperty("版本号")
	private String versionValue;

	/**
	 * 文件版本ID
	 */
	@ExcelProperty(value = "版本ID")
	@ApiModelProperty("版本ID")
	private String versionId;
    /**
     * 是否培训
     */
	@ExcelProperty(value = "是否培训")
	@ApiModelProperty("是否培训")
	@JsonProperty("yNTrain")
	private String yNTrain;

    /**
     * 培训部门
     */
	@ExcelProperty(value = "培训部门")
	@ApiModelProperty("培训部门")
	private String trainDept;

    /**
     * 是否已合稿
     */
	@ExcelProperty(value = "是否已合稿")
	@ApiModelProperty("是否已合稿")
	@JsonProperty("yNMergeDraft")
	private String yNMergeDraft;

    /**
     * 是否已签章
     */
	@ExcelProperty(value = "是否已签章")
	@ApiModelProperty("是否已签章")
	@JsonProperty("yNEncrypt")
	private String yNEncrypt;

    /**
     * 是否分发
     */
	@ExcelProperty(value = "是否分发")
	@ApiModelProperty("是否分发")
	@JsonProperty("yNDistribute")
	private String yNDistribute;

    /**
     * 编制文件编号
     */
	@ExcelProperty(value = "编制文件编号")
	@ApiModelProperty("编制文件编号")
	private String fileId;

	/**
	 * 主文档PDF的file_id
	 */
	private String pdfFileId;

	/**
	 * 加密(签章)文件basic_file表id
	 */
	private String encryptFileId;

    /**
     * 记录状态
     */
	@ExcelProperty(value = "记录状态")
	@ApiModelProperty("记录状态")
	private String recordStatus;

	/**
	 * 流程状态
	 */
	@ExcelProperty(value = "流程状态")
	@ApiModelProperty("流程状态")
	private String processStatus;
	private Date processEndTime;

    /**
     * 变更原因
     */
	@ExcelProperty(value = "变更原因")
	@ApiModelProperty("变更原因")
	private String changeReason;

    /**
     * 内容概述
     */
	@ExcelProperty(value = "内容概述")
	@ApiModelProperty("内容概述")
	private String content;

    /**
     * 备注
     */
	@ExcelProperty(value = "备注")
	@ApiModelProperty("备注")
	private String remark;

	/**
	 * 变更要素，多个以,隔开
	 */
	@ExcelProperty(value = "变更要素，多个以,隔开")
	@ApiModelProperty("变更要素，多个以,隔开")
	private String changeFactor;

	//@ApiModelProperty(value = "当前生效版本")
	//private StandardVo file;
	/**
	 * 编制正文
	 */
	@ApiModelProperty(value = "编制正文(变更版本主文件)")
	private ModifyApplyLinkVo standardDoc;

	/**
	 * 备注附件
	 */
	@ApiModelProperty(value = "备注附件")
	private List<ModifyApplyLinkVo> remarkDoc;

	/**
	 * 编制正文
	 */
	@ApiModelProperty(value = "上个编制正文(当前生效版本主文件)")
	private DocLinkLogVo preStandardDoc;

	/**
	 * 附件
	 */
	@ApiModelProperty(value = "附件文件(变更版本附件)")
	private List<ModifyApplyLinkVo> appendixes;

	/**
	 * 附件
	 */
	@ApiModelProperty(value = "上个附件文件(当前生效版本附件)")
	private List<DocLinkLogVo> preAppendixes;

	/**
	 * 分发列表
	 */
	@ApiModelProperty(value = "分发列表", required = true)
	private List<ModifyApplyDistributeVo> distributeList;

	/**
	 * 关联文件
	 */
	@ApiModelProperty(value = "关联文件")
	private List<ModifyApplyLinkVo> docLinks;

	/**
	 * 关联或新增记录
	 */
	@ApiModelProperty(value = "关联或新增记录")
	private List<ModifyApplyLinkVo> recordLinks;

	/**
	 * 历史版本
	 */
	@ApiModelProperty(value = "历史版本")
	private List<VersionVo> versions;

	/**
	 * 签章链接
	 */
	@ApiModelProperty(value = "签章链接")
	private String signatureUrl;

	/**
	 * 是否已签章 Y=已 N=未 默认N
	 */
	@ApiModelProperty(value = "是否已签章 Y=已 N=未 默认N")
	private String isSignature;

	private String changeId;

	/**
	 * 设置生效时间
	 */
	private Date setupTime;

	private String dataType;

	private String procInstId;


	/**
	 * 上级文件版本ID
	 */
	private String upVersionId;

	/**
	 * 上级文件名称
	 */
	private String upDocName;

	/**
	 * 上级文件编号
	 */
	private String parentDocId;

	/**
	 * 分类所属类型
	 * DOC文件台账、RECORD记录台账、FOREIGN外来文件
	 */
	private String classType;

	/**
	 * 步骤进度
	 */
	private Integer step;

	/**
	 * 批次
	 */
	private String batch;

	/**
	 * 发布时间
	 */
	private Date releaseTime;

	/**
	 * 分发类型 公司 company、部门 dept、个人 person、部门和个人 dept_person
	 */
	private String distributeType;

	/**
	 * 分发类型 公司 company、部门 dept、个人 person、部门和个人 dept_person
	 */
	private String trainType;

	/**
	 * 是否上传客户记录 Y是 N否
	 */
	private String whetherCustomer;

	/**
	 * 文件状态
	 */
	private String docStatus;

	/**
	 * 是否留用 Y是 N否
	 */
	private String whetherRetain;

	/**
	 * 流程截止日期
	 */
	private Date deadline;

	/**
	 * 留用截止日期
	 */
	private Date retainDeadline;

	/**
	 * 当前处理环节
	 */
	private String actTaskName;

	/**
	 * 是当前处理人
	 */
	private String assigneeUserName;

	/**
	 * 合规性
	 */
	private String compliance;

	/**
	 * 批次Id
	 */
	private String batchId;

	private String type;

	/**
	 * 内部文件编号
	 */
	private String internalDocId;
}

package com.rzdata.process.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.rzdata.framework.annotation.ExcelDictFormat;
import com.rzdata.framework.convert.ExcelDictConvert;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 打印任务视图对象 doc_print_task
 *
 * <AUTHOR>
 * @date 2024-01-18
 */
@Data
@ApiModel("打印任务视图对象")
@ExcelIgnoreUnannotated
public class PrintTaskVo {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private String id;

    /**
     * 文件编号
     */
    @ExcelProperty(value = "文件编号")
    @ApiModelProperty("文件编号")
    private String docId;

    /**
     * 文件名称
     */
    @ExcelProperty(value = "文件名称")
    @ApiModelProperty("文件名称")
    private String docName;

    /**
     * 文件版本
     */
    @ExcelProperty(value = "文件版本")
    @ApiModelProperty("文件版本")
    private String versionId;

    /**
     * 打印份数
     */
    @ExcelProperty(value = "打印份数")
    @ApiModelProperty("打印份数")
    private Integer printCount;

    /**
     * 打印状态
     */
    @ExcelProperty(value = "打印状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "print_status")
    @ApiModelProperty("打印状态（pending待打印 printing 打印中 completed已打印 failure打印失败）")
    private String status;

    /**
     * 已打印份数
     */
    @ExcelProperty(value = "已打印份数")
    private Integer printedCount;
    /**
     * 打印机名称
     */
    @ExcelProperty(value = "打印机名称")
    @ApiModelProperty("打印机名称")
    private String printerName;

    /**
     * 打印描述
     */
    @ExcelProperty(value = "打印描述")
    @ApiModelProperty("打印描述")
    private String printDesc;

    /**
     * 创建者
     */
    @ApiModelProperty("创建者")
    private String createBy;

    /**
     * 创建者名称
     */
    @ExcelProperty(value = "打印人")
    @ApiModelProperty("创建者名称")
    private String createName;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 更新者
     */
    @ApiModelProperty("更新者")
    private String updateBy;

    /**
     * 更新者名称
     */
    @ApiModelProperty("更新者名称")
    private String updateName;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private Date updateTime;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;

    /**
     * 文件类型
     */
    @ExcelDictFormat(dictType = "change_type")
    @ApiModelProperty("文件类型")
    private String docType;

    /**
     * 电子文件名称
     */
    @ApiModelProperty("电子文件名称")
    private String electronicFileName;

    /**
     * 分类号
     */
    @ApiModelProperty("分类号")
    private String classificationNo;

    /**
     * 分发明细的ID
     */
    private String docDistributeId;

    /**
     * 文件的ID
     */
    private String fileId;
} 
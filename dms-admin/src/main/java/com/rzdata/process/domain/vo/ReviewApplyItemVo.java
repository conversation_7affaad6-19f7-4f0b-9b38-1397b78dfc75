package com.rzdata.process.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.rzdata.framework.core.domain.BaseEntity;
import com.rzdata.process.domain.WorkFlowInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;


/**
 * 文件复审申请视图对象 doc_review_apply
 *
 * <AUTHOR>
 * @date 2021-12-30
 */
@Data
@ApiModel("复审文件清单视图对象")
@ExcelIgnoreUnannotated
public class ReviewApplyItemVo extends BaseEntity {

	private static final long serialVersionUID=1L;

	private String id;
	/**
	 * 文件复审申请表主键
	 */
	private String applyId;

	/**
	 * 文件名称
	 */
	private String docName;
	/**
	 * 文件编号
	 */
	private String docId;

	/**
	 * 文件类型
	 */
	private String docClass;

	/**
	 * 版本号
	 */
	private String versionValue;
	/**
	 * 文件版本ID
	 */
	private String versionId;

	/**
	 * 租户id
	 */
	private String tenantId;

	/**
	 * 复审标准，EXTENSION=文件延长有效期，KEEP=文件保持现状，UPDATE=文件修订，DISUSE=文件作废
	 */
	private String reviewAction;

	/**
	 * 编制人
	 */
	private String userName;

	/**
	 * 编制部门
	 */
	private String deptId;

	/**
	 * 原因
	 */
	private String reason;

	/**
	 * 编制人
	 */
	private String nickName;

	/**
	 * 编制部门
	 */
	private String deptName;

	/**
	 * 复审时间
	 */
	private Date reviewTime;

	/**
	 * 设置时间
	 */
	private Date setupTime;

}

package com.rzdata.process.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.rzdata.framework.core.domain.BaseEntity;
import com.rzdata.process.domain.WorkFlowInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;


/**
 * 文件复审申请视图对象 doc_review_apply
 *
 * <AUTHOR>
 * @date 2021-12-30
 */
@Data
@ApiModel("文件复审申请视图对象")
@ExcelIgnoreUnannotated
public class ReviewApplyVo extends BaseEntity {

	private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
	@ExcelProperty(value = "主键")
	@ApiModelProperty("主键")
	private String id;
	/**
	 * 申请标题
	 */
	private String applyTitle;
	/**
	 * 申请类型
	 */
	private String applyType;
	/**
	 * 申请部门
	 */
	private String deptId;
	/**
	 * 申请人
	 */
	private String userName;
	/**
	 * 申请原因
	 */
	private String reason;
	/**
	 * 申请状态
	 */
	private String status;

	/**
	 * 体系文件台账stdd、项目文件台账project
	 */
	private String dataType;

	private String nickName;
	/**
	 * 文件复审清单信息
	 */
	private List<ReviewApplyItemVo> itemList;


	private String deptName;

	private Long reviewCycle;

	private String tenantId;

	private String classType;
	/**
	 *  批次
	 */
	private String batch;
}

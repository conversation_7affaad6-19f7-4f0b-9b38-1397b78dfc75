package com.rzdata.process.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.rzdata.process.domain.DocExt;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;


/**
 * 标准文件视图对象 doc_standard
 *
 * <AUTHOR>
 * @date 2021-12-30
 */
@Data
@ApiModel("标准文件视图对象")
@ExcelIgnoreUnannotated
public class StandardVo extends DocExt {

    private static final long serialVersionUID = 1L;

    /**
     * 文件编号，根据编号规则自动生成
     */
    @ExcelProperty(value = "文件编号")
    @ApiModelProperty("文件编号，根据编号规则自动生成")
    private String id;

    /**
     * 体系文件stdd、项目文件project
     */
    private String dataType;

    /**
     * 文件类型
     */
    @ExcelProperty(value = "文件类型")
    @ApiModelProperty("文件类型")
    private String docClass;

    /**
     * 文件名称
     */
    @ExcelProperty(value = "文件名称")
    @ApiModelProperty("文件名称")
    private String docName;

    /**
     * 文件状态
     */
    @ExcelProperty(value = "文件状态")
    @ApiModelProperty("文件状态")
    private String status;

    /**
     * 文件有效期
     */
    @ExcelProperty(value = "文件有效期")
    @ApiModelProperty("文件有效期")
    private Long expiration;

    /**
     * 日期格式显示文件有效期
     */
    @ApiModelProperty("文件有效期")
    private String expirationDate;

    /**
     * 当前文件版本
     */
    @ExcelProperty(value = "当前文件版本")
    @ApiModelProperty("当前文件版本")
    private String currentVersion;

    /**
     * 编制部门编号
     */
    @ExcelProperty(value = "编制部门编号")
    @ApiModelProperty("编制部门编号")
    private String deptId;

    /**
     * 编制部门名称
     */
    private String deptName;

    /**
     * 编制人
     */
    @ExcelProperty(value = "编制人")
    @ApiModelProperty("编制人")
    private String userName;

    /**
     * 编制人昵称
     */
    private String nickName;

    /**
     * 申请时间
     */
    @ExcelProperty(value = "申请时间")
    @ApiModelProperty("申请时间")
    private Date applyTime;

    /**
     * 编制文件编号
     */
    @ExcelProperty(value = "编制文件编号")
    @ApiModelProperty("编制文件编号")
    private String fileId;

    /**
     * 编制文件名称
     */
    private String fileName;

    /**
     * 加密文件编号
     */
    @ExcelProperty(value = "加密文件编号")
    @ApiModelProperty("加密文件编号")
    private String encryptFileId;

    @ExcelProperty(value = "合稿文件编号")
    @ApiModelProperty("合稿文件编号")
    private String mergeFileId;
    /**
     * 内容概述
     */
    @ExcelProperty(value = "内容概述")
    @ApiModelProperty("内容概述")
    private String content;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    @ApiModelProperty("备注")
    private String remark;

    /**
     * 培训部门
     */
    private String trainDept;

    /**
     * 编制正文
     */
    @ApiModelProperty(value = "编制正文")
    private DocLinkLogVo standardDoc;

    /**
     * 附件
     */
    @ApiModelProperty(value = "附件文件")
    private List<DocLinkLogVo> appendixes;

    /**
     * 当前生效版本
     */
    @ApiModelProperty(value = "编制正文(当前生效版本)")
    private DocLinkLogVo preStandardDoc;

    /**
     * 附件
     */
    @ApiModelProperty(value = "附件文件(当前生效附件)")
    private List<DocLinkLogVo> preAppendixes;

    /**
     * 备注附件
     */
    @ApiModelProperty(value = "备注附件")
    private List<DocLinkLogVo> remakeAppendixes;

    /**
     * 关联文件
     */
    @ApiModelProperty(value = "关联文件")
    private List<DocLinkLogVo> docLinks;

    /**
     * 关联或新增记录
     */
    @ApiModelProperty(value = "关联记录")
    private List<DocLinkLogVo> recordLinks;

    /**
     * 关联或新增记录
     */
    @ApiModelProperty(value = "备注附件")
    private List<DocLinkLogVo> remarkDoc;

    /**
     * 历史版本
     */
    @ApiModelProperty(value = "历史版本")
    private List<VersionVo> versions;

    /**
     * 分发部门
     */
    @ApiModelProperty(value = "分发部门", required = true)
    private List<ModifyApplyDistributeVo> distributeDepths;

    /**
     * 变更要素，多个以,隔开
     */
    @ApiModelProperty(value = "变更要素，多个以,隔开", required = true)
    private String changeFactor;

    /**
     * 变更原因
     */
    @ApiModelProperty(value = "变更原因", required = true)
    private String changeReason;

    /**
     * 变更类型
     */
    @ApiModelProperty(value = "变更类型", required = true)
    private String changeType;

    @ApiModelProperty("复审周期")
    private Date recheckDate;

    /**
     * 是否初始化文件
     * 否 = 0
     * 是 = 1
     */
    @ApiModelProperty("是否初始化文件")
    private String initFile;

    @JsonProperty("yNTrain")
    private Integer yNTrain;

    private String workflowStatus;

    private String tenantId;

    private Date endDate;

    private String versionValue;

    private String forever;

    private String className;
    /**
     * 版本生效时间
     */
    private Date startDate;
    /**
     * 复审时间
     */
    private Date reviewTime;

    private String fileType;

    /**
     * 文件编号
     */
    private String docId;

    /**
     * 文件编号
     */
    private String isSign;

    /**
     * 版本id
     */
    private String versionId;

    /**
     * 是否完成基线申请 0=否  1=是
     */
    private String baseStatus;

    private String procInstId;

    private String projectId;

    private String projectName;

    private Date releaseTime;

    /**
     * 上级文件版本ID
     */
    private String upVersionId;
    /**
     * 上级文件名称
     */
    private String upDocName;
    /**
     * 上级文件编号
     */
    private String parentDocId;
    /**
     * 分类所属类型
     * DOC文件台账、RECORD记录台账、FOREIGN外来文件
     */
    private String classType;

    private String secDeptId;
    /**
     * 分发类型 公司 company、部门 dept、个人 person、部门和个人 dept_person
     */
    private String distributeType;

    /**
     * 文件编号
     */
    private String docCode;

    /**
     * 编制人登录ID
     */
    private String editUserName;

    /**
     * 编制人姓名
     */
    private String editNickName;

    /**
     * 编制人所属组织ID
     */
    private String editDeptId;

    /**
     * 编制人所属组织名称
     */
    private String editDeptName;

    /**
     * 编制人所属二级部门ID
     */
    private String editSecDeptId;

    /**
     * 编制人所属二级部门名称
     */
    private String editSecDeptName;

    /**
     * 文件所属类型（是文件所属分类的顶级）
     */
    private String docClassType;


    /**
     * 产品线
     */
    private String productLine;

    /**
     * 工序
     */
    private String process;

    /**
     * 产品类别 民品 civil  汽车部品 car
     */
    private String productType;


    /**
     * 是否有关联文件
     */
    private String haveLinkFile;

    /**
     * 关联程序文件版本id
     */
    private String programVersionId;

    /**
     * 关联程序文件编号
     */
    private String programDocId;

    /**
     * 关联程序文件名称
     */
    private String programDocName;

    /**
     * 物料编码
     */
    private String partNumber;
    /**
     * 物料描述
     */
    private String partRemark;
    /**
     * 工厂
     */
    private String factorys;

    /**
     * 产品版本
     */
    private String productVersion;

    /**
     * 保管部门
     */
    private String custodyDeptId;

    /**
     * 保存期限
     */
    private String shelfLife;

    /**
     * 文件生效日期
     */
    private Date fileEffectiveDate;

    /**
     * 修订日期
     */
    private Date revisionDate;

    /**
     * 合规性
     */
    private String compliance;

    /**
     * 客户编码
     */
    private String customerCode;
    /**
     * 设备编码
     */
    private String deviceCode;

    /**
     * 设备名称
     */
    private String deviceName;

    /**
     * 是否上传客户记录 Y是 N否
     */
    private String whetherCustomer;

    /**
     * 内部文件编号
     */
    private String internalDocId;
}

package com.rzdata.process.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


@Data
@ApiModel("流程代办记录视图对象")
@ExcelIgnoreUnannotated
public class UniteworkTaskVo {

	private static final long serialVersionUID = 1L;

	@ApiModelProperty("流程定义名称")
	private String procDefName;

	@ApiModelProperty("流程标题")
	private String title;

	@ApiModelProperty("处理环节")
	private String curActName;

	@ApiModelProperty(value = "发起人")
	private String startUserName;

	@ApiModelProperty(value = "接收人ID")
	private String recUserId;

	@ApiModelProperty(value = "接收时间")
	private String sendTime;

	@ApiModelProperty(value = "代办地址")
	private String url;

	@ApiModelProperty(value = "文件编号")
	private String docId;

	@ApiModelProperty(value = "版本id")
	private String versionId;

	@ApiModelProperty(value = "文件版本")
	private String versionValue;

	@ApiModelProperty("当前环节")
	private String actTaskName;

	@ApiModelProperty("当前环节处理人")
	private String assigneeUserName;

	@ApiModelProperty("状态")
	private String status;

	@ApiModelProperty("部门id")
	private String deptId;

	@ApiModelProperty("流程实例id")
	private String procInstId;
	@ApiModelProperty("当前流程实例id")
	private String curActInstId;
	@ApiModelProperty("当前环节实例id")
	private String actInstId;
	@ApiModelProperty("父流程实例id")
	private String topProcInstId;
	@ApiModelProperty("请求类型")
	private String changeType;

	private Boolean doing;
}

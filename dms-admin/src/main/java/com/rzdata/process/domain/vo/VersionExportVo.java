package com.rzdata.process.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.rzdata.framework.annotation.ExcelDictFormat;
import com.rzdata.framework.convert.ExcelDictConvert;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;


/**
 * 文件版本记录视图对象 doc_version
 *
 * <AUTHOR>
 * @date 2021-12-30
 */
@Data
@ApiModel("文件版本记录视图对象")
@ExcelIgnoreUnannotated
public class VersionExportVo {

	private static final long serialVersionUID = 1L;

	/**
	 * 序号
	 */
	@ExcelProperty(value = "序号")
	@ApiModelProperty("序号")
	private Integer indexNum;

    /**
     * 主键
     */
	@ApiModelProperty("主键")
	private String id;

	/**
	 * 文件名称
	 */
	@ExcelProperty(value = "文件名称")
	@ApiModelProperty("文件名称")
	private String docName;

	/**
	 * 文件编号
	 */
	@ExcelProperty(value = "文件编号")
	@ApiModelProperty("文件编号")
	private String docId;

	/**
	 * 初版发行日期
	 */
	@ExcelProperty(value = "初版发行日期")
	@DateTimeFormat("yyyy-MM-dd")
	@ApiModelProperty("初版发行日期")
	private Date releaseTime;


	/**
	 * 文件版本
	 */
	@ExcelProperty(value = "文件版本")
	@ApiModelProperty("文件版本")
	private String versionValue;

	/**
	 * 版本生效时间
	 */
	@ExcelProperty(value = "生效日期")
	@DateTimeFormat("yyyy-MM-dd")
	@ApiModelProperty("生效日期")
	private Date startDate;

	@ExcelProperty(value = "修改内容")
	@ApiModelProperty("修改内容")
	private String content;

	@ExcelProperty(value = "文件复审日期")
	@DateTimeFormat("yyyy-MM-dd")
	@ApiModelProperty("复审日期")
	private Date reviewTime;

	@ExcelProperty(value = "修订日期")
	@DateTimeFormat("yyyy-MM-dd")
	@ApiModelProperty("修订日期")
	private Date revisionDate;
	@ExcelProperty(value = "失效日期")
	@DateTimeFormat("yyyy-MM-dd")
	@ApiModelProperty("失效日期")
	private Date endDate;

	@ExcelProperty(value = "文件状态",converter = ExcelDictConvert.class)
	@ApiModelProperty("文件状态")
	@ExcelDictFormat(readConverterExp = "0=草稿,1=有效,2=失效,3=留用,doing=变更中")
	private String status;


	@ExcelProperty(value = "编制部门")
	@ApiModelProperty("部门名称")
	private String deptName;

	@ExcelProperty(value = "编制人员")
	@ApiModelProperty("编制人员")
	private String nickName;

	@ExcelProperty(value="订单号")
	private String ext2;
	@ExcelProperty(value="计划单号")
	private String ext4;
	@ExcelProperty(value="合同编号")
	private String ext8;
	@ExcelProperty(value="外部编号")
	private String ext12;

	/**
	 * 文件类型
	 */
	@ApiModelProperty("文件类型")
	private String docClassName;


	/**
     * 变更申请流水号
     */
	@ApiModelProperty("变更申请流水号")
	private String applyId;


    /**
     * 变更原因
     */
	@ApiModelProperty("变更原因")
	private String reason;

	/**
	 * 文件类型
	 */
	@ApiModelProperty("文件类型")
	private String docClass;



	/**
	 * 编制部门编号
	 */

	@ApiModelProperty("编制部门编号")
	private String deptId;

	/**
	 * 编制人
	 */
	@ApiModelProperty("编制人")
	private String userName;

	/**
	 * 申请时间
	 */
	@ApiModelProperty("申请时间")
	private Date applyTime;

	/**
	 * 编制文件编号
	 */
	@ApiModelProperty("编制文件编号")
	private String fileId;

	@ApiModelProperty("合稿文件编号")
	private String mergeFileId;

	/**
	 * 加密文件编号
	 */
	@ApiModelProperty("加密文件编号")
	private String encryptFileId;

}

package com.rzdata.process.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.rzdata.framework.annotation.ExcelDictFormat;
import com.rzdata.framework.convert.ExcelDictConvert;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;


/**
 * 文件版本记录视图对象 doc_version
 *
 * <AUTHOR>
 * @date 2021-12-30
 */
@Data
@ApiModel("文件版本记录视图对象")
@ExcelIgnoreUnannotated
public class VersionForeignExportVo {


	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@ApiModelProperty("主键")
	private String id;



	/**
	 * 文件编号
	 */
	@ExcelProperty(value = "文件编号")
	@ApiModelProperty("文件编号")
	private String docId;

	/**
	 * 文件名称
	 */
	@ExcelProperty(value = "文件名称")
	@ApiModelProperty("文件名称")
	private String docName;



	/**
	 * 文件生效日期
	 */
	@ExcelProperty(value = "发行日期")
	@DateTimeFormat("yyyy-MM-dd")
	@ApiModelProperty("发行日期")
	private Date fileEffectiveDate;


	/**
	 * 文件版本号
	 */
	@ExcelProperty(value = "最新版本号")
	@ApiModelProperty("最新版本号")
	private String versionValue;



	@ExcelProperty(value = "受控日期")
	@DateTimeFormat("yyyy-MM-dd")
	@ApiModelProperty("受控日期")
	private Date startDate;



	@ExcelProperty(value = "修订日期")
	@DateTimeFormat("yyyy-MM-dd")
	@ApiModelProperty("修订日期")
	private Date revisionDate;

	@ExcelProperty(value = "失效日期")
	@DateTimeFormat("yyyy-MM-dd")
	@ApiModelProperty("失效日期")
	private Date endDate;

	@ExcelProperty(value = "修改内容")
	@ApiModelProperty("修改内容")
	private String content;

	@ExcelProperty(value = "文件状态",converter = ExcelDictConvert.class)
	@ApiModelProperty("文件状态")
	@ExcelDictFormat(dictType = "standard_status")
	private String status;

	@ExcelProperty(value = "合规性")
	@ApiModelProperty("合规性")
	private String compliance;

	/**
	 * 编制人
	 */
	@ExcelProperty(value = "编制人")
	@ApiModelProperty("编制人")
	private String userName;

	@ExcelProperty(value="订单号")
	private String ext2;
	@ExcelProperty(value="计划单号")
	private String ext4;
	@ExcelProperty(value="合同编号")
	private String ext8;
	@ExcelProperty(value="外部编号")
	private String ext12;

	/**
	 * 变更申请流水号
	 */
	@ApiModelProperty("变更申请流水号")
	private String applyId;


	/**
	 * 文件类型
	 */
	@ApiModelProperty("文件类型")
	private String docClassName;




	@ApiModelProperty("部门名称")
	private String deptName;

	@ApiModelProperty("编制人员")
	private String nickName;


	/**
	 * 变更原因
	 */
	@ApiModelProperty("变更原因")
	private String reason;

	/**
	 * 文件类型
	 */
	@ApiModelProperty("文件类型")
	private String docClass;



	/**
	 * 编制部门编号
	 */

	@ApiModelProperty("编制部门编号")
	private String deptId;


	/**
	 * 申请时间
	 */
	@ApiModelProperty("申请时间")
	private Date applyTime;

	/**
	 * 编制文件编号
	 */
	@ApiModelProperty("编制文件编号")
	private String fileId;

	@ApiModelProperty("合稿文件编号")
	private String mergeFileId;

	/**
	 * 加密文件编号
	 */
	@ApiModelProperty("加密文件编号")
	private String encryptFileId;


	/**
	 * 上次复审时间
	 */
	@ApiModelProperty("上次复审时间")
	private Date reviewTime;

	/**
	 * 版本生效截止时间
	 */
	@DateTimeFormat("yyyy-MM-dd")
	@ApiModelProperty("版本生效截止时间")
	private Date releaseTime;

}

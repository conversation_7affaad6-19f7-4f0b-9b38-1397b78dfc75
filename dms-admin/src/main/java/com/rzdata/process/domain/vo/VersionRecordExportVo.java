package com.rzdata.process.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;


/**
 * 文件版本记录视图对象 doc_version
 *
 * <AUTHOR>
 * @date 2021-12-30
 */
@Data
@ApiModel("文件版本记录视图对象")
@ExcelIgnoreUnannotated
public class VersionRecordExportVo {

	private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
	@ApiModelProperty("主键")
	private String id;

	/**
	 * 文件名称
	 */
	@ExcelProperty(value = "记录名称", index = 0)
	@ApiModelProperty("记录名称")
	private String docName;

    /**
     * 文件编号
     */
	@ExcelProperty(value = "记录编号", index = 1)
	@ApiModelProperty("记录编号")
	private String docId;

	/**
	 * 文件版本号
	 */
	@ExcelProperty(value = "版本号", index = 2)
	@ApiModelProperty("版本号")
	private String versionValue;


	/**
	 * 文件类型
	 */
	@ExcelProperty(value = "文件类型", index = 3)
	@ApiModelProperty("文件类型")
	private String docClassName;


	/**
	 * 修改时间
	 */
	@ExcelProperty(value = "修改时间", index = 5)
	@DateTimeFormat("yyyy-MM-dd")
	@ApiModelProperty("修改时间")
	private Date updateTime;


	/**
	 * 修改内容
	 */
	@ExcelProperty(value = "修改内容", index = 6)
	@ApiModelProperty("修改内容")
	private String content;

	/**
	 * 保存期限
	 */
	@ExcelProperty(value = "保存期限", index = 7)
	@ApiModelProperty("保存期限")
	private String shelfLife;


	@ExcelProperty(value = "编制部门", index = 8)
	@ApiModelProperty("部门名称")
	private String deptName;

	@ExcelProperty(value = "编制人员", index = 9)
	@ApiModelProperty("编制人员")
	private String nickName;


	/**
	 * 生效日期
	 */
	@ExcelProperty(value = "生效日期", index = 10)
	@DateTimeFormat("yyyy-MM-dd")
	@ApiModelProperty("生效日期")
	private Date startDate;

	/**
	 * 版本状态
	 */
	@ApiModelProperty("版本状态")
	private String status;

	/**
	 * 文件类型
	 */
	@ApiModelProperty("文件类型")
	private String docClass;

}

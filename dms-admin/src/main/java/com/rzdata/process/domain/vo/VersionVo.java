package com.rzdata.process.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.rzdata.framework.core.domain.BaseEntity;
import com.rzdata.process.domain.DocExt;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.Year;
import java.util.Date;


/**
 * 文件版本记录视图对象 doc_version
 *
 * <AUTHOR>
 * @date 2021-12-30
 */
@Data
@ApiModel("文件版本记录视图对象")
@ExcelIgnoreUnannotated
public class VersionVo extends DocExt {

	private static final long serialVersionUID = 1L;

	/**
	 * 序号
	 */
	@ExcelProperty(value = "序号")
	@ApiModelProperty("序号")
	private Integer indexNum;
    /**
     * 主键
     */
	@ExcelProperty(value = "主键")
	@ApiModelProperty("主键")
	private String id;

	/**
	 * 体系文件stdd、项目文件project
	 */
	private String dataType;

	/**
	 * 标准文件主键
	 */
	@ExcelProperty(value = "标准文件主键")
	@ApiModelProperty("标准文件主键")
	private String standardId;
    /**
     * 文件编号
     */
	@ExcelProperty(value = "文件编号")
	@ApiModelProperty("文件编号")
	private String docId;

	/**
	 * 文件名称
	 */
	@ExcelProperty(value = "文件名称")
	@ApiModelProperty("文件名称")
	private String docName;

	/**
	 * 关联文件编号
	 */
	@ApiModelProperty("关联文件编号")
	private String recordDocId;

    /**
     * 变更申请流水号
     */
	@ExcelProperty(value = "变更申请流水号")
	@ApiModelProperty("变更申请流水号")
	private String applyId;

    /**
     * 文件版本号
     */
	@ExcelProperty(value = "文件版本号")
	@ApiModelProperty("文件版本号")
	private String versionValue;

    /**
     * 版本生效时间
     */
	@ExcelProperty(value = "版本生效时间")
	@ApiModelProperty("版本生效时间")
	private Date startDate;

    /**
     * 版本生效截止时间
     */
	@ExcelProperty(value = "版本生效截止时间")
	@ApiModelProperty("版本生效截止时间")
	private Date endDate;

    /**
     * 版本状态
     */
	@ExcelProperty(value = "版本状态")
	@ApiModelProperty("版本状态")
	private String status;

    /**
     * 变更原因
     */
	@ExcelProperty(value = "变更原因")
	@ApiModelProperty("变更原因")
	private String reason;


	/**
	 * 内容概述
	 */
	@ApiModelProperty("内容概述")
	private String content;

	/**
	 * 备注
	 */
	@ApiModelProperty("备注")
	private String remark;

	/**
	 * 变更要素，多个以,隔开
	 */
	@ApiModelProperty("变更要素，多个以,隔开")
	private String changeFactor;

	/**
	 * 变更原因
	 */
	@ApiModelProperty("变更原因")
	private String changeReason;
	/**
	 * 文件类型
	 */
	@ExcelProperty(value = "文件类型")
	@ApiModelProperty("文件类型")
	private String docClass;

	/**
	 * 编制部门编号
	 */
	@ExcelProperty(value = "编制部门编号")
	@ApiModelProperty("编制部门编号")
	private String deptId;

	/**
	 * 编制人
	 */
	@ExcelProperty(value = "编制人")
	@ApiModelProperty("编制人")
	private String userName;

	/**
	 * 变更类型
	 */
	private String changeType;

	/**
	 * 触发来源类型 体系文件台账stdd、项目文件台账project、ECN流程pre_change_apply
	 */
	private String invokeType;

	/**
	 * 触发来源ID 触发来源类型为stdd则为空、project为项目的主键、pre_change_apply为其表的主键
	 */
	private String invokeId;

	/**
	 * 编制二级部门编号
	 */
	@ExcelProperty(value = "编制二级部门编号")
	@ApiModelProperty("编制二级部门编号")
	private String secDeptId;

	/**
	 * 申请时间
	 */
	@ExcelProperty(value = "申请时间")
	@ApiModelProperty("申请时间")
	private Date applyTime;

	/**
	 * 编制文件编号
	 */
	@ApiModelProperty("编制文件编号")
	private String fileId;

	@ExcelProperty(value = "合稿文件编号")
	@ApiModelProperty("合稿文件编号")
	private String mergeFileId;

	/**
	 * 加密文件编号
	 */
	@ExcelProperty(value = "加密文件编号")
	@ApiModelProperty("加密文件编号")
	private String encryptFileId;

	@ApiModelProperty("部门名称")
	private String deptName;

	private String compileDeptName;

	/**
	 * 上次复审时间
	 */
	@ExcelProperty(value = "上次复审时间")
	@ApiModelProperty("上次复审时间")
	private Date reviewTime;

	private String forever;

	private String className;

	private String changeContent;

	private String receiveStatus;
	/**
	 * 培训部门
	 */
	@ExcelProperty(value = "流程实例id")
	@ApiModelProperty("流程实例id")
	private String procInstId;

	private Boolean hasPerms;

	private Boolean isBorrow;

	private String versionId;

	private Boolean inFavorites;

	private String baseStatus;

	/**
	 * 发布时间
	 */
	private Date releaseTime;

	/**
	 * 上级文件版本ID
	 */
	private String upVersionId;
	/**
	 * 上级文件编号
	 */
	private String parentDocId;
	/**
	 * 分类所属类型
	 * DOC文件台账、RECORD记录台账、FOREIGN外来文件
	 */
	private String classType;

	private String nickName;

	/**
	 * 分发类型 公司 company、部门 dept、个人 person、部门和个人 dept_person
	 */
	private String distributeType;

	/**
	 * 产品线
	 */
	private String productLine;

	/**
	 * 是否上传客户记录 Y是 N否
	 */
	private String whetherCustomer;
	/**
	 * 合规性
	 */
	private String compliance;

	/**
	 * standard状态
	 */
	private String standardStatus;

	/**
	 * 内部文件编号
	 */
	private String internalDocId;

	/**
	 * 建议数量
	 */
	private Integer adviseCount;
}

package com.rzdata.process.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.rzdata.framework.annotation.ExcelDictFormat;
import com.rzdata.framework.convert.ExcelDictConvert;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;


/**
 * 流程申请记录视图对象 doc_workflow_apply_log
 *
 * <AUTHOR>
 * @date 2022-01-05
 */
@Data
@ApiModel("流程申请记录视图对象")
@ExcelIgnoreUnannotated
public class WorkflowApplyLogVo {

	private static final long serialVersionUID = 1L;

	/**
	 * 序号
	 */
	@ExcelProperty(value = "序号")
	@ApiModelProperty("序号")
	private Integer indexNum;

    /**
     * 主键
     */
	@ApiModelProperty("主键")
	private String id;

    /**
     * 流程类别
     */
	@ExcelProperty(value = "变更类型", converter = ExcelDictConvert.class)
	@ExcelDictFormat(dictType = "sys_apply_type")
	@ApiModelProperty("变更类型")
	private String applyClass;

    /**
     * 流程处理状态
     */
	@ApiModelProperty("流程处理状态")
	private String applyStatus;

    /**
     * 文件名称
     */
	@ExcelProperty(value = "流程标题")
	@ApiModelProperty("文件名称")
	private String docName;

	/**
	 * 文件编号
	 */
	@ExcelProperty(value = "文件编号")
	@ApiModelProperty("文件编号")
	private String docId;

	@ApiModelProperty("部门名称")
	@ExcelProperty(value = "申请部门")
	private String deptName;

	@ExcelProperty(value = "申请人")
	private String nickName;


    /**
     * 文件类型
     */
	@ApiModelProperty("文件类型")
	private String docClass;

    /**
     * 文件版本ID
     */
	@ApiModelProperty("文件版本ID")
	private String versionId;

    /**
     * 文件版本号
     */
	@ApiModelProperty("文件版本号")
	private String versionValue;

    /**
     * 发送人
     */
	@ApiModelProperty("发送人")
	private String sender;

    /**
     * 编制部门编号
     */
	@ApiModelProperty("编制部门编号")
	private String deptId;

    /**
     * 流程实例ID
     */
	@ApiModelProperty("流程实例ID")
	private String procInstId;

    /**
     * 流程实例key
     */
	@ApiModelProperty("流程实例key")
	private String procDefKey;

	/**
	 * 流程状态
	 */
	@ExcelProperty(value = "流程状态", converter = ExcelDictConvert.class)
	@ExcelDictFormat(dictType = "process_status")
	private String procStatus;
    /**
     * 申请时间
     */
	@ApiModelProperty("当前环节名称")
	private String actDefName;

	@ExcelProperty(value = "当前环节名称")
	@ApiModelProperty("当前环节名称")
	private String procName;
	@ExcelProperty(value = "当前环节处理人")
	@ApiModelProperty("当前环节处理人")
	private String procNameUser;

	/**
	 * 申请时间
	 */
	@ExcelProperty(value = "申请时间")
	@ApiModelProperty("申请时间")
	private Date applyTime;

	/**
	 * 更新时间
	 */
	@ApiModelProperty(value = "更新时间")
	@ExcelProperty(value = "结束时间")
	private Date updateTime;

	private String userName;

	@ApiModelProperty("变更原因")
	private String changeType;

	//@ApiModelProperty("当前生效版本")
	//private String validityVersion;

	//@ApiModelProperty("生效时间")
	//private Date validityTime;
}

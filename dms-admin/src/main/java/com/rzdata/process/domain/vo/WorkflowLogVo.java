package com.rzdata.process.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.rzdata.framework.annotation.ExcelDictFormat;
import com.rzdata.framework.convert.ExcelDictConvert;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;


/**
 * 流程审批记录视图对象 doc_workflow_log
 *
 * <AUTHOR>
 * @date 2021-12-30
 */
@Data
@ApiModel("流程审批记录视图对象")
@ExcelIgnoreUnannotated
public class WorkflowLogVo {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@ExcelProperty(value = "主键")
	@ApiModelProperty("主键")
	private String id;

	@ApiModelProperty("主键")
	private String flowId;

	/**
	 * 流程实例ID
	 */
	@ExcelProperty(value = "流程实例ID")
	@ApiModelProperty("流程实例ID")
	private String procInstId;

	/**
	 * 业务ID
	 */
	@ExcelProperty(value = "业务ID")
	@ApiModelProperty("业务ID")
	private String businessId;

	/**
	 * 当前环节定义Id
	 */
	@ApiModelProperty(value = "当前环节定义Id")
	private String actDefId;
	/**
	 * 当前环节定义名称
	 */
	@ApiModelProperty(value = "当前环节定义名称")
	private String actDefName;
	/**
	 * 下一环节定义Id
	 */
	@ApiModelProperty(value = "下一环节定义Id")
	private String nextDefId;
	/**
	 * 下一环节定义名称
	 */
	@ApiModelProperty(value = "下一环节定义名称")
	private String nextDefName;

	/**
	 * 当前环节实例ID
	 */
	@ExcelProperty(value = "当前环节实例ID")
	@ApiModelProperty("环节实例ID")
	private String actInstId;

	/**
	 * 意见
	 */
	@ExcelProperty(value = "意见")
	@ApiModelProperty("意见")
	private String opinion;

	/**
	 * 发送人
	 */
	@ExcelProperty(value = "发送人")
	@ApiModelProperty("发送人")
	private String sender;

	/**
	 * 发送人部门
	 */
	@ApiModelProperty(value = "发送人部门", required = true)
	private String senderDeptId;

	/**
	 * 接收人
	 */
	@ExcelProperty(value = "接收人")
	@ApiModelProperty("接收人")
	private String receiver;

	/**
	 * 环节处理状态
	 */
	@ExcelProperty(value = "环节处理状态")
	@ApiModelProperty("环节处理状态")
	private String actStatus;

	@ApiModelProperty("流程实例key")
	private String procDefKey;

	/**
	 * 创建时间
	 */
	@ApiModelProperty(value = "创建时间")
	private Date createTime;

	/**
	 * 更新时间
	 */
	@ApiModelProperty(value = "更新时间")
	private Date updateTime;

	@ApiModelProperty("审批人名称")
	private String userName;

	@ApiModelProperty("部门名称")
	private String deptName;

	@ApiModelProperty("职位名称")
	private String postName;

	private Boolean review;

	private String nickName;
	/**
	 * 流程是否通过 true=通过 false=不通过
	 */
	private Boolean pass;

	/**
	 * 会审批次 批次相同为同一次会审
	 */
	private String batch;

	/**
	 * 标签
	 */
	private String title;

	/**
	 * 转派状态
	 */
	private Boolean transfer;


	/**
	 * 环节序号
	 */
	private Long actDefOrder;

	/**
	 * 标记
	 */
	private String mark;
	/**
	 * 方向 true正 false反
	 */
	private Boolean direction;
}

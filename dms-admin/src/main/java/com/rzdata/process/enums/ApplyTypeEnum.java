package com.rzdata.process.enums;

import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @Date 2022/1/4 上午9:21
 * @Version 1.0
 * @Desc 工作流类型枚举
 */
public enum ApplyTypeEnum {
    /**
     * 未知
     */
    UNKNOWN,
    /**
     * 文件变更
     */
    CHANGE,
    /**
     * 文件新增
     */
    ADD,
    /**
     * 修订
     */
    UPDATE,
    /**
     * 作废
     */
    DISUSE,
    /**
     * 复审
     */
    REVIEW,
    /**
     * 补发
     */
    REISSUE,
    /**
     * 增发
     */
    EXTRA,
    /**
     * 借阅
     */
    BORROW,
    /**
     * 丢失
     */
    LOST,

    /**
     * 留用
     */
    RETAIN;

    public static ApplyTypeEnum toType(String type) {
        return Stream.of(ApplyTypeEnum.values()).filter(v -> v.name().equalsIgnoreCase(type)).findFirst().orElse(ApplyTypeEnum.UNKNOWN);
    }
}

package com.rzdata.process.enums;


/**
 * 文件站内消息
 * <AUTHOR>
 * @date 2024年5月7日19:26:19
 */
public enum DocMessageEnum {

    /**
     * 文件变更
     */
    FLOW_CHANGE(103,"【文件申请】文件 %s 申请通过"),
    /**
     * 文件新增
     */
    FLOW_ADD(104,"【文件新增】文件 %s(%s) 已发布"),
    /**
     * 文件修订
     */
    FLOW_UPDATE(105,"【文件修订】文件 %s(%s) 已发布"),

    /**
     * 文件作废
     */
    FLOW_DISUSE(106,"【文件作废】文件 %s(%s) 已作废"),

    /**
     * 增发
     */
    FLOW_EXTRA(107,"【文件增发】文件 %s(%s) 已增发"),

    /**
     * 补发
     */
    FLOW_REISSUE(108,"【文件补发】文件 %s(%s) 已补发"),

    /**
     * 丢失
     */
    FLOW_LOST(109,"【文件丢失】文件 %s(%s) 已丢失"),

    /**
     * 复审
     */
    FLOW_REVIEW(110,"【文件复审】文件 %s(%s) 已复审"),

    /**
     * 打印
     */
    FLOW_PRINT(110,"【文件打印】文件 %s(%s) 已经发布，请联系部门文控进行打印！"),

    /**
     * 借阅
     */
    FLOW_BORROW(111,"【文件借阅】文件 %s(%s) 已借阅"),

    /**
     * 流程催办
     */
    PROCESS_SUPERVISE(112,"【流程催办】流程待办 %s 请尽快办理"),


    /**
     * 回收
     */
    FLOW_RECYCLE(113,"文件名称：%s, 版本: %s，该文件已失效，请于2个工作日内完成回收。"),
    /**
     * 打印
     */
    SEND_MSG_TYPE_PRINT(114,"文件名称：%s, 版本: %s，请联系部门文控进行打印。"),

    /**
     * 文件有效期提醒
     */
    SEND_MSG_TYPE_LIFESPAN(115,"【文件有效期】文件名称：%s, 版本: %s，到期（%s）"),
    ;

    DocMessageEnum(Integer code, String status) {
        this.code = code;
        this.msg = status;
    }
    private Integer code;
    private String msg;

    public Integer getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    public static String getMsg(String code){
        for (DocMessageEnum sendType : DocMessageEnum.values()){
            if(sendType.getCode().equals(code)){
                return sendType.getMsg();
            }
        }
        return "";
    }
}

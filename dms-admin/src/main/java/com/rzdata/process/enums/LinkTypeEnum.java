package com.rzdata.process.enums;

import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @Date 2022/1/5 下午1:51
 * @Version 1.0
 * @Desc 链接类型
 */
public enum LinkTypeEnum {
    /**
     * 未知
     */
    UNKNOWN,
    /**
     * 正文
     */
    DOC,
    /**
     * 附件
     */
    APPENDIX,

    /**
     * 备注附件
     */
    APPENDIX_REMARK,

    /**
     * 正文
     */
    //DOC_1,
    /**
     * 附件
     */
    //APPENDIX_1,
    /**
     * 记录
     */
    RECORD,

    /**
     * 记录 多对多
     */
    NOTE,

    /**
     * 记录文件(多对多) 关联的主文件
     */
    NOTE_DOC,
    /**
     * 关联其它文件
     */
    REF_DOC,

    /**
     * 外来文件
     */
    FOREIGN;

    public static LinkTypeEnum toType(String type) {
        return Stream.of(LinkTypeEnum.values()).filter(v -> v.name().equalsIgnoreCase(type)).findFirst().orElse(LinkTypeEnum.UNKNOWN);
    }
}

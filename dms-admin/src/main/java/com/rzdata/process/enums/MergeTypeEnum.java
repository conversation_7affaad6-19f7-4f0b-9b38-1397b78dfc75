package com.rzdata.process.enums;


import lombok.Getter;

import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @Date 2022/3/3 14:54
 * @Version 1.0
 * @Description 预览来源:本部门 外部门 公司文件 借阅
 */
@Getter
public enum MergeTypeEnum {
    /**
     * 未知
     */
    UNKNOWN("UNKNOWN"),
    /**
     * 生效文件
     */
    VALID("VALID"),
    /**
     * 失效文件
     */
    INVALID("INVALID");

    private String type;

    MergeTypeEnum(String type) {
        this.type = type;
    }
    public static MergeTypeEnum toType(int type) {
        return Stream.of(MergeTypeEnum.values()).filter(v -> v.getType().equals(type)).findFirst().orElse(MergeTypeEnum.UNKNOWN);
    }

}

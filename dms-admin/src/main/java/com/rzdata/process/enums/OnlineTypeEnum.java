package com.rzdata.process.enums;


public enum OnlineTypeEnum {

    /**
     * 在线编辑
     */
    EDIT("online_edit","在线编辑"),

    /**
     * 在线修订
     */
    REVISION("online_revision","在线修订"),

    /**
     * 在线批注
     */
    ANNOTATION("online_annotation","在线批注"),

    ERRATA("errata","勘误");

    OnlineTypeEnum(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }
    private String code;
    private String msg;

    public String getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    public static String getMsg(String code){
        for (OnlineTypeEnum recordStatusEnum : OnlineTypeEnum.values()){
            if(recordStatusEnum.getCode().equals(code)){
                return recordStatusEnum.getMsg();
            }
        }
        return "";
    }
}

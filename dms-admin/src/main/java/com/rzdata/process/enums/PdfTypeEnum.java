package com.rzdata.process.enums;


public enum PdfTypeEnum {
    BASICFILE("basicFile","原文件"),
    /**
     * 转换
     */
    TRANSITION("transition","转换"),
    /**
     * 生成封面
     */
    COVER("cover","封面"),
    /**
     * 合稿封面
     */
    MERGE("merge","合稿"),
    /**
     * 签章
     */
    SIGNATURE("signature","签章"),

    /**
     * 分发
     */
    DISTRIBUTE("distribute","分发"),

    /**
     * 版本比对
     */
    COMPARE("compare","比对");

    PdfTypeEnum(String code, String status) {
        this.code = code;
        this.msg = status;
    }
    private String code;
    private String msg;

    public String getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    public static String getMsg(String code){
        for (PdfTypeEnum applyStatusEnum : PdfTypeEnum.values()){
            if(applyStatusEnum.getCode().equals(code)){
                return applyStatusEnum.getMsg();
            }
        }
        return "";
    }
}

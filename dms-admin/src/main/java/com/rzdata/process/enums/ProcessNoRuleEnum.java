package com.rzdata.process.enums;

import java.util.stream.Stream;

/**
 * @auther xcy
 * @create 2022-01-11 11:18
 * 流程类型编号规则 固定字符串
 */
public enum ProcessNoRuleEnum {
    /**
     * 未知
     */
    UNKNOWN,

    /**
     * 变更
     */
    BG,

    /**
     * 借阅
     */
    JY,

    /**
     * 增发
     */
    ZF,

    /**
     * 新增
     */
    XZ,

    /**
     * 作废
     */
    SC,

    /**
     * 修订
     */
    XD,

    /**
     * 复审
     */
    FS,

    /**
     * 建议
     */
    WJ,

    /**
     * 补发
     */
    BF,

    /**
     * 丢失
     */
    DS;

    public static ProcessNoRuleEnum toType(String type) {
        return Stream.of(ProcessNoRuleEnum.values()).filter(v -> v.name().equalsIgnoreCase(type)).findFirst().orElse(ProcessNoRuleEnum.UNKNOWN);
    }

}

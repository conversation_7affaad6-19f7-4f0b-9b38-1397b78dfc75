package com.rzdata.process.enums;


public enum RecordStatusEnum {

    /**
     * 草稿
     */
    DRAFT("draft","草稿"),

    /**
     * 进行中
     */
    DOING("doing","进行中"),

    /**
     * 已完成
     */
    DONE("done","已办结"),

     /**
     * 作废
     */
    CANCEL("cancel","已撤销");

    RecordStatusEnum(String code,String msg) {
        this.code = code;
        this.msg = msg;
    }
    private String code;
    private String msg;

    public String getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    public static String getMsg(String code){
        for (RecordStatusEnum recordStatusEnum : RecordStatusEnum.values()){
            if(recordStatusEnum.getCode().equals(code)){
                return recordStatusEnum.getMsg();
            }
        }
        return "";
    }
}

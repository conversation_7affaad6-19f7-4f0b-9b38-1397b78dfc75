package com.rzdata.process.enums;


/**
 * 发送邮件类型
 * <AUTHOR>
 * @date 2024年5月7日10:44:27
 */
public enum SendType {
    /**
     * 归档提醒
     */
    GDTX("gdtx","归档提醒"),
    FS("fs","文件复审提醒"),
    XG("xg","体系文件宣贯提醒"),
    LZ("lz","编制人员离职提醒"),
    SUPERVISE("supervise","流程催办提醒"),

    /**
     * 待办事项提醒
     */
    TO_DO_REMINDER("toDoReminder","待办事项提醒"),

    /**
     * 邮件变更通知
     */
    GLWJ("glwj","关联文件提醒"),
    HS("hs","文件回收提醒"),
    PRINT("print","文件打印提醒"),
    GDXG("gdxg","文件归档和宣贯提醒"),
    ZF("zf","文件作废提醒"),
    LIFESPAN("lifespan","文件有效期提醒"),
    FILE_REVIEW_PROMPT("file_review_prompt","文件复审提醒"),
    ;

    SendType(String code, String status) {
        this.code = code;
        this.msg = status;
    }
    private String code;
    private String msg;

    public String getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    public static String getMsg(String code){
        for (SendType sendType : SendType.values()){
            if(sendType.getCode().equals(code)){
                return sendType.getMsg();
            }
        }
        return "";
    }
}

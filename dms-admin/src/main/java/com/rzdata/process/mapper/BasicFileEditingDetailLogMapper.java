package com.rzdata.process.mapper;

import com.rzdata.framework.core.mybatisplus.core.BaseMapperPlus;
import com.rzdata.process.domain.BasicFileEditingDetailLog;
import com.rzdata.process.domain.bo.BasicFileEditingDetailLogBo;
import org.apache.ibatis.annotations.Param;

/**
 * 编辑明细日志Mapper接口
 *
 * <AUTHOR>
 * @date 2025-04-30
 */
public interface BasicFileEditingDetailLogMapper extends BaseMapperPlus<BasicFileEditingDetailLog> {
    /**
     * 查询文件最后一条编辑记录
     *
     * @param protoFileId 文件ID
     * @return 编辑记录
     */
    BasicFileEditingDetailLog selectLastEditLog(@Param("protoFileId") String protoFileId);

    int releaseFileEditLock(@Param("bo") BasicFileEditingDetailLogBo bo);
}

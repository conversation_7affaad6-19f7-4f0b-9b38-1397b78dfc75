package com.rzdata.process.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rzdata.framework.core.mybatisplus.core.BaseMapperPlus;
import com.rzdata.process.domain.DocLinkLog;
import com.rzdata.process.domain.vo.DocLinkSearchResultVo;
import com.rzdata.process.domain.bo.DocLinkLogBo;
import com.rzdata.process.domain.vo.DocLinkLogVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 文件关联记录Mapper接口
 *
 * <AUTHOR>
 * @date 2022-01-06
 */
public interface DocLinkLogMapper extends BaseMapperPlus<DocLinkLog> {

    List<DocLinkLog> queryLink(@Param("bo") DocLinkLogBo bo);

    Page<DocLinkLogVo> getPageList(@Param("page") Page<DocLinkLogVo> page, @Param("bo") DocLinkLogBo bo);

    /**
     * 查询文件关联记录
     * @param versionId 版本号
     * @param linkType 关联类型: DOC正文 APPENDIX附件 RECORD记录 REF_DOC关联其他文件
     * @return
     */
    List<DocLinkLogVo> queryDocLinkVo(@Param("versionId")String versionId, @Param("linkType") String linkType);

    int getLinkCount(@Param("bo") DocLinkLogBo bo);

    /**
     * 根据文件id查询
     * @param fileIds
     * @return
     */
    List<DocLinkLogVo> queryByfileIdsDocLinkVo(@Param("fileIds") List<String> fileIds);

    /**
     * 获取某版本下有效的一条有文件编号的关联记录
     * @param versionId
     * @param linkCode
     * @return
     */
    Boolean disuseValidLinkLog(@Param("versionId")String versionId, @Param("linkCode") String linkCode);

    List<DocLinkLogVo> queryInit2Account(@Param("dataType")String dataType,
                                         @Param("docClass")String docClass,
                                         @Param("standardId")String standardId,
                                         @Param("versionId")String versionId,
                                         @Param("linkCode") String linkCode);
    Page<DocLinkSearchResultVo> docLinkSearchPage(@Param("page") Page<DocLinkSearchResultVo> page, @Param("bo") DocLinkLogBo bo);

    List<DocLinkSearchResultVo> docLinkSearchItems(@Param("versionId") String versionId,@Param("linkTypeList") List<String> linkTypeList);

}

package com.rzdata.process.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rzdata.framework.core.mybatisplus.core.BaseMapperPlus;
import com.rzdata.process.domain.DocDistribute;
import com.rzdata.process.domain.bo.DocDisStatisticsBo;
import com.rzdata.process.domain.bo.DocStatisticsBo;
import com.rzdata.process.domain.bo.DocStatisticsTrainingBo;
import com.rzdata.process.domain.vo.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/2/8 17:05
 * @Version 1.0
 * @Description
 */
@Mapper
public interface DocStatisticsMapper extends BaseMapperPlus<DocDistribute> {
    /**
     * 变更类型统计
     * @param bo 文档统计参数实体
     * @return DocStatisticsVo
     */
    Page<DocStatisticsChangeTypeVo> selectChangeType(@Param("page") Page<DocStatisticsChangeTypeVo> page, @Param("bo") DocStatisticsBo bo);



    DocStatisticsChangeTypeVo selectChangeTypeSum(@Param("bo") DocStatisticsBo bo);
    /**
     * 变更要素统计
     * @param bo 文档统计参数实体
     * @return DocStatisticsVo
     */
    List<DocStatisticsChangeFactorVo> selectChangeFactor(@Param("bo") DocStatisticsBo bo);


    /**
     * 变更类型统计
     * @param bo 文档统计参数实体
     * @return DocStatisticsVo
     */
    Page<DocStatisticsTrainingVo> selectTraining(@Param("page") Page<DocStatisticsTrainingVo> page, @Param("bo") DocStatisticsTrainingBo bo);
}

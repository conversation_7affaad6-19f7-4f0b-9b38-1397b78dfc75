package com.rzdata.process.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rzdata.process.domain.DocVersionFavorites;
import com.rzdata.framework.core.mybatisplus.core.BaseMapperPlus;
import com.rzdata.process.domain.bo.DocDistributeBo;
import com.rzdata.process.domain.vo.DocDistributeVo;
import org.apache.ibatis.annotations.Param;

/**
 * 我的收藏Mapper接口
 *
 * <AUTHOR>
 * @date 2023-01-03
 */
public interface DocVersionFavoritesMapper extends BaseMapperPlus<DocVersionFavorites> {

    Page<DocDistributeVo> queryPageFavorites(@Param("page")Page<DocDistributeVo> page, @Param("bo") DocDistributeBo bo, @Param("loginDeptId") String loginDeptId, @Param("userId") String userId);

}

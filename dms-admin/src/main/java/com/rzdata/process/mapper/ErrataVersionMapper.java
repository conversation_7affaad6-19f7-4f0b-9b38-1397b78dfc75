package com.rzdata.process.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rzdata.process.domain.ErrataVersion;
import com.rzdata.framework.core.mybatisplus.core.BaseMapperPlus;
import com.rzdata.process.domain.bo.ErrataVersionBo;
import com.rzdata.process.domain.bo.WorkflowApplyLogBo;
import com.rzdata.process.domain.vo.ErrataVersionVo;
import com.rzdata.process.domain.vo.WorkflowApplyLogVo;
import org.apache.ibatis.annotations.Param;

/**
 * 勘误文件版本记录Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-08
 */
public interface ErrataVersionMapper extends BaseMapperPlus<ErrataVersion> {

    Page<ErrataVersionVo> queryPageList(@Param("page") Page<ErrataVersionVo> page, @Param("bo") ErrataVersionBo bo, @Param("ew") LambdaQueryWrapper<ErrataVersion> queryWrapper);

}

package com.rzdata.process.mapper;

import com.rzdata.process.domain.PrintTask;
import com.rzdata.process.domain.vo.PrintTaskVo;
import com.rzdata.framework.core.mybatisplus.core.BaseMapperPlus;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rzdata.process.domain.bo.PrintTaskBo;
import org.apache.ibatis.annotations.Param;

/**
 * 打印任务Mapper接口
 *
 * <AUTHOR>
 * @date 2024-01-18
 */
public interface PrintTaskMapper extends BaseMapperPlus<PrintTask> {

    /**
     * 查询打印任务分页列表
     *
     * @param page 分页对象
     * @param bo 打印任务业务对象
     * @return 打印任务分页列表
     */
    Page<PrintTaskVo> selectPrintTaskPage(Page<PrintTask> page, @Param("bo") PrintTaskBo bo);
} 
package com.rzdata.process.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rzdata.process.domain.ReviewApply;
import com.rzdata.framework.core.mybatisplus.core.BaseMapperPlus;
import com.rzdata.process.domain.bo.ProcessBo;
import com.rzdata.process.domain.bo.ProcessWorkFlowBo;
import com.rzdata.process.domain.bo.ReviewApplyBo;
import com.rzdata.process.domain.vo.ReviewApplyVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 文件复审申请Mapper接口
 *
 * <AUTHOR>
 * @date 2021-12-30
 */
@Mapper
public interface ReviewApplyMapper extends BaseMapperPlus<ReviewApply> {

    Page<ReviewApplyVo> selectReviewApplyPage(@Param("page") Page<ReviewApplyVo> page,
                                              @Param("bo") ReviewApplyBo bo,
                                              @Param("ids")List<String> ids);

    List<Map<String, Object>> selectReviewApply(@Param("bo") ProcessBo bo, @Param("ids")List<String> ids);

    ReviewApplyVo selectDetailById(@Param("id")String id);

    Collection<? extends Map<String, Object>> selectReviewApply4New(@Param("bo") ProcessWorkFlowBo processBo, @Param("ids") List<String> businessIds);

    /**
     *  查出未结束的复审申请
     *  定时任务专用
     * @return 复审申请列表
     */
    @InterceptorIgnore(tenantLine = "true")
    List<ReviewApplyVo> selectUndueList(@Param("statusList")List<String> statusList);

    /**
     * 查询待复审的文件，即时发送消息提醒（剩余多少天复审）
     *
     * @param days
     * @return
     */
    List<HashMap> selectUnReviewList(@Param("days") int days, @Param("fileType") String fileType);

    ReviewApplyVo selectDocStatus(@Param("versionId") String  versionId);
}

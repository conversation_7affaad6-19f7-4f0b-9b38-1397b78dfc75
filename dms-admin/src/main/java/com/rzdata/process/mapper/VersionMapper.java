package com.rzdata.process.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rzdata.framework.core.mybatisplus.core.BaseMapperPlus;
import com.rzdata.process.domain.Version;
import com.rzdata.process.domain.bo.StandardBo;
import com.rzdata.process.domain.bo.VersionBo;
import com.rzdata.process.domain.dto.VersionDTO;
import com.rzdata.process.domain.vo.VersionVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 文件版本记录Mapper接口
 *
 * <AUTHOR>
 * @date 2021-12-30
 */
@Mapper
public interface VersionMapper extends BaseMapperPlus<Version> {

    Page<VersionVo> selectVersionPage(@Param("page") Page<VersionVo> page, @Param("bo") VersionBo bo);

    Page<VersionVo> queryPageOtherDeptList(@Param("page") Page<VersionVo> page, @Param("bo") VersionBo bo);

    VersionVo selectDetailById(@Param("id") String id);

    List<VersionVo> selectVersionListByAppId(@Param("applyId") String applyId);

    List<VersionVo> selectVersionList(@Param("ids") List<String> ids);

    Version selectVersionByDocId(@Param("docId") String docId);

    List<VersionVo> selectVersionListByDocIdAndVersionId(@Param("docId") String docId,@Param("versionId") String versionId);

    Page<VersionVo> selectRecordFileCompany(@Param("page") Page<VersionVo> page,@Param("bo") VersionBo bo);

    Page<VersionVo> selectRecordFileCompanyOtherDept(@Param("page") Page<VersionVo> page,@Param("bo") VersionBo bo);

    /**
     * 检查是否有借阅权限
     * @param docId 文档id
     * @param versionId 版本id
     * @return 0表示没有借阅权限 大于0表示有借阅权限
     */
    int checkAuthByBorrow(@Param("docId") String docId, @Param("versionId") String versionId,@Param("date") Date now);


    int checkAuthByDis(@Param("versionId") String versionId, @Param("userName") String userName,@Param("deptId") String deptId);

    /**
     * 查询版本和标准文件的集合
     * @param bo
     */
    List<VersionVo> versionAndStandardList(@Param("bo") StandardBo bo);

    String getDocNameByVersionId(@Param("versionId")String versionId);

    /**
     * 获取某个版本主文件下的记录文件有效版本列表
     * @param versionId
     * @return
     */
    List<Version> selectRecordVersionList(@Param("versionId")String versionId);

    /**
     * 查询版本中，文件分类+物料编码+物料描述是否唯一
     * @param bo
     * @return
     */
    List<VersionVo> queryVersionPartList(@Param("bo") VersionBo bo);

    Page<VersionDTO> pageApi(@Param("page") Page<VersionDTO> page, @Param("bo") VersionBo bo,@Param("limitDocClass") String limitDocClass);

    Page<VersionVo> selectVersionPageAll(@Param("page") Page<VersionVo> page, @Param("bo") VersionBo bo);
}

package com.rzdata.process.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rzdata.framework.core.mybatisplus.core.BaseMapperPlus;
import com.rzdata.process.domain.WorkflowApplyLog;
import com.rzdata.process.domain.bo.IndexBo;
import com.rzdata.process.domain.bo.WorkflowApplyLogBo;
import com.rzdata.process.domain.vo.IndexDeptVo;
import com.rzdata.process.domain.vo.IndexVo;
import com.rzdata.process.domain.vo.VersionVo;
import com.rzdata.process.domain.vo.WorkflowApplyLogVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 流程申请记录Mapper接口
 *
 * <AUTHOR>
 * @date 2022-01-05
 */
@Mapper
public interface WorkflowApplyLogMapper extends BaseMapperPlus<WorkflowApplyLog> {

    Page<WorkflowApplyLogVo> selectWorkflowApplyLogPage(@Param("page") Page<WorkflowApplyLogVo> page, @Param("bo") WorkflowApplyLogBo bo);

    Date selectEffectDate(@Param("docId") String docId);

    List<String> selectBorrowUser(@Param("id")String id);

    List<VersionVo> getDocInfoByApplyId(@Param("applyId")String applyId);


    List<Map<String, Object>> selectExtraList(@Param("id") String id);

    List<IndexVo> selectFileStatistic(@Param("bo") IndexBo bo);

    List<IndexDeptVo> selectDeptList();

    String queryProcessStatus(@Param("docId") String docId);

    Integer selectApplySerial(@Param("applyClass") String applyClass, @Param("applyTime") String applyTime);

    Page<WorkflowApplyLogVo> queryPageList(@Param("page") Page<WorkflowApplyLogVo> page, @Param("bo") WorkflowApplyLogBo bo, @Param("bpmDataSource") String bpmDataSource);
}

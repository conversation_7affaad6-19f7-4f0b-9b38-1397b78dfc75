package com.rzdata.process.mapper;

import com.rzdata.framework.core.mybatisplus.core.BaseMapperPlus;
import com.rzdata.process.domain.WorkflowSupervise;
import com.rzdata.process.domain.bo.UniteworkTaskBo;
import com.rzdata.process.domain.vo.UniteworkTaskVo;
import com.rzdata.process.domain.vo.WorkflowSuperviseVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 流程督办Mapper接口
 *
 * <AUTHOR>
 * @date 2024-11-14
 */
@Mapper
public interface WorkflowSuperviseMapper extends BaseMapperPlus<WorkflowSupervise> {

    List<WorkflowSuperviseVo> queryActReProcDefList(@Param("tenantId") String tenantId,
            @Param("bpmDataSource") String bpmDataSource);

    List<UniteworkTaskVo> queryUniteworkTaskList(@Param("bo") UniteworkTaskBo bo,
            @Param("bpmDataSource") String bpmDataSource);
}

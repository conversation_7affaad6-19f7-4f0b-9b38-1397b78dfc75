package com.rzdata.process.service;


import com.rzdata.framework.constant.CommonI18nConstant;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.utils.I18nUtils;
import com.rzdata.plugins.wordcompare.WordCompareService;
import com.rzdata.plugins.work2pdf.Word2PdfConfig;
import com.rzdata.process.domain.BasicFile;
import com.rzdata.process.mapper.BasicFileMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.UUID;

/*
 * 文件比对业务服务类
 *
 * @author: xiefc
 * @date:2023/12/5 10:48
 */
@Slf4j
@Service
public class DocBuildCompareService {

    @Autowired
    WordCompareService wordCompareService;

    @Autowired
    Word2PdfConfig word2PdfConfig;

    @Autowired
    IStoreFileService iStoreFileService;

    @Autowired
    BasicFileMapper basicFileMapper;

    /**
     * 文件比对
     *
     * @return
     */
    public AjaxResult processCompare(String fileId1,String fileId2) {
        if(!word2PdfConfig.isEnable()) {
            return AjaxResult.error(I18nUtils.getTitle(CommonI18nConstant.SERVICE_DOC_BUILD_NO_WORD_TO_PDF));
        }
        if(StringUtils.isEmpty(fileId1)) {
            return AjaxResult.error("fileId1 is not empty");
        }
        if(StringUtils.isEmpty(fileId2)) {
            return AjaxResult.error("fileId2 is not empty");
        }
        try {
            // 配置根目录
            String storeRootPath = iStoreFileService.getStorePath();
            // 基础文件对象
            BasicFile fileVo = basicFileMapper.selectById(fileId1);
            BasicFile fileVo2 = basicFileMapper.selectById(fileId2);
            // 获取绝对路径
            String fileType= fileVo.getFileType();
            String fileName = UUID.randomUUID().toString().replaceAll("-","")+"."+fileType;
            String absoluteFilePath = storeRootPath + File.separator + fileVo.getFilePath();
            String absoluteFilePath2 = storeRootPath + File.separator + fileVo2.getFilePath();
            // 结果文件输出目录
            String outPdfDirPath = storeRootPath +File.separator+ word2PdfConfig.getOutPdfDirPath() + File.separator +fileId1 + File.separator;
            File dirObj = new File(outPdfDirPath);
            if(!dirObj.exists()) {
                dirObj.mkdirs();
            }
            // 定义结果文件路径
            String outPdfFilePath = outPdfDirPath+fileName.replace(fileVo.getFileType(),"");
            if(outPdfFilePath.indexOf(".") != -1) {
                outPdfFilePath += "pdf";
            } else {
                outPdfFilePath += ".pdf";
            }
            // 文件比对
            AjaxResult res = this.wordCompareService.process(absoluteFilePath,absoluteFilePath2,outPdfFilePath);
            return res;
        } catch(Exception e) {
            log.error("processCompare异常",e);
            return AjaxResult.error("processCompare异常",e);
        }
    }

}


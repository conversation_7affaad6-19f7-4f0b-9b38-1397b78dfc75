package com.rzdata.process.service;


import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.rzdata.config.CustomConfig;
import com.rzdata.framework.constant.CommonI18nConstant;
import com.rzdata.framework.constant.Constants;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.exception.ServiceException;
import com.rzdata.framework.utils.I18nUtils;
import com.rzdata.plugins.watermark.PdfWaterMarkConfig;
import com.rzdata.plugins.watermark.PdfWaterMarkService;
import com.rzdata.plugins.work2pdf.Word2PdfConfig;
import com.rzdata.process.domain.BasicFile;
import com.rzdata.process.domain.vo.BasicFileVo;
import com.rzdata.process.mapper.BasicFileMapper;
import com.rzdata.setting.domain.DocClassSignature;
import com.rzdata.setting.domain.Signature;
import com.rzdata.setting.domain.bo.WatermarkParamBo;
import com.rzdata.setting.domain.vo.DocClassSignatureVo;
import com.rzdata.setting.service.IDocClassSignatureService;
import com.rzdata.setting.service.ISignatureService;
import lombok.extern.slf4j.Slf4j;
import net.rzdata.file.transformer.client.RzFileTransformerClient;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.*;
import java.nio.file.Files;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/*
 * 文件签章服务类
 *
 * @author: xiefc
 * @date:2023/7/15 18:09
 */
@Slf4j
@Service
public class DocBuildSignatureService {

    /**
     * 钉钉基础配置
     */
    @Resource
    Word2PdfConfig word2PdfConfig;

    @Autowired
    IStoreFileService iStoreFileService;

    @Autowired
    BasicFileMapper basicFileMapper;

    @Autowired
    PdfWaterMarkService pdfWaterMarkService;

    @Autowired
    PdfWaterMarkConfig pdfWaterMarkConfig;

    @Autowired
    IDocClassSignatureService docClassSignatureService;

    @Autowired
    ISignatureService signatureService;

    @Autowired
    RzFileTransformerClient client;

    @Autowired
    CustomConfig customConfig;

    /**
     * word转PDF
     *
     * @return
     */
    public String processWord2Pdf(BasicFile basicFile) throws IOException {
        if(!word2PdfConfig.isEnable()) {
            throw new ServiceException(I18nUtils.getTitle(CommonI18nConstant.SERVICE_DOC_BUILD_NO_WORD_TO_PDF));
        }
        String storeRootPath = iStoreFileService.getStorePath();
        String fileName = UUID.randomUUID().toString().replaceAll("-","")+"."+ Constants.FILE_TYPE_PDF;
        //原文件路径
        String absoluteFilePath = storeRootPath + File.separator + basicFile.getFilePath();
        // PDF输出目录
        String outPdfFilePath = storeRootPath +File.separator+ word2PdfConfig.getOutPdfDirPath() + File.separator +basicFile.getId() + File.separator + fileName;

        processWord2Pdf(FileUtil.getInputStream(absoluteFilePath),outPdfFilePath,basicFile.getFileName());

        return outPdfFilePath;
    }


    /**
     * word转PDF
     *
     * @return
     */
    public void processWord2Pdf(InputStream inputStream,String desPath,String fileName) throws IOException {
        if (!FileUtil.exist(desPath)) {
            FileUtil.mkParentDirs(desPath);
        }
        byte[] outPdfFileByte = client.transformFromOfficeToPdf(inputStream, fileName);
        IoUtil.close(inputStream);
        // 写入本地实体文件
        File outPdfFileObj = new File(desPath);
        FileUtils.writeByteArrayToFile(outPdfFileObj,outPdfFileByte);
    }
    /**
     * pdf增加水印
     *
     * @return
     */
    public String processPdfWaterMark(String docClass, BasicFile basicFile, String type, WatermarkParamBo param) {
        if(!pdfWaterMarkConfig.isEnable()) {
            throw new ServiceException(I18nUtils.getTitle(CommonI18nConstant.SERVICE_PDF_WM_NOT_ACTIVATED));
        }
        String defaultClassConfig = "ALL";
        if(StringUtils.isNotEmpty(docClass)) {
            defaultClassConfig = docClass;
        }
        // 源头PDF文件路径
        String storeRootPath = iStoreFileService.getStorePath();
        // 水印DPF文件路径
        String destDirPath = storeRootPath+ File.separator+ pdfWaterMarkConfig.getOutPdfDirPath() +File.separator+ basicFile.getId();
        String fileName=basicFile.getFileName();
        fileName=fileName.replace(" ","").replace(basicFile.getFileType(),"").replace(".","");
        fileName+= "_"+System.currentTimeMillis();
        fileName+= "."+basicFile.getFileType();
        String destFilePath =  destDirPath +File.separator+ fileName;
        // 待附加水印类型（文件生效章,文件失效章,文件分发章）
        // type中增加“外来文件”、“临时文件”
        type = type+",外来文件章,临时文件章,文件留用章";
        String[] typeArray = type.split(",");
        List<DocClassSignatureVo> list = docClassSignatureService.listVo(new LambdaQueryWrapper<DocClassSignature>().eq(DocClassSignature::getDocClass,defaultClassConfig));
        for(String item : typeArray) {
            Optional<DocClassSignatureVo> optional = list.stream().filter(docClassSignatureVo -> item.equals(docClassSignatureVo.getSignatureFactorName())).findFirst();
            if(optional.isPresent()) {
                if(item.equals("文件生效章")) {
                    // 受控
                    param.setCtrlMarkFilePath(this.getSignatureFilePath(optional.get()));
                } else if(item.equals("文件失效章")) {
                    // 作废
                    param.setCancelMarkFilePath(this.getSignatureFilePath(optional.get())); 
                } else if(item.equals("文件分发章")) {
                    // 分发
                    param.setDistributeMarkFilePath(this.getSignatureFilePath(optional.get()));
                }else if(item.equals("外来文件章")) {
                    // 外来文件
                    param.setForeignFilePath(this.getSignatureFilePath(optional.get()));
                }else if(item.equals("临时文件章")) {
                    // 临时文件
                    param.setTemporaryFilePath(this.getSignatureFilePath(optional.get()));
                }else if(item.equals("文件留用章")) {
                    // 临时文件
                    param.setRetainMarkFilePath(this.getSignatureFilePath(optional.get()));
                }

            }
        }
        pdfWaterMarkService.process(basicFile, destFilePath,param);
        return destFilePath;
    }


    private BasicFile getSignatureFilePath(DocClassSignatureVo obj) {
        BasicFile result = null;
        Signature sign = signatureService.getById(obj.getSignatureId());
        if(sign != null) {
            result = basicFileMapper.selectById(sign.getFileId());
        }
        return result;
    }



}


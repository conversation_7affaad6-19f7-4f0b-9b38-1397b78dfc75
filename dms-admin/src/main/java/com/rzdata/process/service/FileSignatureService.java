package com.rzdata.process.service;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.system.SystemUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.deepoove.poi.data.PictureRenderData;
import com.deepoove.poi.data.PictureType;
import com.rzdata.config.CustomConfig;
import com.rzdata.framework.constant.CommonI18nConstant;
import com.rzdata.framework.constant.Constants;
import com.rzdata.framework.constant.NumberConstants;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.core.domain.entity.SysUser;
import com.rzdata.framework.core.service.ConfigService;
import com.rzdata.framework.utils.DateUtils;
import com.rzdata.framework.utils.I18nUtils;
import com.rzdata.framework.utils.StringUtils;
import com.rzdata.process.domain.*;
import com.rzdata.process.domain.bo.DocLinkLogBo;
import com.rzdata.process.domain.bo.ModifyApplyLinkBo;
import com.rzdata.process.domain.vo.*;
import com.rzdata.process.enums.*;
import com.rzdata.process.mapper.ModifyApplyMapper;
import com.rzdata.process.utils.OfficeFileUtil;
import com.rzdata.process.utils.PDFMergeExampleUtil;
import com.rzdata.setting.domain.Codraft;
import com.rzdata.setting.domain.DocClass;
import com.rzdata.setting.domain.DocClassWatermarkSettingDetail;
import com.rzdata.setting.domain.bo.WatermarkParamBo;
import com.rzdata.setting.service.ICodraftService;
import com.rzdata.setting.service.IDocClassService;
import com.rzdata.setting.service.IDocClassWatermarkSettingService;
import com.rzdata.system.domain.vo.BasicUserSignatureVo;
import com.rzdata.system.service.IBasicUserSignatureService;
import com.rzdata.system.service.ISysDeptService;
import com.rzdata.system.service.ISysUserService;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletResponse;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.*;
import java.net.URLEncoder;
import java.rmi.ServerException;
import java.util.*;
import java.util.List;
import java.util.stream.Collectors;

/*
 * 文件签章-业务服务类
 *
 * @author: xiefc
 * @date:2023/8/11
 */
@Slf4j
@Service
public class FileSignatureService {


    @Autowired
    IBasicFileService basicFileService;

    @Autowired
    IBasicFilePdfService basicFilePdfService;

    @Autowired
    private IStoreFileService iStoreFileService;

    @Autowired
    IDocDistributeService docDistributeService;

    @Autowired
    IVersionService versionService;

    @Autowired
    IDocLinkLogService docLinkLogService;

    @Autowired
    ModifyApplyMapper modifyApplyMapper;

    @Autowired
    IModifyApplyLinkService modifyApplyLinkService;

    @Autowired
    IDocVersionLinkService iDocVersionLinkService;

    @Autowired
    FileSignatureService fileSignatureService;

    @Autowired
    IStandardService standardService;

    @Autowired
    CustomConfig customConfig;

    @Autowired
    IDocClassService docClassService;
    @Autowired
    ICodraftService codraftService;

    @Autowired
    IWorkflowLogService workflowLogService;

    @Autowired
    ISysUserService sysUserService;

    @Autowired
    IModifyApplyDistributeService modifyApplyDistributeService;

    @Autowired
    ISysDeptService sysDeptService;

    @Autowired
    ConfigService configService;

    @Autowired
    IBasicFileService iBasicFileService;

    @Autowired
    IDocClassWatermarkSettingService docClassWatermarkSettingService;

    @Autowired
    IModifyApplyTrainService modifyApplyTrainService;


    /**
     * 在文件新增、修订、作废流程过程中在发布环节，进行【签章生效】和【执行发布】
     *
     * @param applyId
     * @param type
     * @return
     */
    public String signEffective(String applyId, String type) {
        ModifyApply modifyApplyObj = modifyApplyMapper.selectById(applyId);
        if ("effective".equals(type)) {
            // 签章生效功能
            if (ObjectUtil.isEmpty(modifyApplyObj.getSetupTime())) {
                modifyApplyObj.setSetupTime(new Date());
            }
            // 签章生效
            modifyApplyObj.setIsSignature("E");
        } else if ("publish".equals(type)) {
            // 执行发布功能
            // 签章生效+发布
            if (ObjectUtil.isEmpty(modifyApplyObj.getReleaseTime())) {
                modifyApplyObj.setReleaseTime(new Date());
            }
            modifyApplyObj.setIsSignature("Y");
        }
        // 变更类型 ADD=新增 UPDATE=修订 DISUSE = 作废
        WatermarkParamBo param = new WatermarkParamBo();
        param.setStartDate(I18nUtils.getTitle(CommonI18nConstant.FILE_PDF_START_DATE) + ":" + DateUtil.format(modifyApplyObj.getSetupTime(), "YYYY-MM-dd"));
        DocClass docClass = this.docClassService.getById(modifyApplyObj.getDocClass());
        if (modifyApplyObj.getChangeType().equals("DISUSE")) {
            // 文件作废流程
            Version currVersion = this.versionService.getById(modifyApplyObj.getVersionId());
            this.fileSignatureService.handleLinkLog(applyId, currVersion);
            // 设置当前文件台账相关文件签章失效
            this.signEffectiveCore(currVersion, "cancel", null, null);
            modifyApplyObj.setIsSignature("Y");
            String encryptFileId = this.basicFilePdfService.getPdfId(modifyApplyObj.getFileId(), PdfTypeEnum.SIGNATURE.getCode());
            modifyApplyObj.setEncryptFileId(encryptFileId);
            modifyApplyMapper.updateById(modifyApplyObj);
            return encryptFileId;
        } else if ((modifyApplyObj.getChangeType().equals("ADD") || modifyApplyObj.getChangeType().equals("UPDATE"))
                && "publish".equals(type)) {
            // 文件新增、修订流程且 在执行发布功能，在水印文字追加发布日期
            param.setReleaseTime(I18nUtils.getTitle(CommonI18nConstant.FILE_PDF_RELEASE_DATE) + ":" + DateUtil.format(modifyApplyObj.getReleaseTime(), "YYYY-MM-dd"));
        }
        ModifyApplyLinkBo modifyApplyLinkBo = new ModifyApplyLinkBo();
        modifyApplyLinkBo.setApplyId(applyId);
        List<ModifyApplyLinkVo> linkVoList = modifyApplyLinkService.queryList(modifyApplyLinkBo);
        try {
            String signPdfId = null;
            for (int i = 0; i < linkVoList.size(); i++) {
                ModifyApplyLinkVo modifyApplyLinkVo = linkVoList.get(i);
                BasicFile basicFile = basicFileService.getById(modifyApplyLinkVo.getFileId());
                // 指定台账类型或文件口站名 不需要水印章
                boolean isSign = !this.isNoSignFileType(docClass.getClassType(), basicFile.getId(), Constants.ADD_TYPE_WORKFLOW);

                List<DocClassWatermarkSettingDetail> docClassWatermarkSettingDetailList = docClassWatermarkSettingService.getDocClassWatermarkSettingDetailList
                        (modifyApplyLinkVo.getLinkType().equals(LinkTypeEnum.APPENDIX.name()) ? modifyApplyObj.getDocClass() : modifyApplyLinkVo.getDocClass(),
                                modifyApplyObj.getChangeType(), true, modifyApplyLinkVo.getLinkType().equals(LinkTypeEnum.APPENDIX.name()) ? Constants.APPLIED_RANGE_ATTACH : Constants.APPLIED_RANGE_MAIN);
                param.setDocClassWatermarkSettingDetailList(docClassWatermarkSettingDetailList);
                param.setVersionValue(modifyApplyLinkVo.getVersionValue());
                param.setDocId(modifyApplyObj.getDocId());
                param.setExt2(modifyApplyObj.getExt2());
                param.setExt8(modifyApplyObj.getExt8());
                param.setExt9(modifyApplyObj.getExt9());

                // 流程处理人水印参数设置
                processHandlerWatermarkParam(docClassWatermarkSettingDetailList, modifyApplyObj, param);

                log.error("docClassWatermarkSettingDetailList==="+ JSON.toJSONString(param));
                if (LinkTypeEnum.DOC.name().equals(modifyApplyLinkVo.getLinkType()) && this.isSignFileType(modifyApplyLinkVo.getDocName())) {
                    // 当前变更版本且是word
                    signPdfId = basicFilePdfService.fileToPdfSignature("ALL", modifyApplyLinkVo.getFileId(), isSign ? "文件生效章" : "", param);
                } else if ((LinkTypeEnum.APPENDIX.name().equals(modifyApplyLinkVo.getLinkType()) || LinkTypeEnum.RECORD.name().equals(modifyApplyLinkVo.getLinkType()))
                        && this.isSignFileType(basicFile.getFileName())) {
                    // 变更版本附件、关联记录且是word
                    if (NumberUtil.equals(modifyApplyLinkVo.getStatus(), NumberConstants.ONE) && (LinkTypeEnum.APPENDIX.name().equals(modifyApplyLinkVo.getLinkType()))) {
                        //要生效的版本附件
                        basicFilePdfService.fileToPdfSignature("ALL", modifyApplyLinkVo.getFileId(), isSign ? "文件生效章" : "", param);
                    } else if (NumberUtil.equals(modifyApplyLinkVo.getStatus(), NumberConstants.ONE) && (LinkTypeEnum.RECORD.name().equals(modifyApplyLinkVo.getLinkType()))) {
                        //要生效的关联记录
                        basicFilePdfService.fileToPdfSignature("ALL", modifyApplyLinkVo.getFileId(), isSign ? "文件生效章" : "", param);
                    }
                } else {
                    log.info("其他附件不参与签章doc_modify_apply_link id=" + modifyApplyLinkVo.getApplyId() + "," + modifyApplyLinkVo.getDocName());
                }
            }
            // 更新变更申请表记录
            modifyApplyMapper.updateById(modifyApplyObj);
            // 返回签章的ID
            return signPdfId;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * @description 流程处理人水印参数设置
     * <AUTHOR>
     * @param docClassWatermarkSettingDetailList docClassWatermarkSettingDetailList
     * @param modifyApply modifyApply
     * @param param param
     * @updateTime 2024/11/25
     */
    private void processHandlerWatermarkParam(List<DocClassWatermarkSettingDetail> docClassWatermarkSettingDetailList,
                                              ModifyApply modifyApply, WatermarkParamBo param) {

        String busId = StringUtils.isNotEmpty(modifyApply.getBatchId())?modifyApply.getBatchId():modifyApply.getId();
        boolean hasOrganizer = docClassWatermarkSettingDetailList.stream().anyMatch(docClassWatermarkSettingDetail ->
                docClassWatermarkSettingDetail.getWatermarkSettingCode().equals(Constants.ORGANIZER_IMAGE));

        boolean hasAuditor = docClassWatermarkSettingDetailList.stream().anyMatch(docClassWatermarkSettingDetail ->
                docClassWatermarkSettingDetail.getWatermarkSettingCode().equals(Constants.AUDITOR_IMAGE));

        boolean hasApprover = docClassWatermarkSettingDetailList.stream().anyMatch(docClassWatermarkSettingDetail ->
                docClassWatermarkSettingDetail.getWatermarkSettingCode().equals(Constants.APPROVER_IMAGE));

        if(hasOrganizer || hasAuditor || hasApprover){
            List<WorkflowLog> workflowLogLists = workflowLogService.list(new LambdaQueryWrapper<>(new WorkflowLog().setBusinessId(busId)).orderByDesc(WorkflowLog::getCreateTime));
            // 编制人，organizer
            if (workflowLogLists.size() > 0 && hasOrganizer) {
                WorkflowLog workflowLog = workflowLogLists.stream()
                        .filter(m -> m.getTitle() != null && m.getTitle().contains("Creator"))
                        .findFirst()
                        .orElse(null);
                if (workflowLog != null) {
                    BasicFile basicFile = null;
                    try {
                        BasicUserSignatureVo signature = basicUserSignatureService.getByUserCode(modifyApply.getUserName());
                        if(null != signature){
                            basicFile = basicFileService.getById(signature.getFileId());
                        }
                    } catch (Exception e) {
                        log.error("FileSignatureService-->processHandlerWatermarkParam----编制人异常###", e);
                    }
                    param.setOrganizerSignatureFile(basicFile);
                    param.setOrganizerHandleTime(DateUtil.format(workflowLog.getCreateTime(), "YYYY-MM-dd"));
                }
            }
            // 审核人，auditor
            if (workflowLogLists.size() > 0 && hasAuditor) {
                WorkflowLog workflowLog = workflowLogLists.stream()
                        .filter(m -> m.getTitle() != null && m.getTitle().contains("Approver"))
                        .findFirst()
                        .orElse(null);
                if (workflowLog != null) {
                    BasicFile basicFile = null;
                    try {
                        BasicUserSignatureVo signature = basicUserSignatureService.getByUserCode(workflowLog.getSender());
                        if(null != signature){
                            basicFile = basicFileService.getById(signature.getFileId());
                        }
                    } catch (Exception e) {
                        log.error("FileSignatureService-->processHandlerWatermarkParam----审核人异常###", e);
                    }
                    param.setAuditorSignatureFile(basicFile);
                    param.setAuditorHandleTime(DateUtil.format(workflowLog.getCreateTime(), "YYYY-MM-dd"));
                }
            }
            // 批准人，approver
            if (workflowLogLists.size() > 0 && hasApprover) {
                WorkflowLog workflowLog = workflowLogLists.stream()
                        .filter(m -> m.getTitle() != null && m.getTitle().contains("Reviewer"))
                        .findFirst()
                        .orElse(null);
                if (workflowLog != null) {
                    BasicFile basicFile = null;
                    try {
                        BasicUserSignatureVo signature = basicUserSignatureService.getByUserCode(workflowLog.getSender());
                        if(null != signature){
                            basicFile = basicFileService.getById(signature.getFileId());
                        }
                    } catch (Exception e) {
                        log.error("FileSignatureService-->processHandlerWatermarkParam----批准人异常###", e);
                    }
                    param.setApproverSignatureFile(basicFile);
                    param.setApproverHandleTime(DateUtil.format(workflowLog.getCreateTime(), "YYYY-MM-dd"));
                }
            }
        }

        // 内部文件编号，internal_file_number
        boolean hasInternalFileNumber = docClassWatermarkSettingDetailList.stream().anyMatch(docClassWatermarkSettingDetail ->
                docClassWatermarkSettingDetail.getWatermarkSettingCode().equals(Constants.INTERNAL_FILE_NUMBER_TEXT));
        if(hasInternalFileNumber){
            param.setInternalFileNumber(modifyApply.getInternalDocId());
        }
    }

    /**
     * 在文件新增、修订、作废流程过程中在发布环节，进行封面生成
     *
     * @param applyId
     * @param type
     * @return
     */
    public String coverEffective(String applyId, String type) {
        ModifyApply modifyApplyObj = modifyApplyMapper.selectById(applyId);
        modifyApplyObj.setIsSignature("C");
        if (ObjectUtil.isEmpty(modifyApplyObj.getSetupTime())) {
            modifyApplyObj.setSetupTime(new Date());
        }
        if (ObjectUtil.isEmpty(modifyApplyObj.getReleaseTime())) {
            modifyApplyObj.setReleaseTime(new Date());
        }

        LambdaQueryWrapper<ModifyApplyLink> lqw = Wrappers.lambdaQuery();
        lqw.eq(ModifyApplyLink::getApplyId, applyId);
        lqw.in(ModifyApplyLink::getLinkType, LinkTypeEnum.DOC.name(), LinkTypeEnum.RECORD.name());
        List<ModifyApplyLinkVo> linkVoList = modifyApplyLinkService.listVo(lqw);
        try {
            String asSyncRootPath = iStoreFileService.getStorePath();
            String mergeRootPath = asSyncRootPath + File.separator + "merge";
            String signPdfId = null;
            /**
             * 查询最后一次同意的客户记录
             */
//            ModifyApplyTrain customer = modifyApplyTrainService.getOne(new LambdaQueryWrapper<ModifyApplyTrain>()
//                    .eq(ModifyApplyTrain::getApplyId, applyId)
//                    .eq(ModifyApplyTrain::getIsDeleted, NumberConstants.ZERO)
//                    .eq(ModifyApplyTrain::getType, "customer")
//                    .orderByDesc(ModifyApplyTrain::getCreateTime)
//                    .last("limit 1"));
//            List<BasicFile> customerList = new ArrayList<>();
//            if (customer != null) {
//                for (String customerFileId : customer.getFileIds().split(Constants.ID_SPLIT_KEY)) {
//                    BasicFile pdfFile = basicFilePdfService.getPdfFilePathByFileId(customerFileId, PdfTypeEnum.TRANSITION.getCode());
//                    customerList.add(pdfFile);
//                }
//            }
            Version version = BeanUtil.toBean(modifyApplyObj,Version.class);
            for (int i = 0; i < linkVoList.size(); i++) {
                version.setApplyId(applyId);
                ModifyApplyLinkVo modifyApplyLinkVo = linkVoList.get(i);
                // 如果主文件为PDF文件，则取主文件，如果主文件为非PDF文件，则再进行查询
                BasicFile pdfFile = basicFilePdfService.getPdfFilePathByFileId(modifyApplyLinkVo.getFileId(), PdfTypeEnum.TRANSITION.getCode());
                //主文件流
                BufferedInputStream pdfFileInput = iStoreFileService.getInputStream(pdfFile);
                //合稿路径
                String mergeFilePath = mergeRootPath + File.separator + pdfFile.getFilePath();

                //将最后一次客户同意的回执文件合到主文件的上面
                /*for (int j = 0; j <customerList.size(); j++) {
                    BasicFile customerBasicFile = customerList.get(j);
                    BufferedInputStream customerInput = iStoreFileService.getInputStream(customerBasicFile);
                    PDFMergeExampleUtil.merge(pdfFileInput,customerInput , mergeFilePath);
                    pdfFileInput = FileUtil.getInputStream(mergeFilePath);
                    pdfFileInput.mark(pdfFileInput.available()+1);
                }*/
                //封面参数
                /**
                 * sunlord记录文件需要封面
                 * bug：32060 顺络正式环境生成封面，主文件和关联的记录文件生成的封面为同样的内容
                 */
                if (LinkTypeEnum.DOC.name().equals(modifyApplyLinkVo.getLinkType())) {
                    version.setDocId(modifyApplyObj.getDocId());
                    version.setDocName(modifyApplyObj.getDocName());
                }else {
                    version.setDocId(modifyApplyLinkVo.getDocId());
                    version.setDocName(modifyApplyLinkVo.getDocName());
                }
                Map<String, Object> map = this.getDocMap(modifyApplyObj.getBatchId(),version, modifyApplyObj.getDocClass());
                List<Codraft> codraftList = this.getDocClassList(modifyApplyObj.getDocClass());
                //合稿模板必须是一页的不然要统计模板的页数
                PDDocument document = PDDocument.load(pdfFileInput);
                if (ObjectUtil.isNotEmpty(codraftList)) {
                    int page = codraftList.size();
                    //主文件pdf的页数
                    page += document.getNumberOfPages();
                    map.put("page", page);
                    document.close();
                }
                pdfFileInput.reset();
                if (ObjectUtil.isNotEmpty(codraftList)) {
                    for (int j = 0; j < codraftList.size(); j++) {
                        Codraft codraft = codraftList.get(j);
                        BasicFile basicFile = basicFileService.getById(codraft.getFileId());
                        //获取封面附件
                        BufferedInputStream codraftInput = iStoreFileService.getInputStream(basicFile);

                        String fileName = modifyApplyLinkVo.getApplyId() + "_" + codraft.getCodraftName() + "." + basicFile.getFileType();

                        // 生成封面临时文件路径
                        String tempFilePath = asSyncRootPath + File.separator + "cover" + File.separator + fileName;

                        OutputStream out = FileUtil.getOutputStream(tempFilePath);
                        // 封面生成
                        OfficeFileUtil.generateDocxFromTl(codraftInput, map, out);
                        IoUtil.close(codraftInput);
                        IoUtil.close(out);

                        String desPath = tempFilePath.replace(tempFilePath.substring(tempFilePath.lastIndexOf(".")),".pdf");
                        // 将封面文件转换为pdf
                        iBasicFileService.createPdfFile(FileUtil.getInputStream(tempFilePath), desPath,fileName);

                        BufferedInputStream onePdfFile = "before".equals(codraft.getPosition()) ? FileUtil.getInputStream(desPath) : pdfFileInput;
                        BufferedInputStream towPdfFile = !"before".equals(codraft.getPosition()) ? FileUtil.getInputStream(desPath) : pdfFileInput;
                        //合稿
                        PDFMergeExampleUtil.merge(onePdfFile, towPdfFile, mergeFilePath);
                        pdfFileInput = FileUtil.getInputStream(mergeFilePath);
                    }
                }
                IoUtil.close(pdfFileInput);
                BasicFile basicFile = basicFilePdfService.uploading(new File(mergeFilePath));
                signPdfId = basicFile.getId();
                basicFilePdfService.pdfBasicFileSave(modifyApplyLinkVo.getFileId(),signPdfId, PdfTypeEnum.MERGE.getCode());
            }
            modifyApplyObj.setCoverFileId(signPdfId);
            // 更新变更申请表记录
            modifyApplyMapper.updateById(modifyApplyObj);
            // 返回合稿的ID
            return signPdfId;
        } catch (Exception e) {
            log.error("signEffective error", e);
            return null;
        }
    }

    public String coverEffectiveVersion(Version version, String docClassId) {
        DocLinkLogBo bo = new DocLinkLogBo();
        bo.setPVersionId(version.getId());
        bo.setLinkType(LinkTypeEnum.DOC.name());
        List<DocLinkLog> linkVoList = docLinkLogService.queryLink(bo);
        try {
            String asSyncRootPath = iStoreFileService.getStorePath();
            String mergeRootPath = asSyncRootPath + File.separator + "merge";
            String signPdfId = null;
            /**
             * 查询最后一次同意的客户记录
             */
//            ModifyApplyTrain customer = modifyApplyTrainService.getOne(new LambdaQueryWrapper<ModifyApplyTrain>()
//                    .eq(ModifyApplyTrain::getApplyId, applyId)
//                    .eq(ModifyApplyTrain::getIsDeleted, NumberConstants.ZERO)
//                    .eq(ModifyApplyTrain::getType, "customer")
//                    .orderByDesc(ModifyApplyTrain::getCreateTime)
//                    .last("limit 1"));
//            List<BasicFile> customerList = new ArrayList<>();
//            if (customer != null) {
//                for (String customerFileId : customer.getFileIds().split(Constants.ID_SPLIT_KEY)) {
//                    BasicFile pdfFile = basicFilePdfService.getPdfFilePathByFileId(customerFileId, PdfTypeEnum.TRANSITION.getCode());
//                    customerList.add(pdfFile);
//                }
//            }
                DocLinkLog modifyApplyLinkVo = linkVoList.get(0);
                // 如果主文件为PDF文件，则取主文件，如果主文件为非PDF文件，则再进行查询
                BasicFile pdfFile = basicFilePdfService.getPdfFilePathByFileId(modifyApplyLinkVo.getFileId(), PdfTypeEnum.TRANSITION.getCode());
                //主文件流
                BufferedInputStream pdfFileInput = iStoreFileService.getInputStream(pdfFile);
                //合稿路径
                String mergeFilePath = mergeRootPath + File.separator + pdfFile.getFilePath();

                //将最后一次客户同意的回执文件合到主文件的上面
                /*for (int j = 0; j <customerList.size(); j++) {
                    BasicFile customerBasicFile = customerList.get(j);
                    BufferedInputStream customerInput = iStoreFileService.getInputStream(customerBasicFile);
                    PDFMergeExampleUtil.merge(pdfFileInput,customerInput , mergeFilePath);
                    pdfFileInput = FileUtil.getInputStream(mergeFilePath);
                    pdfFileInput.mark(pdfFileInput.available()+1);
                }*/
                //封面参数
                ModifyApply modifyApplyObj = modifyApplyMapper.selectById(version.getApplyId());
                Map<String, Object> map = this.getDocMap(modifyApplyObj.getBatchId(),version, docClassId);
                List<Codraft> codraftList = this.getDocClassList(docClassId);
                //合稿模板必须是一页的不然要统计模板的页数
                PDDocument document = PDDocument.load(pdfFileInput);
                if (ObjectUtil.isNotEmpty(codraftList)) {
                    int page = codraftList.size();
                    //主文件pdf的页数
                    page += document.getNumberOfPages();
                    map.put("page", page);
                    document.close();
                }
                pdfFileInput.reset();
                if (ObjectUtil.isNotEmpty(codraftList)) {
                    for (int j = 0; j < codraftList.size(); j++) {
                        Codraft codraft = codraftList.get(j);
                        BasicFile basicFile = basicFileService.getById(codraft.getFileId());
                        //获取封面附件
                        BufferedInputStream codraftInput = iStoreFileService.getInputStream(basicFile);

                        String fileName = version.getApplyId() + "_" + codraft.getCodraftName() + "." + basicFile.getFileType();

                        // 生成封面临时文件路径
                        String tempFilePath = asSyncRootPath + File.separator + "cover" + File.separator + fileName;

                        OutputStream out = FileUtil.getOutputStream(tempFilePath);
                        // 封面生成
                        OfficeFileUtil.generateDocxFromTl(codraftInput, map, out);
                        IoUtil.close(codraftInput);
                        IoUtil.close(out);

                        String desPath = tempFilePath.replace(tempFilePath.substring(tempFilePath.lastIndexOf(".")),".pdf");
                        // 将封面文件转换为pdf
                        iBasicFileService.createPdfFile(FileUtil.getInputStream(tempFilePath), desPath,fileName);

                        BufferedInputStream onePdfFile = "before".equals(codraft.getPosition()) ? FileUtil.getInputStream(desPath) : pdfFileInput;
                        BufferedInputStream towPdfFile = !"before".equals(codraft.getPosition()) ? FileUtil.getInputStream(desPath) : pdfFileInput;
                        //合稿
                        PDFMergeExampleUtil.merge(onePdfFile, towPdfFile, mergeFilePath);
                        pdfFileInput = FileUtil.getInputStream(mergeFilePath);
                    }
                }
                IoUtil.close(pdfFileInput);
                BasicFile basicFile = basicFilePdfService.uploading(new File(mergeFilePath));
                signPdfId = basicFile.getId();
                basicFilePdfService.pdfBasicFileSave(modifyApplyLinkVo.getFileId(),signPdfId, PdfTypeEnum.MERGE.getCode());
            return signPdfId;
        } catch (Exception e) {
            log.error("signEffective error", e);
            return null;
        }
    }

    public void coverDownload(String applyId, HttpServletResponse response) {
        ModifyApply modifyApplyObj = modifyApplyMapper.selectById(applyId);
        if (ObjectUtil.isEmpty(modifyApplyObj.getSetupTime())) {
            modifyApplyObj.setSetupTime(new Date());
        }
        if (ObjectUtil.isEmpty(modifyApplyObj.getReleaseTime())) {
            modifyApplyObj.setReleaseTime(new Date());
        }
        LambdaQueryWrapper<ModifyApplyLink> lqw = Wrappers.lambdaQuery();
        lqw.eq(ModifyApplyLink::getApplyId, applyId);
        lqw.in(ModifyApplyLink::getLinkType, LinkTypeEnum.DOC.name());
        ModifyApplyLinkVo modifyApplyLinkVo = modifyApplyLinkService.getVoOne(lqw);
        try {
            String asSyncRootPath = iStoreFileService.getStorePath();
            String mergeRootPath = asSyncRootPath + File.separator + "cover"+ File.separator +"merge";

            BasicFile pdfFile = basicFilePdfService.getPdfFilePathByFileId(modifyApplyObj.getFileId(), PdfTypeEnum.TRANSITION.getCode());
            //主文件流
            BufferedInputStream pdfFileInput = iStoreFileService.getInputStream(pdfFile);

            //合稿路径
            String mergeFilePath = mergeRootPath + File.separator + pdfFile.getFilePath();

            List<Codraft> codraftList = this.getDocClassList(modifyApplyObj.getDocClass());
            if (codraftList != null) {
                //封面参数
                Version version = BeanUtil.toBean(modifyApplyObj,Version.class);
                Map<String, Object> map = this.getDocMap(modifyApplyObj.getBatchId(),version,modifyApplyObj.getDocClass());

                //合稿模板必须是一页的不然要统计模板的页数
                int page = codraftList.size();
                PDDocument document = PDDocument.load(pdfFileInput);
                //主文件pdf的页数
                page += document.getNumberOfPages();
                document.close();
                pdfFileInput.reset();
                map.put("page", page);
                if (ObjectUtil.isNotEmpty(codraftList)) {
                    for (int j = 0; j < codraftList.size(); j++) {
                        Codraft codraft = codraftList.get(j);
                        BasicFile basicFile = basicFileService.getById(codraft.getFileId());
                        //获取封面附件
                        BufferedInputStream codraftInput = iStoreFileService.getInputStream(basicFile);

                        String fileName = modifyApplyLinkVo.getApplyId() + "_" + codraft.getCodraftName() + "." + basicFile.getFileType();
                        // 生成封面临时文件路径
                        String tempFilePath = asSyncRootPath + File.separator + "cover" + File.separator + fileName;
                        OutputStream out = FileUtil.getOutputStream(tempFilePath);
                        // 封面生成
                        OfficeFileUtil.generateDocxFromTl(codraftInput, map, out);
                        IoUtil.close(codraftInput);
                        IoUtil.close(out);

                        String desPath = tempFilePath.replace(tempFilePath.substring(tempFilePath.lastIndexOf(".")),".pdf");
                        // 将封面文件转换为pdf
                        iBasicFileService.createPdfFile(FileUtil.getInputStream(tempFilePath), desPath,fileName);

                        BufferedInputStream onePdfFile = "before".equals(codraft.getPosition()) ? FileUtil.getInputStream(desPath) : pdfFileInput;
                        BufferedInputStream towPdfFile = !"before".equals(codraft.getPosition()) ? FileUtil.getInputStream(desPath) : pdfFileInput;
//                        合稿
                        PDFMergeExampleUtil.merge(onePdfFile, towPdfFile, mergeFilePath);
                        pdfFileInput = FileUtil.getInputStream(mergeFilePath);
                    }
                }
            }
            response.setContentType("application/octet-stream");
            response.setHeader("content-type", "application/octet-stream");
            response.setHeader("Content-Disposition", "attachment;fileName=");
            response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
            try {
                OutputStream outputStream = response.getOutputStream();
                IoUtil.copy(pdfFileInput, outputStream);
                IoUtil.close(pdfFileInput);
                IoUtil.close(outputStream);
            } catch (Exception e) {
                log.error("download file error:", e);
            }
        } catch (Exception e) {
            log.error("signEffective error", e);
        }
    }

    public Map<String, Object> getDocMap(String batchId,Version version, String docClass) {
        Map<String, Object> result = new HashMap<String, Object>();
        result.put("versionValue", version.getVersionValue());
        result.put("className", docClassService.getClassName(docClass));
        result.put("docId", version.getDocId());
        String deptName = sysDeptService.getDeptName(version.getDeptId());
        result.put("deptName", StringUtils.isNotEmpty(deptName)?deptName:"");
        result.put("docName",version.getDocName());
        result.put("releaseTime", DateUtil.format(version.getReleaseTime(), "YYYY-MM-dd"));
        result.put("ext3", version.getExt3());
        result.put("ext11", version.getExt11());
        result.put("ext12", version.getExt12());
        result.put("ext13", version.getExt13());
        // 查询审批人
        List<WorkflowLog> workflowLogList = workflowLogService.list(new QueryWrapper<>(new WorkflowLog().setBusinessId(StringUtils.isNotEmpty(batchId)?batchId:version.getApplyId()).setReview(true)));
        String userNames = "";
        List<String> userNameList = new ArrayList<String>();
        for (WorkflowLog workflowLog : workflowLogList) {
            String userName = sysUserService.getNickName(workflowLog.getSender());
            userNameList.add(userName);
        }
        if (userNameList.size() > 0) {
            List<String> ans = userNameList.stream().distinct().collect(Collectors.toList());
            userNames = String.join("，", ans);
        }
        result.put("userNames", userNames);

        //查询宣贯人
        List<ModifyApplyDistribute> modifyApplyDistributeList = modifyApplyDistributeService.list(new QueryWrapper<ModifyApplyDistribute>(new ModifyApplyDistribute()
                .setApplyId(version.getApplyId())
                .setCategory("train")));
        String xUserNames = "";
        if (modifyApplyDistributeList.size() > 0) {
            List<String> listName = new ArrayList<String>();
            // 过滤个人
            List<ModifyApplyDistribute> people = modifyApplyDistributeList.stream()
                    .filter(m -> "person".equals(m.getType()))
                    .collect(Collectors.toList());
            if (people.size() > 0) {
                List<String> personNames = people.stream()
                        .map(ModifyApplyDistribute::getReceiveNickName)
                        .distinct()
                        .collect(Collectors.toList());
                listName.addAll(personNames);
            }

            // 过滤部门
            List<ModifyApplyDistribute> dept = modifyApplyDistributeList.stream()
                    .filter(m -> "dept".equals(m.getType()))
                    .collect(Collectors.toList());
            if (dept.size() > 0) {
                List<String> deptNames = dept.stream()
                        .map(ModifyApplyDistribute::getReceiveUserDept)
                        .distinct()
                        .collect(Collectors.toList());
                if (deptNames.size() > 0) {
                    listName.addAll(deptNames);
                }
            }
            // 过滤公司
            List<ModifyApplyDistribute> company = modifyApplyDistributeList.stream()
                    .filter(m -> "company".equals(m.getType()))
                    .collect(Collectors.toList());
            if (company.size() > 0) {
                List<String> companyNames = company.stream()
                        .map(ModifyApplyDistribute::getReceiveUserDept)
                        .distinct()
                        .collect(Collectors.toList());
                listName.addAll(companyNames);
            }
            xUserNames = String.join("，", listName);
        }
        result.put("xUserNames", xUserNames);

        // 查询分发部门及分数
        List<ModifyApplyDistribute> printModifyApplyDistributeList = modifyApplyDistributeService.list(new QueryWrapper<ModifyApplyDistribute>(new ModifyApplyDistribute()
                .setApplyId(version.getApplyId())
                .setCategory("print")));
        String xDepts = "";
        for (ModifyApplyDistribute modifyApplyDistribute : printModifyApplyDistributeList) {
            if ("".equals(xDepts)) {
                xDepts = modifyApplyDistribute.getReceiveUserDept() + "（" + modifyApplyDistribute.getNums() + "）";
            } else {
                xDepts = xDepts + "，" + modifyApplyDistribute.getReceiveUserDept() + "（" + modifyApplyDistribute.getNums() + "）";
            }
        }
        result.put("xDepts", xDepts);

        //查询历史版本
        List<Version> versionList = versionService.list(new QueryWrapper<Version>(new Version()
                .setDocId(version.getDocId())).lambda().orderByAsc(Version::getStartDate));
        List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

        int i = 0;
        for (Version ver : versionList) {
            Map<String, Object> map = new HashMap<String, Object>();
            i++;
            map.put("index", i);
            map.put("versionValue", ver.getVersionValue());
            String userName = sysUserService.getNickName(ver.getUserName());
            map.put("deptName", sysDeptService.getDeptName(ver.getDeptId()));
            map.put("userName", userName);
            //map.put("changeReason", I18nUtils.getTitle(CommonI18nConstant.FILE_PDF_CHANGE_BEFORE_CONTENT) + ":" + version.getChangeReason() + ";" + I18nUtils.getTitle(CommonI18nConstant.FILE_PDF_CHANGE_AFTER_CONTENT) + ":" + version.getContent() + ";");
            map.put("changeReason", ver.getContent());
            if (ApplyStatusEnum.UPDATE.getCode().equalsIgnoreCase(ver.getChangeType())) {
                map.put("changeType", "升版\nChange\nEdition");
            }

            if (StringUtils.isNotEmpty(ver.getApplyId())) {
                setExData(ver.getApplyId(), result, map, ver.getUserName());
                list.add(map);
            }
        }
        //由于没有发布，需要默认插一条数据
        Map<String, Object> mapOne = new HashMap<String, Object>();
        mapOne.put("versionValue", version.getVersionValue());
        mapOne.put("deptName", sysDeptService.getDeptName(version.getDeptId()));
        mapOne.put("userName", sysUserService.getNickName(version.getUserName()));
        //mapOne.put("changeReason", I18nUtils.getTitle(CommonI18nConstant.FILE_PDF_CHANGE_BEFORE_CONTENT) + ":" + modifyApplyLinkVo.getChangeReason() + ";" + I18nUtils.getTitle(CommonI18nConstant.FILE_PDF_CHANGE_AFTER_CONTENT) + ":" + modifyApplyLinkVo.getContent() + ";");
        mapOne.put("changeReason", version.getContent());
        if (ApplyStatusEnum.UPDATE.getCode().equalsIgnoreCase(version.getChangeType())) {
            mapOne.put("changeType", "升版\nChange\nEdition");
        }
        i++;
        mapOne.put("index", i);

        setExData(StringUtils.isNotEmpty(batchId)?batchId:version.getApplyId(), result, mapOne, version.getUserName());

        list.add(mapOne);
        result.put("listlabel", list);
        return result;
    }

    @Autowired
    private IBasicUserSignatureService basicUserSignatureService;

    /**
     * 设置 Creator,Approver,Reviewer 值
     *
     * @param busiid
     * @param result
     * @param mapOne
     * @param organizerCode
     */
    private void setExData(String busiid, Map<String, Object> result, Map<String, Object> mapOne, String organizerCode) {
        ModifyApply modifyApplyObj = modifyApplyMapper.selectById(busiid);
        List<WorkflowLog> workflowLogLists = workflowLogService.list(new LambdaQueryWrapper<>(new WorkflowLog().setBusinessId(modifyApplyObj!=null && StringUtils.isNotEmpty(modifyApplyObj.getBatchId())?modifyApplyObj.getBatchId():busiid)).orderByDesc(WorkflowLog::getCreateTime));
        // 从workflowLogLists 查找title 为Creator,Approver,Reviewer 的记录

        if (workflowLogLists.size() > 0) {
            WorkflowLog workflowLog = workflowLogLists.stream()
                    .filter(m -> m.getTitle() != null && m.getTitle().contains("Creator"))
                    .sorted(Comparator.comparing(WorkflowLog::getCreateTime).reversed())
                    .findFirst()
                    .orElse(null);
            if (workflowLog != null) {
                List<SysUser> userList = sysUserService.selectListAllByRoleKey("10");
                mapOne.put("creator", getPic(organizerCode));
                mapOne.put("createTime", DateUtil.format(workflowLog.getCreateTime(), "YYYY-MM-dd"));

                result.put("creator", getPic(organizerCode));
                result.put("createTime", DateUtil.format(workflowLog.getCreateTime(), "YYYY-MM-dd"));

                if(userList.size()>0){
                    mapOne.put("Guandai", getPic(userList.get(0).getUserName()));
                    result.put("Guandai", getPic(userList.get(0).getUserName()));
                }else{
                    mapOne.put("Guandai", "");
                    result.put("Guandai", "");
                }
                mapOne.put("GuandaiTime", DateUtil.format(workflowLog.getCreateTime(), "YYYY-MM-dd"));
                result.put("GuandaiTime", DateUtil.format(workflowLog.getCreateTime(), "YYYY-MM-dd"));
            }
            workflowLog = workflowLogLists.stream()
                    .filter(m -> m.getTitle() != null && m.getTitle().contains("Quality"))
                    .sorted(Comparator.comparing(WorkflowLog::getCreateTime).reversed())
                    .findFirst()
                    .orElse(null);
            if (workflowLog != null) {
                String quality = sysUserService.getNickName(workflowLog.getSender());
                mapOne.put("quality", getPic(workflowLog.getSender()));
                mapOne.put("qualityTime", DateUtil.format(workflowLog.getCreateTime(), "YYYY-MM-dd"));

                result.put("quality", getPic(workflowLog.getSender()));
                result.put("qualityTime", DateUtil.format(workflowLog.getCreateTime(), "YYYY-MM-dd"));
            }

            workflowLog = workflowLogLists.stream()
                    .filter(m -> m.getTitle() != null && m.getTitle().contains("Approver"))
                    .sorted(Comparator.comparing(WorkflowLog::getCreateTime).reversed())
                    .findFirst()
                    .orElse(null);
            if (workflowLog != null) {
                String approver = sysUserService.getNickName(workflowLog.getSender());
                mapOne.put("approver", getPic(workflowLog.getSender()));
                mapOne.put("approveTime", DateUtil.format(workflowLog.getCreateTime(), "YYYY-MM-dd"));

                result.put("approver", getPic(workflowLog.getSender()));
                result.put("approveTime", DateUtil.format(workflowLog.getCreateTime(), "YYYY-MM-dd"));
            }
            workflowLog = workflowLogLists.stream()
                    .filter(m -> m.getTitle() != null && m.getTitle().contains("Reviewer"))
                    .sorted(Comparator.comparing(WorkflowLog::getCreateTime).reversed())
                    .findFirst()
                    .orElse(null);
            if (workflowLog != null) {
                String reviewer = sysUserService.getNickName(workflowLog.getSender());
                mapOne.put("reviewer", getPic(workflowLog.getSender()));
                mapOne.put("reviewTime", DateUtil.format(workflowLog.getCreateTime(), "YYYY-MM-dd"));

                result.put("reviewer", getPic(workflowLog.getSender()));
                result.put("reviewTime", DateUtil.format(workflowLog.getCreateTime(), "YYYY-MM-dd"));
            }
            workflowLog = workflowLogLists.stream()
                    .filter(m -> m.getTitle() != null && m.getTitle().contains("countersign"))
                    .sorted(Comparator.comparing(WorkflowLog::getCreateTime).reversed())
                    .findFirst()
                    .orElse(null);
            if (workflowLog != null) {
                List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
                String batch = workflowLog.getBatch();
                if (StringUtils.isNotEmpty(batch)) {
                    List<WorkflowLog> countersignList = workflowLogLists.stream().filter(m -> batch.equals(m.getBatch())).sorted(Comparator.comparing(WorkflowLog::getCreateTime)).collect(Collectors.toList());
//                    Map<String, Object> map = new HashMap<>();
                    for (int i = 0; i <= countersignList.size() - 1; i++) {
//                        int n = i%10;
//                        map.put("countersign"+n,getPic(workflowLog.getSender(),27,16));
//                        map.put("countersignTime"+n,DateUtil.format(workflowLog.getCreateTime(),"YYYY-MM-dd"));
//                        if (n==9||i==countersignList.size()-1) {
//                            list.add(map);
//                            map = new HashMap<>();
//                        }
                        mapOne.put("countersign"+i,getPic(countersignList.get(i).getSender(),70,35));
                        mapOne.put("countersignTime"+i,DateUtil.format(countersignList.get(i).getCreateTime(),"YYYY-MM-dd"));

                        result.put("countersign"+i,getPic(countersignList.get(i).getSender(),70,35));
                        result.put("countersignTime"+i,DateUtil.format(countersignList.get(i).getCreateTime(),"YYYY-MM-dd"));
                    }
                }
//                result.put("countersignList",list);
            }
            workflowLog = workflowLogLists.stream()
                    .filter(m -> m.getTitle() != null && m.getTitle().contains("inspect"))
                    .sorted(Comparator.comparing(WorkflowLog::getCreateTime).reversed())
                    .findFirst()
                    .orElse(null);
            if (workflowLog != null) {
                result.put("inspect", getPic(workflowLog.getSender()));
                result.put("inspectTime", DateUtil.format(workflowLog.getCreateTime(), "YYYY-MM-dd"));
            }
        }
    }

    public PictureRenderData getPic(String username) {
        return getPic(username, 70,35);
    }

    public PictureRenderData getPic(String username, int width, int height) {
        BasicUserSignatureVo signature = basicUserSignatureService.getByUserCode(username);
        byte[] imageBytes;
        // 如果没有对应的名图片，则使用姓名文字生成图片
        if (signature == null) {
            String nickName = sysUserService.getNickName(username);
            imageBytes = genPicByText(nickName);
        } else {
            BasicFile basicFile = basicFileService.getById(signature.getFileId());
            try {
                BufferedInputStream inputStream = iStoreFileService.getInputStream(basicFile);
                imageBytes = IoUtil.readBytes(inputStream);
                IoUtil.close(inputStream);
            }catch (Exception e){
                e.printStackTrace();
                String nickName = sysUserService.getNickName(username);
                imageBytes = genPicByText(nickName);
            }
        }
        return new PictureRenderData(width, height, PictureType.PNG, imageBytes);
    }

    @SneakyThrows
    private byte[] genPicByText(String text) {
        System.setProperty("java.awt.headless", "true");
        // 设置图片大小
        int width = 100;
        int height = 50;

        // 创建一个类型为透明背景的图片
        File fontFile = new File(System.getProperty(SystemUtil.USER_DIR)+"/fonts/SIMFANG.TTF"); // 替换为你的字体文件路径
        log.error("fileIsXeist:{}",fontFile.exists());
        Font customFont = Font.createFont(Font.TRUETYPE_FONT, fontFile);
        BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_ARGB);
        // 获取图片画笔
        Graphics2D g2d = (Graphics2D) image.getGraphics();
        // 设置画笔颜色为白色
        g2d.setColor(Color.WHITE);
        // 填充整个图片
        g2d.fillRect(0, 0, width, height);
        // 设置画笔颜色为黑色
        g2d.setColor(Color.BLACK);
        // 设置字体大小
        g2d.setFont(customFont.deriveFont(Font.BOLD, 20));
        // 在图片上绘制文字
        g2d.drawString(text, 10, 30);

        // 释放画笔资源
        g2d.dispose();
        byte bytes[] = new byte[0];
        try (ByteArrayOutputStream os = new ByteArrayOutputStream()) {
            ImageIO.write(image, "PNG", os);
            bytes = os.toByteArray();
        }
        return bytes;
    }

    /**
     * 根据文件分类ID，查询分类是否设置文件合稿功能，如果没有则向上查找
     */
    public List<Codraft> getDocClassList(String docClassId) {
        List<Codraft> list = new ArrayList<Codraft>();
        if (StringUtils.isEmpty(docClassId)) {
            return list;
        }
        DocClass docClass = this.docClassService.getById(docClassId);
        if (docClass == null) {
            return list;
        }
        if (Constants.VALUE_Y.equals(docClass.getOpenMerge())) {
            list = this.codraftService.selectByClassId(docClassId);
        } else {
            DocClass parentDocClass = this.docClassService.getOne(
                    new QueryWrapper<DocClass>(new DocClass()
                            .setId(docClass.getParentClassId())
                            .setApplyMerge(Constants.VALUE_Y)
                            .setOpenMerge(Constants.VALUE_Y)));
            if (parentDocClass != null) {
                list = this.codraftService.selectByClassId(docClassId);
                ;
            } else {
                list = this.getDocClassList(docClass.getParentClassId());
            }
        }
        return list;
    }

    /**
     * 是否可签章文件类型
     *
     * @param fileName
     * @return
     */
    private boolean isSignFileType(String fileName) {
        boolean result = false;
        fileName = fileName.toLowerCase(Locale.ROOT);
        // OFFICE文件+PDF文件
        if (fileName.contains(".doc") || fileName.contains(".xlsx") || fileName.equals(".docx") || fileName.contains(".xls") || fileName.contains(".xlst") || fileName.contains(".ppt") || fileName.contains(".pptx") || fileName.contains(".pdf")) {
            result = true;
        }
        return result;
    }


    /**
     * 是否为不需要签章的台账类型或扩展名
     *
     * @param accountType
     * @param basicFileId
     * @param addType     文件添加方式 导入import 流程workflow
     * @return
     */
    public boolean isNoSignFileType(String accountType, String basicFileId, String addType) {
        boolean result = false;
        // 获取附件对象
        BasicFile file = this.basicFileService.getById(basicFileId);
        // 获取配置文件中指定不签章的文件扩展名
        List<String> noSignFileExdTypes = ListUtil.toList(this.customConfig.getNoSignFileExdType().split(","));
        List<String> noSignAddType = ListUtil.toList(this.customConfig.getNoSignAddType().split(","));
        if (noSignFileExdTypes.contains(file.getFileType()) && noSignAddType.contains(addType)) {
            return true;
        }
        // 获取配置文件中指定不签章的台账类型
        List<String> noSignAccountTypes = ListUtil.toList(this.customConfig.getNoSignDocType().split(","));
        if (noSignAccountTypes.contains(accountType)) {
            return true;
        }
        return result;
    }

    /**
     * 用于文件打印生成PDF文件
     *
     * @param docDistributeId
     * @param distributeChar
     * @return
     * @throws ServerException
     */
    public AjaxResult signEffectiveDis(String docDistributeId, String distributeChar) throws ServerException {
        DocDistribute distributeObj = this.docDistributeService.getById(docDistributeId);
        Version versionObj = this.versionService.getById(distributeObj.getVersionId());
        String distributeNum = distributeChar;
        if (Integer.parseInt(distributeChar) < 10) {
            distributeNum = "0" + distributeChar;
        }
        if (distributeObj != null) {
            // 生成分发文件
            this.signEffectiveCore(versionObj, "distribute", docDistributeId, distributeNum);
        } else {
            return AjaxResult.error(I18nUtils.getTitle(CommonI18nConstant.FILE_SIGNATURE_DISTRIBUTE_RECORD_NOT_EXIST));
        }
        return AjaxResult.success();
    }

    public AjaxResult signEffectiveDisByVersionId(String versionId) throws ServerException {
        Version versionObj = this.versionService.getById(versionId);
        // 生成分发文件
        this.signEffectiveCore(versionObj, "distribute", versionId, "");
        return AjaxResult.success();
    }


    /**
     * 对文件版本进行文件分发、文件作废的签章（基于文件版本）
     *
     * @param versionObj
     * @param action
     * @param docDistributeId
     * @param distributeNum
     */
    public void signEffectiveCore(Version versionObj, String action, String docDistributeId, String distributeNum) {
        try {
            // 是否初始化文件台账 true是 false否
            boolean initFile = false;
            if (versionObj.getChangeReason() != null) {
                if (versionObj.getChangeReason().equals("系统初始化")) {
                    initFile = true;
                }
            }
            if ("distribute".equals(action)) {
                // 文件分发动作-生成分发PDF文件
                // 1.文件台账主文件-当前生效版本
                List<DocLinkLogVo> docList = docLinkLogService.queryDocLinkVo(versionObj.getId(), LinkTypeEnum.DOC.name());
                // 生成分发PDF文件
                // String topChar = "生效日期:"+DateUtil.format(versionObj.getStartDate(),"YYYY-MM-dd") + " 发布日期:"+DateUtil.format(versionObj.getReleaseTime(),"YYYY-MM-dd")  + " 分发日期:"+DateUtil.format(new Date(),"YYYY-MM-dd") ;
                WatermarkParamBo param = new WatermarkParamBo();
                param.setStartDate(I18nUtils.getTitle(CommonI18nConstant.FILE_PDF_START_DATE) + ":" + DateUtil.format(versionObj.getStartDate(), "YYYY-MM-dd"));
                param.setReleaseTime(I18nUtils.getTitle(CommonI18nConstant.FILE_PDF_RELEASE_DATE) + ":" + DateUtil.format(versionObj.getReleaseTime(), "YYYY-MM-dd"));
                param.setDispenseDate(I18nUtils.getTitle(CommonI18nConstant.FILE_PDF_DISPENSE_DATE) + ":" + DateUtil.format(new Date(), "YYYY-MM-dd"));
                String qrCode= customConfig.getMobileFrontUrl()+"pages/receive/index?id="+docDistributeId;
                for (DocLinkLogVo item : docList) {
                    if (!this.isSignFileType(item.getFileName())) {
                        continue;
                    }
                    //获取文件名称后缀
                    String fileNameSuffix = getFileNameSuffix(item.getFileName());
                    item.getFileName().toLowerCase(Locale.ROOT);
                    List<DocClassWatermarkSettingDetail> docClassWatermarkSettingDetailList = docClassWatermarkSettingService.getDocClassWatermarkSettingDetailList
                            (item.getDocClass(), Constants.BIZ_TYPE_PRINT, true, Constants.APPLIED_RANGE_MAIN);
                    log.error("docClassWatermarkSettingDetailList:{}", docClassWatermarkSettingDetailList.size());
                    param.setDocClassWatermarkSettingDetailList(docClassWatermarkSettingDetailList);
                    param.setVersionValue(item.getVersionValue());
                    param.setDocId(initFile ? null : versionObj.getDocId());
                    param.setDistributeChar(distributeNum);
                    param.setQrCode(qrCode);
                    param.setInternalFileNumber(versionObj.getInternalDocId());
                    log.error("fileNameSuffix:{}", fileNameSuffix);
                    //是否是pdf
                    if (Constants.FILE_TYPE_PDF.equals(fileNameSuffix)) {
                        this.basicFilePdfService.fileToPdfDistribute(docDistributeId, "ALL", item.getFileId(), "文件生效章,文件分发章", param);
                    } else {
                        this.basicFilePdfService.fileToPdfDistribute(docDistributeId, "ALL", item.getFileId(), "文件生效章,文件分发章", param);
                    }
                }
                // 2.文件台账版本附件（不加文件编号水印）
                List<DocLinkLogVo> appendList = docLinkLogService.queryDocLinkVo(versionObj.getId(), LinkTypeEnum.APPENDIX.name());
                for (DocLinkLogVo item : appendList) {
                    if (!this.isSignFileType(item.getFileName())) {
                        BasicFilePdf basicFilePdf= new BasicFilePdf();
                        basicFilePdf.setBizId(docDistributeId);
                        basicFilePdf.setFileId(item.getFileId());
                        basicFilePdf.setPdfId(item.getFileId());
                        basicFilePdf.setPdfType(PdfTypeEnum.DISTRIBUTE.getCode());
                        basicFilePdf.setStatus(YNEnum.YES.name());
                        basicFilePdf.setCreateTime(DateUtil.date());
                        basicFilePdfService.save(basicFilePdf);
                        continue;
                    }
                    //获取文件名称后缀
                    String fileNameSuffix = getFileNameSuffix(item.getFileName());
                    item.getFileName().toLowerCase(Locale.ROOT);
                    List<DocClassWatermarkSettingDetail> docClassWatermarkSettingDetailList = docClassWatermarkSettingService.getDocClassWatermarkSettingDetailList
                            (docList.get(0).getDocClass(), Constants.BIZ_TYPE_PRINT, true, Constants.APPLIED_RANGE_ATTACH);
                    param.setDocClassWatermarkSettingDetailList(docClassWatermarkSettingDetailList);
                    param.setVersionValue(item.getVersionValue());
                    param.setDocId(initFile ? null : versionObj.getDocId());
                    param.setDistributeChar(distributeNum);
                    param.setQrCode(qrCode);
                    //是否是pdf
                    if (Constants.FILE_TYPE_PDF.equals(fileNameSuffix)) {
                        this.basicFilePdfService.fileToPdfDistribute(docDistributeId, "ALL", item.getFileId(), "文件分发章", param);
                    } else {
                        this.basicFilePdfService.fileToPdfDistribute(docDistributeId, "ALL", item.getFileId(), "文件生效章,文件分发章", param);
                    }
                }
                // 3.文件台账关联记录
                // 分发记录文件，PDF文件不需要分发号
                List<DocLinkLogVo> recordList = docLinkLogService.queryDocLinkVo(versionObj.getId(), LinkTypeEnum.RECORD.name());
                for (DocLinkLogVo item : recordList) {
                    if (!this.isSignFileType(item.getFileName())) {
                        continue;
                    }
                    String fileNameSuffix = getFileNameSuffix(item.getFileName());
                    item.getFileName().toLowerCase(Locale.ROOT);
                    List<DocClassWatermarkSettingDetail> docClassWatermarkSettingDetailList = docClassWatermarkSettingService.getDocClassWatermarkSettingDetailList
                            (item.getDocClass(), Constants.BIZ_TYPE_PRINT, true, Constants.APPLIED_RANGE_MAIN);
                    param.setDocClassWatermarkSettingDetailList(docClassWatermarkSettingDetailList);
                    param.setVersionValue(item.getVersionValue());
                    param.setDocId(initFile ? null : item.getLinkCode());
                    param.setQrCode(qrCode);
                    //是否是pdf
                    if (Constants.FILE_TYPE_PDF.equals(fileNameSuffix)) {
                        this.basicFilePdfService.fileToPdfDistribute(docDistributeId, "ALL", item.getFileId(), "文件分发章", null);
                    } else {
                        this.basicFilePdfService.fileToPdfDistribute(docDistributeId, "ALL", item.getFileId(), "文件生效章,文件分发章", param);
                    }
                }
            } else if ("cancel".equals(action)) {
                // 文件作废动作-生成作废PDF文件
                // 当前生效版本
                List<DocLinkLogVo> docList = docLinkLogService.queryDocLinkVo(versionObj.getId(), LinkTypeEnum.DOC.name());
                // 关联记录的附件
                List<DocLinkLogVo> recordList = docLinkLogService.queryDocLinkVo(versionObj.getId(), LinkTypeEnum.RECORD.name());
                docList.addAll(recordList);
                // 生成分发PDF文件
                // String topChar = "生效日期:"+DateUtil.format(versionObj.getStartDate(),"YYYY-MM-dd") + " 发布日期:"+DateUtil.format(versionObj.getReleaseTime(),"YYYY-MM-dd")  + " 作废日期:"+DateUtil.format(new Date(),"YYYY-MM-dd") ;
                //作废日期
                // String cancelData = "作废日期:"+DateUtil.format(new Date(),"YYYY-MM-dd") ;
                WatermarkParamBo param = new WatermarkParamBo();
                param.setStartDate(I18nUtils.getTitle(CommonI18nConstant.FILE_PDF_START_DATE) + ":" + DateUtil.format(versionObj.getStartDate(), "YYYY-MM-dd"));
                param.setReleaseTime(I18nUtils.getTitle(CommonI18nConstant.FILE_PDF_RELEASE_DATE) + ":" + DateUtil.format(versionObj.getReleaseTime(), "YYYY-MM-dd"));
                param.setCancelData(I18nUtils.getTitle(CommonI18nConstant.FILE_PDF_CANCEL_DATE) + ":" + DateUtil.format(versionObj.getEndDate(), "YYYY-MM-dd"));
                param.setRetainData(I18nUtils.getTitle(CommonI18nConstant.FILE_PDF_RETAIN_DATE) + ":" + DateUtil.format(versionObj.getReviewTime(), "YYYY-MM-dd"));
                // 循环处理 主文件+关联记录
                String bizType = CommonStatusEnum.RETAIN.getStatus().equals(versionObj.getStatus()) ? ApplyTypeEnum.RETAIN.name() : ApplyTypeEnum.DISUSE.name();
                for (DocLinkLogVo item : docList) {
                    List<DocClassWatermarkSettingDetail> docClassWatermarkSettingDetailList = docClassWatermarkSettingService.getDocClassWatermarkSettingDetailList
                            (item.getDocClass(), bizType, true, item.getLinkType().equals(LinkTypeEnum.APPENDIX.name()) ? Constants.APPLIED_RANGE_ATTACH : Constants.APPLIED_RANGE_MAIN);
                    param.setDocClassWatermarkSettingDetailList(docClassWatermarkSettingDetailList);
                    param.setVersionValue(item.getVersionValue());
                    param.setDocId(initFile ? null : item.getLinkCode());
                    param.setInternalFileNumber(versionObj.getInternalDocId());
                    //status为1是 关联记录不用盖失效章的
                    if (!this.isSignFileType(item.getFileName()) || Constants.ONE.equals(item.getStatus())) {
                        continue;
                    }
                    // 指定台账类型或文件口站名 不需要水印章
                    boolean isSign = !this.isNoSignFileType(versionObj.getClassType(), item.getFileId(), Constants.ADD_TYPE_WORKFLOW);
                    String fileNameSuffix = getFileNameSuffix(item.getFileName());
                    item.getFileName().toLowerCase(Locale.ROOT);
                    if (Constants.FILE_TYPE_PDF.equals(fileNameSuffix)) {
                        // PDF文件
                        this.basicFilePdfService.fileToPdfSignature("ALL", item.getFileId(), isSign ? "文件失效章" : "", param);
                    } else {
                        this.basicFilePdfService.fileToPdfSignature("ALL", item.getFileId(), isSign ? "文件生效章,文件失效章" : "", param);
                    }
                }
                // 生效版本的附件（作废的时候，不添加文件编号水印）
                List<DocLinkLogVo> appendList = docLinkLogService.queryDocLinkVo(versionObj.getId(), LinkTypeEnum.APPENDIX.name());
                for (DocLinkLogVo item : appendList) {
                    List<DocClassWatermarkSettingDetail> docClassWatermarkSettingDetailList = docClassWatermarkSettingService.getDocClassWatermarkSettingDetailList
                            (docList.get(0).getDocClass(), ApplyTypeEnum.DISUSE.name(), true, item.getLinkType().equals(LinkTypeEnum.APPENDIX.name()) ? Constants.APPLIED_RANGE_ATTACH : Constants.APPLIED_RANGE_MAIN);
                    param.setDocClassWatermarkSettingDetailList(docClassWatermarkSettingDetailList);
                    param.setVersionValue(item.getVersionValue());
                    param.setDocId(item.getLinkCode());
                    //status为1是 关联记录不用盖失效章的
                    if (!this.isSignFileType(item.getFileName()) || Constants.ONE.equals(item.getStatus())) {
                        continue;
                    }
                    // 指定台账类型或文件口站名 不需要水印章
                    boolean isSign = !this.isNoSignFileType(versionObj.getClassType(), item.getFileId(), Constants.ADD_TYPE_WORKFLOW);
                    String fileNameSuffix = getFileNameSuffix(item.getFileName());
                    item.getFileName().toLowerCase(Locale.ROOT);
                    if (Constants.FILE_TYPE_PDF.equals(fileNameSuffix)) {
                        // PDF文件
                        this.basicFilePdfService.fileToPdfSignature("ALL", item.getFileId(), isSign ? "文件失效章" : "", param);
                    } else {
                        this.basicFilePdfService.fileToPdfSignature("ALL", item.getFileId(), isSign ? "文件生效章,文件失效章" : "", param);
                    }
                }
            }
        } catch (Exception e) {
            log.error("signEffectiveCore异常", e);
        }

    }

    private String getFileNameSuffix(String fileName) {
        return fileName.substring(fileName.lastIndexOf(".") + 1);
    }


    /**
     * 更改上版本关联记录的状态记录
     *
     * @param applyId     流程编号
     * @param lastVersion 旧版本
     */
    @Transactional(rollbackFor = Exception.class)
    public void handleLinkLog(String applyId, Version lastVersion) {
        //获取关联记录中不需要失效的文件
        List<String> docIdList = modifyApplyLinkService.list(new LambdaQueryWrapper<ModifyApplyLink>()
                .eq(ModifyApplyLink::getApplyId, applyId)
                .eq(ModifyApplyLink::getStatus, NumberConstants.ONE)
                .eq(ModifyApplyLink::getIsDeleted, NumberConstants.ONE)
                .select(ModifyApplyLink::getDocId)).stream().map(ModifyApplyLink::getDocId).collect(Collectors.toList());
        //获取上版本的所有记录的id
        List<String> linkIdList = iDocVersionLinkService.list(new LambdaQueryWrapper<DocVersionLink>()
                        .eq(DocVersionLink::getVersionId, lastVersion.getId())
                        .select(DocVersionLink::getLinkId))
                .stream().map(DocVersionLink::getLinkId).collect(Collectors.toList());
        //更新记录状态
        if (linkIdList.size() > 0) {
            docLinkLogService.update(new LambdaUpdateWrapper<DocLinkLog>().set(DocLinkLog::getStatus, NumberConstants.TWO).set(DocLinkLog::getEndDate, DateUtil.date())
                    .in(DocLinkLog::getId, linkIdList)
//                    .ne(DocLinkLog::getLinkType,LinkTypeEnum.RECORD.name())
                    .and(ObjectUtil.isNotEmpty(docIdList), wrapper -> {
                        wrapper.notIn(DocLinkLog::getLinkCode, docIdList)
                                .or().isNull(DocLinkLog::getLinkCode);
                    }));
        }
    }


    public void addPreviewWaterMark(String pdfFileId, String text, HttpServletResponse response) throws ServerException {
        BasicFileVo fileVo = basicFileService.getVoById(pdfFileId);
        String absoluteFilePath = iStoreFileService.getStorePath() + File.separator + fileVo.getFilePath();
        File file = new File(absoluteFilePath);
        if (file.exists()) {
            response.setContentType("application/octet-stream");
            response.setHeader("content-type", "application/octet-stream");
            String fileName = fileVo.getFileName();
            try {
                fileName = URLEncoder.encode(fileVo.getFileName(), "utf8");
            } catch (Exception e) {
            }
            response.setHeader("Content-Disposition", "attachment;fileName=" + fileName);
            byte[] buffer = new byte[1024];
            //输出流
            OutputStream os = null;
            try (FileInputStream fis = new FileInputStream(file);
                 BufferedInputStream bis = new BufferedInputStream(fis);) {
                os = response.getOutputStream();
                int i = bis.read(buffer);
                while (i != -1) {
                    os.write(buffer);
                    i = bis.read(buffer);
                }
            } catch (Exception e) {
                log.error("addPreviewWaterMark file error:", e);
            }
        } else {
            log.error("addPreviewWaterMark file path " + absoluteFilePath + " not found");
        }
    }

    public void refreshSignature(Version version, String docClass,String bizType) throws Exception {
        // 文件扩展名
        //主文件转pdf 签章
        String startDate = DateUtils.format(version.getStartDate(), "yyyy-MM-dd");
        String releaseTime = DateUtils.format(version.getReleaseTime(), "yyyy-MM-dd");
        // String topChar = "生效日期:"+startDate+" 发布日期:"+releaseTime ;
        WatermarkParamBo param = new WatermarkParamBo();
        param.setStartDate(I18nUtils.getTitle(CommonI18nConstant.FILE_PDF_START_DATE) + ":" + startDate);
        param.setReleaseTime(I18nUtils.getTitle(CommonI18nConstant.FILE_PDF_RELEASE_DATE) + ":" + releaseTime);
        param.setInternalFileNumber(version.getInternalDocId());
        // 指定台账类型或文件口站名 不需要水印章
        boolean isSign = !fileSignatureService.isNoSignFileType(version.getClassType(), version.getFileId(),StringUtils.isEmpty(version.getApplyId())?Constants.ADD_TYPE_IMPORT:Constants.ADD_TYPE_WORKFLOW);
        BasicFile basicFile = iBasicFileService.getById(version.getFileId());

        //PDF文件的主文件支持盖章
        List<DocClassWatermarkSettingDetail> docClassWatermarkSettingDetailList = docClassWatermarkSettingService.getDocClassWatermarkSettingDetailList(docClass, bizType, true, Constants.APPLIED_RANGE_MAIN);
        param.setDocClassWatermarkSettingDetailList(docClassWatermarkSettingDetailList);
        param.setVersionValue(version.getVersionValue());
        param.setDocId(version.getDocId());
        basicFilePdfService.fileToPdfSignature("ALL", version.getFileId(), isSign ? "文件生效章" : "", param);

//        if (!Constants.FILE_TYPE_PDF.equals(basicFile.getFileType())) {
//            // 非PDF文件的主文件
//            List<DocClassWatermarkSettingDetail> docClassWatermarkSettingDetailList = docClassWatermarkSettingService.getDocClassWatermarkSettingDetailList(docClass, bizType, true, Constants.APPLIED_RANGE_MAIN);
//            param.setDocClassWatermarkSettingDetailList(docClassWatermarkSettingDetailList);
//            param.setVersionValue(version.getVersionValue());
//            param.setDocId(version.getDocId());
//            basicFilePdfService.fileToPdfSignature("ALL", version.getFileId(), isSign ? "文件生效章" : "", param);
//        }

        //关联附件转pdf,签章
        List<DocLinkLogVo> linkLogVoList = docLinkLogService.queryDocLinkVo(version.getId(), LinkTypeEnum.APPENDIX.name());
        List<DocClassWatermarkSettingDetail> appendixWatermarkSettingDetailList = docClassWatermarkSettingService.getDocClassWatermarkSettingDetailList
                (docClass, bizType, true, Constants.APPLIED_RANGE_ATTACH);
        param.setDocClassWatermarkSettingDetailList(appendixWatermarkSettingDetailList);
        for (int i = 0; i < linkLogVoList.size(); i++) {
            DocLinkLogVo docLinkLogVo = linkLogVoList.get(i);
            if (isSignFileType(docLinkLogVo.getFileName())) {
                isSign = !this.fileSignatureService.isNoSignFileType(version.getClassType(), docLinkLogVo.getFileId(), StringUtils.isEmpty(version.getApplyId())?Constants.ADD_TYPE_IMPORT:Constants.ADD_TYPE_WORKFLOW);
                basicFilePdfService.fileToPdfSignature("ALL", docLinkLogVo.getFileId(), isSign ? "文件生效章" : "", param);
            }
        }
        deletePrint(version.getId());
    }

    //逻辑删除打印生成的水印文件
    public void deletePrint(String versionId){
        List<DocDistribute> list = docDistributeService.list(new LambdaQueryWrapper<DocDistribute>().eq(DocDistribute::getVersionId,versionId).eq(DocDistribute::getType,Constants.DISTRIBUTE_TYPE_PRINT));
        if (list.size()>0) {
            List<String> idList = list.stream().map(DocDistribute::getId).collect(Collectors.toList());
            basicFilePdfService.update(new LambdaUpdateWrapper<BasicFilePdf>()
                    .set(BasicFilePdf::getStatus,YNEnum.NO.name())
                    .in(BasicFilePdf::getBizId,idList)
                    .eq(BasicFilePdf::getPdfType,PdfTypeEnum.DISTRIBUTE.getCode())
                    .eq(BasicFilePdf::getStatus,YNEnum.YES.name()));
        }
    }

}


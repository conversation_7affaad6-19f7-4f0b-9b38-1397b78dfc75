package com.rzdata.process.service;

import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.core.mybatisplus.core.IServicePlus;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.process.domain.BasicFileEditingDetailLog;
import com.rzdata.process.domain.bo.BasicFileEditingDetailLogBo;
import com.rzdata.process.domain.vo.BasicFileEditingDetailLogVo;

import java.util.Collection;
import java.util.List;

/**
 * 编辑明细日志Service接口
 *
 * <AUTHOR>
 * @date 2025-04-30
 */
public interface IBasicFileEditingDetailLogService extends IServicePlus<BasicFileEditingDetailLog, BasicFileEditingDetailLogVo> {
	/**
	 * 查询单个
	 * @return
	 */
	BasicFileEditingDetailLogVo queryById(String id);

	/**
	 * 查询列表
	 */
    TableDataInfo<BasicFileEditingDetailLogVo> queryPageList(BasicFileEditingDetailLogBo bo);

	/**
	 * 查询列表
	 */
	List<BasicFileEditingDetailLogVo> queryList(BasicFileEditingDetailLogBo bo);

	/**
	 * 根据新增业务对象插入编辑明细日志
	 * @param bo 编辑明细日志新增业务对象
	 * @return
	 */
	Boolean insertByBo(BasicFileEditingDetailLogBo bo);

	/**
	 * 根据编辑业务对象修改编辑明细日志
	 * @param bo 编辑明细日志编辑业务对象
	 * @return
	 */
	Boolean updateByBo(BasicFileEditingDetailLogBo bo);

	/**
	 * 校验并删除数据
	 * @param ids 主键集合
	 * @param isValid 是否校验,true-删除前校验,false-不校验
	 * @return
	 */
	Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);

	/**
     * 检查文件是否可以编辑
     *
     * @param bo 编辑实例
     * @param userId 当前用户ID
     * @return 是否可以编辑
     */
	AjaxResult checkFileEditStatus(BasicFileEditingDetailLogBo bo, String userId);

    /**
     * 释放文件编辑锁
     *
     * @param bo 编辑实例
     * @param userId 当前用户ID
     */
    void releaseFileEditLock(String protoFileId, String userId);
}

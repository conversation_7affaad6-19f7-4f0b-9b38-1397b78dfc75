package com.rzdata.process.service;

import com.rzdata.framework.core.mybatisplus.core.IServicePlus;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.process.domain.BasicFile;
import com.rzdata.process.domain.BasicFilePdf;
import com.rzdata.process.domain.bo.BasicFilePdfBo;
import com.rzdata.process.domain.vo.BasicFilePdfVo;
import com.rzdata.setting.domain.bo.WatermarkParamBo;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.rmi.ServerException;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 附件转PDFService接口
 *
 * <AUTHOR>
 * @date 2023-07-31
 */
public interface IBasicFilePdfService extends IServicePlus<BasicFilePdf, BasicFilePdfVo> {
	/**
	 * 查询单个
	 * @return
	 */
	BasicFilePdfVo queryById(String id);

	/**
	 * 查询列表
	 */
    TableDataInfo<BasicFilePdfVo> queryPageList(BasicFilePdfBo bo);

	/**
	 * 查询列表
	 */
	List<BasicFilePdfVo> queryList(BasicFilePdfBo bo);

	/**
	 * 根据新增业务对象插入附件转PDF
	 * @param bo 附件转PDF新增业务对象
	 * @return
	 */
	Boolean insertByBo(BasicFilePdfBo bo);

	/**
	 * 根据编辑业务对象修改附件转PDF
	 * @param bo 附件转PDF编辑业务对象
	 * @return
	 */
	Boolean updateByBo(BasicFilePdfBo bo);

	/**
	 * 校验并删除数据
	 * @param ids 主键集合
	 * @param isValid 是否校验,true-删除前校验,false-不校验
	 * @return
	 */
	Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);

	/**
	 * file转pdf
	 * @param basicFile 源文件
	 * @return
	 */
	BasicFile fileToPdf(BasicFile basicFile) throws Exception;

	/**
	 * file转pdf
	 * @param basicFile 源文件
	 * @return
	 */
	BasicFile fileToPdfChoose(BasicFile basicFile) throws Exception;

	/**
	 * file转pdf用章文件
	 * @param fileId
	 * @return
	 */
	String fileToPdfSignature(String docClass, String fileId, String type, WatermarkParamBo param) throws Exception;


	/**
	 * file转pdf分发文件
	 * @param fileId
	 * @return
	 */
	String fileToPdfDistribute(String bizId, String docClass, String fileId, String type, WatermarkParamBo param) throws Exception;

	/**
	 * 根据源文件获取PDFID
	 * @param fileId  源文件ID
	 * @param type  PdfTypeEnum
	 * @return
	 */
	String getPdfId(String fileId,String type);

	/**
	 * 保存pdf源文件
	 * @param id
	 */
	void pdfBasicFileSave(String id);

	void pdfBasicFileSave(String fileId,String pdfFileId,String type);


	/**
	 * 文档比对
	 *
	 * @param fileId1 word文件ID1
	 * @param fileId2 word文件ID2
	 * @return
	 */
	String fileCompareToPdf(String fileId1,String fileId2,String businessId) throws ServerException;

	BasicFile uploading(MultipartFile file) throws Exception;

	BasicFile uploading(File file) throws Exception;

	BasicFile getPdfFilePathByFileId(String fileId, String type);
}

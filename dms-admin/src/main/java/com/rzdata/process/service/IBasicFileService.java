package com.rzdata.process.service;

import com.rzdata.framework.core.mybatisplus.core.IServicePlus;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.process.domain.BasicFile;
import com.rzdata.process.domain.bo.BasicFileBo;
import com.rzdata.process.domain.vo.BasicFileVo;
import com.rzdata.setting.domain.bo.WatermarkParamBo;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 附件Service接口
 *
 * <AUTHOR>
 * @date 2022-01-04
 */
public interface IBasicFileService extends IServicePlus<BasicFile, BasicFileVo> {
	/**
	 * 批量查询
	 * @param fileIds ID
	 * @return
	 */
	Map<String, BasicFileVo> queryByIdList(List<String> fileIds);
	/**
	 * 查询单个
	 * @return
	 */
	BasicFileVo queryById(String id);

	/**
	 * 查询列表
	 */
    TableDataInfo<BasicFileVo> queryPageList(BasicFileBo bo);

	/**
	 * 查询列表
	 */
	List<BasicFileVo> queryList(BasicFileBo bo);

	/**
	 * 根据新增业务对象插入附件
	 * @param bo 附件新增业务对象
	 * @return
	 */
	String insertByBo(BasicFileBo bo);


	String uploading(MultipartFile file,String path) throws Exception;

	/**
	 * 根据编辑业务对象修改附件
	 * @param bo 附件编辑业务对象
	 * @return
	 */
	Boolean updateByBo(BasicFileBo bo);

	/**
	 * 校验并删除数据
	 * @param ids 主键集合
	 * @param isValid 是否校验,true-删除前校验,false-不校验
	 * @return
	 */
	Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);

	/**
	 * 生成原始WORD对应的PDF文件
	 */
	BasicFile createPdfFile(BasicFile basicFile) throws Exception;

	/**
	 * 生成原始WORD对应的PDF文件
	 */
	void createPdfFile(InputStream inputStream, String desPath,String fileName) throws IOException;

	/**
	 * 生成签章PDF文件
	 */
	String createEncryptFileId(String docClass, BasicFile basicFile, String type, WatermarkParamBo param);


	/**
	 * 生成原始WORD对应的PDF文件
	 */
	String createComparePdfFile(String fileId1,String fileId2,String businessId);
}

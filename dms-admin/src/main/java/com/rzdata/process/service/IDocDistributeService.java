package com.rzdata.process.service;

import com.rzdata.process.domain.DocDistribute;
import com.rzdata.process.domain.Version;
import com.rzdata.process.domain.dto.VersionDistributeDTO;
import com.rzdata.process.domain.vo.DocDistributeVo;
import com.rzdata.process.domain.bo.DocDistributeBo;
import com.rzdata.framework.core.mybatisplus.core.IServicePlus;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.process.domain.vo.DocTrainReadVo;

import java.util.Collection;
import java.util.List;

/**
 * 文件分发明细Service接口
 *
 * <AUTHOR>
 * @date 2023-07-20
 */
public interface IDocDistributeService extends IServicePlus<DocDistribute, DocDistributeVo> {
	/**
	 * 查询单个
	 * @return
	 */
	DocDistributeVo queryById(String id);

	/**
	 * 查询列表
	 */
    TableDataInfo<DocDistributeVo> queryPageList(DocDistributeBo bo);

	TableDataInfo<DocDistributeVo> queryPageDocList(DocDistributeBo bo);

	/**
	 * 权限过滤查询
	 * @param bo
	 * @return
	 */
	TableDataInfo<DocDistributeVo> queryAuthFilterPageDocList(DocDistributeBo bo);


	/**
	 * 查询阅读同意数据列表
	 * @param bo
	 * @return
	 */
	TableDataInfo<DocDistributeVo> readTrainPage(DocDistributeBo bo);
	/**
	 * 查询列表
	 */
	List<DocDistributeVo> queryList(DocDistributeBo bo);

	/**
	 * 根据权限过滤
	 * @param bo
	 * @return
	 */
	List<DocDistributeVo> authFilterList(DocDistributeBo bo);

	Boolean receiveByIds(Collection<String> ids);


	Boolean recoveryByIds(Collection<String> ids);

	Boolean lostByIds(Collection<String> ids);
	/**
	 * 根据新增业务对象插入文件分发明细
	 * @param bo 文件分发明细新增业务对象
	 * @return
	 */
	Boolean insertByBo(DocDistributeBo bo);

	VersionDistributeDTO updateList(VersionDistributeDTO dto);

	Boolean updateCodeList(VersionDistributeDTO dto);

	/**
	 * 根据编辑业务对象修改文件分发明细
	 * @param bo 文件分发明细编辑业务对象
	 * @return
	 */
	Boolean updateByBo(DocDistributeBo bo);

	/**
	 * 校验并删除数据
	 * @param ids 主键集合
	 * @param isValid 是否校验,true-删除前校验,false-不校验
	 * @return
	 */
	Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);

	void handleDistribute(Version version, String docClass);

	void handleDistributeByApply(String applyId,String versionId,String docId,String docName, String docClass);

	void handleDistributeByApply(String applyId,String versionId,String docId,String docName, String docClass,String oldType,String newType);

	void refreshDistribute(Version version,String docClass);

	List<DocDistributeVo> getPrintGroupList(String versionId);

	Integer getMaxCodeByVersionId(String versionId);

	List<DocDistributeVo> postQueryList(DocDistributeBo bo);

	/**
	 * 发送待回收邮件
	 * @param ids 分发记录Id
	 */
	Boolean sendRecoveryEMail(Collection<String> ids);


	TableDataInfo<DocDistributeVo> trainDetailList(DocDistributeBo bo);

	List<DocDistributeVo> trainDetailStatusList(DocDistributeBo bo);

	/**
	 * 预览打印找到对应的文件分发人
	 * @param id
	 * @return
	 */
	DocDistributeVo getPrintAuthById(String id,String fileId);

	/**
	 * 打印找到对应的文件分发人 (批量打印专用)
	 * @param id
	 * @return
	 */
	List<DocDistributeVo> getPrintAuthBatchById(String id,List<String> fileIds);
}

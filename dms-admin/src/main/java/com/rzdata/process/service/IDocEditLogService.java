package com.rzdata.process.service;

import com.rzdata.process.domain.DocEditLog;
import com.rzdata.process.domain.DocLinkLog;
import com.rzdata.process.domain.vo.DocEditLogVo;
import com.rzdata.process.domain.bo.DocEditLogBo;
import com.rzdata.framework.core.mybatisplus.core.IServicePlus;
import com.rzdata.framework.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 文件编辑日志Service接口
 *
 * <AUTHOR>
 * @date 2025-03-03
 */
public interface IDocEditLogService extends IServicePlus<DocEditLog, DocEditLogVo> {
	/**
	 * 查询单个
	 * @return
	 */
	DocEditLogVo queryById(String id);

	/**
	 * 查询列表
	 */
    TableDataInfo<DocEditLogVo> queryPageList(DocEditLogBo bo);

	/**
	 * 查询列表
	 */
	List<DocEditLogVo> queryList(DocEditLogBo bo);

	/**
	 * 根据新增业务对象插入文件编辑日志
	 * @param bo 文件编辑日志新增业务对象
	 * @return
	 */
	Boolean insertByBo(DocEditLogBo bo);

	/**
	 * 根据编辑业务对象修改文件编辑日志
	 * @param bo 文件编辑日志编辑业务对象
	 * @return
	 */
	Boolean updateByBo(DocEditLogBo bo);

	/**
	 * 校验并删除数据
	 * @param ids 主键集合
	 * @param isValid 是否校验,true-删除前校验,false-不校验
	 * @return
	 */
	Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);

	void saveLog(String applyId,String type,String actDefName);

	void saveLogByVersionId(String versionId, String fileId,String fileName,String protoFileId, String type, String actDefName);

	void handleEditLog(String applyId,String versionId);
}

package com.rzdata.process.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.core.mybatisplus.core.IServicePlus;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.process.domain.*;
import com.rzdata.process.domain.bo.DocLinkLogBo;
import com.rzdata.process.domain.vo.DocLinkLogVo;
import com.rzdata.process.domain.vo.DocLinkSearchDetailVo;
import com.rzdata.process.domain.vo.DocLinkSearchResultVo;
import com.rzdata.process.enums.LinkTypeEnum;

import javax.servlet.http.HttpServletResponse;
import java.rmi.ServerException;
import java.util.Collection;
import java.util.List;

/**
 * 文件关联记录Service接口
 *
 * <AUTHOR>
 * @date 2022-01-06
 */
public interface IDocLinkLogService extends IServicePlus<DocLinkLog, DocLinkLogVo> {
	/**
	 * 查询单个
	 * @return
	 */
	DocLinkLogVo queryById(String id);

	/**
	 * 查询列表
	 */
    TableDataInfo<DocLinkLogVo> queryPageList(DocLinkLogBo bo);

	/**
	 * 查询列表
	 */
	List<DocLinkLogVo> queryList(DocLinkLogBo bo);

	/**
	 * 根据新增业务对象插入文件关联记录
	 * @param bo 文件关联记录新增业务对象
	 * @return
	 */
	Boolean insertByBo(DocLinkLogBo bo);

	/**
	 * 根据编辑业务对象修改文件关联记录
	 * @param bo 文件关联记录编辑业务对象
	 * @return
	 */
	Boolean updateByBo(DocLinkLogBo bo) throws ServerException;

	/**
	 * 校验并删除数据
	 * @param ids 主键集合
	 * @param isValid 是否校验,true-删除前校验,false-不校验
	 * @return
	 */
	Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);


	/**
	 * 查出文件的关联记录
	 * @param bo
	 * @return
	 */
	List<DocLinkLog> queryLink(DocLinkLogBo bo);


	/**
	 * 查出文件的关联记录(当前生效)
	 * @param versionId
	 * @param linkType DOC正文 APPENDIX附件 RECORD记录 REF_DOC关联其他文件
	 * @return
	 */
	List<DocLinkLogVo> queryDocLinkVo(String versionId, String linkType);

	int getLinkCount(DocLinkLogBo bo);

	boolean deleteById(String id);

	boolean deleteLinkLog(String linkType, String versionId, String pVersionId);

	/**
	 * 初始化关联记录到记录台账(用于初始化工作)
	 *
	 * @param dataType
	 * @param docClass
	 * @param standardId
	 * @param versionId
	 * @param linkCode
	 *
	 * @return
	 */
	AjaxResult init2Account(String dataType,String docClass,
							String standardId,String versionId,
							String linkCode);

	Boolean disuseValidLinkLog(String versionId,String linkCode);


	/**
	 * 查询待初始化关联记录到记录台账(用于初始化工作)
	 *
	 * @param dataType
	 * @param docClass
	 * @param standardId
	 * @param versionId
	 * @param linkCode
	 *
	 * @return
	 */
	public List<DocLinkLogVo> queryInit2Account(String dataType,String docClass,
									String standardId,String versionId,
									String linkCode);

	AjaxResult init2AccountByLinkLog(Standard sourceStdd, Version currVersion, DocLinkLog logObj);

	DocLinkLog handApplyLinkAdd(ModifyApplyLink applyLink, String linkType, String versionId);

	DocLinkLog handApplyLinkAdd(Version version,String docClass, String linkType, String versionId,String protoFileId);

	TableDataInfo<DocLinkSearchResultVo> docLinkSearchPage(DocLinkLogBo bo);

	List<DocLinkSearchDetailVo> docLinkSearchDetailList(Collection<String> versionIds);

	void docLinkSearchDownload(Collection<String> versionIds, HttpServletResponse response);

	Page<DocLinkSearchResultVo> docLinkSearchDataBuild(Page<DocLinkSearchResultVo> result);

}

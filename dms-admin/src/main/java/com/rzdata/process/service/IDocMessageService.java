package com.rzdata.process.service;

import com.rzdata.eventbus.ProcessResultEvent;
import com.rzdata.framework.core.mybatisplus.core.IServicePlus;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.process.domain.DocMessage;
import com.rzdata.process.domain.ModifyApply;
import com.rzdata.process.domain.bo.DocMessageBo;
import com.rzdata.process.domain.vo.DocMessageVo;
import com.rzdata.process.enums.MsgTypeEnum;

import java.rmi.ServerException;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/2/11 11:02
 * @Version 1.0
 * @Description 消息服务接口
 */

public interface IDocMessageService extends IServicePlus<DocMessage, DocMessageVo> {

    /**
     * 查询列表
     * 
     * @param bo 查询参数
     * @return 消息集合
     */
    TableDataInfo<DocMessageVo> queryListByPage(DocMessageBo bo);

    /**
     * 获取未读消息数量
     * 
     * @param docMessageBo
     * @return
     */
    int getUnreadNum(DocMessageBo docMessageBo);

    /**
     * 文件变更/补发/增发/复审生成消息
     * 
     * @param event
     * @return true=成功 false=失败
     */
    void insertMessage(ProcessResultEvent event, MsgTypeEnum msgType);

    /**
     * 借阅申请生成消息
     * 
     * @param event
     * @return true=成功 false=失败
     */
    void insertMessageByBorrow(ProcessResultEvent event, MsgTypeEnum msgType);

    /**
     * 文件回收消息
     * 
     * @param applyPo
     */
    void insertMessageByReceive(ModifyApply applyPo);

    /**
     * 文件生效消息
     * 
     * @param applyPo
     */
    void insertMessageByValidity(ModifyApply applyPo);

    void insertMessageEndArchiving(String applyId) throws ServerException;

    /**
     * 消息速递
     * 
     * @return
     */
    List<DocMessageVo> queryMessageDelivery();

    /**
     * 生成消息 定时任务专用
     * 
     * @param docMessage
     * @return true=成功 false=失败
     */
    Boolean insert(DocMessage docMessage);

    int pushSimpleMsg(String json);

    /**
     * 发送简单站内消息
     *
     * @param msgList
     * @return
     */
    int pushSimpleMsg(List<DocMessage> msgList);

    /**
     * 保存站内消息
     * 
     * @param versionId 版本id
     * @param applyId   业务流程申请id
     * @param tipName   标题提示明
     * @param docName   文件名称
     * @param docId     文件编号
     * @param msgClass  类型
     * @param pcUrl     pc端链接地址
     * @param mobileUrl 移动端链接地址
     */
    void saveMsg(String versionId, String applyId, String tipName, String docName, String docId, Integer msgClass,
            String content, String pcUrl, String mobileUrl);

    /**
     * 站内消息
     *
     * @param applyId      流程id
     * @param docId        文档ID
     * @param docClass     文档分类
     * @param docName      文档名称
     * @param versionId    版本id
     * @param versionValue 版本号
     * @param deptId       部门ID
     * @param deptName     部门名称
     * @param userName     接收人账户
     * @param userId       接收人ID
     * @param content      消息内容
     * @param msgType      消息类型
     * @param linkUrl      连接地址
     * @param mobileUrl    移动端链接地址
     * @param tenantId     租户ID
     **/
    void sendInstationMessage(String applyId, String docId, String docClass, String docName, String versionId,
            String versionValue, String deptId, String deptName, String userName, String userId,
            String content, String msgType, Integer msgClass, String linkUrl, String mobileUrl, String tenantId);
}

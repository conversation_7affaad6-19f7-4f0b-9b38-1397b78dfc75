package com.rzdata.process.service;

import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.process.domain.bo.DocDisStatisticsBo;
import com.rzdata.process.domain.bo.DocStatisticsBo;
import com.rzdata.process.domain.bo.DocStatisticsTrainingBo;
import com.rzdata.process.domain.bo.StandardBo;
import com.rzdata.process.domain.vo.*;

import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/2/8 14:27
 * @Version 1.0
 * @Description
 */
public interface IDocStatisticsService {

    /**
     * 获取变更类型统计
     * @param docStatisticsBo 文件统计实体类
     * @return
     */
    TableDataInfo<DocStatisticsChangeTypeVo> changeType(DocStatisticsBo docStatisticsBo);

    DocStatisticsChangeTypeVo changeTypeSum(DocStatisticsBo docStatisticsBo);
    /**
     * 获取变更因素统计
     * @param docStatisticsBo 文件统计实体类
     * @return
     */
    AjaxResult<List<DocStatisticsChangeFactorVo>> changeFactor(DocStatisticsBo docStatisticsBo);

    List<DocStatisticsChangeTypeExcelVo> getExcelData(DocStatisticsBo bo);

    /**
     * 查询文件培训台账
     * @return 文件培训台账列表（分页）
     */
    TableDataInfo<DocStatisticsTrainingVo> training(DocStatisticsTrainingBo bo);

    List<DocStatisticsTrainingExcelVo> exportExcelData(DocStatisticsTrainingBo bo);
}

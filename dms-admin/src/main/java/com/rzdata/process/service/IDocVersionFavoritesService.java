package com.rzdata.process.service;

import com.rzdata.process.domain.DocVersionFavorites;
import com.rzdata.process.domain.bo.DocDistributeBo;
import com.rzdata.process.domain.vo.DocDistributeVo;
import com.rzdata.process.domain.vo.DocVersionFavoritesVo;
import com.rzdata.framework.core.mybatisplus.core.IServicePlus;
import com.rzdata.framework.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 我的收藏Service接口
 *
 * <AUTHOR>
 * @date 2023-01-03
 */
public interface IDocVersionFavoritesService extends IServicePlus<DocVersionFavorites, DocVersionFavoritesVo> {

	/**
	 * 我的收藏
	 * @param bo
	 * @return
	 */
	TableDataInfo<DocDistributeVo> queryPageFavorites(DocDistributeBo bo);


	/**
	 * 新增或者删除收藏
	 * @param versionId 版本主键
	 * @return
	 */
	Boolean saveOrRemoveFavorites(String versionId);

	/**
	 * 删除数据
	 * @param ids 主键集合
	 * @return
	 */
	Boolean deleteWithValidByIds(Collection<String> ids);

	/**
	 * 是否在收藏夹
	 * @param versionId 版本主键
	 * @return
	 */
	Boolean inFavorites(String versionId);
}

package com.rzdata.process.service;

import com.rzdata.process.domain.ErrataLink;
import com.rzdata.process.domain.vo.ErrataLinkVo;
import com.rzdata.process.domain.bo.ErrataLinkBo;
import com.rzdata.framework.core.mybatisplus.core.IServicePlus;
import com.rzdata.framework.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 文件关联勘误记录Service接口
 *
 * <AUTHOR>
 * @date 2025-01-13
 */
public interface IErrataLinkService extends IServicePlus<ErrataLink, ErrataLinkVo> {
	/**
	 * 查询单个
	 * @return
	 */
	ErrataLinkVo queryById(String id);

	/**
	 * 查询列表
	 */
    TableDataInfo<ErrataLinkVo> queryPageList(ErrataLinkBo bo);

	/**
	 * 查询列表
	 */
	List<ErrataLinkVo> queryList(ErrataLinkBo bo);

	/**
	 * 根据新增业务对象插入文件关联勘误记录
	 * @param bo 文件关联勘误记录新增业务对象
	 * @return
	 */
	Boolean insertByBo(ErrataLinkBo bo);

	/**
	 * 根据编辑业务对象修改文件关联勘误记录
	 * @param bo 文件关联勘误记录编辑业务对象
	 * @return
	 */
	Boolean updateByBo(ErrataLinkBo bo);

	/**
	 * 校验并删除数据
	 * @param ids 主键集合
	 * @param isValid 是否校验,true-删除前校验,false-不校验
	 * @return
	 */
	Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);
}

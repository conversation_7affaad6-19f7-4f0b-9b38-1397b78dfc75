package com.rzdata.process.service;

import com.rzdata.process.domain.ErrataVersion;
import com.rzdata.process.domain.vo.ErrataVersionVo;
import com.rzdata.process.domain.bo.ErrataVersionBo;
import com.rzdata.framework.core.mybatisplus.core.IServicePlus;
import com.rzdata.framework.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 勘误文件版本记录Service接口
 *
 * <AUTHOR>
 * @date 2025-01-08
 */
public interface IErrataVersionService extends IServicePlus<ErrataVersion, ErrataVersionVo> {
	/**
	 * 查询单个
	 * @return
	 */
	ErrataVersionVo queryById(String id);

	ErrataVersionVo queryPrev(ErrataVersionBo bo);

	/**
	 * 查询列表
	 */
    TableDataInfo<ErrataVersionVo> queryPageList(ErrataVersionBo bo);

	/**
	 * 查询列表
	 */
	List<ErrataVersionVo> queryList(ErrataVersionBo bo);

	/**
	 * 根据新增业务对象插入勘误文件版本记录
	 * @param bo 勘误文件版本记录新增业务对象
	 * @return
	 */
	Boolean insertByBo(ErrataVersionBo bo) throws Exception;

	/**
	 * 根据编辑业务对象修改勘误文件版本记录
	 * @param bo 勘误文件版本记录编辑业务对象
	 * @return
	 */
	Boolean updateByBo(ErrataVersionBo bo);

	/**
	 * 校验并删除数据
	 * @param ids 主键集合
	 * @param isValid 是否校验,true-删除前校验,false-不校验
	 * @return
	 */
	Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);
}

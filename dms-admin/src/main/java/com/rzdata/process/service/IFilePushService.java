package com.rzdata.process.service;

import com.rzdata.process.domain.FilePush;
import com.rzdata.process.domain.vo.FilePushVo;
import com.rzdata.process.domain.bo.FilePushBo;
import com.rzdata.framework.core.mybatisplus.core.IServicePlus;
import com.rzdata.framework.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 文件推送Service接口
 *
 * <AUTHOR>
 * @date 2025-08-05
 */
public interface IFilePushService extends IServicePlus<FilePush, FilePushVo> {
	/**
	 * 查询单个
	 * @return
	 */
	FilePushVo queryById(String id);

	/**
	 * 查询列表
	 */
    TableDataInfo<FilePushVo> queryPageList(FilePushBo bo);

	/**
	 * 查询列表
	 */
	List<FilePushVo> queryList(FilePushBo bo);

	/**
	 * 根据新增业务对象插入文件推送
	 * @param bo 文件推送新增业务对象
	 * @return
	 */
	Boolean insertByBo(FilePushBo bo);

	/**
	 * 根据编辑业务对象修改文件推送
	 * @param bo 文件推送编辑业务对象
	 * @return
	 */
	Boolean updateByBo(FilePushBo bo);

	/**
	 * 校验并删除数据
	 * @param ids 主键集合
	 * @param isValid 是否校验,true-删除前校验,false-不校验
	 * @return
	 */
	Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);
}

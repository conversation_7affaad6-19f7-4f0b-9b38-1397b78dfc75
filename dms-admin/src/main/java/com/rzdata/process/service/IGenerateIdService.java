package com.rzdata.process.service;

import com.rzdata.process.domain.dto.DocIdNumDTO;
import com.rzdata.process.domain.vo.DocNoVo;
import com.rzdata.process.enums.ApplyTypeEnum;
import com.rzdata.process.domain.dto.CreateNewNoDTO;

import java.rmi.ServerException;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2022/1/12 上午10:25
 * @Version 1.0
 * @Desc 生成ID
 */
public interface IGenerateIdService {

    /**
     * 生成文档ID
     * @return
     */
    DocNoVo generateDocId(Map<String,Object> bizMap) throws Exception;

    DocNoVo generateDocId(String docClass,String projectCode) throws Exception;

    /**
     * 生成记录文件ID
     * @param bizMap
     * @return
     */
    DocNoVo generateRecordDocId(Map<String,Object> bizMap) throws Exception;


    /**
     * 保存历史文件编号
     * @param buinessId 业务主键
     * @param docClass 文件类型
     * @param docId 文件编号
     * @return
     */
    boolean saveOldDocId(String buinessId, String docClass, String docId, String oldDocClass, Map<String,Object> map);

    /**
     * 判断文件编号是否存在
     * @param docId 文件编号
     * @return
     */
    boolean checkDocIdExist(String docId);

    /**
     * 更新文件编号流水号
     * @param bo
     * @return
     * @throws Exception
     */
    Boolean updateDocSerialNumber(CreateNewNoDTO bo) throws Exception;

    /**
     * 更新记录文件流水号
     * @param bo
     * @return
     * @throws Exception
     */
    Boolean updateRecordSerialNumber(CreateNewNoDTO bo) throws Exception;

}

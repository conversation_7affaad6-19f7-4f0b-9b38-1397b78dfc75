package com.rzdata.process.service;

import com.rzdata.process.domain.ModifyApply;
import com.rzdata.process.domain.bo.BpmClientInputModelBo;

/**
 * @Description:业务活动中如需发送消息，请使用该服务。通过该服务，将业务与消息发送做解耦
 * @author: gj
 * @date 2024/9/27
 */
public interface IMessageSendEntryService {


    /**
     * 发送工作流消息,通过企业微信发送
     *
     * @param bo      携带的工作流客户端输入模型
     * @param msgType 消息类型，用于区分不同类型的处理逻辑
     */
    void sendWorkFlowMessagQywx(BpmClientInputModelBo bo, String msgType);

    /**
     * 通过企业微信，发送工作流消息
     * @param changeType 流程类型
     * @param docId      文件编号
     * @param applyId    流程ID
     * @param docName    文件名称
     * @param userId     用户ID
     */
     void sendWorkFlowMessagQywx(String changeType,String docId,String applyId,String docName,String userId);

    /**
     * 发送企业微信消息
     * @param docId     文档编号
     * @param bizId     业务id
     * @param userId    用户id
     * @param msgContent 发送消息内容
     */
    public void sendMsgQywx(String docId,String bizId, String userId,String msgContent);

}

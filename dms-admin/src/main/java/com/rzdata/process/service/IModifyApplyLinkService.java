package com.rzdata.process.service;

import com.rzdata.process.domain.ModifyApplyLink;
import com.rzdata.process.domain.vo.ModifyApplyLinkVo;
import com.rzdata.process.domain.bo.ModifyApplyLinkBo;
import com.rzdata.framework.core.mybatisplus.core.IServicePlus;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.process.enums.LinkTypeEnum;

import java.util.Collection;
import java.util.List;

/**
 * 文件变更操作申请引用Service接口
 *
 * <AUTHOR>
 * @date 2021-12-31
 */
public interface IModifyApplyLinkService extends IServicePlus<ModifyApplyLink, ModifyApplyLinkVo> {
	/**
	 * 查询单个
	 * @return
	 */
	ModifyApplyLinkVo queryById(String id);

	/**
	 * 查询列表
	 */
    TableDataInfo<ModifyApplyLinkVo> queryPageList(ModifyApplyLinkBo bo);

	/**
	 * 查询列表
	 */
	List<ModifyApplyLinkVo> queryList(ModifyApplyLinkBo bo);

	/**
	 * 查询列表
	 */
	List<ModifyApplyLinkVo> selectListByVersionLink(ModifyApplyLinkBo bo);

	/**
	 * 根据新增业务对象插入文件变更操作申请引用
	 * @param bo 文件变更操作申请引用新增业务对象
	 * @return
	 */
	Boolean insertByBo(ModifyApplyLinkBo bo);

	/**
	 * 根据编辑业务对象修改文件变更操作申请引用
	 * @param bo 文件变更操作申请引用编辑业务对象
	 * @return
	 */
	Boolean updateByBo(ModifyApplyLinkBo bo);

	/**
	 * 校验并删除数据
	 * @param ids 主键集合
	 * @param isValid 是否校验,true-删除前校验,false-不校验
	 * @return
	 */
	Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);

	/**
	 * 通过业务id查询ModifyApplyLink表的关联记录(ModifyApplyLink表这是暂存 因为业务中文件的关系可能会调整变化 最终生效文件的关联记录在doc_link_log表中)
	 * @param applyId  业务id
	 * @param linkType 关联类型 {@link com.rzdata.process.enums.ApplyTypeEnum}
	 * @return
	 */
	List<ModifyApplyLinkVo> queryDocByApplyIdAndType(String applyId, LinkTypeEnum linkType);

	void onlineUpdate(ModifyApplyLinkBo bo);
}

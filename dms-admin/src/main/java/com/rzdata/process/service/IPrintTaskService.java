package com.rzdata.process.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.google.protobuf.ServiceException;
import com.rzdata.framework.core.mybatisplus.core.IServicePlus;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.process.domain.PrintTask;
import com.rzdata.process.domain.bo.PrintTaskBo;
import com.rzdata.process.domain.vo.PrintTaskVo;

import java.util.List;

/**
 * 打印任务Service接口
 *
 * <AUTHOR>
 * @date 2024-01-18
 */
public interface IPrintTaskService extends IServicePlus<PrintTask,PrintTaskVo> {

    /**
     * 查询打印任务分页列表
     */
    TableDataInfo<PrintTaskVo> queryPageList(PrintTaskBo bo);

    /**
     * 查询打印任务列表
     */
    List<PrintTaskVo> queryList(PrintTaskBo bo);

    /**
     * 新增打印任务
     */
    Boolean insertByBo(PrintTaskBo bo);

    /**
     * 修改打印任务
     */
    Boolean updateByBo(PrintTaskBo bo);

    /**
     * 校验并批量删除打印任务信息
     */
    Boolean deleteWithValidByIds(List<String> ids, Boolean isValid);

    /**
     * 执行打印任务
     */
    Boolean executePrintTask(String id);
} 
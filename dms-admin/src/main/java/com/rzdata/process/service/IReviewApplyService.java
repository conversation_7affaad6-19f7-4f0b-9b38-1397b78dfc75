package com.rzdata.process.service;

import com.blueland.bpmclient.model.ProcessInstanceModel;
import com.rzdata.process.domain.ReviewApply;
import com.rzdata.process.domain.vo.ReviewApplyVo;
import com.rzdata.process.domain.bo.ReviewApplyBo;
import com.rzdata.framework.core.mybatisplus.core.IServicePlus;
import com.rzdata.framework.core.page.TableDataInfo;

import java.util.Collection;
import java.util.HashMap;
import java.util.List;

/**
 * 文件复审申请Service接口
 *
 * <AUTHOR>
 * @date 2021-12-30
 */
public interface IReviewApplyService extends IServicePlus<ReviewApply, ReviewApplyVo> {
	/**
	 * 查询单个
	 * @return
	 */
	ReviewApplyVo queryById(String id);

	ReviewApplyVo queryByBpmnId(String bpmnId);

	/**
	 * 查询列表
	 */
    TableDataInfo<ReviewApplyVo> queryPageList(ReviewApplyBo bo);

	/**
	 * 查询列表
	 */
	List<ReviewApplyVo> queryList(ReviewApplyBo bo);

	/**
	 * 根据新增业务对象插入文件复审申请
	 * @param bo 文件复审申请新增业务对象
	 * @return
	 */
	ProcessInstanceModel insertByBo(ReviewApplyBo bo);

	/**
	 * 根据编辑业务对象修改文件复审申请
	 * @param bo 文件复审申请编辑业务对象
	 * @return
	 */
	Boolean updateByBo(ReviewApplyBo bo);

	/**
	 * 校验并删除数据
	 * @param ids 主键集合
	 * @param isValid 是否校验,true-删除前校验,false-不校验
	 * @return
	 */
	Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);

	/**
	 *  查出未结束的复审申请
	 *  定时任务专用
	 * @return 复审申请列表
	 */
	List<ReviewApplyVo> selectUndueList(List<String> statusList);


	/**
	 * 查询待复审的文件，即时发送消息提醒（剩余多少天复审）
	 *
	 * @param days
	 * @return
	 */
	List<HashMap> selectUnReviewList(int days, String fileType);

	ReviewApplyVo selectDocStatus(String versionId);
}

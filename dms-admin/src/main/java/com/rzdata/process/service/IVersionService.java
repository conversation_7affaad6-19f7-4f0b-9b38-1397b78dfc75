package com.rzdata.process.service;

import com.rzdata.framework.core.domain.model.LoginUser;
import com.rzdata.framework.core.mybatisplus.core.IServicePlus;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.process.domain.DocLinkLog;
import com.rzdata.process.domain.ModifyApplyLink;
import com.rzdata.process.domain.Standard;
import com.rzdata.process.domain.Version;
import com.rzdata.process.domain.bo.StandardBo;
import com.rzdata.process.domain.bo.VersionBo;
import com.rzdata.process.domain.dto.VersionDTO;
import com.rzdata.process.domain.vo.VersionExportVo;
import com.rzdata.process.domain.vo.VersionVo;

import javax.servlet.http.HttpServletResponse;
import java.util.Collection;
import java.util.List;

/**
 * 文件版本记录Service接口
 *
 * <AUTHOR>
 * @date 2021-12-30
 */
public interface IVersionService extends IServicePlus<Version, VersionVo> {
	/**
	 * 查询单个
	 * @return
	 */
	VersionVo queryById(String id);

	/**
	 * 查询有效的版本
	 * @return
	 */
	Version queryOneByStandardId(String id);

	VersionVo queryByDocIdAndVersion(String docId, String versionValue);

	/**
	 * 查询列表
	 */
    TableDataInfo<VersionVo> queryPageList(VersionBo bo);

	TableDataInfo<VersionVo> queryPageOtherDeptList(VersionBo bo);


	List<VersionVo> queryVersionPartList(VersionBo bo);

	/**
	 * 查询版本中，文件分类+物料编码+物料描述是否唯一
	 * @param bo
	 * @return
	 */
	Boolean validateUniqueness(VersionBo bo);

	/**
	 * 查询列表
	 */
	List<VersionVo> queryList(VersionBo bo);

	/**
	 * 根据新增业务对象插入文件版本记录
	 * @param bo 文件版本记录新增业务对象
	 * @return
	 */
	Boolean insertByBo(VersionBo bo);

	/**
	 * 根据编辑业务对象修改文件版本记录
	 * @param bo 文件版本记录编辑业务对象
	 * @return
	 */
	Boolean updateByBo(VersionBo bo);

	/**
	 * 校验并删除数据
	 * @param ids 主键集合
	 * @param isValid 是否校验,true-删除前校验,false-不校验
	 * @return
	 */
	Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);

	List<VersionVo> selectVersionListByAppId(String applyId);

	List<VersionVo> selectVersionList(List<String> applyId);

	List<VersionVo> selectVersionListByDocId(String docId);

	List<VersionVo> selectVersionListByDocIdAndVersionId(String docId, String versionId);

	TableDataInfo<VersionVo> selectRecordFileCompany(VersionBo bo);

	TableDataInfo<VersionVo> selectRecordFileCompanyOtherDept(VersionBo bo);

	boolean docIdExist(String docId);

	boolean isbom(String id);

	boolean checkAuthByDis(String versionId,String userName,String deptId);

	/**
	 * 查询版本和标准文件的集合
	 * @param bo
	 * @return
	 */
	List<VersionVo> versionAndStandardList(StandardBo bo);

	String getDocNameByVersionId(String versionId);

	Version getValidVersionByDocId(String docId);

	List<Version> selectRecordVersionList(String versionId);

	List<VersionExportVo> transformData(List<VersionExportVo> listVo);

	/**
	 * 导出失效公司文件
	 * @param bo
	 * @param response
	 * @param exportName		导出名称
	 * @param invokerQueryList	来源查询类型
	 */
	void exportForList(VersionBo bo, HttpServletResponse response, String exportName, String invokerQueryList);

	Version saveCopyByLink(Standard standard, Version version, ModifyApplyLink applyLink);

	TableDataInfo<VersionDTO> pageApi(VersionBo bo,String limitDocClass);

	TableDataInfo<VersionVo> queryPageListAll(VersionBo bo);

	void exportListAll(VersionBo bo, HttpServletResponse response, String exportName);

	void updateVersionData(Version version, List<DocLinkLog> linkList,String docClass) throws Exception;

	void refreshSignature(Version version,String docClass,String bizType) throws Exception;

	void checkPerms(VersionVo versionVo, boolean isQa, LoginUser user);
}

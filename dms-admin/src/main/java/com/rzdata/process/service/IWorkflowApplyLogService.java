package com.rzdata.process.service;

import com.rzdata.eventbus.ProcessResultEvent;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.core.mybatisplus.core.IServicePlus;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.process.domain.WorkflowApplyLog;
import com.rzdata.process.domain.bo.WorkflowApplyLogBo;
import com.rzdata.process.domain.vo.WorkflowApplyLogVo;

import java.util.Collection;
import java.util.List;

/**
 * 流程申请记录Service接口
 *
 * <AUTHOR>
 * @date 2022-01-05
 */
public interface IWorkflowApplyLogService extends IServicePlus<WorkflowApplyLog, WorkflowApplyLogVo> {
	/**
	 * 查询单个
	 * @return
	 */
	WorkflowApplyLogVo queryById(String id);

	/**
	 * 查询列表
	 */
    TableDataInfo<WorkflowApplyLogVo> queryPageList(WorkflowApplyLogBo bo);

	/**
	 * 查询列表
	 */
	List<WorkflowApplyLogVo> queryList(WorkflowApplyLogBo bo);


	/**
	 * 根据新增业务对象插入流程申请记录
	 * @param bo 流程申请记录新增业务对象
	 * @return
	 */
	Boolean insertByBo(WorkflowApplyLogBo bo);

	/**
	 * 根据编辑业务对象修改流程申请记录
	 * @param bo 流程申请记录编辑业务对象
	 * @return
	 */
	Boolean updateByBo(WorkflowApplyLogBo bo);

	/**
	 * 校验并删除数据
	 * @param ids 主键集合
	 * @param isValid 是否校验,true-删除前校验,false-不校验
	 * @return
	 */
	Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);

	AjaxResult<String> selectStatus(String versionId,boolean inDraft);

	AjaxResult<String> selectStatusByDocId(String versionId,boolean inDraft);

	AjaxResult<String> selectStatusRecord(String versionId);

    String queryProcessStatus(String docId);


	/**
	 * 查询单个
	 * @return
	 */
	WorkflowApplyLog queryByProcInstId(String procInstId);

    String selectApplySerial(String applyClass);

	/**
	 * 根据业务id更新数据状态
	 */
	void updateStatusByBusId(WorkflowApplyLog waLog, ProcessResultEvent event);
}

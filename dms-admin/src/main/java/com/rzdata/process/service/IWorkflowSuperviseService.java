package com.rzdata.process.service;

import com.rzdata.framework.core.mybatisplus.core.IServicePlus;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.process.domain.WorkflowSupervise;
import com.rzdata.process.domain.bo.UniteworkTaskBo;
import com.rzdata.process.domain.bo.WorkflowSuperviseBo;
import com.rzdata.process.domain.vo.UniteworkTaskVo;
import com.rzdata.process.domain.vo.WorkflowSuperviseVo;

import java.util.Collection;
import java.util.List;

/**
 * 流程督办Service接口
 *
 * <AUTHOR>
 * @date 2024-11-14
 */
public interface IWorkflowSuperviseService extends IServicePlus<WorkflowSupervise, WorkflowSuperviseVo> {

    WorkflowSuperviseVo queryById(String id);

    TableDataInfo<WorkflowSuperviseVo> queryPageList(WorkflowSuperviseBo bo);

    void sync();

    Boolean insertByBo(WorkflowSuperviseBo bo);

    Boolean updateByBo(WorkflowSuperviseBo bo);

    Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);

    List<UniteworkTaskVo> queryUniteworkTaskList(UniteworkTaskBo bo);
}

package com.rzdata.process.service;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Maps;
import com.rzdata.process.domain.WorkflowSupervise;
import com.rzdata.process.domain.bo.UniteworkTaskBo;
import com.rzdata.process.domain.vo.UniteworkTaskVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class SuperviseWorkflowEntranceService {

    @Autowired
    private SuperviseWorkflowHandleService superviseWorkflowHandleService;

    @Autowired
    private IWorkflowSuperviseService workflowSuperviseService;

    @Autowired
    private ThreadPoolTaskExecutor executor;

    /**
     * @description 开始执行流程督办
     * <AUTHOR>
     * @param superviseTimeValue superviseTimeValue
     * @updateTime 2024/11/14
     */
    public void launchSuperviseWorkflow(String superviseTimeValue) {
        try {
            // 参数校验和转换
            int offsetTime = 0;
            if (StrUtil.isEmpty(superviseTimeValue)) {
                return;
            }
            try {
                offsetTime = Integer.parseInt(superviseTimeValue);
            } catch (NumberFormatException e) {
                log.error("时间偏移值格式错误: {}", superviseTimeValue, e);
                return;
            }
            // 获取当前时间往前推offsetTime小时的时间
            DateTime nowDateTimeOffset = DateUtil.offsetHour(DateUtil.date(), -offsetTime);
            String endTime = DateUtil.format(nowDateTimeOffset, "yyyy-MM-dd HH:mm:ss");
            Map<String, Object> params = Maps.newHashMap();
            params.put("endTime", endTime);
            log.info("开始执行流程督办任务, 时间偏移: {}小时", offsetTime);
            // 查询开启了流程督办的流程定义列表
            LambdaQueryWrapper<WorkflowSupervise> lqw = Wrappers.lambdaQuery();
            lqw.eq(WorkflowSupervise::getSupervise, "0");
            List<WorkflowSupervise> workflowSuperviseList = workflowSuperviseService.list(lqw);

            executor.execute(() -> {
                try {
                    log.debug("查询到{}个需要督办的流程", workflowSuperviseList.size());

                    // 遍历查询每个流程定义下的待办
                    for (WorkflowSupervise workflowSupervise : workflowSuperviseList) {
                        UniteworkTaskBo uniteworkTaskBo = new UniteworkTaskBo();
                        uniteworkTaskBo.setStatus("1");
                        uniteworkTaskBo.setProcDefKey(workflowSupervise.getProcDefKey());
                        uniteworkTaskBo.setParams(params);

                        System.out.println("getProcDefKey===========" + workflowSupervise.getProcDefKey());

                        if (workflowSupervise.getProcDefKey().equals("Process_OK187LWD")) {
                            System.out.println("test===========" + workflowSupervise.getProcDefKey());
                        }
                        // 查询已超时的待办集合
                        List<UniteworkTaskVo> uniteworkTaskList = workflowSuperviseService
                                .queryUniteworkTaskList(uniteworkTaskBo);
                        if (uniteworkTaskList == null || uniteworkTaskList.isEmpty()) {
                            log.debug("流程[{}]没有需要督办的任务", workflowSupervise.getProcDefKey());
                            continue;
                        }

                        log.info("开始处理流程[{}]的{}个超时任务", workflowSupervise.getProcDefKey(), uniteworkTaskList.size());
                        superviseWorkflowHandleService.superviseAsyncHandle(uniteworkTaskList);

                        // 休息一秒后继续执行
                        try {
                            Thread.sleep(1000);
                        } catch (InterruptedException e) {
                            log.error("线程休眠被中断", e);
                        }
                    }
                } catch (Exception e) {
                    log.error("流程督办任务执行异常", e);
                }
            });
        } catch (Exception e) {
            log.error("启动流程督办任务失败", e);
        }
    }

}

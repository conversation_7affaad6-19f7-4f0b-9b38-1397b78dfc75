package com.rzdata.process.service;

import cn.hutool.core.util.StrUtil;
import com.rzdata.config.CustomConfig;
import com.rzdata.config.SuperviseStrategyConfig;
import com.rzdata.framework.constant.DocMsgConstants;
import com.rzdata.framework.core.domain.entity.SysUser;
import com.rzdata.framework.core.domain.model.LoginUser;
import com.rzdata.framework.utils.SecurityUtils;
import com.rzdata.oa.config.OaProperties;
import com.rzdata.process.domain.DocMessage;
import com.rzdata.process.domain.vo.UniteworkTaskVo;
import com.rzdata.process.enums.DocMessageEnum;
import com.rzdata.process.enums.MsgTypeEnum;
import com.rzdata.process.enums.SendType;
import com.rzdata.process.utils.MsgTemplateUtils;
import com.rzdata.system.service.ISysNotifyLogService;
import com.rzdata.system.service.ISysUserService;
import com.rzdata.system.service.WorkflowService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class SuperviseWorkflowHandleService {

    @Autowired
    private IDocMessageService docMessageService;

    @Autowired
    private ISysNotifyLogService sysNotifyLogService;

    @Autowired
    private ISysUserService sysUserService;

    @Autowired
    private SuperviseStrategyConfig superviseStrategyConfig;

    @Autowired
    OaProperties oaProperties;

    @Autowired
    WorkflowService workflowService;

    @Autowired
    CustomConfig customConfig;

    /**
     * @description 流程督办
     * <AUTHOR>
     * @param uniteworkTaskList uniteworkTaskList
     * @updateTime 2024/11/14
     */
    public void superviseAsyncHandle(List<UniteworkTaskVo> uniteworkTaskList) {
        for (UniteworkTaskVo uniteworkTaskVo : uniteworkTaskList) {
            executeSupervise(uniteworkTaskVo);
        }
    }

    /**
     * @description 执行流程督办
     * <AUTHOR>
     * @param uniteworkTaskVo uniteworkTaskVo
     * @updateTime 2024/11/14
     */
    public void executeSupervise(UniteworkTaskVo uniteworkTaskVo) {
        try { 
            String url = uniteworkTaskVo.getUrl();
            String pcUrl = url.replace(customConfig.getWorkflowUniteWorkUrl(), customConfig.getFrontUrl() + "workflow")+ "&status=" + workflowService.getFlowStatus(uniteworkTaskVo.getProcInstId(), uniteworkTaskVo.getActInstId())
                    + "&invokerForm=systemMsg";
            // 提取type参数
            String type = MsgTemplateUtils.getParameterValue(url, "type");
            String mobileAfterUrl = "/pages/workflowList/addWorkflow/"+type;
            String mobileUrl = url.replace(customConfig.getWorkflowUniteWorkUrl(), mobileAfterUrl)+ "&status=" + workflowService.getFlowStatus(uniteworkTaskVo.getProcInstId(), uniteworkTaskVo.getActInstId())
                    + "&invokerForm=systemMsg";
            // 发送系统站内消息
            docMessageService.sendInstationMessage(null, uniteworkTaskVo.getDocId(), null, null, uniteworkTaskVo.getVersionId(), uniteworkTaskVo.getVersionValue(),
                    uniteworkTaskVo.getDeptId(), null, uniteworkTaskVo.getRecUserId(), uniteworkTaskVo.getRecUserId(),
                    String.format(DocMessageEnum.PROCESS_SUPERVISE.getMsg(), uniteworkTaskVo.getTitle()),
                    DocMsgConstants.MSG_TYPE_MSG, MsgTypeEnum.PROC_SUPERVISE.getType(), pcUrl, mobileUrl, null);
        } catch (Exception e) {
            log.error("执行流程督办-发送系统站内消息异常，流程标题：{}", uniteworkTaskVo.getTitle(), e);
        }

        try {
            if (superviseStrategyConfig.isSendEamil()) {
                // 发送催办邮件
                sendSuperviseEmail(uniteworkTaskVo);
            }
        } catch (Exception e) {
            log.error("执行流程督办-发送催办邮件异常，流程标题：{}", uniteworkTaskVo.getTitle(), e);
        }
    }

    /**
     * @description 发送流程督办站内消息
     * <AUTHOR>
     * @param uniteworkTaskVo uniteworkTaskVo
     * @updateTime 2024/11/14
     */
    private void sendInstationMessage(UniteworkTaskVo uniteworkTaskVo) {
        // 当前登录用户
        LoginUser currUser = null;
        try {
            currUser = SecurityUtils.getLoginUser();
        } catch (Exception e) {
        }

        DocMessage docMessage = new DocMessage();
        docMessage.setVersionValue("default");
        // 消息状态 0=未读 1=已读
        docMessage.setMsgStatus(0);
        docMessage.setCreateTime(new Date());
        // 创建人
        docMessage.setCreateUserId(currUser != null ? currUser.getUserId() : "");
        docMessage.setCreateUser(currUser != null ? currUser.getUsername() : "");
        docMessage.setMsgClass(MsgTypeEnum.PROC_SUPERVISE.getType());
        docMessage.setMsgInfo(String.format(DocMessageEnum.PROCESS_SUPERVISE.getMsg(), uniteworkTaskVo.getTitle()));
        // 接收人
        docMessage.setRecoveryUser(uniteworkTaskVo.getRecUserId());

        docMessageService.save(docMessage);
    }

    /**
     * @description 发送流程催办邮件提醒
     * <AUTHOR>
     * @param uniteworkTaskVo uniteworkTaskVo
     * @updateTime 2024/11/14
     */
    public void sendSuperviseEmail(UniteworkTaskVo uniteworkTaskVo) {
        String recUserId = uniteworkTaskVo.getRecUserId();
        SysUser sysUser = sysUserService.selectUserByUserName(recUserId);
        boolean sendEmail = true;
        if (StrUtil.isNotEmpty(superviseStrategyConfig.getSendEmailTestUserName()) &&
                !superviseStrategyConfig.getSendEmailTestUserName().equals(sysUser.getUserName())) {
            sendEmail = false;
        }
        if (sendEmail && null != sysUser) {
            Map<String, String> param = new HashMap<>();
            param.put("title", uniteworkTaskVo.getTitle());
            sysNotifyLogService.notAsyncSendEmail(SendType.SUPERVISE.getCode(), sysUser.getNickName(),
                    sysUser.getEmail(), sysUser.getUserId(), param);
        }
    }
}

package com.rzdata.process.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.IdUtil;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.core.domain.entity.SysUser;
import com.rzdata.framework.core.redis.RedisCache;
import com.rzdata.framework.utils.PageUtils;
import com.rzdata.framework.core.page.PagePlus;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.utils.StringUtils;
import com.rzdata.process.domain.BasicFileEditingDetailLog;
import com.rzdata.process.domain.bo.BasicFileEditingDetailLogBo;
import com.rzdata.process.domain.vo.BasicFileEditingDetailLogVo;
import com.rzdata.process.mapper.BasicFileEditingDetailLogMapper;
import com.rzdata.process.service.IBasicFileEditingDetailLogService;
import com.rzdata.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.rzdata.framework.core.mybatisplus.core.ServicePlusImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 编辑明细日志Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-30
 */
@Service
public class BasicFileEditingDetailLogServiceImpl extends ServicePlusImpl<BasicFileEditingDetailLogMapper, BasicFileEditingDetailLog, BasicFileEditingDetailLogVo> implements IBasicFileEditingDetailLogService {

    private static final String FILE_EDIT_LOCK_KEY = "file:edit:lock:";
    private static final int LOCK_EXPIRE_TIME = 10; // 锁过期时间（秒）
    @Resource
    private RedisCache redisCache;

    @Autowired
    ISysUserService iSysUserService;

    @Resource
    private BasicFileEditingDetailLogMapper basicFileEditingDetailLogMapper;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult checkFileEditStatus(BasicFileEditingDetailLogBo bo, String userName) {
        Map<String,Object> result = new HashMap<String,Object>();
        // 1. 检查Redis锁
        String lockKey = FILE_EDIT_LOCK_KEY + bo.getProtoFileId();
        String currentLock = redisCache.getCacheObject(lockKey);
        
        if (currentLock != null && !currentLock.equals(userName)) {
            SysUser sysUser = iSysUserService.selectUserByUserName(userName);
            result.put("nickName",sysUser.getNickName());
            result.put("userName",userName);
            // 文件正在被其他人编辑
            return AjaxResult.error("文件正在被"+sysUser.getNickName()+"编辑",result);
        }

        // 2. 检查数据库中的编辑状态
        BasicFileEditingDetailLog lastEditLog = basicFileEditingDetailLogMapper.selectLastEditLog(bo.getProtoFileId());
        if (lastEditLog != null && "E".equals(lastEditLog.getStatus()) && !lastEditLog.getCreateBy().equals(userName)) {
            SysUser sysUser = iSysUserService.selectUserByUserName(userName);
            result.put("nickName",sysUser.getNickName());
            result.put("userName",userName);
            return AjaxResult.error("文件正在被"+sysUser.getNickName()+"编辑",result);
        }

        // 3. 设置Redis锁
        redisCache.setCacheObject(lockKey, userName, LOCK_EXPIRE_TIME, TimeUnit.SECONDS);

        // 4. 记录编辑日志
        bo.setId(IdUtil.simpleUUID());
        bo.setCreateTime(new Date());
        bo.setCreateBy(userName);
        bo.setStatus("E"); // 1表示正在编辑
        this.insertByBo(bo);
        return AjaxResult.success();
    }

    @Override
    public void releaseFileEditLock(String protoFileId, String userId) {
        // 1. 释放Redis锁
        String lockKey = FILE_EDIT_LOCK_KEY + protoFileId;
        redisCache.deleteObject(lockKey);

        BasicFileEditingDetailLogBo bo = new BasicFileEditingDetailLogBo();
        // 2. 更新编辑日志状态
        bo.setUpdateBy(userId);
        bo.setUpdateTime(new Date());
        bo.setStatus("Y");
        bo.setProtoFileId(protoFileId);
        basicFileEditingDetailLogMapper.releaseFileEditLock(bo);
    }
    
    @Override
    public BasicFileEditingDetailLogVo queryById(String id){
        return getVoById(id);
    }

    @Override
    public TableDataInfo<BasicFileEditingDetailLogVo> queryPageList(BasicFileEditingDetailLogBo bo) {
        PagePlus<BasicFileEditingDetailLog, BasicFileEditingDetailLogVo> result = pageVo(PageUtils.buildPagePlus(), buildQueryWrapper(bo));
        return PageUtils.buildDataInfo(result);
    }

    @Override
    public List<BasicFileEditingDetailLogVo> queryList(BasicFileEditingDetailLogBo bo) {
        return listVo(buildQueryWrapper(bo));
    }

    private LambdaQueryWrapper<BasicFileEditingDetailLog> buildQueryWrapper(BasicFileEditingDetailLogBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<BasicFileEditingDetailLog> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getFileId()), BasicFileEditingDetailLog::getFileId, bo.getFileId());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), BasicFileEditingDetailLog::getStatus, bo.getStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getBizId()), BasicFileEditingDetailLog::getBizId, bo.getBizId());
        return lqw;
    }

    @Override
    public Boolean insertByBo(BasicFileEditingDetailLogBo bo) {
        BasicFileEditingDetailLog add = BeanUtil.toBean(bo, BasicFileEditingDetailLog.class);
        validEntityBeforeSave(add);
        boolean flag = save(add);
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    @Override
    public Boolean updateByBo(BasicFileEditingDetailLogBo bo) {
        BasicFileEditingDetailLog update = BeanUtil.toBean(bo, BasicFileEditingDetailLog.class);
        validEntityBeforeSave(update);
        return updateById(update);
    }

    /**
     * 保存前的数据校验
     *
     * @param entity 实体类数据
     */
    private void validEntityBeforeSave(BasicFileEditingDetailLog entity){
        //TODO 做一些数据校验,如唯一约束
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return removeByIds(ids);
    }
}

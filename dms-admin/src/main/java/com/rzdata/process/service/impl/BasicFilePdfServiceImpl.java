package com.rzdata.process.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.rzdata.asas7.service.FileUploadDownloadService;
import com.rzdata.fileencry.IFileEncryDecryService;
import com.rzdata.framework.constant.CommonI18nConstant;
import com.rzdata.framework.constant.Constants;
import com.rzdata.framework.constant.DocConstants;
import com.rzdata.framework.core.mybatisplus.core.ServicePlusImpl;
import com.rzdata.framework.core.page.PagePlus;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.core.redis.RedisCache;
import com.rzdata.framework.utils.I18nUtils;
import com.rzdata.framework.utils.PageUtils;
import com.rzdata.framework.utils.SecurityUtils;
import com.rzdata.framework.utils.StringUtils;
import com.rzdata.framework.utils.file.FileUtils;
import com.rzdata.framework.utils.spring.SpringUtils;
import com.rzdata.process.domain.BasicFile;
import com.rzdata.process.domain.BasicFilePdf;
import com.rzdata.process.domain.PrintTask;
import com.rzdata.process.domain.bo.BasicFileBo;
import com.rzdata.process.domain.bo.BasicFilePdfBo;
import com.rzdata.process.domain.vo.BasicFilePdfVo;
import com.rzdata.process.domain.vo.BasicFileVo;
import com.rzdata.process.domain.vo.DocLinkLogVo;
import com.rzdata.process.enums.PdfTypeEnum;
import com.rzdata.process.enums.YNEnum;
import com.rzdata.process.mapper.BasicFilePdfMapper;
import com.rzdata.process.mapper.DocLinkLogMapper;
import com.rzdata.process.service.*;
import com.rzdata.process.utils.DateUtils;
import com.rzdata.setting.domain.bo.WatermarkParamBo;
import com.rzdata.system.service.ISysConfigService;
import io.contentBusAPI.docAccess.client.model.FileOsbeginuploadRes;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StreamUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.rmi.ServerException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 附件转PDFService业务层处理
 *
 * <AUTHOR>
 * @date 2023-07-31
 */
@Slf4j
@Service
public class BasicFilePdfServiceImpl extends ServicePlusImpl<BasicFilePdfMapper, BasicFilePdf, BasicFilePdfVo> implements IBasicFilePdfService {
    @Autowired
    private IBasicFileService iBasicFileService;

    @Resource
    private DocLinkLogMapper docLinkLogMapper;

    @Autowired
    private ISysConfigService configService;

    @Autowired
    private FileUploadDownloadService fileUploadService;

    @Autowired
    private IFileEncryDecryService iFileEncryDecryService;

    @Autowired
    private IStoreFileService iStoreFileService;

    @Autowired
    private IStandardService iStandardService;

    @Autowired
    private IPrintTaskService printTaskService;

    @Override
    public BasicFilePdfVo queryById(String id){
        return getVoById(id);
    }

    @Override
    public TableDataInfo<BasicFilePdfVo> queryPageList(BasicFilePdfBo bo) {
        PagePlus<BasicFilePdf, BasicFilePdfVo> result = pageVo(PageUtils.buildPagePlus(), buildQueryWrapper(bo));
        TableDataInfo<BasicFilePdfVo> table = PageUtils.buildDataInfo(result);
        if (table.getRows().size()>0) {
            List<BasicFilePdfVo> basicFilePdfVoList = table.getRows();
            List<String> fileIdList = basicFilePdfVoList.stream()
                    .map(BasicFilePdfVo::getFileId)
                    .collect(Collectors.toList());
            List<DocLinkLogVo> docLinkLogVos = docLinkLogMapper.queryByfileIdsDocLinkVo(fileIdList);

            List<String> bizIdList = basicFilePdfVoList.stream()
                    .map(BasicFilePdfVo::getBizId)
                    .collect(Collectors.toList());
            bizIdList=bizIdList.stream().distinct().collect(Collectors.toList());
            List<PrintTask> printTaskList = printTaskService.list(new LambdaQueryWrapper<PrintTask>().in(PrintTask::getDocDistributeId, bizIdList)
                    .eq(PrintTask::getStatus, Constants.PATROL_TASK_STATUS_COMPLETED));
            Map<String, List<PrintTask>> collect = printTaskList.stream().collect(Collectors.groupingBy(v -> v.getFileId() + "#" + v.getDocDistributeId()));

            for(BasicFilePdfVo item : table.getRows()) {
                item.setPrintStatus(Constants.VALUE_N);
                // 设置源文件对象
                item.setFileObj(iBasicFileService.getVoById(item.getFileId()));
                if(CollUtil.isNotEmpty(docLinkLogVos)){
                    for (DocLinkLogVo docLinkLogVo : docLinkLogVos) {
                        if(item.getFileId().equals(docLinkLogVo.getFileId())){
                            item.setDocLinkLogVo(docLinkLogVo);
                            break;
                        }
                    }
                }

                if(StringUtils.isNotEmpty(collect.get(item.getFileId()+ "#" + item.getBizId()))){
                    item.setPrintStatus(Constants.VALUE_Y);
                }
            }
        }else {
            if (StringUtils.isNotEmpty(bo.getFileId())) {
                // 没有数据就将
                BasicFileVo vo = iBasicFileService.getVoById(bo.getFileId());
                if (vo!=null) {
                    BasicFilePdfVo item= new BasicFilePdfVo();
                    item.setPdfId(bo.getFileId());
                    item.setPdfType(PdfTypeEnum.BASICFILE.getCode());
                    item.setFileObj(vo);
                    item.setPrintStatus(Constants.VALUE_N);
                    table.getRows().add(item);
                }
            }
        }
        return table;
    }

    @Override
    public List<BasicFilePdfVo> queryList(BasicFilePdfBo bo) {
        return listVo(buildQueryWrapper(bo));
    }

    private LambdaQueryWrapper<BasicFilePdf> buildQueryWrapper(BasicFilePdfBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<BasicFilePdf> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getBizId()), BasicFilePdf::getBizId, bo.getBizId());
        lqw.eq(StringUtils.isNotBlank(bo.getFileId()), BasicFilePdf::getFileId, bo.getFileId());
        lqw.eq(StringUtils.isNotBlank(bo.getPdfId()), BasicFilePdf::getPdfId, bo.getPdfId());
        lqw.eq(StringUtils.isNotBlank(bo.getPdfType()), BasicFilePdf::getPdfType, bo.getPdfType());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), BasicFilePdf::getStatus, bo.getStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getTenantId()), BasicFilePdf::getTenantId, bo.getTenantId());
        lqw.in(ObjectUtil.isNotEmpty(bo.getFileIdList()), BasicFilePdf::getFileId, bo.getFileIdList());
        return lqw;
    }

    @Override
    public Boolean insertByBo(BasicFilePdfBo bo) {
        BasicFilePdf add = BeanUtil.toBean(bo, BasicFilePdf.class);
        validEntityBeforeSave(add);
        boolean flag = save(add);
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    @Override
    public Boolean updateByBo(BasicFilePdfBo bo) {
        BasicFilePdf update = BeanUtil.toBean(bo, BasicFilePdf.class);
        validEntityBeforeSave(update);
        return updateById(update);
    }

    /**
     * 保存前的数据校验
     *
     * @param entity 实体类数据
     */
    private void validEntityBeforeSave(BasicFilePdf entity){
        //TODO 做一些数据校验,如唯一约束
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return removeByIds(ids);
    }

    @Override
    public BasicFile fileToPdf(BasicFile basicFile) throws Exception {
        BasicFile pdfBasicFile= iBasicFileService.createPdfFile(basicFile);
        if(pdfBasicFile==null){
            throw new ServerException(I18nUtils.getTitle(CommonI18nConstant.FILE_PDF_ERROR));
        }
        BasicFilePdf basicFilePdf= new BasicFilePdf();
        basicFilePdf.setFileId(basicFile.getId());
        basicFilePdf.setPdfId(pdfBasicFile.getId());
        basicFilePdf.setPdfType(PdfTypeEnum.TRANSITION.getCode());
        basicFilePdf.setStatus(YNEnum.YES.name());
        basicFilePdf.setCreateTime(DateUtil.date());
        this.save(basicFilePdf);
        return pdfBasicFile;
    }

    @Override
    public BasicFile fileToPdfChoose(BasicFile basicFile) throws Exception {
        BasicFilePdf mergeFilePdf = this.getPdfObj(null,basicFile.getId(),PdfTypeEnum.MERGE.getCode());
        if(mergeFilePdf !=null){
            return iBasicFileService.getById(mergeFilePdf.getPdfId());
        }
        BasicFilePdf filePdf = this.getPdfObj(null,basicFile.getId(),PdfTypeEnum.TRANSITION.getCode());
        if(filePdf!=null){
            return iBasicFileService.getById(filePdf.getPdfId());
        }
        if(Constants.FILE_TYPE_PDF.equals(basicFile.getFileType())) {
            return basicFile;
        }else{
            return fileToPdf(basicFile);
        }
    }

    @Override
    public String fileToPdfSignature(String docClass, String fileId, String type, WatermarkParamBo param) throws Exception {
        BasicFile basicFile = iBasicFileService.getById(fileId);
        BasicFile pdfBasicFile= this.fileToPdfChoose(basicFile);
        String signPdfId = iBasicFileService.createEncryptFileId(docClass,pdfBasicFile,type,param);
        if(signPdfId == null){
            throw new ServerException(I18nUtils.getTitle(CommonI18nConstant.FILE_FAIL_GENERATE_CHAPTER_PDF));
        }
        BasicFilePdf filePdf = this.getPdfObj(null,fileId,PdfTypeEnum.SIGNATURE.getCode());
        if(filePdf!=null){
            filePdf.setPdfId(signPdfId);
            filePdf.setUpdateTime(DateUtil.date());
            this.updateById(filePdf);
        }else{
            BasicFilePdf basicFilePdf= new BasicFilePdf();
            basicFilePdf.setFileId(fileId);
            basicFilePdf.setPdfId(signPdfId);
            basicFilePdf.setPdfType(PdfTypeEnum.SIGNATURE.getCode());
            basicFilePdf.setStatus(YNEnum.YES.name());
            basicFilePdf.setCreateTime(DateUtil.date());
            this.save(basicFilePdf);
        }
        return signPdfId;
    }

    /**
     * file转pdf分发文件
     *
     * @param fileId
     * @return
     */
    @Override
    public String fileToPdfDistribute(String bizId,String docClass,String fileId,String type,WatermarkParamBo param) throws Exception {
        BasicFile basicFile = iBasicFileService.getById(fileId);
        BasicFile pdfBasicFile= this.fileToPdfChoose(basicFile);
        String signPdfId = iBasicFileService.createEncryptFileId(docClass,pdfBasicFile,type,param);
        if(signPdfId == null){
            throw new ServerException(I18nUtils.getTitle(CommonI18nConstant.FILE_FAIL_GENERATE_DISTRIBUTION_PDF));
        }
        BasicFilePdf filePdf = this.getPdfObj(bizId,fileId,PdfTypeEnum.DISTRIBUTE.getCode());
        if(filePdf!=null){
            // 更新
            filePdf.setPdfId(signPdfId);
            filePdf.setUpdateTime(DateUtil.date());
            this.updateById(filePdf);
        }else{
            // 新增
            BasicFilePdf basicFilePdf= new BasicFilePdf();
            basicFilePdf.setBizId(bizId);
            basicFilePdf.setFileId(fileId);
            basicFilePdf.setPdfId(signPdfId);
            basicFilePdf.setPdfType(PdfTypeEnum.DISTRIBUTE.getCode());
            basicFilePdf.setStatus(YNEnum.YES.name());
            basicFilePdf.setCreateTime(DateUtil.date());
            this.save(basicFilePdf);
        }
        return signPdfId;
    }

    @Override
    public String getPdfId(String fileId, String type) {
        BasicFilePdf filePdf = getPdfObj(null,fileId,type);
        return filePdf==null ? null : filePdf.getPdfId();
    }
    /**
     * 根据源文件ID、类型获取
     * @param fileId
     * @param type
     * @return
     */
    private BasicFilePdf getPdfObj(String bizId,String fileId, String type){
        LambdaQueryWrapper<BasicFilePdf> lwq= new LambdaQueryWrapper<>();
        lwq.eq(StringUtils.isNotBlank(bizId), BasicFilePdf::getBizId, bizId);
        lwq.eq(BasicFilePdf::getFileId,fileId);
        lwq.eq(BasicFilePdf::getPdfType, type);
        lwq.eq(BasicFilePdf::getStatus, "YES");
        return this.getBaseMapper().selectList(lwq).stream().findFirst().orElse(null);
    }

    /**
     * 根据源文件ID获取
     *
     * @param fileId
     * @return
     */
    private List<BasicFilePdf> getPdfList(String fileId){
        LambdaQueryWrapper<BasicFilePdf> lwq= new LambdaQueryWrapper<>();
        lwq.eq(BasicFilePdf::getFileId,fileId);
        lwq.eq(BasicFilePdf::getStatus, "Y");
        return this.getBaseMapper().selectList(lwq);
    }

    @Override
    public void pdfBasicFileSave(String fileId) {
        BasicFilePdf basicFilePdf= new BasicFilePdf();
        basicFilePdf.setFileId(fileId);
        basicFilePdf.setPdfId(fileId);
        basicFilePdf.setPdfType(PdfTypeEnum.BASICFILE.getCode());
        basicFilePdf.setStatus(YNEnum.YES.name());
        basicFilePdf.setCreateTime(DateUtil.date());
        this.save(basicFilePdf);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void pdfBasicFileSave(String fileId,String pdfFileId,String type) {
        BasicFilePdf filePdf = this.getPdfObj(null,fileId, type);
        BasicFilePdf basicFilePdf= new BasicFilePdf();
        if(ObjectUtil.isNotEmpty(filePdf)){
            basicFilePdf.setId(filePdf.getId());
        }
        basicFilePdf.setFileId(fileId);
        basicFilePdf.setPdfId(pdfFileId);
        basicFilePdf.setPdfType(type);
        basicFilePdf.setStatus(YNEnum.YES.name());
        basicFilePdf.setCreateTime(DateUtil.date());
        saveOrUpdate(basicFilePdf);
    }


    /**
     * 文档比对
     *
     * @param fileId1 word文件ID1
     * @param fileId2 word文件ID2
     * @return
     */
    @Override
    public String fileCompareToPdf(String fileId1, String fileId2,String businessId) throws ServerException {
        BasicFile bfObj1 = iBasicFileService.getById(fileId1);
        BasicFile bfObj2 = iBasicFileService.getById(fileId2);
        if(!this.isWordFile(bfObj1.getFileType())) {
            throw new ServerException(I18nUtils.getTitle(CommonI18nConstant.FILE_COMPARISON1_NON_WORD)+bfObj1.getFilePath());
        }
        if(!this.isWordFile(bfObj2.getFileType())) {
            throw new ServerException(I18nUtils.getTitle(CommonI18nConstant.FILE_COMPARISON2_NON_WORD)+bfObj1.getFilePath());
        }
        BasicFilePdf filePdf = this.getPdfObj(null,fileId1,PdfTypeEnum.COMPARE.getCode());
        if(filePdf!=null){
            return filePdf.getPdfId();
        }
        String pdfId= this.iBasicFileService.createComparePdfFile(fileId2,fileId1,businessId);
        if(pdfId==null){
            throw new ServerException(I18nUtils.getTitle(CommonI18nConstant.FILE_DOC_COMPARISON_FAILED));
        }
        BasicFilePdf basicFilePdf= new BasicFilePdf();
        basicFilePdf.setFileId(fileId1);
        basicFilePdf.setPdfId(pdfId);
        basicFilePdf.setPdfType(PdfTypeEnum.COMPARE.getCode());
        basicFilePdf.setStatus(YNEnum.YES.name());
        basicFilePdf.setCreateTime(DateUtil.date());
        basicFilePdf.setBizId(businessId);
        this.save(basicFilePdf);
        return pdfId;
    }

    private boolean isWordFile(String fileType) {
        boolean result = false;
        if(fileType.toLowerCase().equals("doc") || fileType.toLowerCase().equals("docx")|| fileType.toLowerCase().equals("xls") || fileType.toLowerCase().equals("xlsx")) {
            result = true;
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BasicFile uploading(MultipartFile file) throws Exception {
        String storeRootPath = iStoreFileService.getStorePath();
        Date data = new Date();
        String fileId = DateUtil.format(data, DateUtils.YYYYMMDDHHMMSSSSS);
        String fileNameOld = file.getOriginalFilename();
        if(StringUtils.contains(fileNameOld,Constants.SLASH)) {
            fileNameOld = fileNameOld.substring(fileNameOld.lastIndexOf(Constants.SLASH) + 1);
        }
        String fileType= fileNameOld.substring(fileNameOld.lastIndexOf(".")+1);

        // 相对路径
        String relativeFilePath = "process"+ File.separator+ fileId + File.separator + fileNameOld;
        // 绝对路径
        String absFilePath = storeRootPath + File.separator + relativeFilePath;

        FileUtil.copyFile(file.getInputStream(),new File(absFilePath));
        // 判断是否加密
        boolean fileEncry = Boolean.parseBoolean(configService.selectConfigByKey(Constants.FILE_ENCRYT));
        if (fileEncry) {
            try {
                // 解密之后的文件流
                iFileEncryDecryService.isSdFileAndDecrypt(absFilePath);
            } catch (Exception e) {
                log.error("解密文件失败",e);
            }
        }
        // 建立附件表记录
        BasicFile basicFile = new BasicFile();
        basicFile.setId(fileId);
        // 判断是否开启对接爱数平台
        boolean asPlatform = Boolean.parseBoolean(configService.selectConfigByKey(Constants.ASAS7_PLATFORM));
        if (asPlatform) {
            // 调用爱数上传附件接口
            try {
                FileOsbeginuploadRes fileOsbeginuploadRes = fileUploadService.singleUpload(absFilePath);
                basicFile.setExternalFilePath(fileOsbeginuploadRes.getName());
                basicFile.setExternalFileId(fileOsbeginuploadRes.getDocid());
                basicFile.setExternalRev(fileOsbeginuploadRes.getRev());
            } catch (Exception e) {
                log.error("uploading上传文件到爱数异常1",e);
                // 爱数令牌超时情况，删除爱数令牌然后再重新上传
                SpringUtils.getBean(RedisCache.class).deleteObject(com.rzdata.asas7.util.Constants.ASAS7_TOKEN);
                try {
                    FileOsbeginuploadRes fileOsbeginuploadRes = fileUploadService.singleUpload(absFilePath);
                    basicFile.setExternalFilePath(fileOsbeginuploadRes.getName());
                    basicFile.setExternalFileId(fileOsbeginuploadRes.getDocid());
                    basicFile.setExternalRev(fileOsbeginuploadRes.getRev());
                } catch (Exception e2) {
                    log.error("uploading上传文件到爱数异常2",e2);
                }
            }
        }
        basicFile.setFileSize(file.getSize());
        basicFile.setFileType(fileType.toLowerCase(Locale.ROOT));
        basicFile.setFileName(fileNameOld);
        basicFile.setFilePath(relativeFilePath);
        basicFile.setCreateTime(data);

        iBasicFileService.save(basicFile);

        if(Constants.FILE_TYPE_PDF.equals(basicFile.getFileType())){
            pdfBasicFileSave(fileId);
        }else {
            // 识别OFFICE文档转PDF文件
            String supportExts = configService.selectConfigByKey(Constants.FILE_TO_PDF_EXT);
            List<String> extList = ListUtil.toList(supportExts.split(","));
            if(extList.contains(basicFile.getFileType())){
                // OFFICE文档转PDF文件
                fileToPdf(basicFile);
            }
        }
        return basicFile;
    }


    @Override
    public BasicFile uploading(File file) throws Exception{
        String storeRootPath = iStoreFileService.getStorePath();
        Date data = new Date();
        String fileId = DateUtil.format(data, DateUtils.YYYYMMDDHHMMSSSSS);
        String fileNameOld = file.getName();
        if(StringUtils.contains(fileNameOld,Constants.SLASH)) {
            fileNameOld = fileNameOld.substring(fileNameOld.lastIndexOf(Constants.SLASH) + 1);
        }

        String fileType= fileNameOld.substring(fileNameOld.lastIndexOf(".")+1);

        // 相对路径
        String relativeFilePath = "process"+ File.separator+ fileId + File.separator + fileNameOld;
        // 绝对路径
        String absFilePath = storeRootPath + File.separator + relativeFilePath;

        FileUtil.copyFile(file,new File(absFilePath));

        // 建立附件表记录
        BasicFile basicFile = new BasicFile();
        basicFile.setId(fileId);
        // 判断是否开启对接爱数平台
        boolean asPlatform = Boolean.parseBoolean(configService.selectConfigByKey(Constants.ASAS7_PLATFORM));
        if (asPlatform) {
            // 调用爱数上传附件接口
            try {
                FileOsbeginuploadRes fileOsbeginuploadRes = fileUploadService.singleUpload(absFilePath);
                basicFile.setExternalFilePath(fileOsbeginuploadRes.getName());
                basicFile.setExternalFileId(fileOsbeginuploadRes.getDocid());
                basicFile.setExternalRev(fileOsbeginuploadRes.getRev());
            } catch (Exception e) {
                log.error("uploading上传文件到爱数异常1",e);
                // 爱数令牌超时情况，删除爱数令牌然后再重新上传
                SpringUtils.getBean(RedisCache.class).deleteObject(com.rzdata.asas7.util.Constants.ASAS7_TOKEN);
                try {
                    FileOsbeginuploadRes fileOsbeginuploadRes = fileUploadService.singleUpload(absFilePath);
                    basicFile.setExternalFilePath(fileOsbeginuploadRes.getName());
                    basicFile.setExternalFileId(fileOsbeginuploadRes.getDocid());
                    basicFile.setExternalRev(fileOsbeginuploadRes.getRev());
                } catch (Exception e2) {
                    log.error("uploading上传文件到爱数异常2",e2);
                }
            }
        }
        basicFile.setFileSize(file.length());
        basicFile.setFileType(fileType);
        basicFile.setFileName(fileNameOld);
        basicFile.setFilePath(relativeFilePath);
        basicFile.setCreateTime(data);
        iBasicFileService.save(basicFile);
        return basicFile;
    }

    @Override
    public BasicFile getPdfFilePathByFileId(String fileId, String type) {
        BasicFile basicFileMain = iBasicFileService.getById(fileId);
        if(Constants.FILE_TYPE_PDF.equals(basicFileMain.getFileType())){
            return basicFileMain;
        }else{
            String mainPdfId = getPdfId(fileId,type);
            return iBasicFileService.getById(mainPdfId);
        }
    }
}

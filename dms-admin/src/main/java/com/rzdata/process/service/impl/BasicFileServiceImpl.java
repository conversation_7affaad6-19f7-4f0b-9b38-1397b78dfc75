package com.rzdata.process.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.rzdata.asas7.service.FileUploadDownloadService;
import com.rzdata.fileencry.IFileEncryDecryService;
import com.rzdata.framework.constant.Constants;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.core.mybatisplus.core.ServicePlusImpl;
import com.rzdata.framework.core.page.PagePlus;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.core.redis.RedisCache;
import com.rzdata.framework.utils.*;
import com.rzdata.framework.utils.spring.SpringUtils;
import com.rzdata.plugins.wordcompare.WordCompareService;
import com.rzdata.process.domain.BasicFile;
import com.rzdata.process.domain.bo.BasicFileBo;
import com.rzdata.process.domain.vo.BasicFileVo;
import com.rzdata.process.mapper.BasicFileMapper;
import com.rzdata.process.service.*;
import com.rzdata.process.utils.DateUtils;
import com.rzdata.setting.domain.bo.WatermarkParamBo;
import com.rzdata.system.service.ISysConfigService;
import io.contentBusAPI.docAccess.client.model.FileOsbeginuploadRes;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 附件Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-01-04
 */
@Slf4j
@Service
public class BasicFileServiceImpl extends ServicePlusImpl<BasicFileMapper, BasicFile, BasicFileVo> implements IBasicFileService {

    @Autowired
    private IStoreFileService iStoreFileService;

    @Autowired
    private ISysConfigService configService;

    @Autowired
    private FileUploadDownloadService fileUploadService;

    @Autowired
    DocBuildSignatureService docBuildSignatureService;

    @Autowired
    DocBuildCompareService docBuildCompareService;

    @Autowired
    WordCompareService wordCompareService;

    @Autowired
    RedisUtils redisUtils;

    @Autowired
    private IFileEncryDecryService iFileEncryDecryService;

    @Override
    public Map<String, BasicFileVo> queryByIdList(List<String> fileIds) {
        if (CollectionUtil.isEmpty(fileIds)) {
            return new HashMap<>();
        }
        return Optional.ofNullable(this.listVoByIds(fileIds)).orElse(Lists.newArrayList())
                .stream().collect(Collectors.toMap(BasicFileVo::getId, Function.identity()));
    }

    @Override
    public BasicFileVo queryById(String id){
        return getVoById(id);
    }

    @Override
    public TableDataInfo<BasicFileVo> queryPageList(BasicFileBo bo) {
        PagePlus<BasicFile, BasicFileVo> result = pageVo(PageUtils.buildPagePlus(), buildQueryWrapper(bo));
        return PageUtils.buildDataInfo(result);
    }

    @Override
    public List<BasicFileVo> queryList(BasicFileBo bo) {
        return listVo(buildQueryWrapper(bo));
    }

    private LambdaQueryWrapper<BasicFile> buildQueryWrapper(BasicFileBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<BasicFile> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getFileName()), BasicFile::getFileName, bo.getFileName());
        lqw.eq(bo.getFileSize() != null, BasicFile::getFileSize, bo.getFileSize());
        lqw.eq(StringUtils.isNotBlank(bo.getFileType()), BasicFile::getFileType, bo.getFileType());
        lqw.eq(StringUtils.isNotBlank(bo.getFileClass()), BasicFile::getFileClass, bo.getFileClass());
        lqw.eq(StringUtils.isNotBlank(bo.getBusinessId()), BasicFile::getBusinessId, bo.getBusinessId());
        lqw.eq(StringUtils.isNotBlank(bo.getFilePath()), BasicFile::getFilePath, bo.getFilePath());
        lqw.eq(StringUtils.isNotBlank(bo.getExternalFileId()), BasicFile::getExternalFileId, bo.getExternalFileId());
        return lqw;
    }

    @Override
    public String insertByBo(BasicFileBo bo) {
        BasicFile add = BeanUtil.toBean(bo, BasicFile.class);
        validEntityBeforeSave(add);
        boolean flag = save(add);
        if (flag) {
            bo.setId(add.getId());
        }
        return bo.getId();
    }

    @Override
    public String uploading(MultipartFile file,String path) throws Exception {
        String storeRootPath = iStoreFileService.getStorePath();
        String fileNameOld = file.getOriginalFilename();
        if(StringUtils.contains(fileNameOld,Constants.SLASH)) {
            fileNameOld = fileNameOld.substring(fileNameOld.lastIndexOf(Constants.SLASH) + 1);
        }
        // 相对路径
        String relativeDirPath = path + File.separator + fileNameOld;
        // 绝对路径
        String absFilePath = storeRootPath + relativeDirPath;

        FileUtil.copyFile(file.getInputStream(),new File(absFilePath));

        // 判断是否加密
        boolean fileEncry = Boolean.parseBoolean(configService.selectConfigByKey(Constants.FILE_ENCRYT));
        if (fileEncry) {
            try {
                // 解密之后的文件流
                iFileEncryDecryService.isSdFileAndDecrypt(absFilePath);
            } catch (Exception e) {
                log.error("解密文件失败",e);
            }
        }
        return absFilePath;
    }

    @Override
    public Boolean updateByBo(BasicFileBo bo) {
        BasicFile update = BeanUtil.toBean(bo, BasicFile.class);
        validEntityBeforeSave(update);
        return updateById(update);
    }

    /**
     * 保存前的数据校验
     *
     * @param entity 实体类数据
     */
    private void validEntityBeforeSave(BasicFile entity){
        //TODO 做一些数据校验,如唯一约束
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return removeByIds(ids);
    }

    @Override
    public BasicFile createPdfFile(BasicFile basicFile) throws Exception {
        String absPdfFilePath = this.docBuildSignatureService.processWord2Pdf(basicFile);
        File file = new File(absPdfFilePath);
        // 建立对象
        BasicFile bo = new BasicFile();
        boolean asPlatform = Boolean.parseBoolean(configService.selectConfigByKey(Constants.ASAS7_PLATFORM));
        if (asPlatform) {
            // 调用爱数上传附件接口
            try {
                FileOsbeginuploadRes fileOsbeginuploadRes = fileUploadService.singleUpload(absPdfFilePath);
                bo.setExternalFilePath(fileOsbeginuploadRes.getName());
                bo.setExternalFileId(fileOsbeginuploadRes.getDocid());
                bo.setExternalRev(fileOsbeginuploadRes.getRev());
            } catch (Exception e) {
                log.error("createPdfFile上传文件到爱数异常1",e);
                // 爱数令牌超时情况，删除爱数令牌然后再重新上传
                SpringUtils.getBean(RedisCache.class).deleteObject(com.rzdata.asas7.util.Constants.ASAS7_TOKEN);
                try {
                    FileOsbeginuploadRes fileOsbeginuploadRes = fileUploadService.singleUpload(absPdfFilePath);
                    bo.setExternalFilePath(fileOsbeginuploadRes.getName());
                    bo.setExternalFileId(fileOsbeginuploadRes.getDocid());
                    bo.setExternalRev(fileOsbeginuploadRes.getRev());
                } catch (Exception e2) {
                    log.error("createPdfFile上传文件到爱数异常2",e2);
                }
            }
        }
        bo.setFileSize(file.length());
        bo.setFileType(file.getName().substring(file.getName().lastIndexOf(".")+1));
        bo.setFileName(file.getName());
        bo.setFilePath(absPdfFilePath.replace(iStoreFileService.getStorePath()+File.separator,""));
        bo.setCreateTime(new Date());
        save(bo);
        return bo;
    }

    @Override
    public void createPdfFile(InputStream inputStream, String desPath, String fileName) throws IOException {
        docBuildSignatureService.processWord2Pdf(inputStream,desPath,fileName);
    }

    @Override
    public String createEncryptFileId(String docClass, BasicFile basicFile, String type, WatermarkParamBo param) {
        String absPdfFilePath = docBuildSignatureService.processPdfWaterMark(docClass,basicFile,type,param);
        File file = new File(absPdfFilePath);
        // 建立对象
        BasicFileBo bo = new BasicFileBo();
        boolean asPlatform = Boolean.parseBoolean(configService.selectConfigByKey(Constants.ASAS7_PLATFORM));
        if (asPlatform) {
            // 调用爱数上传附件接口
            try {
                FileOsbeginuploadRes fileOsbeginuploadRes = fileUploadService.singleUpload(absPdfFilePath);
                bo.setExternalFilePath(fileOsbeginuploadRes.getName());
                bo.setExternalFileId(fileOsbeginuploadRes.getDocid());
                bo.setExternalRev(fileOsbeginuploadRes.getRev());
            } catch (Exception e) {
                log.error("createEncryptFileId上传文件到爱数异常1",e);
                // 爱数令牌超时情况，删除爱数令牌然后再重新上传
                SpringUtils.getBean(RedisCache.class).deleteObject(com.rzdata.asas7.util.Constants.ASAS7_TOKEN);
                try {
                    FileOsbeginuploadRes fileOsbeginuploadRes = fileUploadService.singleUpload(absPdfFilePath);
                    bo.setExternalFilePath(fileOsbeginuploadRes.getName());
                    bo.setExternalFileId(fileOsbeginuploadRes.getDocid());
                    bo.setExternalRev(fileOsbeginuploadRes.getRev());
                } catch (Exception e2) {
                    log.error("createEncryptFileId上传文件到爱数异常2",e2);
                }
            }
        }
        bo.setFileSize(file.length());
        bo.setFileType(file.getName().substring(file.getName().lastIndexOf(".")+1));
        bo.setFileName(file.getName());
        bo.setFilePath(absPdfFilePath.replace(iStoreFileService.getStorePath()+File.separator,""));
        bo.setCreateBy(SecurityUtils.getLoginUser().getUsername());
        bo.setCreateTime(new Date());
        bo.setFileClass(this.getFileClass(type));
        return this.insertByBo(bo);
    }

    /**
     * 生成文件比对文件对象
     *
     * @param fileId1
     * @param fileId2
     */
    @Override
    public String createComparePdfFile(String fileId1, String fileId2, String businessId) {
        AjaxResult res = this.docBuildCompareService.processCompare(fileId1,fileId2);
        if(res.getCode() == 500) {
            // 创建PDF文件失败
            log.error(res.getMsg());
            return null;
        }
        String absPdfFilePath = (String)res.getData();
        File file = new File(absPdfFilePath);
        // 建立对象
        BasicFileBo bo = new BasicFileBo();
        String uuid = IdUtil.simpleUUID();
        bo.setId(uuid);
        Boolean asPlatform = Boolean.valueOf(configService.selectConfigByKey(Constants.ASAS7_PLATFORM));
        if (asPlatform) {
            // 调用爱数上传附件接口
            try {
                FileOsbeginuploadRes fileOsbeginuploadRes = fileUploadService.singleUpload(absPdfFilePath);
                bo.setExternalFilePath(fileOsbeginuploadRes.getName());
                bo.setExternalFileId(fileOsbeginuploadRes.getDocid());
                bo.setExternalRev(fileOsbeginuploadRes.getRev());
            } catch (Exception e) {
                log.error("createPdfFile上传文件到爱数异常1",e);
                // 爱数令牌超时情况，删除爱数令牌然后再重新上传
                SpringUtils.getBean(RedisCache.class).deleteObject(com.rzdata.asas7.util.Constants.ASAS7_TOKEN);
                try {
                    FileOsbeginuploadRes fileOsbeginuploadRes = fileUploadService.singleUpload(absPdfFilePath);
                    bo.setExternalFilePath(fileOsbeginuploadRes.getName());
                    bo.setExternalFileId(fileOsbeginuploadRes.getDocid());
                    bo.setExternalRev(fileOsbeginuploadRes.getRev());
                } catch (Exception e2) {
                    log.error("createPdfFile上传文件到爱数异常2",e2);
                }
            }
        }
        bo.setFileSize(file.length());
        bo.setFileType(file.getName().substring(file.getName().lastIndexOf(".")+1));
        bo.setFileName(file.getName());
        bo.setFilePath(absPdfFilePath.replace(iStoreFileService.getStorePath(),""));
        bo.setCreateTime(new Date());
        bo.setFileClass("文件比对");
        bo.setBusinessId(businessId);
        //bo.setMessageDigest(FileUtils.extractChecksum(file.()));
        return this.insertByBo(bo);
    }

    private String getFileClass(String type) {
        String result = "";
        if(type.contains("文件分发章")) {
            result = "文件分发章";
        } else if(type.contains("文件失效章")) {
            result = "文件失效章";
        } else if(type.contains("文件生效章")) {
            result = "文件生效章";
        }
        return result;
    }
}

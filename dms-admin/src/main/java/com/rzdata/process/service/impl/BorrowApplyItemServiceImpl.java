package com.rzdata.process.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rzdata.framework.utils.PageUtils;
import com.rzdata.framework.core.page.PagePlus;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.utils.StringUtils;
import org.springframework.stereotype.Service;
import com.rzdata.framework.core.mybatisplus.core.ServicePlusImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.rzdata.process.domain.bo.BorrowApplyItemBo;
import com.rzdata.process.domain.vo.BorrowApplyItemVo;
import com.rzdata.process.domain.BorrowApplyItem;
import com.rzdata.process.mapper.BorrowApplyItemMapper;
import com.rzdata.process.service.IBorrowApplyItemService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 文件借阅选择借阅人Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-12-31
 */
@Service
public class BorrowApplyItemServiceImpl extends ServicePlusImpl<BorrowApplyItemMapper, BorrowApplyItem, BorrowApplyItemVo> implements IBorrowApplyItemService {

    @Override
    public BorrowApplyItemVo queryById(String id){
        return getVoById(id);
    }

    @Override
    public TableDataInfo<BorrowApplyItemVo> queryPageList(BorrowApplyItemBo bo) {
        PagePlus<BorrowApplyItem, BorrowApplyItemVo> result = pageVo(PageUtils.buildPagePlus(), buildQueryWrapper(bo));
        return PageUtils.buildDataInfo(result);
    }

    public TableDataInfo<BorrowApplyItemVo> getMyBorrowItemList(BorrowApplyItemBo bo){
        Page<BorrowApplyItemVo> page = new Page<>(bo.getPageNum(), bo.getPageSize());
        Page<BorrowApplyItemVo> iPage = baseMapper.getMyBorrowItemList(page, bo);
        return PageUtils.buildDataInfo(iPage);
    }

    @Override
    public List<BorrowApplyItemVo> queryList(BorrowApplyItemBo bo) {
        return listVo(buildQueryWrapper(bo));
    }

    private LambdaQueryWrapper<BorrowApplyItem> buildQueryWrapper(BorrowApplyItemBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<BorrowApplyItem> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getApplyId()), BorrowApplyItem::getApplyId, bo.getApplyId());
        return lqw;
    }

    @Override
    public Boolean insertByBo(BorrowApplyItemBo bo) {
        BorrowApplyItem add = BeanUtil.toBean(bo, BorrowApplyItem.class);
        validEntityBeforeSave(add);
        boolean flag = save(add);
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    @Override
    public Boolean updateByBo(BorrowApplyItemBo bo) {
        BorrowApplyItem update = BeanUtil.toBean(bo, BorrowApplyItem.class);
        validEntityBeforeSave(update);
        return updateById(update);
    }

    /**
     * 保存前的数据校验
     *
     * @param entity 实体类数据
     */
    private void validEntityBeforeSave(BorrowApplyItem entity){
        //TODO 做一些数据校验,如唯一约束
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return removeByIds(ids);
    }

    @Override
    public boolean isValidBorrowByVersionId(String versionId,String userName){
        Integer isValid = baseMapper.isValidBorrowByVersionId(versionId, userName);
        return isValid!=null&&isValid>0;
    }
}

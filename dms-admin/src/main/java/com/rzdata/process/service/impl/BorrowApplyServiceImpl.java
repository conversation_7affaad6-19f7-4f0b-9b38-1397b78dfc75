package com.rzdata.process.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.blueland.bpmclient.model.ProcessInstanceModel;
import com.google.common.eventbus.AllowConcurrentEvents;
import com.google.common.eventbus.Subscribe;
import com.rzdata.config.ProcessConfig;
import com.rzdata.eventbus.ProcessEventBus;
import com.rzdata.eventbus.ProcessResultEvent;
import com.rzdata.framework.constant.Constants;
import com.rzdata.framework.constant.ProcessStatusConstants;
import com.rzdata.framework.core.domain.entity.SysUser;
import com.rzdata.framework.core.mybatisplus.core.ServicePlusImpl;
import com.rzdata.framework.core.page.PagePlus;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.utils.PageUtils;
import com.rzdata.framework.utils.SecurityUtils;
import com.rzdata.framework.utils.StringUtils;
import com.rzdata.process.domain.BorrowApply;
import com.rzdata.process.domain.BorrowApplyItem;
import com.rzdata.process.domain.WorkflowApplyLog;
import com.rzdata.process.domain.bo.BorrowApplyBo;
import com.rzdata.process.domain.bo.WorkflowApplyLogBo;
import com.rzdata.process.domain.dto.UserDTO;
import com.rzdata.process.domain.vo.BorrowApplyVo;
import com.rzdata.process.domain.vo.WorkflowApplyLogVo;
import com.rzdata.process.enums.ApplyStatusEnum;
import com.rzdata.process.enums.ApplyTypeEnum;
import com.rzdata.process.enums.DocMessageEnum;
import com.rzdata.process.enums.RecordStatusEnum;
import com.rzdata.process.mapper.BorrowApplyMapper;
import com.rzdata.process.mapper.StandardMapper;
import com.rzdata.process.service.*;
import com.rzdata.setting.service.IDocPresetUserService;
import com.rzdata.system.service.ISysConfigService;
import com.rzdata.system.service.ISysDeptService;
import com.rzdata.system.service.ISysUserService;
import com.rzdata.system.service.WorkflowService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.*;
import java.util.stream.Collectors;

import static com.rzdata.framework.constant.Constants.PROCESS_KEY;

/**
 * 文件借阅申请Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-12-31
 */
@Slf4j
@Service
public class BorrowApplyServiceImpl extends ServicePlusImpl<BorrowApplyMapper, BorrowApply, BorrowApplyVo> implements IBorrowApplyService {

    @Autowired
    WorkflowService workflowService;

    @Autowired
    ISysConfigService configService;

    @Autowired
    IWorkflowApplyLogService iWorkflowApplyLogService;

    @Autowired
    IBorrowApplyItemService iBorrowApplyItemService;
    @Autowired
    private  IGenerateIdService iGenerateIdService;

    @Autowired
    StandardMapper standardMapper;

    @Autowired
    ISysDeptService sysDeptService;

    @Autowired
    IDocMessageService docMessageService;

    @Autowired
    IVersionService versionService;

    @Autowired
    IDocPresetUserService iDocPresetUserService;

    @Autowired
    ISysUserService sysUserService;

    @Override
    public BorrowApplyVo queryById(String id) {
        return getVoById(id);
    }

    @Override
    public BorrowApplyVo queryByBpmnId(String bpmnId) {
        WorkflowApplyLogVo workflowApplyLog = iWorkflowApplyLogService.getVoOne(new LambdaQueryWrapper<WorkflowApplyLog>().eq(WorkflowApplyLog::getProcInstId,bpmnId).last("limit 1"));
        return queryById(workflowApplyLog.getId());
    }

    @Override
    public TableDataInfo<BorrowApplyVo> queryPageList(BorrowApplyBo bo) {
        PagePlus<BorrowApply, BorrowApplyVo> result = pageVo(PageUtils.buildPagePlus(), buildQueryWrapper(bo));
        return PageUtils.buildDataInfo(result);
    }

    @Override
    public List<BorrowApplyVo> queryList(BorrowApplyBo bo) {
        return listVo(buildQueryWrapper(bo));
    }

    private LambdaQueryWrapper<BorrowApply> buildQueryWrapper(BorrowApplyBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<BorrowApply> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getApplyTitle()), BorrowApply::getApplyTitle, bo.getApplyTitle());
        lqw.eq(bo.getDeptId() != null, BorrowApply::getDeptId, bo.getDeptId());
        lqw.like(StringUtils.isNotBlank(bo.getUserName()), BorrowApply::getUserName, bo.getUserName());
        lqw.eq(StringUtils.isNotBlank(bo.getReason()), BorrowApply::getReason, bo.getReason());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), BorrowApply::getStatus, bo.getStatus());
        return lqw;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ProcessInstanceModel insertByBo(BorrowApplyBo bo) {
        BorrowApply add = BeanUtil.toBean(bo, BorrowApply.class);
        if (BooleanUtil.isTrue(bo.getEditStatus())) {
            add.setCreateBy(SecurityUtils.getUsername());
        }
        boolean flag = saveOrUpdate(add);
        // 保存关系数据
        ProcessInstanceModel processInstanceModel = null;
        if (flag) {
            bo.setId(add.getId());
            if (BooleanUtil.isTrue(bo.getEditStatus())) {
                saveApplyItem(bo);
            }
            if (BooleanUtil.isTrue(bo.getPresetUserEdit())) {
                iDocPresetUserService.updatePresetUser(bo.getPresetUserList(),bo.getId());
            }
            // 开启流程 调用工作流相关接口
            try {
                bo.getBpmClientInputModel().setStatus(bo.getRecordStatus());
                bo.getBpmClientInputModel().setBizType(ApplyTypeEnum.BORROW.name());
                if(StringUtils.equals(bo.getRecordStatus(), RecordStatusEnum.DRAFT.getCode())){
                    processInstanceModel = workflowService.saveExecute(bo.getBpmClientInputModel(), add.getId());
                }else if (StringUtils.equals(bo.getRecordStatus(),RecordStatusEnum.CANCEL.getCode())){
                    processInstanceModel = workflowService.cancelExecute(bo.getBpmClientInputModel(), add.getId());
                }else{
                    processInstanceModel = workflowService.nextExecute(bo.getBpmClientInputModel(), add.getId());
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return processInstanceModel;
    }

    private void saveApplyItem(BorrowApplyBo bo) {
        QueryWrapper<BorrowApplyItem> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(BorrowApplyItem:: getApplyId, bo.getId());
        iBorrowApplyItemService.remove(queryWrapper);
        if (ObjectUtil.isNotEmpty(bo.getItemList())) {
            for (BorrowApplyItem item : bo.getItemList()) {
                item.setApplyId(bo.getId());
            }
            iBorrowApplyItemService.saveBatch(bo.getItemList());
        }
    }

    /**
     * 同步事件监听处理
     */
    @Subscribe
    @AllowConcurrentEvents
    protected void onProcessEvent(ProcessResultEvent event) {
        ProcessConfig processConfig = JSONUtil.toBean(configService.selectConfigByKey(PROCESS_KEY), ProcessConfig.class);
        if (!event.getApplyType().contains(processConfig.getProcDefKeyJYSQ())) {
            return;
        }
        BorrowApply borrowApply = this.baseMapper.selectById(event.getApplyId());
        if (ObjectUtil.isEmpty(borrowApply)) {
            return;
        }
        borrowApply.setStatus(event.getStatus());
        this.baseMapper.updateById(borrowApply);
        Date date = DateUtil.date();
        String status = event.getStatus();
        //获取借阅通过的记录
        List<BorrowApplyItem> itemAllList = iBorrowApplyItemService.list(new LambdaQueryWrapper<BorrowApplyItem>()
                .eq(BorrowApplyItem::getApplyId,event.getApplyId()));
        if (Objects.equals(ProcessStatusConstants.TO_DONE , event.getStatus())) {
            //获取借阅通过的记录
            List<BorrowApplyItem> itemList = itemAllList.stream().filter(item->ApplyStatusEnum.PASS.getCode().equals(item.getBorrowAction())).collect(Collectors.toList());
            for (BorrowApplyItem item:itemList) {
                item.setStartTime(date);
                if (Constants.VALUE_N.equals(item.getIsForever())&&item.getTimeLimit()!=null) {
                    item.setEndTime(DateUtil.offset(date, DateField.DAY_OF_YEAR,item.getTimeLimit()));
                }
            }
            iBorrowApplyItemService.updateBatchById(itemList);

            BorrowApplyItem oneItem = new BorrowApplyItem();
            if(CollUtil.isNotEmpty(itemList)){
                oneItem = itemList.get(0);
            }
            try {
                //保存站内消息
                docMessageService.saveMsg(oneItem.getVersionId(),borrowApply.getId(), DocMessageEnum.FLOW_BORROW.getMsg(),oneItem.getDocName(),oneItem.getDocId(),DocMessageEnum.FLOW_BORROW.getCode(), null, null, null);
            }catch (Exception e){
                log.error("BorrowApplyServiceImpl-->onProcessEvent----e###", e);
            }
        } else {
            //结论环节转派 业务数据存储的审核人员也跟着转变
            if (BooleanUtil.isTrue(event.getTransferStatus())) {
                String userName = SecurityUtils.getUsername();
                SysUser user = sysUserService.selectUserByUserName((String) event.getModel().getWf_receivers().get(0).get("receiveUserId"));
                UserDTO userDTO = new UserDTO();
                userDTO.setUserName(user.getUserName());
                userDTO.setNickName(user.getNickName());
                userDTO.setDeptId(user.getDeptId());
                userDTO.setDeptName(user.getDept().getDeptName());
                String presetUser= JSONUtil.toJsonStr(userDTO);
                List<BorrowApplyItem> itemList = itemAllList.stream().filter(borrowApplyItem -> {
                    if (userName.equals(JSONUtil.toBean(borrowApplyItem.getPresetUser(), UserDTO.class).getUserName())){
                        borrowApplyItem.setPresetUser(presetUser);
                        return true;
                    }
                    return false;
                }).collect(Collectors.toList());
                if (itemList.size()>0) {
                    iBorrowApplyItemService.updateBatchById(itemList);
                }
            }
        }
        saveWorkFlowLog(borrowApply,itemAllList,status,event.getProcessInst().getProcInstTitle(),event);
    }

    /**
     * 保存申请记录
     */
    public void saveWorkFlowLog(BorrowApply apply, List<BorrowApplyItem> itemList, String status, String procTitle, ProcessResultEvent event) {
        WorkflowApplyLog log = new WorkflowApplyLog();
        log.setId(apply.getId());
        log.setDocId(itemList.stream().map(BorrowApplyItem::getDocId).filter(StringUtils::isNotEmpty).collect(Collectors.joining(Constants.ID_SPLIT_KEY)));
        log.setVersionValue(itemList.stream().map(BorrowApplyItem::getVersionValue).collect(Collectors.joining(Constants.ID_SPLIT_KEY)));
        log.setDocName(procTitle);
        log.setProcStatus(status);
        log.setUserName(apply.getUserName());
        log.setDeptId(apply.getDeptId());
        iWorkflowApplyLogService.updateStatusByBusId(log,event);
    }

    /**
     * 注册事件
     */
    @PostConstruct
    private void registerEventBus() {
        ProcessEventBus.register(this);
    }

    /**
     * 取消注册事件
     */
    @PreDestroy
    private void unregisterEventBus() {
        ProcessEventBus.unregister(this);
    }

    @Override
    public Boolean updateByBo(BorrowApplyBo bo) {
        BorrowApply update = BeanUtil.toBean(bo, BorrowApply.class);
        validEntityBeforeSave(update);
        saveApplyItem(bo);
        if (BooleanUtil.isTrue(bo.getPresetUserEdit())) {
            iDocPresetUserService.updatePresetUser(bo.getPresetUserList(),bo.getId());
        }
        String procTitle = workflowService.updateFlowTitle(bo.getApplyTitle(),bo.getId());
        saveWorkFlowLog(update,bo.getItemList(),bo.getRecordStatus(),procTitle,null);
        return updateById(update);
    }

    /**
     * 保存前的数据校验
     *
     * @param entity 实体类数据
     */
    private void validEntityBeforeSave(BorrowApply entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return removeByIds(ids);
    }

    @Override
    public List<BorrowApplyVo> selectBorrowList() {
        return this.baseMapper.selectBorrowList();
    }
}

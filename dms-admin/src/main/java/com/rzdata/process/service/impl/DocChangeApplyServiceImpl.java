package com.rzdata.process.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.blueland.bpmclient.model.ProcessInstanceModel;
import com.google.common.collect.Lists;
import com.google.common.eventbus.AllowConcurrentEvents;
import com.google.common.eventbus.Subscribe;
import com.rzdata.config.ProcessConfig;
import com.rzdata.eventbus.ProcessEventBus;
import com.rzdata.eventbus.ProcessResultEvent;
import com.rzdata.framework.constant.DocMsgConstants;
import com.rzdata.framework.constant.ProcessStatusConstants;
import com.rzdata.framework.core.domain.entity.SysUser;
import com.rzdata.framework.core.mybatisplus.core.ServicePlusImpl;
import com.rzdata.framework.core.page.PagePlus;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.utils.PageUtils;
import com.rzdata.framework.utils.SecurityUtils;
import com.rzdata.framework.utils.StringUtils;
import com.rzdata.process.api.WorkflowApiController;
import com.rzdata.process.domain.DocChangeApply;
import com.rzdata.process.domain.ModifyApplyLink;
import com.rzdata.process.domain.WorkflowApplyLog;
import com.rzdata.process.domain.bo.DocChangeApplyBo;
import com.rzdata.process.domain.bo.ModifyApplyBo;
import com.rzdata.process.domain.bo.ModifyApplyLinkBo;
import com.rzdata.process.domain.vo.*;
import com.rzdata.process.enums.*;
import com.rzdata.process.mapper.DocChangeApplyMapper;
import com.rzdata.process.service.*;
import com.rzdata.process.utils.MsgTemplateUtils;
import com.rzdata.setting.domain.vo.DocClassVo;
import com.rzdata.setting.service.IDocClassService;
import com.rzdata.setting.service.IDocPresetUserService;
import com.rzdata.system.service.ISysConfigService;
import com.rzdata.system.service.ISysUserService;
import com.rzdata.system.service.WorkflowService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.*;
import java.util.stream.Collectors;

import static com.rzdata.framework.constant.Constants.PROCESS_KEY;
import static com.rzdata.process.enums.ApplyTypeEnum.ADD;
import static com.rzdata.process.enums.LinkTypeEnum.*;

/**
 * 文件变更申请Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-12-31
 */
@Slf4j
@Service
public class DocChangeApplyServiceImpl extends ServicePlusImpl<DocChangeApplyMapper, DocChangeApply, DocChangeApplyVo>
        implements IDocChangeApplyService {
    private static final String ID_SPLIT_KEY = ",";
    @Autowired
    private WorkflowService workflowService;
    @Autowired
    private ISysConfigService configService;
    @Autowired
    private IWorkflowApplyLogService workflowApplyLogService;
    @Autowired
    private IDocMessageService docMessageService;
    @Autowired
    private IDocClassService iDocClassService;
    @Autowired
    private IDocLinkLogService docLinkLogService;
    @Autowired
    private IModifyApplyLinkService modifyApplyLinkService;
    @Autowired
    private WorkflowApiController workflowApiController;
    @Autowired
    IDocPresetUserService iDocPresetUserService;

    @Autowired
    private ISysUserService sysUserService;

    @Override
    public DocChangeApplyDetailVo queryById(String id) {
        DocChangeApplyVo applyVo = this.baseMapper.selectDetailById(id);
        if (ObjectUtil.isEmpty(applyVo)) {
            return null;
        }
        DocChangeApplyDetailVo detailVo = BeanUtil.toBean(applyVo, DocChangeApplyDetailVo.class);
        List<ModifyApplyLinkVo> appendixList = modifyApplyLinkService.queryDocByApplyIdAndType(id, APPENDIX);
        detailVo.setAppendixFiles(appendixList);

        // 备注附件查询开始
        List<ModifyApplyLinkVo> remarkList = modifyApplyLinkService.queryDocByApplyIdAndType(id, APPENDIX_REMARK);
        detailVo.setRemarkFiles(remarkList);

        // 如果不是新增
        if (!ApplyTypeEnum.toType(applyVo.getChangeType()).equals(ADD)) {
            //当前生效附件
            List<DocLinkLogVo> preAppendixList = docLinkLogService.queryDocLinkVo(applyVo.getVersionId(), APPENDIX.name());
            detailVo.setSxAppendixFiles(preAppendixList);
            //当前生效版本
            List<DocLinkLogVo> preDocList = docLinkLogService.queryDocLinkVo(applyVo.getVersionId(), DOC.name());
            if (ObjectUtil.isNotEmpty(preDocList)) {
                detailVo.setFile(preDocList.get(0));
            }
        }
        return detailVo;
    }

    @Override
    public DocChangeApplyDetailVo queryByBpmnId(String bpmnId) {
        WorkflowApplyLogVo workflowApplyLog = workflowApplyLogService.getVoOne(
                new LambdaQueryWrapper<WorkflowApplyLog>().eq(WorkflowApplyLog::getProcInstId, bpmnId).last("limit 1"));
        return queryById(workflowApplyLog.getId());
    }

    @Override
    public TableDataInfo<DocChangeApplyVo> queryPageList(DocChangeApplyBo bo) {
        PagePlus<DocChangeApply, DocChangeApplyVo> result = pageVo(PageUtils.buildPagePlus(), buildQueryWrapper(bo));
        return PageUtils.buildDataInfo(result);
    }

    @Override
    public List<DocChangeApplyVo> queryList(DocChangeApplyBo bo) {
        return listVo(buildQueryWrapper(bo));
    }

    private LambdaQueryWrapper<DocChangeApply> buildQueryWrapper(DocChangeApplyBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<DocChangeApply> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getId()), DocChangeApply::getId, bo.getId());
        lqw.like(StringUtils.isNotBlank(bo.getApplyTitle()), DocChangeApply::getApplyTitle, bo.getApplyTitle());
        lqw.eq(StringUtils.isNotBlank(bo.getDocId()), DocChangeApply::getDocId, bo.getDocId());
        lqw.eq(StringUtils.isNotBlank(bo.getChangeType()), DocChangeApply::getChangeType, bo.getChangeType());
        lqw.eq(StringUtils.isNotBlank(bo.getDocClass()), DocChangeApply::getDocClass, bo.getDocClass());
        lqw.like(StringUtils.isNotBlank(bo.getDocName()), DocChangeApply::getDocName, bo.getDocName());
        lqw.eq(StringUtils.isNotBlank(bo.getVersionId()), DocChangeApply::getVersionId, bo.getVersionId());
        lqw.eq(StringUtils.isNotBlank(bo.getVersionValue()), DocChangeApply::getVersionValue, bo.getVersionValue());
        lqw.eq(bo.getDeptId() != null, DocChangeApply::getDeptId, bo.getDeptId());
        lqw.eq(StringUtils.isNotBlank(bo.getUserName()), DocChangeApply::getUserName, bo.getUserName());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), DocChangeApply::getStatus, bo.getStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getEditUserName()), DocChangeApply::getEditUserName, bo.getEditUserName());
        lqw.eq(bo.getEditDeptId() != null, DocChangeApply::getEditDeptId, bo.getEditDeptId());
        return lqw;
    }

    /**
     * 创建一条关联记录
     */
    private ModifyApplyLink createModifyApplyLink(ModifyApplyLinkBo linkBo, String applyId, Date now, String versionId,
            String versionValue,
            LinkTypeEnum linkTypeEnum, LinkClassEnum linkClassEnum) {
        ModifyApplyLink linkPo = BeanUtil.toBean(linkBo, ModifyApplyLink.class);
        linkPo.setApplyId(applyId);
        if (ObjectUtil.isEmpty(linkPo.getCreateTime())) {
            linkPo.setCreateTime(now);
        }
        linkPo.setUpdateTime(now);
        linkPo.setLinkType(linkTypeEnum.name());
        linkPo.setDocName(linkBo.getDocName());
        if (ObjectUtil.isNotEmpty(linkBo.getDocId())) {
            linkPo.setDocId(linkBo.getDocId());
        }
        if (ObjectUtil.isNotNull(linkClassEnum)) {
            linkPo.setLinkClass(linkClassEnum.name());
        }
//        if (ObjectUtil.isNotEmpty(linkBo.getDocId())) {
//            String type = this.baseMapper.getDocTypeByDocId(linkBo.getDocId());
//            if (ObjectUtil.isNotEmpty(type)) {
//                linkPo.setLinkClass(type);
//            }
//        }
        if (ObjectUtil.isEmpty(linkBo.getVersionId())) {
            linkPo.setVersionId(versionId);
        }
        if (ObjectUtil.isEmpty(linkBo.getVersionValue())) {
            linkPo.setVersionValue(versionValue);
        }
        return linkPo;
    }

    /**
     * 生成多条记录
     */
    private void buildModifyApplyLink(List<ModifyApplyLink> linkList, List<ModifyApplyLinkBo> linkBoList,
            String applyId,
            Date now, String versionId, String versionValue, LinkTypeEnum linkTypeEnum, LinkClassEnum linkClassEnum) {
        if (ObjectUtil.isNotEmpty(linkBoList)) {
            List<ModifyApplyLink> appendixList = linkBoList.stream().map(appendixBo -> createModifyApplyLink(
                    appendixBo, applyId, now, versionId, versionValue, linkTypeEnum, linkClassEnum))
                    .collect(Collectors.toList());
            linkList.addAll(appendixList);
        }
    }

    /**
     * 组装关联记录
     */
    private List<ModifyApplyLink> buildModifyApplyLink(DocChangeApplyBo bo) {
        List<ModifyApplyLink> linkList = Lists.newArrayList();

        // 备注的附件
        buildModifyApplyLink(linkList, bo.getRemarkDoc(), bo.getId(), bo.getCreateTime(), bo.getVersionId(),
                bo.getVersionValue(), LinkTypeEnum.APPENDIX_REMARK, LinkClassEnum.FILE);

        return linkList;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ProcessInstanceModel insertByBo(DocChangeApplyBo bo) {
        DocChangeApply add = BeanUtil.toBean(bo, DocChangeApply.class);
        if (BooleanUtil.isTrue(bo.getEditStatus())) {
            add.setCreateBy(SecurityUtils.getUsername());
        }
        validEntityBeforeSave(add);
        boolean flag = saveOrUpdate(add);
        ProcessInstanceModel processInstanceModel = null;
        if (flag) {
            bo.setId(add.getId());
            if (BooleanUtil.isTrue(bo.getPresetUserEdit())) {
                iDocPresetUserService.updatePresetUser(bo.getPresetUserList(), bo.getId());
            }
            try {
                bo.getBpmClientInputModel().setStatus(bo.getRecordStatus());
                bo.getBpmClientInputModel().setBizType(ApplyTypeEnum.CHANGE.name());
                if (StringUtils.equals(bo.getRecordStatus(), RecordStatusEnum.DRAFT.getCode())) {
                    processInstanceModel = workflowService.saveExecute(bo.getBpmClientInputModel(), add.getId());
                } else if (StringUtils.equals(bo.getRecordStatus(), RecordStatusEnum.CANCEL.getCode())) {
                    processInstanceModel = workflowService.cancelExecute(bo.getBpmClientInputModel(), add.getId());
                } else {
                    processInstanceModel = workflowService.nextExecute(bo.getBpmClientInputModel(), add.getId());
                }
            } catch (Exception e) {
                // TODO: handle exception here
                log.error("start doc change apply fail:", e);
            }
        }
        return processInstanceModel;
    }

    /**
     * 同步事件监听处理
     */
    @Subscribe
    @AllowConcurrentEvents
    protected void onProcessEvent(ProcessResultEvent event) {
        if (log.isDebugEnabled()) {
            log.debug("onProcessEvent====1===" + event);
        }
        ProcessConfig processConfig = JSONUtil.toBean(configService.selectConfigByKey(PROCESS_KEY),
                ProcessConfig.class);
        if (!event.getApplyType().contains(processConfig.getProcDefKeyBGSQ())) {
            return;
        }
        DocChangeApply changeApply = this.baseMapper.selectById(event.getApplyId());
        if (ObjectUtil.isEmpty(changeApply)) {
            return;
        }
        String status = event.getStatus();
        changeApply.setStatus(status);
        changeApply.setUpdateTime(new Date());
        this.baseMapper.updateById(changeApply);
        saveWorkFlowLog(changeApply, status, event.getProcessInst().getProcInstTitle(), event);
        // 流程结束开启一个新流程
        if (log.isDebugEnabled()) {
            log.debug("onProcessEvent====2===" + event);
        }
        if (ProcessStatusConstants.TO_DONE.equals(status)) {
            // 文件变更申请结束后写入日志
            event.setMsgInfo(ApplyStatusEnum.getMsg(event.getApplyStatus()));
            // docMessageService.insertMessage(event, MsgTypeEnum.CHANGE);
            // 发送站内消息
            SysUser user = sysUserService.selectUserByUserName(changeApply.getUserName());
            String pcUrl = MsgTemplateUtils.buildPcFileDetailUrl(changeApply.getDocId(), changeApply.getVersionId());
            String mobileUrl = MsgTemplateUtils.buildMobilFileDetailUrl(changeApply.getDocId(), changeApply.getVersionId());
            String msgContent = String.format(DocMessageEnum.FLOW_CHANGE.getMsg(),
                    changeApply.getDocName());
            docMessageService.sendInstationMessage(changeApply.getId(), changeApply.getDocId(),
                    changeApply.getDocClass(),
                    changeApply.getDocName(), changeApply.getVersionId(), changeApply.getVersionValue(),
                    changeApply.getDeptId(), null, changeApply.getUserName(), user.getUserId(), msgContent,
                    DocMsgConstants.MSG_TYPE_MSG, MsgTypeEnum.CHANGE.getType(), pcUrl, mobileUrl,
                    SecurityUtils.getLoginUser().getTenantId());
            // 需要根据结论判断是否开启20220411
            if (StringUtils.equals(event.getApplyStatus(), ApplyStatusEnum.PASS.getCode())) {
                changeApply.setApplyIdTemp(changeApply.getId());
                startModifyApply(changeApply, event.getApplyType());
                try {
                    // 发送企业微信
                    docMessageService.saveMsg(changeApply.getVersionId(),
                            changeApply.getId(),
                            DocMessageEnum.FLOW_CHANGE.getMsg(),
                            changeApply.getDocName(),
                            changeApply.getDocId(),
                            DocMessageEnum.FLOW_CHANGE.getCode(), null, pcUrl, mobileUrl);
                } catch (Exception e) {
                    log.error("ExtraApplyServiceImpl-->onProcessEvent--saveMsg--e###", e);
                }
            }
        }
    }

    /**
     * 保存申请记录
     */
    public void saveWorkFlowLog(DocChangeApply apply, String status, String procTitle, ProcessResultEvent event) {
        WorkflowApplyLog log = new WorkflowApplyLog();
        log.setId(apply.getId());
        log.setDocId(apply.getDocId());
        log.setVersionValue(apply.getVersionValue());
        log.setDocName(procTitle);
        log.setProcStatus(status);
        log.setUserName(apply.getUserName());
        log.setDeptId(apply.getDeptId());
        workflowApplyLogService.updateStatusByBusId(log, event);
    }

    /**
     * 开启新增流程
     */
    private void startModifyApply(DocChangeApply changeApply, String invokeType) {
        ModifyApplyBo modifyApplyBo = BeanUtil.toBean(changeApply, ModifyApplyBo.class);
        modifyApplyBo.setId(null);
        modifyApplyBo.setUserName(changeApply.getEditUserName());
        modifyApplyBo.setDeptId(changeApply.getEditDeptId());
        modifyApplyBo.setChangeId(changeApply.getId());
        modifyApplyBo.setChangeType(changeApply.getChangeType());
        modifyApplyBo.setPreChangeCode(changeApply.getApplyTitle());
        modifyApplyBo.setInvokeId(changeApply.getId());
        modifyApplyBo.setInvokeType(invokeType);
        if (log.isDebugEnabled()) {
            log.debug("startModifyApply====1===" + modifyApplyBo);
        }
        workflowApiController.saveOne(modifyApplyBo);
    }

    /**
     * TODO: 根据文档类型获取有效年限
     *
     * @param docClass
     * @return
     */
    private Long getDocExpiration(String docClass) {
        DocClassVo voById = iDocClassService.getVoById(docClass);
        if (ObjectUtil.isNotEmpty(voById)) {
            return voById.getExpiration();
        } else {
            return 1L;
        }
    }

    @Override
    public Boolean updateByBo(DocChangeApplyBo bo) {
        DocChangeApply update = BeanUtil.toBean(bo, DocChangeApply.class);
        validEntityBeforeSave(update);
        String procTitle = workflowService.updateFlowTitle(bo.getApplyTitle(), bo.getId());
        if (BooleanUtil.isTrue(bo.getPresetUserEdit())) {
            iDocPresetUserService.updatePresetUser(bo.getPresetUserList(), bo.getId());
        }
        saveWorkFlowLog(update, bo.getRecordStatus(), procTitle, null);
        return updateById(update);
    }

    /**
     * 保存前的数据校验
     *
     * @param entity 实体类数据
     */
    private void validEntityBeforeSave(DocChangeApply entity) {
        // TODO 做一些数据校验,如唯一约束
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        if (isValid) {
            // TODO 做一些业务上的校验,判断是否需要校验
        }
        return removeByIds(ids);
    }

    /**
     * 注册事件
     */
    @PostConstruct
    private void registerEventBus() {
        ProcessEventBus.register(this);
    }

    /**
     * 取消注册事件
     */
    @PreDestroy
    private void unregisterEventBus() {
        ProcessEventBus.unregister(this);
    }

}

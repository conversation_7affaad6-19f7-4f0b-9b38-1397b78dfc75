package com.rzdata.process.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rzdata.config.CustomConfig;
import com.rzdata.framework.constant.Constants;
import com.rzdata.framework.constant.DocMsgConstants;
import com.rzdata.framework.constant.PermissionConstants;
import com.rzdata.framework.constant.RoleConstants;
import com.rzdata.framework.core.domain.entity.SysDeptDetail;
import com.rzdata.framework.core.domain.entity.SysUser;
import com.rzdata.framework.core.domain.model.LoginUser;
import com.rzdata.framework.core.mybatisplus.core.ServicePlusImpl;
import com.rzdata.framework.core.page.PagePlus;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.enums.DistributeTypeEnum;
import com.rzdata.framework.exception.ServiceException;
import com.rzdata.framework.utils.PageUtils;
import com.rzdata.framework.utils.SecurityUtils;
import com.rzdata.framework.utils.StringUtils;
import com.rzdata.process.domain.*;
import com.rzdata.process.domain.bo.DocDistributeBo;
import com.rzdata.process.domain.dto.VersionDistributeDTO;
import com.rzdata.process.domain.vo.DocDistributeVo;
import com.rzdata.process.domain.vo.VersionVo;
import com.rzdata.process.enums.DocMessageEnum;
import com.rzdata.process.enums.MsgTypeEnum;
import com.rzdata.process.enums.SendType;
import com.rzdata.process.mapper.DocDistributeMapper;
import com.rzdata.process.service.*;
import com.rzdata.process.utils.MsgTemplateUtils;
import com.rzdata.setting.domain.vo.DocClassVo;
import com.rzdata.setting.service.IDocClassService;
import com.rzdata.system.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 文件分发明细Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-07-20
 */
@Service
@Slf4j
public class DocDistributeServiceImpl extends ServicePlusImpl<DocDistributeMapper, DocDistribute, DocDistributeVo>
        implements IDocDistributeService {

    @Autowired
    private IVersionService iVersionService;

    @Autowired
    private IStandardService iStandardService;

    @Autowired
    private IDocClassService iDocClassService;

    @Autowired
    ITenantService iTenantService;

    @Autowired
    IModifyApplyDistributeService iModifyApplyDistributeService;

    @Autowired
    CustomConfig customConfig;

    @Autowired
    ISysUserService iSysUserService;

    @Autowired
    ISysUserService sysUserService;

    @Autowired
    ISysNotifyLogService sysNotifyLogService;

    @Autowired
    IDocClassService docClassService;

    @Autowired
    private IDocPrintDataAuthService docPrintDataAuthService;

    @Autowired
    IDocDistributeReadRecordsService docDistributeReadRecordsService;

    @Autowired
    DocDistributeMapper docDistributeMapper;

    @Autowired
    ISysDeptService iSysDeptService;

    @Autowired
    private ISysDeptDetailService iSysDeptDetailService;

    @Autowired
    private IDocMessageService docMessageService;

    @Autowired
    private ISysNotifyTemplateService sysNotifyTemplateService;

    @Autowired
    private IMessageSendEntryService messageSendEntryService;

    @Autowired
    private IPrintTaskService printTaskService;

    @Autowired
    ISysRoleService sysRoleService;

    @Override
    public DocDistributeVo queryById(String id) {
        return getVoById(id);
    }

    @Override
    public TableDataInfo<DocDistributeVo> queryPageList(DocDistributeBo bo) {
        PagePlus<DocDistribute, DocDistributeVo> result = pageVo(PageUtils.buildPagePlus(), buildQueryWrapper(bo));
        result.getRecordsVo().forEach(item -> {
            item.setClassName(iDocClassService.getById(item.getDocClass()).getClassName());
            item.setVersionValue(iVersionService.getById(item.getVersionId()).getVersionValue());
        });
        return PageUtils.buildDataInfo(result);
    }

    @Override
    public TableDataInfo<DocDistributeVo> queryPageDocList(DocDistributeBo bo) {
        return PageUtils.buildDataInfo(baseMapper.queryPageDoc(PageUtils.buildPage(), bo));
    }

    @Override
    public TableDataInfo<DocDistributeVo> readTrainPage(DocDistributeBo bo) {
        List<String> docClassList = bo.getDocClassList();
        if (docClassList != null) {
            docClassList.add(bo.getDocClass());
            bo.setDocClass(null);
            bo.setDocClassList(docClassList);
        }
        Page<DocDistributeVo> page = PageUtils.buildPage(bo.getPageNum(), bo.getPageSize(), bo.getOrderByColumn(),
                bo.getIsAsc());
        Page<DocDistributeVo> iPage = baseMapper.selectReadTranList(page, bo);
        iPage.getRecords().forEach(item -> {
            item.setDeptName(iSysDeptService.getDeptName(item.getDeptId()));
        });
        return PageUtils.buildDataInfo(iPage);
    }

    @Override
    public TableDataInfo<DocDistributeVo> queryAuthFilterPageDocList(DocDistributeBo bo) {
        // 获取当前的用户
        LoginUser loginUser = SecurityUtils.getLoginUser();
        Set<String> menuPermission = loginUser.getPermissions();
        // 如果是超级管理员 或者配置了查询所有权限，则不过滤数据
        if (!(SecurityUtils.isAdmin(loginUser.getUserId())
                || menuPermission.contains(PermissionConstants.DOC_PRINT_ALL_DATA_AUTH))) {
            if (menuPermission.contains(PermissionConstants.DOC_PRINT_USER_DATA_AUTH)) {
                // 根据用户查询权限
                List<DocPrintDataAuth> docPrintDataAuthList = docPrintDataAuthService
                        .list(new LambdaQueryWrapper<DocPrintDataAuth>()
                                .eq(DocPrintDataAuth::getUserName, loginUser.getUsername())
                                .select(DocPrintDataAuth::getId, DocPrintDataAuth::getDocDistributeId));
                if (CollUtil.isNotEmpty(docPrintDataAuthList)) {
                    List<String> ids = docPrintDataAuthList.stream().map(DocPrintDataAuth::getDocDistributeId)
                            .collect(Collectors.toList());
                    bo.setIds(ids);
                }
            }
            bo.setReceiveUserName(loginUser.getUsername());
            return PageUtils.buildDataInfo(baseMapper.queryPageDoc(PageUtils.buildPage(), bo));
        }
        return PageUtils.buildDataInfo(baseMapper.queryPageDoc(PageUtils.buildPage(), bo));
    }

    @Override
    public List<DocDistributeVo> queryList(DocDistributeBo bo) {
        return listVo(buildQueryWrapper(bo));
    }

    @Override
    public List<DocDistributeVo> authFilterList(DocDistributeBo bo) {
        // 获取当前的用户
        LoginUser loginUser = SecurityUtils.getLoginUser();
        boolean bool = false;
        if (StringUtils.isNotEmpty(bo.getVersionId())) {
            String deptId = iVersionService.getOne(new LambdaQueryWrapper<Version>()
                    .eq(Version::getId, bo.getVersionId()).select(Version::getId, Version::getDeptId)).getDeptId();
            if (iSysDeptDetailService.count(new LambdaQueryWrapper<SysDeptDetail>().eq(SysDeptDetail::getDeptId, deptId)
                    .apply("find_in_set({0}, doc_manager_code)", loginUser.getUsername())) > 0) {
                bool = true;
            }
        }
        Set<String> menuPermission = loginUser.getPermissions();
        // 如果是超级管理员 或者配置了查询所有权限，则不过滤数据
        if (!(SecurityUtils.isAdmin(loginUser.getUserId())
                || menuPermission.contains(PermissionConstants.DOC_PRINT_ALL_DATA_AUTH) || bool)) {
            if (menuPermission.contains(PermissionConstants.DOC_PRINT_USER_DATA_AUTH)) {
                // 根据用户查询权限
                List<DocPrintDataAuth> docPrintDataAuthList = docPrintDataAuthService
                        .list(new LambdaQueryWrapper<DocPrintDataAuth>()
                                .eq(DocPrintDataAuth::getUserName, loginUser.getUsername())
                                .select(DocPrintDataAuth::getId, DocPrintDataAuth::getDocDistributeId));
                if (CollUtil.isNotEmpty(docPrintDataAuthList)) {
                    List<String> ids = docPrintDataAuthList.stream().map(DocPrintDataAuth::getDocDistributeId)
                            .collect(Collectors.toList());
                    bo.setIds(ids);
                }
            }
        }
        return listVo(buildQueryWrapper(bo));
    }

    @Override
    public Boolean receiveByIds(Collection<String> ids) {
        // 签收未签收的文件
        update(new LambdaUpdateWrapper<DocDistribute>().in(DocDistribute::getId, ids)
                .eq(DocDistribute::getReceive, false)
                .set(DocDistribute::getReceive, true).set(DocDistribute::getReceiveTime, DateUtil.date())
                .set(DocDistribute::getStatus, Constants.DISTRIBUTE_STATUS_RECEIVE));
        return true;
    }

    @Override
    public Boolean recoveryByIds(Collection<String> ids) {
        // 回收未回收的文件
        update(new LambdaUpdateWrapper<DocDistribute>().in(DocDistribute::getId, ids)
                .eq(DocDistribute::getRecovery, false)
                .set(DocDistribute::getRecovery, true).set(DocDistribute::getRecoveryTime, DateUtil.date())
                .set(DocDistribute::getStatus, Constants.DISTRIBUTE_STATUS_RECOVERY));
        return true;
    }

    @Override
    public Boolean lostByIds(Collection<String> ids) {
        // 丢失未丢失未回收的文件
        update(new LambdaUpdateWrapper<DocDistribute>().in(DocDistribute::getId, ids).eq(DocDistribute::getLost, false)
                .eq(DocDistribute::getRecovery, false)
                .set(DocDistribute::getLost, true).set(DocDistribute::getLostTime, DateUtil.date())
                .set(DocDistribute::getStatus, Constants.DISTRIBUTE_STATUS_LOST));
        return true;
    }

    private LambdaQueryWrapper<DocDistribute> buildQueryWrapper(DocDistributeBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<DocDistribute> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getTenantId()), DocDistribute::getTenantId, bo.getTenantId());
        lqw.eq(StringUtils.isNotBlank(bo.getVersionId()), DocDistribute::getVersionId, bo.getVersionId());
        lqw.eq(bo.getCode() != null, DocDistribute::getCode, bo.getCode());
        lqw.like(StringUtils.isNotBlank(bo.getDocId()), DocDistribute::getDocId, bo.getDocId());
        lqw.eq(StringUtils.isNotBlank(bo.getDocClass()), DocDistribute::getDocClass, bo.getDocClass());
        lqw.like(StringUtils.isNotBlank(bo.getDocName()), DocDistribute::getDocName, bo.getDocName());
        lqw.eq(bo.getNums() != null, DocDistribute::getNums, bo.getNums());
        lqw.eq(bo.getPrintNums() != null, DocDistribute::getPrintNums, bo.getPrintNums());
        lqw.eq(StringUtils.isNotBlank(bo.getPrintFlag()), DocDistribute::getPrintFlag, bo.getPrintFlag());
        lqw.eq(StringUtils.isNotBlank(bo.getReceiveUserName()), DocDistribute::getReceiveUserName,
                bo.getReceiveUserName());
        lqw.like(StringUtils.isNotBlank(bo.getReceiveNickName()), DocDistribute::getReceiveNickName,
                bo.getReceiveNickName());
        lqw.eq(StringUtils.isNotBlank(bo.getReceiveUserDept()), DocDistribute::getReceiveUserDept,
                bo.getReceiveUserDept());
        lqw.eq(bo.getReceiveTime() != null, DocDistribute::getReceiveTime, bo.getReceiveTime());
        lqw.eq(bo.getLostTime() != null, DocDistribute::getLostTime, bo.getLostTime());
        lqw.eq(bo.getRecoveryTime() != null, DocDistribute::getRecoveryTime, bo.getRecoveryTime());
        lqw.eq(bo.getReceive() != null, DocDistribute::getReceive, bo.getReceive());
        lqw.eq(bo.getLost() != null, DocDistribute::getLost, bo.getLost());
        lqw.eq(bo.getRecovery() != null, DocDistribute::getRecovery, bo.getRecovery());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), DocDistribute::getStatus, bo.getStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getType()), DocDistribute::getType, bo.getType());
        lqw.ne(StringUtils.isNotBlank(bo.getNeType()), DocDistribute::getType, bo.getNeType());
        lqw.and(StringUtils.isNotBlank(bo.getSearchValue()), queryWrapper -> {
            queryWrapper.like(DocDistribute::getCode, bo.getSearchValue())
                    .or().like(DocDistribute::getReceiveUserDept, bo.getSearchValue())
                    .or().like(DocDistribute::getDocName, bo.getSearchValue())
                    .or().like(DocDistribute::getDocId, bo.getSearchValue());
        });
        lqw.orderByAsc(DocDistribute::getCode);
        /**
         * 文件回收详情页面中，增加签收部门、签收时间条件
         */
        if (ObjectUtil.isNotEmpty(bo.getDeptIds()) && !bo.getDeptIds().isEmpty()) {
            lqw.in(DocDistribute::getReceiveUserDeptId, bo.getDeptIds());
        }
        if (ObjectUtil.isNotEmpty(bo.getStartTime()) && ObjectUtil.isNotEmpty(bo.getEndTime())) {
            lqw.ge(DocDistribute::getReceiveTime, bo.getStartTime());
            lqw.le(DocDistribute::getReceiveTime, bo.getEndTime());
        }
        lqw.in(bo.getIds() != null && bo.getIds().size() > 0, DocDistribute::getId, bo.getIds());
        return lqw;
    }

    @Override
    public Boolean insertByBo(DocDistributeBo bo) {
        DocDistribute add = BeanUtil.toBean(bo, DocDistribute.class);
        validEntityBeforeSave(add);
        boolean flag = save(add);
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public VersionDistributeDTO updateList(VersionDistributeDTO dto) {
        iVersionService.update(new LambdaUpdateWrapper<Version>().eq(Version::getId, dto.getVersionId())
                .set(Version::getDistributeType, dto.getDistributeType()));
        updateDistribute(dto.getCompanyDataList(), Constants.TYPE_COMPANY, dto.getVersionId());
        updateDistribute(dto.getDeptDataList(), Constants.TYPE_DEPT, dto.getVersionId());
        updateDistribute(dto.getPersonDataList(), Constants.TYPE_PERSON, dto.getVersionId());
        return dto;
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateDistribute(List<DocDistribute> list, String distributeType, String versionId) {
        List<DocDistribute> addList = new ArrayList<>();
        List<String> idList = new ArrayList<>();
        for (DocDistribute item : list) {
            if (ObjectUtil.isEmpty(item.getId())) {
                item.setType(distributeType);
                addList.add(item);
            } else {
                idList.add(item.getId());
            }
        }
        remove(new LambdaQueryWrapper<DocDistribute>().eq(DocDistribute::getVersionId, versionId)
                .eq(DocDistribute::getType, distributeType).notIn(idList.size() > 0, DocDistribute::getId, idList));
        saveBatch(addList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateCodeList(VersionDistributeDTO dto) {
        if (dto.getPrintDelList().size() > 0) {
            removeByIds(dto.getPrintDelList());
        }
        if (dto.getPrintDataList().size() > 0) {
            Integer code = baseMapper.getMaxCodeByVersionId(dto.getVersionId());
            for (DocDistribute item : dto.getPrintDataList()) {
                if (ObjectUtil.isEmpty(item.getId())) {
                    item.setCode(++code);
                    item.setType(Constants.DISTRIBUTE_TYPE_PRINT);
                }
            }
            saveBatch(dto.getPrintDataList());
        }
        return true;
    }

    @Override
    public Boolean updateByBo(DocDistributeBo bo) {
        DocDistribute update = BeanUtil.toBean(bo, DocDistribute.class);
        validEntityBeforeSave(update);
        return updateById(update);
    }

    /**
     * 保存前的数据校验
     *
     * @param entity 实体类数据
     */
    private void validEntityBeforeSave(DocDistribute entity) {
        // TODO 做一些数据校验,如唯一约束
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        if (isValid) {
            // TODO 做一些业务上的校验,判断是否需要校验
        }
        return removeByIds(ids);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handleDistribute(Version version, String docClass) {
        String distributeType = StringUtils.isEmpty(customConfig.getDistributeRangeType()) ? Constants.TYPE_DEPT
                : customConfig.getDistributeRangeType();
        List<DocDistribute> list = new ArrayList<>();
        if (Constants.TYPE_COMPANY.equals(distributeType)) {
            List<Tenant> tList = iTenantService.list();
            tList.forEach(item -> {
                DocDistribute distribute = new DocDistribute();
                distribute.setVersionId(version.getId());
                distribute.setDocId(version.getDocId());
                distribute.setDocClass(docClass);
                distribute.setDocName(version.getDocName());
                distribute.setReceiveUserDeptId(item.getId());
                distribute.setReceiveUserDept(item.getTenantName());
                distribute.setType(Constants.TYPE_DEPT);
                list.add(distribute);
            });
        } else if (Constants.TYPE_DEPT.equals(distributeType)) {
            DocDistribute distributeDept = new DocDistribute();
            distributeDept.setVersionId(version.getId());
            distributeDept.setDocId(version.getDocId());
            distributeDept.setDocClass(docClass);
            distributeDept.setDocName(version.getDocName());
            // String secDeptId = iSysDeptService.getSecDeptId(version.getDeptId());
            distributeDept.setReceiveUserDeptId(version.getDeptId());
            distributeDept.setReceiveUserDept(iSysDeptService.getDeptName(version.getDeptId()));
            distributeDept.setType(Constants.TYPE_DEPT);
            list.add(distributeDept);
        }
        saveBatch(list);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handleDistributeByApply(String applyId, String versionId, String docId, String docName,
            String docClass) {
        handleDistributeByApply(applyId, versionId, docId, docName, docClass, null, null);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handleDistributeByApply(String applyId, String versionId, String docId, String docName, String docClass,
            String oldType, String newType) {
        List<DocDistribute> list = new ArrayList<>();
        Integer code = baseMapper.getMaxCodeByVersionId(versionId);
        List<ModifyApplyDistribute> distributeList = iModifyApplyDistributeService
                .list(new LambdaQueryWrapper<ModifyApplyDistribute>().eq(ModifyApplyDistribute::getApplyId, applyId));
        List<SysUser> companyFileManagerList = iSysUserService.selectListAllByRoleKey("company_file_manager");
        for (ModifyApplyDistribute item : distributeList) {
            if (Constants.DISTRIBUTE_TYPE_PRINT.equals(item.getCategory())) {
                // 打印范围
                for (int i = 0; i < item.getNums(); i++) {
                    DocDistribute distribute = new DocDistribute();
                    distribute.setVersionId(versionId);
                    distribute.setDocId(docId);
                    distribute.setDocClass(docClass);
                    distribute.setDocName(docName);
                    distribute.setReceiveUserDeptId(item.getReceiveUserDeptId());
                    distribute.setReceiveUserDept(item.getReceiveUserDept());
                    distribute.setPrintPaperType(item.getPrintPaperType());
                    // 是公司或者部门 需要设置打印接收人是公司或者部门文件管理员
                    if (Constants.TYPE_COMPANY.equals(item.getType())) {
                        if (companyFileManagerList.size() > 0) {
                            distribute.setReceiveUserName(companyFileManagerList.get(0).getUserName());
                            distribute.setReceiveNickName(companyFileManagerList.get(0).getNickName());
                        }
                        // } else if (Constants.TYPE_DEPT.equals(item.getType())){
                        // List<SysUser> deptFileManagerList =
                        // iSysUserService.getFullPathRoleUserList("dept_file_manager",item.getReceiveUserDeptId());
                        // if (deptFileManagerList.size()>0) {
                        // distribute.setReceiveUserName(deptFileManagerList.get(0).getUserName());
                        // distribute.setReceiveNickName(deptFileManagerList.get(0).getNickName());
                        // }
                    } else {
                        distribute.setReceiveUserName(item.getReceiveUserName());
                        distribute.setReceiveNickName(item.getReceiveNickName());
                    }
                    distribute.setType(item.getCategory());
                    distribute.setCode(++code);
                    list.add(distribute);
                }
            } else {
                // 查看权限范围
                DocDistribute distribute = new DocDistribute();
                distribute.setVersionId(versionId);
                distribute.setDocId(docId);
                distribute.setDocClass(docClass);
                distribute.setDocName(docName);
                distribute.setReceiveUserName(item.getReceiveUserName());
                distribute.setReceiveNickName(item.getReceiveNickName());
                distribute.setReceiveUserDeptId(item.getReceiveUserDeptId());
                distribute.setReceiveUserDept(item.getReceiveUserDept());
                distribute.setType(item.getType());
                if (oldType == null) {
                    // 新设置的
                    list.add(distribute);
                } else {
                    // 增发设置的

                    // 以前不是公司 现在是公司 把以前非打印类型的删了，增加现在的
                    // 以前是公司 现在是公司 把没有的新增
                    // 以前不是公司 现在不是公司 把没有的新增
                    // 以前是公司 现在不是公司 不用新增
                    if (DistributeTypeEnum.company.name().equals(newType)
                            || !DistributeTypeEnum.company.name().equals(oldType)) {
                        long count = count(new LambdaQueryWrapper<DocDistribute>()
                                .eq(DocDistribute::getVersionId, versionId)
                                .eq(DocDistribute::getType, item.getType())
                                .eq(StringUtils.isNotEmpty(item.getReceiveUserName()),
                                        DocDistribute::getReceiveUserName, item.getReceiveUserName())
                                .eq(DocDistribute::getReceiveUserDeptId, item.getReceiveUserDeptId()));
                        if (count == 0) {
                            list.add(distribute);
                        }
                    }
                }
            }
        }
        // 以前不是公司 现在是公司 把以前非打印类型的删了，增加现在的
        if (!DistributeTypeEnum.company.name().equals(oldType) && DistributeTypeEnum.company.name().equals(newType)) {
            remove(new LambdaQueryWrapper<DocDistribute>().eq(DocDistribute::getVersionId, versionId)
                    .ne(DocDistribute::getType, Constants.DISTRIBUTE_TYPE_PRINT));
        }
        saveBatch(list);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void refreshDistribute(Version version,String docClass) {
        iVersionService.update(new LambdaUpdateWrapper<Version>().set(Version::getDistributeType, Constants.TYPE_DEPT)
                .eq(Version::getId, version.getId()));
        remove(new LambdaQueryWrapper<DocDistribute>().eq(DocDistribute::getVersionId, version.getId()));
        handleDistribute(version, docClass);
    }

    @Override
    public List<DocDistributeVo> getPrintGroupList(String versionId) {
        return baseMapper.getPrintGroupList(versionId);
    }

    @Override
    public Integer getMaxCodeByVersionId(String versionId) {
        return baseMapper.getMaxCodeByVersionId(versionId);
    }

    /**
     * 发送待回收邮件
     *
     * @param ids 分发记录Id
     */
    @Override
    public Boolean sendRecoveryEMail(Collection<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return true;
        }

        List<DocDistribute> list = listByIds(ids);
        if (CollectionUtils.isEmpty(list)) {
            return true;
        }

        list.forEach(this::sendNotifications);
        return true;
    }

    /**
     * 发送各类通知
     */
    private void sendNotifications(DocDistribute item) {
        Version version = iVersionService.getOne(new LambdaQueryWrapper<Version>()
                .eq(Version::getId, item.getVersionId())
                .select(Version::getDocName, Version::getDocId, Version::getId, Version::getVersionValue,
                        Version::getDeptId));
        if (ObjectUtil.isEmpty(version)) {
            throw new ServiceException("版本不存在");
        }
        String deptName = iSysDeptService.getDeptName(version.getDeptId());
        SysUser user = sysUserService.selectUserByUserName(item.getReceiveUserName());
        if (ObjectUtil.isEmpty(user)) {
            return;
        }
        String linkUrl = MsgTemplateUtils.buildPcFileDetailUrl(version.getDocId(), version.getId());
        String mobileUrl = MsgTemplateUtils.buildMobilFileDetailUrl(version.getDocId(), version.getId());
        Map<String, String> param = buildNotificationParams(version, item, deptName, linkUrl, mobileUrl);

        // 发送邮件
        try {
            sysNotifyLogService.sendEmail(SendType.HS.getCode(), user.getNickName(),
                    user.getEmail(), user.getUserId(), param);
        } catch (Exception e) {
            log.error("文件回收-发送邮件异常: ", e);
        }

        String msgContent = String.format(DocMessageEnum.FLOW_RECYCLE.getMsg(),
                StrUtil.isNotBlank(version.getDocName()) ? version.getDocName() : "",
                StrUtil.isNotBlank(version.getVersionValue()) ? version.getVersionValue() : "");

        // 发送站内信
        try {
            docMessageService.sendInstationMessage(null, version.getDocId(), item.getDocClass(),
                    version.getDocName(), version.getId(), version.getVersionValue(),
                    version.getDeptId(), deptName, user.getUserName(), user.getUserId(), msgContent,
                    DocMsgConstants.MSG_TYPE_MSG, MsgTypeEnum.RECEIVE.getType(), linkUrl, null,
                    SecurityUtils.getLoginUser().getTenantId());
        } catch (Exception e) {
            log.error("文件回收-发送站内信异常: ", e);
        }

        // 发送企业微信
        try {
            messageSendEntryService.sendMsgQywx(version.getDocId(), item.getId(),
                    user.getUserName(), msgContent);
        } catch (Exception e) {
            log.error("文件回收-发送企业微信异常: ", e);
        }
    }

    /**
     * 构建通知参数
     */
    private Map<String, String> buildNotificationParams(Version version, DocDistribute item, String deptName,
            String linkUrl, String mobileUrl) {
        Map<String, String> param = new HashMap<>();
        param.put("pcLink", linkUrl);
        param.put("mobileLink", mobileUrl);
        param.put("docName", item.getDocName());
        param.put("version", version.getVersionValue());
        param.put("deptName", deptName);
        param.put("number", "1");
        param.put("docCode", item.getDocId());
        return param;
    }

    /**
     * 站内消息
     **/
    private void sendInstationMessage(String sendType, Map<String, String> param, String userId) {
        String content = String.format("文件名称：%s,版本：%s,文件已失效，请于2个工作日内完成回收。",
                param.get("docName"), param.get("version"));

        // 当前登录用户
        LoginUser currUser = null;
        try {
            currUser = SecurityUtils.getLoginUser();
        } catch (Exception e) {
        }
        DocMessage docMessage = new DocMessage();
        docMessage.setVersionValue("default");
        // 消息状态 0=未读 1=已读
        docMessage.setMsgStatus(0);
        docMessage.setCreateTime(new Date());
        // 创建人
        docMessage.setCreateUserId(currUser != null ? currUser.getUserId() : "");
        docMessage.setCreateUser(currUser != null ? currUser.getUsername() : "");
        docMessage.setMsgClass(MsgTypeEnum.RECEIVE.getType());
        docMessage.setMsgInfo(content);
        // 接收人
        docMessage.setRecoveryUser(userId);
        docMessageService.save(docMessage);
    }

    @Override
    public List<DocDistributeVo> postQueryList(DocDistributeBo bo) {
        return docDistributeMapper.postQueryList(bo);
    }

    /**
     *
     * @param bo
     * @return
     */
    @Override
    public TableDataInfo<DocDistributeVo> trainDetailList(DocDistributeBo bo) {
        Page<DocDistributeVo> page = PageUtils.buildPage(bo.getPageNum(), bo.getPageSize(), bo.getOrderByColumn(),
                bo.getIsAsc());
        Page<DocDistributeVo> iPage = baseMapper.trainDetailList(page, bo);
        return PageUtils.buildDataInfo(iPage);
    }

    public List<DocDistributeVo> trainDetailStatusList(DocDistributeBo bo) {
        return baseMapper.trainDetailStatusList(bo);
    }

    /**
     * 通过fileId找到对应的文件分发人
     * @param id
     * @return
     */
    @Override
    public DocDistributeVo getPrintAuthById(String id,String fileId) {
        if(StringUtils.isEmpty(id) || StringUtils.isEmpty(fileId)){
            return null;
        }
        DocDistributeVo docDistributeVo = this.queryById(id);
        if(docDistributeVo!=null){
            //已分配打印权限
            if(Constants.VALUE_Y.equals(docDistributeVo.getPrintFlag())){
                //获取当前用户信息
                String username = SecurityUtils.getUsername();
                Set<String> strings = sysRoleService.selectRolePermissionByUserId(SecurityUtils.getUserId());
                //判断是不是当前登录人 且是没有打印过的
                if((username.equals(docDistributeVo.getReceiveUserName()) && strings.contains(Constants.DISTRIBUTE_TYPE_PRINT)) || "1".equals(SecurityUtils.getUserId())
                || strings.contains(RoleConstants.COMPANY_FILE_MANAGER)){
                    //判断打印记录是否有打印过的数据
                    List<PrintTask> printTasks = printTaskService.list(new LambdaQueryWrapper<PrintTask>()
                            .eq(PrintTask::getDocDistributeId, id)
                            .eq(PrintTask::getFileId, fileId)
                            .eq(PrintTask::getStatus, Constants.PATROL_TASK_STATUS_COMPLETED)
                    );
                    if(StringUtils.isEmpty(printTasks)){
                        //说明没有打印记录
                        VersionVo versionVo = iVersionService.getVoById(docDistributeVo.getVersionId());
                        docDistributeVo.setDocId(versionVo.getDocId());
                        docDistributeVo.setDocName(versionVo.getDocName());
                        docDistributeVo.setVersionId(versionVo.getVersionValue());
                        //匹配上的只会有一条数据
                        DocClassVo docClassVo = docClassService.queryById(docDistributeVo.getDocClass());
                        docDistributeVo.setClassName(docClassVo.getClassName());
                        docDistributeVo.setFileId(fileId);
                        return docDistributeVo;
                    }
                }
            }
        }
        return null;
    }

    /**
     * 预览打印找到对应的文件分发人 (批量打印专用)
     * @param id
     * @return
     */



    @Override
    public List<DocDistributeVo> getPrintAuthBatchById(String id, List<String> fileIds) {
        List<DocDistributeVo> docDistributeVoList=new ArrayList<>();
        if(StringUtils.isEmpty(id) || StringUtils.isEmpty(fileIds)){
            return docDistributeVoList;
        }
        DocDistributeVo docDistributeVo = this.queryById(id);

        if(docDistributeVo!=null){
            //已分配打印权限
            if(Constants.VALUE_Y.equals(docDistributeVo.getPrintFlag())){
                //获取当前用户信息
                String username = SecurityUtils.getUsername();
                Set<String> strings = sysRoleService.selectRolePermissionByUserId(SecurityUtils.getUserId());
                //判断是不是当前登录人 且是没有打印过的
                if((username.equals(docDistributeVo.getReceiveUserName()) && strings.contains(Constants.DISTRIBUTE_TYPE_PRINT)) || "1".equals(SecurityUtils.getUserId())
                        || strings.contains(RoleConstants.COMPANY_FILE_MANAGER)){
                    //判断打印记录是否有打印过的数据
                    List<PrintTask> printTasks = printTaskService.list(new LambdaQueryWrapper<PrintTask>()
                            .eq(PrintTask::getDocDistributeId, id)
                            .in(PrintTask::getFileId, fileIds)
                            .eq(PrintTask::getStatus, Constants.PATROL_TASK_STATUS_COMPLETED)
                    );
                    if(StringUtils.isEmpty(printTasks)){
                        VersionVo versionVo = iVersionService.getVoById(docDistributeVo.getVersionId());
                        docDistributeVo.setDocId(versionVo.getDocId());
                        docDistributeVo.setDocName(versionVo.getDocName());
                        docDistributeVo.setVersionId(versionVo.getVersionValue());
                        //匹配上的只会有一条数据
                        DocClassVo docClassVo = docClassService.queryById(docDistributeVo.getDocClass());
                        docDistributeVo.setClassName(docClassVo.getClassName());
                        //说明没有打印记录
                        for(String str:fileIds){
                            DocDistributeVo docDistributeVo1 = BeanUtil.copyProperties(docDistributeVo, DocDistributeVo.class);
                            docDistributeVo1.setFileId(str);
                            docDistributeVoList.add(docDistributeVo1);
                        }
                    }
                }
            }
        }

        return docDistributeVoList;
    }
}

package com.rzdata.process.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.rzdata.framework.utils.PageUtils;
import com.rzdata.framework.core.page.PagePlus;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.utils.SecurityUtils;
import com.rzdata.framework.utils.StringUtils;
import com.rzdata.process.domain.DocLinkLog;
import com.rzdata.process.domain.ModifyApplyLink;
import com.rzdata.process.enums.LinkTypeEnum;
import com.rzdata.process.mapper.ModifyApplyLinkMapper;
import org.springframework.stereotype.Service;
import com.rzdata.framework.core.mybatisplus.core.ServicePlusImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.rzdata.process.domain.bo.DocEditLogBo;
import com.rzdata.process.domain.vo.DocEditLogVo;
import com.rzdata.process.domain.DocEditLog;
import com.rzdata.process.mapper.DocEditLogMapper;
import com.rzdata.process.service.IDocEditLogService;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 文件编辑日志Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-03-03
 */
@Service
public class DocEditLogServiceImpl extends ServicePlusImpl<DocEditLogMapper, DocEditLog, DocEditLogVo> implements IDocEditLogService {

    @Resource
    private ModifyApplyLinkMapper modifyApplyLinkMapper;

    @Override
    public DocEditLogVo queryById(String id){
        return getVoById(id);
    }

    @Override
    public TableDataInfo<DocEditLogVo> queryPageList(DocEditLogBo bo) {
        PagePlus<DocEditLog, DocEditLogVo> result = pageVo(PageUtils.buildPagePlus(), buildQueryWrapper(bo));
        return PageUtils.buildDataInfo(result);
    }

    @Override
    public List<DocEditLogVo> queryList(DocEditLogBo bo) {
        return listVo(buildQueryWrapper(bo));
    }

    private LambdaQueryWrapper<DocEditLog> buildQueryWrapper(DocEditLogBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<DocEditLog> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getApplyId()), DocEditLog::getApplyId, bo.getApplyId());
        lqw.eq(StringUtils.isNotBlank(bo.getVersionId()), DocEditLog::getVersionId, bo.getVersionId());
        lqw.eq(StringUtils.isNotBlank(bo.getFileId()), DocEditLog::getFileId, bo.getFileId());
        lqw.eq(StringUtils.isNotBlank(bo.getProtoFileId()), DocEditLog::getProtoFileId, bo.getProtoFileId());
        lqw.eq(StringUtils.isNotBlank(bo.getType()), DocEditLog::getType, bo.getType());
        lqw.like(StringUtils.isNotBlank(bo.getActDefName()), DocEditLog::getActDefName, bo.getActDefName());
        lqw.like(StringUtils.isNotBlank(bo.getCreateName()), DocEditLog::getCreateName, bo.getCreateName());
        return lqw;
    }

    @Override
    public Boolean insertByBo(DocEditLogBo bo) {
        DocEditLog add = BeanUtil.toBean(bo, DocEditLog.class);
        validEntityBeforeSave(add);
        boolean flag = save(add);
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    @Override
    public Boolean updateByBo(DocEditLogBo bo) {
        DocEditLog update = BeanUtil.toBean(bo, DocEditLog.class);
        validEntityBeforeSave(update);
        return updateById(update);
    }

    /**
     * 保存前的数据校验
     *
     * @param entity 实体类数据
     */
    private void validEntityBeforeSave(DocEditLog entity){
        //TODO 做一些数据校验,如唯一约束
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return removeByIds(ids);
    }

    @Override
    public void saveLog(String applyId, String type, String actDefName) {
        List<ModifyApplyLink> linkList= modifyApplyLinkMapper.selectList(new LambdaQueryWrapper<ModifyApplyLink>()
                .eq(ModifyApplyLink::getApplyId,applyId)
                .in(ModifyApplyLink::getLinkType, LinkTypeEnum.DOC.name(), LinkTypeEnum.APPENDIX.name()));
        for (ModifyApplyLink modifyApplyLink : linkList) {
            if (count(new LambdaQueryWrapper<DocEditLog>()
                    .eq(DocEditLog::getApplyId,applyId)
                    .eq(DocEditLog::getProtoFileId,modifyApplyLink.getProtoFileId())
                    .eq(DocEditLog::getFileId,modifyApplyLink.getFileId())
                    .orderByDesc(DocEditLog::getCreateTime).last("limit 1"))<1){
                DocEditLog log = new DocEditLog();
                log.setApplyId(applyId);
                log.setFileId(modifyApplyLink.getFileId());
                log.setFileName(modifyApplyLink.getDocName());
                log.setType(type);
                log.setProtoFileId(modifyApplyLink.getProtoFileId());
                log.setActDefName(actDefName);
                log.setCreateName(SecurityUtils.getNickname());
                save(log);
            }
        }
    }

    @Override
    public void saveLogByVersionId(String versionId, String fileId,String fileName,String protoFileId, String type, String actDefName) {
        if (count(new LambdaQueryWrapper<DocEditLog>()
                .eq(DocEditLog::getVersionId,versionId)
                .eq(DocEditLog::getProtoFileId,protoFileId)
                .eq(DocEditLog::getFileId,fileId)
                .orderByDesc(DocEditLog::getCreateTime).last("limit 1"))<1){
            DocEditLog log = new DocEditLog();
            log.setVersionId(versionId);
            log.setFileId(fileId);
            log.setFileName(fileName);
            log.setType(type);
            log.setProtoFileId(protoFileId);
            log.setActDefName(actDefName);
            log.setCreateName(SecurityUtils.getNickname());
            save(log);
        }
    }


    @Override
    public void handleEditLog(String applyId,String versionId) {
        update(new LambdaUpdateWrapper<DocEditLog>().set(DocEditLog::getVersionId,versionId).eq(DocEditLog::getApplyId,applyId));
    }
}

package com.rzdata.process.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ZipUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rzdata.framework.constant.CommonI18nConstant;
import com.rzdata.framework.constant.Constants;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.core.domain.entity.SysDept;
import com.rzdata.framework.core.domain.model.LoginUser;
import com.rzdata.framework.core.mybatisplus.core.ServicePlusImpl;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.core.service.ConfigService;
import com.rzdata.framework.utils.I18nUtils;
import com.rzdata.framework.utils.PageUtils;
import com.rzdata.framework.utils.SecurityUtils;
import com.rzdata.framework.utils.StringUtils;
import com.rzdata.process.domain.*;
import com.rzdata.process.domain.bo.DocLinkLogBo;
import com.rzdata.process.domain.dto.FileDTO;
import com.rzdata.process.domain.vo.*;
import com.rzdata.process.enums.LinkTypeEnum;
import com.rzdata.process.mapper.DocLinkLogMapper;
import com.rzdata.process.mapper.StandardMapper;
import com.rzdata.process.mapper.VersionMapper;
import com.rzdata.process.service.*;
import com.rzdata.setting.service.ICodeRuleLogService;
import com.rzdata.setting.service.ICodeRuleService;
import com.rzdata.system.service.ISysDeptService;
import com.rzdata.system.service.ISysUserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.BufferedInputStream;
import java.io.File;
import java.io.OutputStream;
import java.rmi.ServerException;
import java.util.Collection;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 文件关联记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-01-06
 */
@Service
@Slf4j
public class DocLinkLogServiceImpl extends ServicePlusImpl<DocLinkLogMapper, DocLinkLog, DocLinkLogVo> implements IDocLinkLogService {

    @Autowired
    VersionMapper versionMapper;

    StandardMapper standardMapper;

    @Autowired
    IGenerateIdService iGenerateIdService;

    @Autowired
    private ICodeRuleService iCodeRuleService;

    @Autowired
    ICodeRuleLogService iCodeRuleLogService;

    @Autowired
    IDocVersionLinkService iDocVersionLinkService;

    @Autowired
    ISysDeptService sysDeptService;

    @Autowired
    ISysUserService sysUserService;

    @Autowired
    private ConfigService configService;

    @Autowired
    private IBorrowApplyItemService iBorrowApplyItemService;

    @Autowired
    IBasicFilePdfService basicFilePdfService;

    @Autowired
    IVersionService versionService;

    @Autowired
    IBasicFileService basicFileService;

    @Autowired
    private IStoreFileService iStoreFileService;

    @Override
    public DocLinkLogVo queryById(String id){
        return getVoById(id);
    }

    @Override
    public TableDataInfo<DocLinkLogVo> queryPageList(DocLinkLogBo bo) {
        Page<DocLinkLogVo> page = new Page<>(bo.getPageNum(), bo.getPageSize());
        Page<DocLinkLogVo> result = this.baseMapper.getPageList(page, bo);
//        PagePlus<DocLinkLog, DocLinkLogVo> result = pageVo(PageUtils.buildPagePlus(), buildQueryWrapper(bo));
        return PageUtils.buildDataInfo(result);
    }

    @Override
    public List<DocLinkLogVo> queryList(DocLinkLogBo bo) {
        return listVo(buildQueryWrapper(bo));
    }

    private LambdaQueryWrapper<DocLinkLog> buildQueryWrapper(DocLinkLogBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<DocLinkLog> lqw = Wrappers.lambdaQuery();
        //lqw.eq(StringUtils.isNotBlank(bo.getDocId()), DocLinkLog::getDocId, bo.getDocId());
        //lqw.eq(StringUtils.isNotBlank(bo.getStandardId()), DocLinkLog::getStandardId, bo.getStandardId());
        lqw.eq(StringUtils.isNotBlank(bo.getLinkType()),DocLinkLog::getLinkType,bo.getLinkType());
        //lqw.eq(StringUtils.isNotBlank(bo.getVersionId()), DocLinkLog::getVersionId, bo.getVersionId());
        return lqw;
    }

    @Override
    public Boolean insertByBo(DocLinkLogBo bo) {
        DocLinkLog add = BeanUtil.toBean(bo, DocLinkLog.class);
        validEntityBeforeSave(add);
        boolean flag = save(add);
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateByBo(DocLinkLogBo bo) throws ServerException {
        DocLinkLog update = BeanUtil.toBean(bo, DocLinkLog.class);
        validEntityBeforeSave(update);
        return updateById(update);
    }

    /**
     * 保存前的数据校验
     *
     * @param entity 实体类数据
     */
    private void validEntityBeforeSave(DocLinkLog entity){
        //TODO 做一些数据校验,如唯一约束
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return removeByIds(ids);
    }

    public List<DocLinkLog> queryLink(DocLinkLogBo bo) {
        return baseMapper.queryLink(bo);
    }

    @Override
    public List<DocLinkLogVo> queryDocLinkVo(String versionId, String linkType) {
        return this.baseMapper.queryDocLinkVo(versionId,linkType);
    }

    @Override
    public int getLinkCount(DocLinkLogBo bo) {
        return baseMapper.getLinkCount(bo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteById(String id) {
        DocLinkLog linkLog = getById(id);
        // 删除关联link日志
        DocVersionLink link = iDocVersionLinkService.getOne(new LambdaQueryWrapper<DocVersionLink>().eq(DocVersionLink::getLinkId,id));
        // 释放关联记录台账
        if(LinkTypeEnum.RECORD.name().equals(linkLog.getLinkType())) {
            // 更新被关联记录文件版本的上级文件版本ID和对应的文件台账编号为NULL
            versionMapper.update(null,new LambdaUpdateWrapper<Version>()
                    .eq(Version::getId,linkLog.getVersionId())
                    .set(Version::getUpVersionId,"")
                    .set(Version::getParentDocId,""));
        }else if (LinkTypeEnum.NOTE.name().equals(linkLog.getLinkType())) {
            //记录文件多对多 相对的也删掉
            deleteLinkLog(LinkTypeEnum.NOTE_DOC.name(),link.getVersionId(),linkLog.getVersionId());
        }
        // 删除关联记录
        iDocVersionLinkService.removeById(link.getId());
        return removeById(id);
    }

    @Override
    public boolean deleteLinkLog(String linkType, String versionId, String pVersionId){
        DocLinkLogBo bo = new DocLinkLogBo();
        bo.setPVersionId(pVersionId);
        bo.setLinkType(linkType);
        bo.setVersionId(versionId);
        List<DocLinkLog> dllList = queryLink(bo);
        if (dllList!=null&&dllList.size()>0) {
           List<String> idList = dllList.stream().map(DocLinkLog::getId).collect(Collectors.toList());
           removeByIds(idList);
           iDocVersionLinkService.remove(new LambdaQueryWrapper<DocVersionLink>().in(DocVersionLink::getLinkId,idList));
        }
        return true;
    }

    /**
     * 初始化关联记录到记录台账(用于初始化工作)
     *
     * @param dataType
     * @param docClass
     * @param standardId
     * @param versionId
     * @param linkCode
     * @return
     */
    @Override
    public AjaxResult init2Account(String dataType, String docClass, String standardId, String versionId, String linkCode) {
        return null;
    }


    @Override
    public Boolean disuseValidLinkLog(String versionId, String linkCode) {
        return baseMapper.disuseValidLinkLog(versionId,linkCode);
    }

    @Override
    public List<DocLinkLogVo> queryInit2Account(String dataType, String docClass, String standardId, String versionId, String linkCode) {
        return this.baseMapper.queryInit2Account(dataType,docClass,standardId,versionId,linkCode);
    }


    @Transactional(rollbackFor = Exception.class)
    public AjaxResult init2AccountByLinkLog(Standard sourceStdd,Version currVersion, DocLinkLog logObj) {
        try {
            // 转文件台账并保存
            QueryWrapper<Standard> stddQuery = new QueryWrapper<>();
            stddQuery.eq("remark",logObj.getLinkCode());
            Standard dbStdd = standardMapper.selectOne(stddQuery);
            if(dbStdd == null) {
                // 不存在则新建文件台账
                dbStdd= this.record2Stdd(sourceStdd,logObj);
                standardMapper.insert(dbStdd);
            } else {
                dbStdd.setCurrentVersion(logObj.getVersionValue());
                standardMapper.updateById(dbStdd);
            }
            // 转文件版本并保存
            Version newVersion = this.record2version(currVersion,logObj,dbStdd);
            versionMapper.insert(newVersion);

            // 保存附件关联日志-主文件
            DocLinkLog newLogObj = new DocLinkLog();
            BeanUtils.copyProperties(logObj,newLogObj);
            newLogObj.setId(null);
            newLogObj.setLinkType("DOC");
            newLogObj.setCreateTime(new Date());
            newLogObj.setUpdateTime(new Date());
            newLogObj.setTenantId("init2Account");
            this.save(newLogObj);
            // 保存文件版本与附件关联过渡表记录
            DocVersionLink newLinkObj = new DocVersionLink();
            newLinkObj.setVersionId(newVersion.getId());
            newLinkObj.setLinkId(newLogObj.getId());
            newLinkObj.setTenantId("init2Account");
            this.iDocVersionLinkService.save(newLinkObj);
            // 标识当前关联记录为已经转化为台账
            logObj.setTenantId("init2Account");
            this.updateById(logObj);
            return AjaxResult.success();
        } catch (Exception e) {
            log.error("init2AccountByLinkLog异常",e);
            return AjaxResult.error(ExceptionUtil.stacktraceToString(e));
        }
    }

    @Override
    public DocLinkLog handApplyLinkAdd(ModifyApplyLink applyLink, String linkType, String versionId) {
        DocLinkLog docLinkLog=new DocLinkLog();
        docLinkLog.setLinkCode(applyLink.getDocId());
        docLinkLog.setFileName(applyLink.getDocName());
        docLinkLog.setFileId(applyLink.getFileId());
        docLinkLog.setVersionValue(applyLink.getVersionValue());
        docLinkLog.setDocClass(applyLink.getDocClass());
        docLinkLog.setVersionId(applyLink.getVersionId());
        docLinkLog.setLinkType(linkType);
        docLinkLog.setStatus(Constants.ONE);
        docLinkLog.setStartDate(applyLink.getStartDate());
        docLinkLog.setReleaseTime(applyLink.getReleaseTime());
        docLinkLog.setProtoFileId(applyLink.getProtoFileId());
        save(docLinkLog);
        //插入关联记录表
        DocVersionLink docVersionLink = new DocVersionLink();
        docVersionLink.setLinkId(docLinkLog.getId());
        docVersionLink.setVersionId(versionId);
        iDocVersionLinkService.save(docVersionLink);
        return docLinkLog;
    }

    @Override
    public DocLinkLog handApplyLinkAdd(Version version,String docClass, String linkType, String versionId, String protoFileId) {
        DocLinkLog docLinkLog=new DocLinkLog();
        docLinkLog.setLinkCode(version.getDocId());
        docLinkLog.setFileName(version.getDocName());
        docLinkLog.setFileId(version.getFileId());
        docLinkLog.setVersionValue(version.getVersionValue());
        docLinkLog.setDocClass(docClass);
        docLinkLog.setVersionId(version.getId());
        docLinkLog.setLinkType(linkType);
        docLinkLog.setStatus(Constants.ONE);
        docLinkLog.setStartDate(version.getStartDate());
        docLinkLog.setReleaseTime(version.getReleaseTime());
        docLinkLog.setProtoFileId(protoFileId);
        save(docLinkLog);
        //插入关联记录表
        DocVersionLink docVersionLink = new DocVersionLink();
        docVersionLink.setLinkId(docLinkLog.getId());
        docVersionLink.setVersionId(versionId);
        iDocVersionLinkService.save(docVersionLink);
        return docLinkLog;
    }

    /**
     * @param bo bo
     * @description 首页查询界面，分页条件查询
     * <AUTHOR>
     * @updateTime 2024/11/19
     */
    @Override
    public TableDataInfo<DocLinkSearchResultVo> docLinkSearchPage(DocLinkLogBo bo) {
        Page<DocLinkSearchResultVo> page = new Page<>(bo.getPageNum(), bo.getPageSize());
        Page<DocLinkSearchResultVo> result = this.baseMapper.docLinkSearchPage(page, bo);
        return PageUtils.buildDataInfo(docLinkSearchDataBuild(result));
    }


    /**
     * 转文件台账对象（用于初始化工作）
     *
     * @param sourceStdd
     * @param logObj
     * @return
     */
    private Standard record2Stdd(Standard sourceStdd, DocLinkLog logObj) {
        Standard newStdd = new Standard();
        // 利用租户ID来标识初始化转化
        newStdd.setTenantId("init2Account");
        newStdd.setId(logObj.getId());
        newStdd.setDocClass(logObj.getDocClass());
        // 去掉扩展名
        newStdd.setDocName(logObj.getFileName());
        // 文件状态 1有效 2失效 logObj.getStatus()
        newStdd.setStatus("1");
        // 版本号
        newStdd.setCurrentVersion(logObj.getVersionValue());
        newStdd.setFileId(logObj.getFileId());
        newStdd.setChangeType("ADD");
        newStdd.setDataType(sourceStdd.getDataType());
        // 所属项目
        newStdd.setProjectId(sourceStdd.getProjectId());
        newStdd.setProjectName(sourceStdd.getProjectName());
        // 临时记录文件编号
        newStdd.setRemark(logObj.getLinkCode());
        return newStdd;
    }

    /**
     * 转文件版本对象（用于初始化工作）
     *
     * @param logObj
     * @param exchangeStdd
     * @return
     */
    private Version record2version(Version upVersion,DocLinkLog logObj,Standard exchangeStdd) {
        Version newVersion = new Version();
        // 利用租户ID来标识初始化转化
        newVersion.setTenantId("init2Account");
        newVersion.setStandardId(exchangeStdd.getId());
        newVersion.setDocId(logObj.getLinkCode());
        newVersion.setVersionValue(logObj.getVersionValue());
        newVersion.setStartDate(logObj.getStartDate());
        newVersion.setEndDate(logObj.getEndDate());
        newVersion.setStatus(logObj.getStatus());
        newVersion.setReason(exchangeStdd.getChangeReason());
        newVersion.setContent(newVersion.getReason());
        newVersion.setChangeReason(newVersion.getReason());
        // 编制人和编制部门
        newVersion.setUserName(upVersion.getUserName());
        newVersion.setDeptId(upVersion.getDeptId());
        // 编制时间
        newVersion.setApplyTime(logObj.getStartDate());
        // 文件版本附件
        newVersion.setFileId(logObj.getFileId());
        newVersion.setUpdateTime(new Date());
        newVersion.setForever("1");
        newVersion.setDataType(exchangeStdd.getDataType());
        newVersion.setReleaseTime(logObj.getReleaseTime());
        // 上级文件版本主键、上级文件文件编号
        newVersion.setUpVersionId(upVersion.getId());
        newVersion.setParentDocId(upVersion.getDocId());
        // 分类所属类型
        newVersion.setClassType("RECORD");
        return newVersion;
    }

    /**
     * @param result result
     * @description 构建首页查询结果，添加权限控制
     * <AUTHOR>
     * @updateTime 2024/11/19
     */
    @Override
    public Page<DocLinkSearchResultVo> docLinkSearchDataBuild(Page<DocLinkSearchResultVo> result) {
        LoginUser user = SecurityUtils.getLoginUser();
        boolean isQa = sysUserService.isQa(user.getUsername());
        List<String> linkTypeList = new ArrayList<>();
        linkTypeList.add(LinkTypeEnum.RECORD.name());
        linkTypeList.add(LinkTypeEnum.NOTE.name());
        linkTypeList.add(LinkTypeEnum.REF_DOC.name());
        linkTypeList.add(LinkTypeEnum.NOTE_DOC.name());
        for (DocLinkSearchResultVo docLinkSearchResultVo : result.getRecords()) {
            // 查询是否具备关联记录和文件
            List<DocLinkSearchResultVo> docLinkSearchItems = this.baseMapper.docLinkSearchItems(docLinkSearchResultVo.getVersionId(),linkTypeList);
            if (docLinkSearchItems.size() > 0) {
                docLinkSearchResultVo.setHasChild("0");
            } else {
                docLinkSearchResultVo.setHasChild("1");
            }
            // 查询部门名称
            SysDept sysDept = sysDeptService.selectDeptById(docLinkSearchResultVo.getDeptId());
            docLinkSearchResultVo.setDeptName(null != sysDept ? sysDept.getDeptName() : "");
            // 查询是否有查阅权限或借阅权限
            checkPerms(docLinkSearchResultVo, isQa, user);
        }
        return result;
    }

    /**
     * @param versionIds versionIds
     * @description 查询主文件的关联文件和关联记录
     * <AUTHOR>
     * @updateTime 2024/11/19
     */
    @Override
    public List<DocLinkSearchDetailVo> docLinkSearchDetailList(Collection<String> versionIds) {
        List<DocLinkSearchDetailVo> docLinkSearchDetailVoList = new ArrayList<>();
        List<String> linkTypeList = new ArrayList<>();
        linkTypeList.add(LinkTypeEnum.RECORD.name());
        linkTypeList.add(LinkTypeEnum.NOTE_DOC.name());
        linkTypeList.add(LinkTypeEnum.NOTE.name());
        linkTypeList.add(LinkTypeEnum.REF_DOC.name());
        for (String versionId : versionIds) {
            DocLinkSearchDetailVo detailVo = new DocLinkSearchDetailVo();
            detailVo.setVersionId(versionId);
            List<DocLinkSearchResultVo> docLinkSearchItems = this.baseMapper.docLinkSearchItems(versionId,linkTypeList);
            // 查询关联记录文件
            List<DocLinkSearchResultVo> recordList = docLinkSearchItems.stream().filter(item -> item.getLinkType().equals(LinkTypeEnum.NOTE.name())||item.getLinkType().equals(LinkTypeEnum.RECORD.name())).collect(Collectors.toList());
            detailVo.setRecordList(recordList);
            // 查询关联文件
            List<DocLinkSearchResultVo> refDocList = docLinkSearchItems.stream().filter(item -> item.getLinkType().equals(LinkTypeEnum.REF_DOC.name())).collect(Collectors.toList());
            detailVo.setRefDocList(refDocList);
            // 查询关联文件的主文件
            List<DocLinkSearchResultVo> noteDocList = docLinkSearchItems.stream().filter(item -> item.getLinkType().equals(LinkTypeEnum.NOTE_DOC.name())).collect(Collectors.toList());
            detailVo.setNoteDocList(noteDocList);
            // 主文件关联记录与文件的查询结果集合
            docLinkSearchDetailVoList.add(detailVo);
        }
        return docLinkSearchDetailVoList;
    }

    /**
     * @param docLinkSearchResultVo docLinkSearchResultVo
     * @param isQa                  isQa
     * @param user                  user
     * @description 检查当前用户是否具备查阅权限或者借阅权限
     * <AUTHOR>
     * @updateTime 2024/11/20
     */
    private void checkPerms(DocLinkSearchResultVo docLinkSearchResultVo, boolean isQa, LoginUser user) {
        if (isQa || SecurityUtils.isAdmin(SecurityUtils.getUserId()) || user.getUsername().equals(docLinkSearchResultVo.getCreateBy()) || user.getUsername().equals(docLinkSearchResultVo.getUserName()) || user.getAncestors().contains(docLinkSearchResultVo.getDeptId())
        ) {
            //是编制部门的 或者 文件分发类型是公司的 或者是公司文件管理员 有权限查看 或者 自己发起的文件自己也能查看
            docLinkSearchResultVo.setHasPerms(true);
        } else {
            docLinkSearchResultVo.setHasPerms(versionMapper.checkAuthByDis(docLinkSearchResultVo.getVersionId(), user.getUsername(), user.getDeptId()) > 0);
        }
        // 判断是否具备借阅权限
        if (!docLinkSearchResultVo.getHasPerms()) {
            docLinkSearchResultVo.setIsBorrow(iBorrowApplyItemService.isValidBorrowByVersionId(docLinkSearchResultVo.getVersionId(), user.getUsername()));
        }
    }

    /**
     * @param versionIds versionIds
     * @param response   response
     * @description 首页搜索下载，批量打包形成压缩包
     * <AUTHOR>
     * @updateTime 2024/11/19
     */
    @Override
    public void docLinkSearchDownload(Collection<String> versionIds, HttpServletResponse response) {
        LoginUser user = SecurityUtils.getLoginUser();
        boolean isQa = sysUserService.isQa(user.getUsername());
        List<String> linkTypeList = new ArrayList<>();
        linkTypeList.add(LinkTypeEnum.DOC.name());
        String storePath = configService.getConfigValue(FileLocalStoreConfig.KEY_LOCAL_STORE_PATH);
        String zipName = System.currentTimeMillis() + ".zip";
        // 当前下载临时处理目录
        String currentDownloadPath = File.separator + "searchDownloadTemp" + File.separator + System.currentTimeMillis();
        // 当前下载临时处理压缩包目录
        String searchDownloadZipPath = currentDownloadPath + File.separator + "zipTemp";
        try {
            List<String> filePaths = new ArrayList<>();
            for (String versionId : versionIds) {
                VersionVo versionVo = versionService.getVoById(versionId);
                versionService.checkPerms(versionVo, isQa, user);
                if (null == versionVo) {
                    continue;
                }
                List<FileDTO> fileItem = new ArrayList<>();
                String zipItemName = versionVo.getDocName() + ".zip";
                // 获取主文件关联的文件（包括关联文件、关联记录）
                List<DocLinkSearchResultVo> docLinkSearchItems = this.baseMapper.docLinkSearchItems(versionId,
                        BooleanUtil.isFalse(versionVo.getHasPerms())&&BooleanUtil.isTrue(versionVo.getIsBorrow())?linkTypeList:null);
                if (null != docLinkSearchItems && docLinkSearchItems.size() > 0) {
                    for (DocLinkSearchResultVo item : docLinkSearchItems) {
                        FileDTO dto = getSearchDownloadPdfPath(item.getFileId());
                        if (dto!=null) {
                            if (LinkTypeEnum.REF_DOC.name().equals(item.getLinkType())) {
                                dto.setFileName(I18nUtils.getTitle(CommonI18nConstant.FILE_LINK_TYPE+LinkTypeEnum.REF_DOC.name())+File.separator+item.getDocName()+File.separator+dto.getFileName());
                            }else if (LinkTypeEnum.RECORD.name().equals(item.getLinkType())||LinkTypeEnum.NOTE.name().equals(item.getLinkType())){
                                dto.setFileName(I18nUtils.getTitle(CommonI18nConstant.FILE_LINK_TYPE+LinkTypeEnum.RECORD.name())+File.separator+item.getDocName()+File.separator+dto.getFileName());
                            }else if (LinkTypeEnum.NOTE_DOC.name().equals(item.getLinkType())){
                                dto.setFileName(I18nUtils.getTitle(CommonI18nConstant.FILE_LINK_TYPE+LinkTypeEnum.NOTE_DOC.name())+File.separator+item.getDocName()+File.separator+dto.getFileName());
                            }else if (LinkTypeEnum.APPENDIX.name().equals(item.getLinkType())){
                                dto.setFileName(I18nUtils.getTitle(CommonI18nConstant.FILE_LINK_TYPE+LinkTypeEnum.APPENDIX.name())+File.separator+dto.getFileName());
                            }
                            fileItem.add(dto);
                        } else {
                            log.error("获取文件流异常:{}",item);
                        }
                    }
                }

                // 各主文件及其关联文件打包后压缩包路径
                String zipItemPath = storePath + searchDownloadZipPath + File.separator + zipItemName;
                if (fileItem.size()>0) {
                    ZipUtil.zip(FileUtil.file(zipItemPath),
                            fileItem.stream().map(FileDTO::getFileName).toArray(String[]::new),
                            fileItem.stream().map(FileDTO::getInputStream).toArray(BufferedInputStream[]::new));
                    filePaths.add(zipItemPath);
                }
            }

            File[] fileArray = filePaths.stream().map(FileUtil::file).toArray(File[]::new);
            String zipPath = storePath + searchDownloadZipPath + File.separator + zipName;
            ZipUtil.zip(FileUtil.file(zipPath), false, fileArray);


            File file = new File(zipPath);
            BufferedInputStream inputStream = null;
            if (file.exists()) {
                inputStream = FileUtil.getInputStream(file);
                inputStream.mark(inputStream.available() + 1);

                response.setContentType("application/octet-stream");
                response.setHeader("content-type", "application/octet-stream");
                response.setHeader("Content-Disposition", "attachment;fileName=" + zipName);
                OutputStream outputStream = response.getOutputStream();
                IoUtil.copy(inputStream, outputStream);
                IoUtil.close(inputStream);
                IoUtil.close(outputStream);
            }
        } catch (Exception e) {
            log.error("下载压缩包异常：" + e.getMessage(), e);
        } finally {
            FileUtil.del(storePath + currentDownloadPath);
        }
    }


    /**
     * @param fileId              fileId
     * @description 获取待下载pdf文件路径
     * <AUTHOR>
     * @updateTime 2024/11/19
     */
    private FileDTO getSearchDownloadPdfPath(String fileId) {
        BasicFileVo originalBasicFileVo = basicFileService.getVoById(fileId);
        BasicFile pdfBasicFile = basicFilePdfService.getPdfFilePathByFileId(fileId, "signature");
        try {
            // 获取实际pdf文件
            if (null != pdfBasicFile) {
                BufferedInputStream inputStream = iStoreFileService.getInputStream(pdfBasicFile);
                if (null!=inputStream) {
                    FileDTO dto = new FileDTO();
                    dto.setInputStream(inputStream);
                    dto.setFileName(null != originalBasicFileVo?FileUtil.getPrefix(originalBasicFileVo.getFileName())+".pdf":pdfBasicFile.getFileName());
                    return dto;
                }
            }
        } catch (Exception e) {
            log.error("获取待下载pdf文件路径异常：" + e.getMessage(), e);
        }
        return null;
    }

    private String fileNameCheck(List<File> fileItem,String fileName,int i){
        if (fileItem.stream().anyMatch(item->fileName.equals(FileUtil.getPrefix(item)))){
            return fileNameCheck(fileItem,fileName+"("+i+")",i++);
        }else {
            return fileName;
        }
    }

}

package com.rzdata.process.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.rzdata.config.CustomConfig;
import com.rzdata.eventbus.ProcessResultEvent;
import com.rzdata.framework.constant.CommonI18nConstant;
import com.rzdata.framework.constant.Constants;
import com.rzdata.framework.constant.DocMsgConstants;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.core.domain.entity.SysDept;
import com.rzdata.framework.core.domain.entity.SysUser;
import com.rzdata.framework.core.domain.model.LoginUser;
import com.rzdata.framework.core.mybatisplus.core.ServicePlusImpl;
import com.rzdata.framework.core.page.PagePlus;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.utils.I18nUtils;
import com.rzdata.framework.utils.PageUtils;
import com.rzdata.framework.utils.SecurityUtils;
import com.rzdata.framework.utils.StringUtils;
import com.rzdata.plugins.mail.IDocStatusMailService;
import com.rzdata.plugins.mail.impl.ReviewSingleNoticeMailService;
import com.rzdata.plugins.msg.BizMsgDto;
import com.rzdata.plugins.msg.MsgService;
import com.rzdata.process.domain.*;
import com.rzdata.process.domain.bo.DocMessageBo;
import com.rzdata.process.domain.bo.ReviewApplyItemBo;
import com.rzdata.process.domain.vo.BorrowApplyItemVo;
import com.rzdata.process.domain.vo.DocMessageVo;
import com.rzdata.process.domain.vo.ReviewApplyItemVo;
import com.rzdata.process.domain.vo.WorkflowLogVo;
import com.rzdata.process.enums.ApplyStatusEnum;
import com.rzdata.process.enums.MsgTypeEnum;
import com.rzdata.process.mapper.DocMessageMapper;
import com.rzdata.process.mapper.VersionMapper;
import com.rzdata.process.service.*;
import com.rzdata.process.utils.MsgTemplateUtils;
import com.rzdata.setting.service.IDocClassService;
import com.rzdata.system.service.ISysDeptService;
import com.rzdata.system.service.ISysUserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.rmi.ServerException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2022/2/11 11:04
 * @Version 1.0
 * @Description 消息服务实现类
 */
@Slf4j
@Service
public class DocMessageServiceImpl extends ServicePlusImpl<DocMessageMapper, DocMessage, DocMessageVo>
        implements IDocMessageService {

    @Autowired
    IWorkflowApplyLogService workflowApplyLogService;

    @Autowired
    IBorrowApplyItemService borrowApplyItemService;

    @Autowired
    ISysUserService iSysUserService;

    @Autowired
    ISysUserService sysUserService;

    @Autowired
    VersionMapper versionMapper;

    @Autowired
    ISysDeptService deptService;

    @Autowired
    IDocClassService iDocClassService;

    @Autowired
    IReviewApplyItemService applyItemService;

    @Resource
    IDocStatusMailService docStatusMailService;

    @Resource
    ReviewSingleNoticeMailService reviewSingleNoticeMailService;

    @Resource
    CustomConfig customConfig;

    @Autowired
    private IWorkflowLogService workflowLogService;
    @Resource
    MsgService msgService;

    @Override
    public TableDataInfo<DocMessageVo> queryListByPage(DocMessageBo bo) {
        PagePlus<DocMessage, DocMessageVo> page = PageUtils.buildPagePlus(bo.getPageNum(), bo.getPageSize());
        PagePlus<DocMessage, DocMessageVo> result = pageVo(page, buildQueryWrapper(bo));
        return PageUtils.buildDataInfo(result);
    }

    @Override
    public int getUnreadNum(DocMessageBo bo) {
        LambdaQueryWrapper<DocMessage> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(DocMessage::getRecoveryUser, SecurityUtils.getLoginUser().getUsername());
        queryWrapper.eq(StringUtils.isNotBlank(bo.getRecoveryUserId()), DocMessage::getRecoveryUserId,
                bo.getRecoveryUserId());
        queryWrapper.eq(StringUtils.isNotNull(bo.getMsgType()), DocMessage::getMsgType, bo.getMsgType());
        queryWrapper.eq(DocMessage::getMsgStatus, 0);
        List<DocMessageVo> docMessageVos = listVo(queryWrapper);
        return docMessageVos.size();
    }

    @Override
    public void insertMessage(ProcessResultEvent event, MsgTypeEnum msgType) {
        DocMessage docMessage = new DocMessage();
        try {
            insertMessage(event, docMessage, msgType);
        } catch (ServerException exception) {
            exception.printStackTrace();
        }
    }

    @Override
    public void insertMessageByBorrow(ProcessResultEvent event, MsgTypeEnum msgType) {
        try {
            DocMessage docMessage = new DocMessage();
            insertMessage(event, docMessage, msgType);
            // 如果为借阅申请 且申请结果为通过时
            if (ApplyStatusEnum.PASS.getCode().equals(event.getApplyStatus())) {
                DocMessage newDocMessage = docMessage;
                // newDocMessage.setId(null);
                LambdaQueryWrapper<BorrowApplyItem> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(BorrowApplyItem::getApplyId, event.getApplyId());
                List<BorrowApplyItemVo> list = borrowApplyItemService.listVo(queryWrapper);
                list = list.stream()
                        .filter(i -> !StringUtils.equals(i.getBorrowUserName(), docMessage.getRecoveryUser()))
                        .collect(Collectors.toList());
                list.forEach(borrowApplyItemVo -> {
                    // 下一流程 也就是借阅人也需要生成一条消息
                    newDocMessage.setRecoveryUser(borrowApplyItemVo.getBorrowUserName());
                    newDocMessage.setId(null);
                    this.insert(newDocMessage);
                });
            }
        } catch (Exception exception) {
            exception.printStackTrace();
        }
    }

    private void insertMessage(ProcessResultEvent event, DocMessage docMessage, MsgTypeEnum msgType)
            throws ServerException {
        String applyId = event.getApplyId();
        WorkflowApplyLog workflowApplyLog = workflowApplyLogService.getById(applyId);
        if (workflowApplyLog == null) {
            throw new ServerException(I18nUtils.getTitle(CommonI18nConstant.FILE_DOC_MESSAGE_NOT_FOUND_FLOW));
        }
        docMessage.setApplyId(applyId);
        docMessage.setDocId(workflowApplyLog.getDocId());
        docMessage.setDocName(workflowApplyLog.getDocName());
        if (ObjectUtil.isNotEmpty(workflowApplyLog.getVersionId())) {
            docMessage.setVersionId(workflowApplyLog.getVersionId());
        }
        if (ObjectUtil.isNotEmpty(workflowApplyLog.getVersionValue())) {
            docMessage.setVersionValue(workflowApplyLog.getVersionValue());
        }
        docMessage.setDocClass(workflowApplyLog.getDocClass());
        SysDept dept = deptService.getById(workflowApplyLog.getDeptId());
        if (ObjectUtils.isNotEmpty(dept)) {
            docMessage.setDeptId(workflowApplyLog.getDeptId());
            docMessage.setDeptName(dept.getDeptName());
        }
        // 只有在修订和作废的变更申请的时候 WorkflowApplyLog的versionId 才会有值 文件新增的变更申请都没有生成文件 没有值
        if (ObjectUtil.isNotEmpty(workflowApplyLog.getVersionId())) {
            Version version = versionMapper.selectById(workflowApplyLog.getVersionId());
            if (ObjectUtils.isNotEmpty(version)) {
                docMessage.setDeptId(version.getDeptId());
                docMessage.setDeptName(deptService.getDeptName(version.getDeptId()));
            }
        }
        docMessage.setMsgStatus(0);
        docMessage.setMsgClass(msgType.getType());
        docMessage.setCreateUser(SecurityUtils.getUsername());
        docMessage.setCreateTime(new Date());
        docMessage.setMsgInfo(ApplyStatusEnum.getMsg(event.getApplyStatus()));
        // 接收人(也就是这条消息需要提醒谁)是这个申请的发起者
        docMessage.setRecoveryUser(workflowApplyLog.getUserName());
        // 这里要判断
        if (msgType.equals(MsgTypeEnum.REVIEW)) {
            ReviewApplyItemBo bo = new ReviewApplyItemBo();
            bo.setApplyId(applyId);
            List<ReviewApplyItemVo> reviewApplyItemVos = applyItemService.queryItemList(bo);
            if (CollectionUtil.isNotEmpty(reviewApplyItemVos)) {
                List<DocMessage> docMessageList = new ArrayList<>();
                for (ReviewApplyItemVo vo : reviewApplyItemVos) {
                    DocMessage doc = BeanUtil.toBean(docMessage, DocMessage.class);
                    doc.setDocId(vo.getDocId());
                    doc.setDocName(vo.getDocName());
                    if (ObjectUtil.isNotEmpty(vo.getVersionId())) {
                        doc.setVersionId(vo.getVersionId());
                    }
                    if (ObjectUtil.isNotEmpty(vo.getVersionValue())) {
                        doc.setVersionValue(vo.getVersionValue());
                    }
                    doc.setDocClass(vo.getDocClass());
                    docMessageList.add(doc);
                    String msgInfo = event.getMsgInfo();
                    if (ApplyStatusEnum.EXTENSION.getCode().equals(event.getApplyStatus())) {
                        /*
                         * Long reviewCycle =
                         * iDocClassService.getById(vo.getDocClass()).getReviewCycle();
                         * Date date = DateUtil.offset(DateUtil.date(), DateField.YEAR,
                         * reviewCycle.intValue());
                         * msgInfo += "至" + DateUtil.formatDate(date);
                         */
                        msgInfo = "有效期延期";
                    }
                    doc.setMsgInfo(msgInfo);
                }
                this.saveBatch(docMessageList);
                return;
            }
        }
        // docMessage.setRecoveryUserId("");
        this.insert(docMessage);
    }

    /**
     * 获取消息显示msgInfo
     * 
     * @param actionType
     * @return
     */
    private String getReviewMsgInfo(String actionType) {
        switch (actionType) {
            case "KEEP":
                return "保持现状";
            case "EXTENSION":
                return "延长有效期";
            case "UPDATE":
                return "文件修订";
            case "DISUSE":
                return "文件作废";
            default:
                return "未知操作";
        }
    }

    @Override
    public void insertMessageByReceive(ModifyApply applyPo) {
        // 准备生成消息
        String deptId = applyPo.getDeptId();
        // 查出部门文件管理员
        List<SysUser> sysUserList = sysUserService.selectDeptFileManagerByDeptId(deptId);
        // 发送消息给部门文件管理员
        sysUserList.forEach(sysUser -> {
            DocMessage docMessage = new DocMessage();
            docMessage.setApplyId(applyPo.getId());
            docMessage.setDocId(applyPo.getDocId());
            if (ObjectUtil.isNotEmpty(applyPo.getVersionId())) {
                docMessage.setVersionId(applyPo.getVersionId());
            }
            if (ObjectUtil.isNotEmpty(applyPo.getVersionValue())) {
                docMessage.setVersionValue(applyPo.getVersionValue());
            }
            docMessage.setDocClass(applyPo.getDocClass());
            docMessage.setDocName(applyPo.getDocName());
            SysDept sysDept = deptService.getById(applyPo.getDeptId());
            if (ObjectUtils.isNotEmpty(sysDept)) {
                docMessage.setDeptId(sysDept.getDeptId());
                docMessage.setDeptName(sysDept.getDeptName());
            }
            docMessage.setMsgStatus(0);
            docMessage.setMsgInfo("待回收");
            docMessage.setMsgClass(MsgTypeEnum.RECEIVE.getType());
            docMessage.setCreateTime(new Date());
            docMessage.setCreateUser(SecurityUtils.getLoginUser().getUsername());
            docMessage.setCreateUserId(SecurityUtils.getLoginUser().getUserId());
            // docMessage.setRecoveryTime(new Date());
            docMessage.setRecoveryUser(sysUser.getUserName());
            docMessage.setRecoveryUserId(sysUser.getUserId());
            this.insert(docMessage);
        });
    }

    @Override
    public void insertMessageByValidity(ModifyApply applyPo) {
        // 查出生产负责人和质量负责人
        List<SysUser> users = sysUserService.selectQaAndSc();
        // 发送消息给部门所有人和生产负责人还有质量负责人
        users.forEach(sysUser -> {
            Version version = versionMapper.selectById(applyPo.getVersionId());
            DocMessage docMessage = new DocMessage();
            docMessage.setApplyId(applyPo.getId());
            docMessage.setDocId(applyPo.getDocId());
            if (ObjectUtil.isNotEmpty(version)) {
                if (ObjectUtil.isNotEmpty(version.getId())) {
                    docMessage.setVersionId(version.getId());
                }
                if (ObjectUtil.isNotEmpty(version.getVersionValue())) {
                    docMessage.setVersionValue(version.getVersionValue());
                }
            }
            docMessage.setDocClass(applyPo.getDocClass());
            docMessage.setDocName(applyPo.getDocName());
            SysDept sysDept = deptService.getById(applyPo.getDeptId());
            if (ObjectUtils.isNotEmpty(sysDept)) {
                docMessage.setDeptName(sysDept.getDeptName());
            }
            docMessage.setMsgStatus(0);
            docMessage.setMsgInfo("生效");
            docMessage.setMsgClass(MsgTypeEnum.VALIDITY.getType());
            docMessage
                    .setCreateTime(ObjectUtil.isNotEmpty(applyPo.getSetupTime()) ? applyPo.getSetupTime() : new Date());
            docMessage.setCreateUser(SecurityUtils.getLoginUser().getUsername());
            docMessage.setCreateUserId(SecurityUtils.getLoginUser().getUserId());
            // docMessage.setCreateUserId("");
            // docMessage.setRecoveryTime(new Date());
            docMessage.setRecoveryUser(sysUser.getUserName());
            docMessage.setRecoveryUserId(sysUser.getUserId());
            this.insert(docMessage);
        });
    }

    @Override
    public void insertMessageEndArchiving(String applyId) throws ServerException {
        WorkflowApplyLog workflowApplyLog = workflowApplyLogService.getById(applyId);
        if (workflowApplyLog == null) {
            throw new ServerException(I18nUtils.getTitle(CommonI18nConstant.FILE_DOC_MESSAGE_NOT_FOUND_FLOW));
        }
        DocMessage docMessage = new DocMessage();
        docMessage.setApplyId(applyId);
        docMessage.setDocId(workflowApplyLog.getDocId());
        docMessage.setDocName(workflowApplyLog.getDocName());
        if (ObjectUtil.isNotEmpty(workflowApplyLog.getVersionId())) {
            docMessage.setVersionId(workflowApplyLog.getVersionId());
        }
        if (ObjectUtil.isNotEmpty(workflowApplyLog.getVersionValue())) {
            docMessage.setVersionValue(workflowApplyLog.getVersionValue());
        }
        docMessage.setDocClass(workflowApplyLog.getDocClass());
        SysDept sysDept = this.deptService.getById(workflowApplyLog.getDeptId());
        if (ObjectUtil.isNotEmpty(sysDept)) {
            docMessage.setDeptId(sysDept.getDeptId());
            docMessage.setDeptName(sysDept.getDeptName());
        }
        docMessage.setMsgStatus(0);
        docMessage.setMsgClass(MsgTypeEnum.END_ARCHIVING.getType());
        docMessage.setCreateUser(SecurityUtils.getUsername());
        docMessage.setCreateTime(new Date());
        docMessage.setMsgInfo("终止");
        // 接收人(也就是这条消息需要提醒谁)是这个申请的发起者
        docMessage.setRecoveryUser(workflowApplyLog.getSender());
        this.insert(docMessage);
        List<SysUser> userList = iSysUserService.selectListAllByRoleKey(Constants.ONE);
        for (SysUser user : userList) {
            docMessage.setRecoveryUser(user.getUserName());
            docMessage.setId(null);
            this.insert(docMessage);
        }
    }

    @Override
    public List<DocMessageVo> queryMessageDelivery() {
        QueryWrapper<DocMessage> queryWrapper = Wrappers.query();
        // queryWrapper.select("distinct
        // doc_id,doc_name,version_value,dept_name,create_time,msg_info");
        // queryWrapper.eq(DocMessage::getRecoveryUser,
        // SecurityUtils.getLoginUser().getUsername());
        queryWrapper.lambda().groupBy(DocMessage::getDocName);
        queryWrapper.lambda().groupBy(DocMessage::getVersionValue);
        queryWrapper.lambda().eq(DocMessage::getMsgClass, MsgTypeEnum.VALIDITY.getType()).or()
                .eq(DocMessage::getMsgClass, MsgTypeEnum.INVALIDATION.getType());
        queryWrapper.lambda().orderByDesc(DocMessage::getCreateTime);
        return listVo(queryWrapper);
    }

    private LambdaQueryWrapper<DocMessage> buildQueryWrapper(DocMessageBo bo) {
        LambdaQueryWrapper<DocMessage> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(StringUtils.isNotBlank(bo.getDocClass()), DocMessage::getDocClass, bo.getDocClass());
        queryWrapper.like(StringUtils.isNotBlank(bo.getDocName()), DocMessage::getDocName, bo.getDocName());
        queryWrapper.like(StringUtils.isNotBlank(bo.getMsgInfo()), DocMessage::getMsgInfo, bo.getMsgInfo());
        queryWrapper.eq(StringUtils.isNotNull(bo.getDeptId()), DocMessage::getDeptId, bo.getDeptId());
        queryWrapper.eq(StringUtils.isNotNull(bo.getMsgClass()), DocMessage::getMsgClass, bo.getMsgClass());
        queryWrapper.eq(DocMessage::getRecoveryUser, SecurityUtils.getLoginUser().getUsername());
        queryWrapper.eq(StringUtils.isNotBlank(bo.getRecoveryUserId()), DocMessage::getRecoveryUserId,
                bo.getRecoveryUserId());
        queryWrapper.eq(StringUtils.isNotNull(bo.getMsgStatus()), DocMessage::getMsgStatus, bo.getMsgStatus());
        queryWrapper.eq(StringUtils.isNotNull(bo.getMsgType()), DocMessage::getMsgType, bo.getMsgType());
        queryWrapper.le(DocMessage::getCreateTime, DateUtil.date());
        queryWrapper.orderByDesc(DocMessage::getCreateTime);
        return queryWrapper;
    }

    @Override
    public Boolean insert(DocMessage docMessage) {
        if (StringUtils.isEmpty(docMessage.getMsgType())) {
            // 默认为文件相关消息
            docMessage.setMsgType(DocMsgConstants.MSG_TYPE_QYWX);
        }
        return save(docMessage);
    }

    @Override
    public int pushSimpleMsg(String json) {
        int result = 0;
        try {
            DocMessage mainMsg = JSONObject.parseObject(json, DocMessage.class);
            if (mainMsg.getDeptId().equals("normal")) {
                // 常规消息传递通道
                mainMsg.setMsgType(this.customConfig.getMsgNormalNoticeChanel());
            } else if (mainMsg.getDeptId().equals("flow")) {
                // 流程消息传递通道
                if (ObjectUtil.isEmpty(mainMsg.getMsgType())) {
                    mainMsg.setMsgType(this.customConfig.getMsgFlowNoticeChanel());
                }
                if (mainMsg.getMsgType().equals(DocMsgConstants.MSG_TYPE_EMAIL)
                        && this.customConfig.getMsgFlowNoticeScope().equals("allUser")) {
                    // 当系统配置为流程参与消息通道为EMAIL和公司所有人的时候
                    mainMsg.setRecoveryUser("allUser");
                }
            }
            List<DocMessage> msgList = new ArrayList<>();
            String recoveryUsers = mainMsg.getRecoveryUser();
            if (recoveryUsers.contains(",")) {
                // 发送给多人
                String[] arr = recoveryUsers.split(",");
                for (String item : arr) {
                    DocMessage copyMsg = new DocMessage();
                    BeanUtil.copyProperties(mainMsg, copyMsg, false);
                    copyMsg.setRecoveryUser(item);
                    msgList.add(copyMsg);
                }
            } else {
                // 发送个单人
                msgList.add(mainMsg);
            }
            result = pushSimpleMsg(msgList);
        } catch (Exception e) {
            log.error("pushSimpleMsg异常", e);
        }
        return result;
    }

    /**
     * 推送简单文本站内消息
     *
     * @param msgList
     * @return
     */
    @Override
    public int pushSimpleMsg(List<DocMessage> msgList) {
        int result = 0;
        if (msgList != null && msgList.size() > 0) {
            // 当前登录用户
            LoginUser currUser = null;
            try {
                currUser = SecurityUtils.getLoginUser();
            } catch (Exception e) {
            }
            for (DocMessage item : msgList) {
                item.setMsgType(item.getMsgType() == null ? DocMsgConstants.MSG_TYPE_TXT : item.getMsgType());
                item.setVersionValue("default");
                // 消息状态 0=未读 1=已读
                item.setMsgStatus(0);
                item.setCreateTime(new Date());
                // 创建人
                item.setCreateUserId(currUser != null ? currUser.getUserId() : "");
                item.setCreateUser(currUser != null ? currUser.getUsername() : "");
                // 新增保存数据行
                boolean res = this.insert(item);
                if (res) {
                    // 发送成功数字累加，后续可考虑在这里对接第三方消息通知（比如钉钉、企业微信微信等）
                    if (item.getMsgType().equals(DocMsgConstants.MSG_TYPE_EMAIL)
                            && StringUtils.isNotEmpty(item.getDocId())) {
                        // 当第三方消息通道设置为email的时候触发
                        if (item.getMsgClass() != 1000) {
                            // 文件新增、作废场景触发邮件通知
                            Version currVersion = versionMapper.selectById(item.getDocId());
                            // addFileMsg 可自动识别文件新增还是失效场景
                            AjaxResult mailRes = this.docStatusMailService.addFileMsg(item.getRecoveryUser().split(","),
                                    currVersion);
                            String mailResJson = JSONObject.toJSONString(mailRes);
                            log.info("send mail res = " + mailResJson);
                            // 临时借用docClass、docName字段，更新发送邮件后的结果
                            item.setDocClass(String.valueOf(mailRes.getCode()));
                            item.setDocName(mailResJson);
                            item.setMsgType(DocMsgConstants.MSG_TYPE_EMAIL);
                            this.updateById(item);
                        } else {
                            // 复审到期提醒邮件
                            AjaxResult mailRes = this.reviewSingleNoticeMailService.notice(item);
                            String mailResJson = JSONObject.toJSONString(mailRes);
                            log.info("send mail res = " + mailResJson);
                            // 临时借用docClass、docName字段，更新发送邮件后的结果
                            item.setDocClass(String.valueOf(mailRes.getCode()));
                            item.setDocName(mailResJson);
                            item.setMsgType(DocMsgConstants.MSG_TYPE_EMAIL);
                            this.updateById(item);
                        }
                    } else if (item.getMsgType().equals(DocMsgConstants.MSG_TYPE_QYWX)) {
                        // 当第三方消息通道设置为企业微信qywx的时候触发
                        // 流程待办提心消息，发送企业微信
                        BizMsgDto msgDto = this.msgService.convert(item);
                        AjaxResult qywxRes = this.msgService.send(msgDto);
                        String qywxResJson = JSONObject.toJSONString(qywxRes);
                        log.info("send qywx res = " + qywxResJson);
                        // 临时借用docClass、docName字段，更新发送企业微信后的结果
                        item.setDocClass(String.valueOf(qywxRes.getCode()));
                        item.setDocName(qywxResJson);
                        item.setPcUrl(customConfig.getTokenRedirectUri());
                        // item.setMsgInfo(item.getMsgInfo().replace("<a href='" +
                        // customConfig.getTokenRedirectUri() + "'>",""));
                        // item.setMsgInfo(item.getMsgInfo().replace("</a>",""));
                        item.setMsgType(DocMsgConstants.MSG_TYPE_QYWX);
                        this.updateById(item);
                    }
                    // 成功发送数量
                    result++;
                }
            }
        }
        return result;
    }

    /**
     * 保存站内消息
     * 
     * @param versionId 版本id
     * @param applyId   业务流程申请id
     * @param tipName   标题提示明
     * @param docName   文件名称
     * @param docId     文件编号
     * @param msgClass  类型
     * @param content   内容
     * @param pcUrl     pc端链接地址
     * @param mobileUrl 移动端链接地址
     */
    @Override
    public void saveMsg(String versionId, String applyId, String tipName, String docName, String docId,
            Integer msgClass, String content, String pcUrl, String mobileUrl) {
        DocMessage docMessage = new DocMessage();
        // 当前生效文件版本
        docMessage.setDocId(StrUtil.isNotBlank(docId) ? docId : null);
        if (StrUtil.isNotBlank(versionId)) {
            docMessage.setVersionId(versionId);
        }
        docMessage.setMsgType(DocMsgConstants.MSG_TYPE_MSG);
        docMessage.setDeptId(DocMsgConstants.MSG_PROCESS_FLOW);
        docMessage.setMsgClass(msgClass);
        docMessage.setApplyId(applyId);
        if (StringUtils.isNotBlank(content)) {
            docMessage.setMsgInfo(content);
        } else {
            docMessage.setMsgInfo(String.format(tipName, StrUtil.isNotBlank(docName) ? docName : "",
                    StrUtil.isNotBlank(docId) ? docId : ""));
        }

        if (StringUtils.isNotBlank(pcUrl)) {
            docMessage.setPcUrl(pcUrl);
        } else {
            String url = MsgTemplateUtils.buildPcFileDetailUrl(docId, versionId);
            docMessage.setPcUrl(url);
        }
        if (StringUtils.isNotBlank(mobileUrl)) {
            docMessage.setMobileUrl(mobileUrl);
        } else {
            String url = MsgTemplateUtils.buildMobilFileDetailUrl(docId, versionId);
            docMessage.setMobileUrl(url);
        }


        List<WorkflowLogVo> workflowLogVos = workflowLogService.selectLogByBusinessId(applyId);
        docMessage.setRecoveryUser(workflowLogVos.stream()
                .map(WorkflowLogVo::getSender)
                .distinct()
                .collect(Collectors.joining(Constants.ID_SPLIT_KEY)));
        this.pushSimpleMsg(JSONUtil.toJsonStr(docMessage));
    }

    /**
     * 站内消息
     * 
     * @param applyId      流程id
     * @param docId        文档ID
     * @param docClass     文档分类
     * @param docName      文档名称
     * @param versionId    版本id
     * @param versionValue 版本号
     * @param deptId       部门ID
     * @param deptName     部门名称
     * @param userName     接收人账户
     * @param userId       接收人ID
     * @param content      消息内容
     * @param msgType      消息类型
     * @param linkUrl      连接地址
     * @param mobileUrl    移动端链接地址
     * @param tenantId     租户ID
     **/
    @Override
    public void sendInstationMessage(String applyId, String docId, String docClass, String docName, String versionId,
            String versionValue, String deptId, String deptName, String userName, String userId,
            String content, String msgType, Integer msgClass, String linkUrl, String mobileUrl, String tenantId) {
        if (StrUtil.isBlank(userId) || StrUtil.isBlank(content)) {
            return;
        }

        // 当前登录用户
        LoginUser currUser = null;
        try {
            currUser = SecurityUtils.getLoginUser();
        } catch (Exception e) {
            log.error("获取当前登录用户失败", e);
        }

        DocMessage docMessage = new DocMessage();
        docMessage.setVersionValue(StrUtil.isNotBlank(versionValue) ? versionValue : "default");
        docMessage.setVersionId(StrUtil.isNotBlank(versionId) ? versionId : null);
        docMessage.setDocId(StrUtil.isNotBlank(docId) ? docId : null);
        docMessage.setDocClass(StrUtil.isNotBlank(docClass) ? docClass : null);
        docMessage.setDocName(StrUtil.isNotBlank(docName) ? docName : null);
        docMessage.setDeptId(StrUtil.isNotBlank(deptId) ? deptId : null);
        docMessage.setDeptName(StrUtil.isNotBlank(deptName) ? deptName : null);
        docMessage.setMsgType(StrUtil.isNotBlank(msgType) ? msgType : null);
        docMessage.setTenantId(StrUtil.isNotBlank(tenantId) ? tenantId : null);
        docMessage.setApplyId(StrUtil.isNotBlank(applyId) ? applyId : null);
        docMessage.setPcUrl(StrUtil.isNotBlank(linkUrl) ? linkUrl : null);
        docMessage.setMobileUrl(StrUtil.isNotBlank(mobileUrl) ? mobileUrl : null);
        // 消息状态 0=未读 1=已读
        docMessage.setMsgStatus(0);
        docMessage.setCreateTime(new Date());
        // 创建人
        docMessage.setCreateUserId(currUser != null ? currUser.getUserId() : "");
        docMessage.setCreateUser(currUser != null ? currUser.getUsername() : "");
        docMessage.setMsgClass(msgClass);
        docMessage.setMsgInfo(content);
        // 接收人
        docMessage.setRecoveryUser(userName);
        docMessage.setRecoveryUserId(userId);
        this.save(docMessage);
    }
}

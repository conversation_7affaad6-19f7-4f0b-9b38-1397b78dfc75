package com.rzdata.process.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.rzdata.framework.constant.Constants;
import com.rzdata.framework.core.domain.entity.SysUser;
import com.rzdata.framework.core.domain.model.LoginUser;
import com.rzdata.framework.core.mybatisplus.core.ServicePlusImpl;
import com.rzdata.framework.core.page.PagePlus;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.enums.SearchAuthEnum;
import com.rzdata.framework.utils.PageUtils;
import com.rzdata.framework.utils.SecurityUtils;
import com.rzdata.framework.utils.StringUtils;
import com.rzdata.process.domain.DocDistribute;
import com.rzdata.process.domain.DocPrintDataAuth;
import com.rzdata.process.domain.bo.DocPrintDataAuthBo;
import com.rzdata.process.domain.vo.DocDistributeVo;
import com.rzdata.process.domain.vo.DocPrintDataAuthVo;
import com.rzdata.process.mapper.DocPrintDataAuthMapper;
import com.rzdata.process.service.IDocDistributeService;
import com.rzdata.process.service.IDocPrintDataAuthService;
import com.rzdata.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * 文件打印数据权限Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-04-29
 */
@Service
public class DocPrintDataAuthServiceImpl extends ServicePlusImpl<DocPrintDataAuthMapper, DocPrintDataAuth, DocPrintDataAuthVo> implements IDocPrintDataAuthService {

    @Autowired
    private ISysUserService sysUserService;

    @Autowired
    private DocPrintDataAuthMapper docPrintDataAuthMapper;

    @Autowired
    private IDocDistributeService  docDistributeService;

    @Override
    public DocPrintDataAuthVo queryById(String id){
        return getVoById(id);
    }

    @Override
    public TableDataInfo<DocPrintDataAuthVo> queryPageList(DocPrintDataAuthBo bo) {
        PagePlus<DocPrintDataAuth, DocPrintDataAuthVo> result = pageVo(PageUtils.buildPagePlus(), buildQueryWrapper(bo));
        return PageUtils.buildDataInfo(result);
    }

    @Override
    public List<DocPrintDataAuthVo> queryList(DocPrintDataAuthBo bo) {
        return listVo(buildQueryWrapper(bo));
    }

    private LambdaQueryWrapper<DocPrintDataAuth> buildQueryWrapper(DocPrintDataAuthBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<DocPrintDataAuth> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getUserId()), DocPrintDataAuth::getUserId, bo.getUserId());
        lqw.like(StringUtils.isNotBlank(bo.getUserName()), DocPrintDataAuth::getUserName, bo.getUserName());
        lqw.eq(StringUtils.isNotBlank(bo.getDocDistributeId()), DocPrintDataAuth::getDocDistributeId, bo.getDocDistributeId());
        return lqw;
    }

    @Override
    public Boolean insertByBo(DocPrintDataAuthBo bo) {
        DocPrintDataAuth add = BeanUtil.toBean(bo, DocPrintDataAuth.class);
        validEntityBeforeSave(add);
        boolean flag = save(add);
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }


    /**
     * 先删除后新增
     * @param bo 文件打印数据权限新增业务对象
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean beforeDelAfterAdd(DocPrintDataAuthBo bo) {
        if(CollUtil.isEmpty(bo.getDocDistributeVoList())){
            return false;
        }
        List<String> userNameList = new ArrayList<>();
        for (DocDistributeVo docDistributeVo : bo.getDocDistributeVoList()) {
            userNameList.add(docDistributeVo.getReceiveUserName());
        }

        List<SysUser> sysUserList = sysUserService.list(new LambdaQueryWrapper<SysUser>().in(SysUser::getUserName, userNameList));
        if(CollUtil.isEmpty(sysUserList)){
            return false;
        }
        List<DocPrintDataAuth> docPrintDataAuthList = new ArrayList<>();
        List<DocDistribute> docDistributeList = new ArrayList<>();
        for (DocDistributeVo docDistributeVo : bo.getDocDistributeVoList()) {
            DocPrintDataAuth docPrintDataAuth = new DocPrintDataAuth();
            for (SysUser sysUser : sysUserList) {
                if(sysUser.getUserName().equals(docDistributeVo.getReceiveUserName())){
                    docPrintDataAuth.setUserId(sysUser.getUserId());
                    break;
                }
            }
            docPrintDataAuth.setUserName(docDistributeVo.getReceiveUserName());
            docPrintDataAuth.setDocDistributeId(docDistributeVo.getId());
            docPrintDataAuthList.add(docPrintDataAuth);

            docDistributeVo.setPrintFlag(Constants.VALUE_Y);
            docDistributeList.add(BeanUtil.copyProperties(docDistributeVo,DocDistribute.class));
        }
        //先删除,后新增
        docPrintDataAuthMapper.deleteBatchByCondition(docPrintDataAuthList);
        this.saveBatch(docPrintDataAuthList);

        //修改文件分发记录对应的打印状态为已授权
        if(StringUtils.isNotEmpty(docDistributeList)){
            docDistributeService.updateBatchById(docDistributeList);
        }
        return true;
    }

    @Override
    public Boolean updateByBo(DocPrintDataAuthBo bo) {
        DocPrintDataAuth update = BeanUtil.toBean(bo, DocPrintDataAuth.class);
        validEntityBeforeSave(update);
        return updateById(update);
    }

    /**
     * 保存前的数据校验
     *
     * @param entity 实体类数据
     */
    private void validEntityBeforeSave(DocPrintDataAuth entity){
        //TODO 做一些数据校验,如唯一约束
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return removeByIds(ids);
    }

    @Override
    public Boolean getCurInfoIsPrintAuth() {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if (ObjectUtil.isNull(loginUser) || CollUtil.isEmpty(loginUser.getPermissions())) {
            return false;
        }
        Set<String> authorities = loginUser.getPermissions();
        if(authorities.contains(Constants.PERMISSION_ALL)){
            return true;
        }

        Boolean flag = authorities.contains(Constants.DMS_PDF_PRINT) && authorities.contains(Constants.FILE_PRINT_DISTRIBUTION_AUTH);
        List<DocPrintDataAuth> docPrintDataAuthList = this.list(new LambdaQueryWrapper<DocPrintDataAuth>().eq(DocPrintDataAuth::getUserId, loginUser.getUserId()));
        if(CollUtil.isNotEmpty(docPrintDataAuthList) && flag){
            return true;
        }
        return false;
    }
}

package com.rzdata.process.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.rzdata.framework.constant.CommonI18nConstant;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.core.redis.RedisCache;
import com.rzdata.framework.utils.I18nUtils;
import com.rzdata.framework.utils.spring.SpringUtils;
import com.rzdata.process.domain.ModifyApply;
import com.rzdata.process.domain.ModifySignature;
import com.rzdata.process.domain.bo.QysCallbackBo;
import com.rzdata.process.service.IDocSignatureService;
import com.rzdata.process.service.IModifyApplyService;
import com.rzdata.process.service.IModifySignatureService;
import com.rzdata.process.service.IStandardService;
import com.rzdata.process.utils.DateUtils;
import com.rzdata.process.utils.DocSignatureUtils;
import com.rzdata.process.utils.FileMergeUtils;
import com.rzdata.setting.service.IDocClassService;
import net.qiyuesuo.sdk.SDKClient;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Date 2022/1/26 17:35
 * @Version 1.0
 * @Description
 */
@Service
public class DocSignatureServiceImpl implements IDocSignatureService {
    /**
     * 契约锁服务地址
     */
    @Value("${qys.url}")
    private String url;

    /**
     * accessKey
     */
    @Value("${qys.accessKey}")
    private String accessKey;

    /**
     * accessSecret
     */
    @Value("${qys.accessSecret}")
    private String accessSecret;

    /**
     * 业务id
     */
    @Value("${qys.bizId}")
    private String bizId;

    /**
     * 印章ID
     */
    @Value("${qys.sealId}")
    private String sealId;

    /**
     * 发起方公司名称
     */
    @Value("${qys.tenantName}")
    private String tenantName;

    /**
     * redis缓存中路径的key
     */
    private static final String DOC_PATH_KEY="docPathKey";


    /**
     * redis缓存中路径的key
     */
    private static final String APPLY_ID_KEY="applyIdKey";

    @Resource
    IDocClassService docClassService;

    @Resource
    IStandardService standardService;

    @Resource
    IModifySignatureService modifySignatureService;

    @Resource
    IModifyApplyService  modifyApplyService;
    /**
     * redis文档编号的key
     */
    private static final String DOC_ID_KEY="docIdKey";
    @Override
    public AjaxResult signature(String id,String docId,String applyId) {
//        Standard standard = standardService.getById(docId);
//        if (standard!=null){
//            //拿到该文件的文件设置
//            DocClassVo docClassVo = docClassService.getVoById(standard.getDocClass());
//            String openSignature = docClassVo.getOpenSignature();
//            if (ObjectUtil.equals("'false'",openSignature)){
//                return AjaxResult.error("未启用文件签章设置!",null);
//            }
//        }else {
//            return AjaxResult.error("未找到文件编号!",null);
//        }

        try {
            SDKClient client = new SDKClient(url, accessKey, accessSecret);
            String docPath = FileMergeUtils.getFilePathById(id);
            File docFile = FileUtils.getFile(docPath);
            //String pdfFilePath = FileMergeUtils.wordToPdf(docPath);
            //File pdfFile = FileUtils.getFile(pdfFilePath);
            InputStream inputStream = FileUtils.openInputStream(docFile);
            //上传文件到契约锁并拿到契约锁文档ID
            Long documentId = DocSignatureUtils.createByFile(client, inputStream, docFile.getName(), FilenameUtils.getExtension(docFile.getName()));
            ArrayList<Long> documentIds = new ArrayList<>();
            documentIds.add(documentId);
            ArrayList<Long> sealIds = new ArrayList<>();
            sealIds.add(Long.parseLong(sealId));
            Long categoryId = DocSignatureUtils.createContractByCategory(client, FilenameUtils.getBaseName(docFile.getName()), true, bizId, sealIds, documentIds,tenantName);

            HashMap<String, String> data = new HashMap<>(2);
            data.put(DOC_PATH_KEY,docPath);
            data.put(DOC_ID_KEY,docId);
            data.put(APPLY_ID_KEY,applyId);
            SpringUtils.getBean(RedisCache.class).setCacheObject(categoryId.toString(),data,60, TimeUnit.MINUTES);
            String signUrl = DocSignatureUtils.signUrl(categoryId, client,tenantName);
            //更新modify表中的签章地址
            ModifyApply modifyApply = modifyApplyService.getById(applyId);
            if (ObjectUtil.isNotEmpty(modifyApply)) {
                modifyApplyService.updateById(modifyApply);
            }
            //插入doc_modify_signature_log记录
            ModifySignature modifySignature = new ModifySignature();
            modifySignature.setSignatureUrl(signUrl);
            modifySignature.setApplyId(applyId);
            modifySignature.setCreateTime(new Date());
            modifySignatureService.save(modifySignature);
            return AjaxResult.success(I18nUtils.getTitle(CommonI18nConstant.DOC_SIGN_VISIT_ADDRESS_TO_COMPLETE),signUrl);
        } catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.error(e.getMessage());
        }
    }

    @Override
    public AjaxResult callBack(QysCallbackBo qysCallback) {
        SDKClient client = new SDKClient(url, accessKey, accessSecret);
        if ("COMPLETE".equals(qysCallback.getStatus())){
            try {
                //拿到合同id
                Long contractId = qysCallback.getContractId();
                //通过合同ID在Redis中取到原文件的路径
                HashMap<String, String> data = SpringUtils.getBean(RedisCache.class).getCacheObject(contractId.toString());
                if (ObjectUtils.isEmpty(data.get(DOC_PATH_KEY))){
                    return AjaxResult.error(I18nUtils.getTitle(CommonI18nConstant.DOC_SIGN_NOT_FOUND_DOWNLOAD_FILE));
                }
                File file = FileUtils.getFile(data.get(DOC_PATH_KEY));
                String parentName = file.getParentFile().getName();
                //下载的文件路径
                String contractPath = parentName+File.separator+FilenameUtils.getBaseName(file.getName())
                        + DateUtils.dateTimeNow(DateUtils.YYYYMMDDHHMMSS)
                        + ".zip";
                DocSignatureUtils.download(contractId,client,contractPath,data.get(DOC_ID_KEY),parentName);
                //更新modify表中的签章状态为已经签章
                String applyId = data.get(APPLY_ID_KEY);
                ModifyApply modifyApply = modifyApplyService.getById(applyId);
                if (ObjectUtil.isNotEmpty(modifyApply)) {
                    modifyApply.setIsSignature("Y");
                    modifyApplyService.updateById(modifyApply);
                }
                return AjaxResult.success();
            } catch (Exception e) {
                e.printStackTrace();
                return AjaxResult.error(e.getMessage());
            }
        }
        return AjaxResult.success();
    }
}

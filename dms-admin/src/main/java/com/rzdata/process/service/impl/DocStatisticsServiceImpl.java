package com.rzdata.process.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rzdata.framework.constant.CommonI18nConstant;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.core.domain.entity.SysDept;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.utils.I18nUtils;
import com.rzdata.framework.utils.PageUtils;
import com.rzdata.process.domain.bo.DocStatisticsBo;
import com.rzdata.process.domain.bo.DocStatisticsTrainingBo;
import com.rzdata.process.domain.vo.*;
import com.rzdata.process.mapper.DocStatisticsMapper;
import com.rzdata.process.service.IDocStatisticsService;
import com.rzdata.setting.domain.bo.DocClassBo;
import com.rzdata.setting.domain.vo.DocClassVo;
import com.rzdata.setting.service.IDocClassService;
import com.rzdata.system.service.ISysDeptService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2022/2/8 14:29
 * @Version 1.0
 * @Description
 */
@Service
public class DocStatisticsServiceImpl implements IDocStatisticsService {


    @Resource
    DocStatisticsMapper docStatisticsMapper;

    @Resource
    ISysDeptService iSysDeptService;
    @Resource
    IDocClassService docClassService;


    /**
     * @param bo 文件统计实体类
     * @return
     */
    @Override
    public TableDataInfo<DocStatisticsChangeTypeVo> changeType(DocStatisticsBo bo) {
        List<String> docClassList = bo.getDocClassList();
        if (docClassList!=null) {
            docClassList.add(bo.getDocClass());
            bo.setDocClass(null);
            bo.setDocClassList(docClassList);
        }
        Page<DocStatisticsChangeTypeVo> page = new Page<>(bo.getPageNum(), bo.getPageSize());
        page.addOrder(OrderItem.asc("doc_Class"));
        Page<DocStatisticsChangeTypeVo> result = docStatisticsMapper.selectChangeType(page,bo);
        for (DocStatisticsChangeTypeVo vVo:result.getRecords()) {
            SysDept dept = iSysDeptService.getById(vVo.getDeptId());
            vVo.setDeptName(dept!=null?dept.getDeptName():null);
        }
        return PageUtils.buildDataInfo(result);
    }
    @Override
    public DocStatisticsChangeTypeVo changeTypeSum(DocStatisticsBo bo) {
        Map<String,Object>  params=bo.getParams();
        if (ObjectUtil.isNotEmpty(params.get("startTime"))) {
            bo.setStartDateTime( DateUtil.parseDate(params.get("startTime").toString()));
        }
        if (ObjectUtil.isNotEmpty(params.get("endTime"))) {
            Date end=DateUtil.parseDate(params.get("endTime").toString());
            end.setHours(23);
            end.setMinutes(59);
            end.setSeconds(59);
            bo.setEndDateTime(end);
        }
        return docStatisticsMapper.selectChangeTypeSum(bo);
    }

    @Override
    public AjaxResult<List<DocStatisticsChangeFactorVo>> changeFactor(DocStatisticsBo bo) {
        try {
            List<DocStatisticsChangeFactorVo> number = docStatisticsMapper.selectChangeFactor(bo);
            return AjaxResult.success(number);
        } catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.error(I18nUtils.getTitle(CommonI18nConstant.DOC_STATISTICS_QUERY_FILE_ERR), null);
        }
    }

    @Override
    public List<DocStatisticsChangeTypeExcelVo> getExcelData(DocStatisticsBo bo) {
        TableDataInfo<DocStatisticsChangeTypeVo> tableDataInfo = this.changeType(bo);
        if (tableDataInfo.getTotal() == 0) {
            return Collections.emptyList();
        }
        DocClassBo docClassBo = new DocClassBo();
        docClassBo.setClassStatus("1");
        List<DocClassVo> classTable = docClassService.queryList(docClassBo);
        return tableDataInfo.getRows().parallelStream()
            .map((o) -> {
                DocStatisticsChangeTypeExcelVo vo = new DocStatisticsChangeTypeExcelVo();
                String docClassName = classTable
                    .stream()
                    .filter((c) -> Objects.equals(c.getId(), o.getDocClass()))
                    .map(DocClassVo::getClassName)
                    .findFirst()
                    .orElse(null);
                vo.setDocClassName(docClassName);
                vo.setDeptName(o.getDeptName());
                vo.setAddNum(o.getAddNum());
                vo.setUpdateNum(o.getUpdateNum());
                vo.setDisuseNum(o.getDisuseNum());
                vo.setTotal(o.getTotal());
                return vo;
            })
            .collect(Collectors.toList());
    }

    @Override
    public TableDataInfo<DocStatisticsTrainingVo> training(DocStatisticsTrainingBo bo) {
        Page<DocStatisticsTrainingVo> page = new Page<>(bo.getPageNum(), bo.getPageSize());
        page.addOrder(OrderItem.desc("release_time"));
        Page<DocStatisticsTrainingVo> result = docStatisticsMapper.selectTraining(page, bo);
        for (int i = 0, cnt = result.getRecords().size(); i < cnt; i++) {
            result.getRecords().get(i)
                .setIndex(page.getSize() * (page.getCurrent() - 1) + i + 1);
        }
        return PageUtils.buildDataInfo(result);
    }

    @Override
    public List<DocStatisticsTrainingExcelVo> exportExcelData(DocStatisticsTrainingBo bo) {
        TableDataInfo<DocStatisticsTrainingVo> tableDataInfo = this.training(bo);
        return tableDataInfo.getRows().parallelStream()
            .map((vo) -> {
                DocStatisticsTrainingExcelVo excelVo = BeanUtil.toBean(vo, DocStatisticsTrainingExcelVo.class);
                excelVo.setTrained(vo.getTrained() ? "是" : "否");
                return excelVo;
            })
            .collect(Collectors.toList());
    }
}

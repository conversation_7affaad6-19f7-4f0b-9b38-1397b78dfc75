package com.rzdata.process.service.impl;

import cn.hutool.core.bean.BeanUtil;
    import com.rzdata.framework.utils.PageUtils;
import com.rzdata.framework.core.page.PagePlus;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.utils.StringUtils;
import org.springframework.stereotype.Service;
import com.rzdata.framework.core.mybatisplus.core.ServicePlusImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.rzdata.process.domain.bo.ErrataLinkBo;
import com.rzdata.process.domain.vo.ErrataLinkVo;
import com.rzdata.process.domain.ErrataLink;
import com.rzdata.process.mapper.ErrataLinkMapper;
import com.rzdata.process.service.IErrataLinkService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 文件关联勘误记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-13
 */
@Service
public class ErrataLinkServiceImpl extends ServicePlusImpl<ErrataLinkMapper, ErrataLink, ErrataLinkVo> implements IErrataLinkService {

    @Override
    public ErrataLinkVo queryById(String id){
        return getVoById(id);
    }

    @Override
    public TableDataInfo<ErrataLinkVo> queryPageList(ErrataLinkBo bo) {
        PagePlus<ErrataLink, ErrataLinkVo> result = pageVo(PageUtils.buildPagePlus(), buildQueryWrapper(bo));
        return PageUtils.buildDataInfo(result);
    }

    @Override
    public List<ErrataLinkVo> queryList(ErrataLinkBo bo) {
        return listVo(buildQueryWrapper(bo));
    }

    private LambdaQueryWrapper<ErrataLink> buildQueryWrapper(ErrataLinkBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<ErrataLink> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getParentId()), ErrataLink::getParentId, bo.getParentId());
        lqw.eq(StringUtils.isNotBlank(bo.getLinkCode()), ErrataLink::getLinkCode, bo.getLinkCode());
        lqw.like(StringUtils.isNotBlank(bo.getFileName()), ErrataLink::getFileName, bo.getFileName());
        lqw.eq(StringUtils.isNotBlank(bo.getFileId()), ErrataLink::getFileId, bo.getFileId());
        lqw.eq(StringUtils.isNotBlank(bo.getVersionId()), ErrataLink::getVersionId, bo.getVersionId());
        lqw.eq(StringUtils.isNotBlank(bo.getVersionValue()), ErrataLink::getVersionValue, bo.getVersionValue());
        lqw.eq(StringUtils.isNotBlank(bo.getLinkType()), ErrataLink::getLinkType, bo.getLinkType());
        lqw.eq(StringUtils.isNotBlank(bo.getTenantId()), ErrataLink::getTenantId, bo.getTenantId());
        lqw.eq(StringUtils.isNotBlank(bo.getDocClass()), ErrataLink::getDocClass, bo.getDocClass());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), ErrataLink::getStatus, bo.getStatus());
        lqw.eq(bo.getStartDate() != null, ErrataLink::getStartDate, bo.getStartDate());
        lqw.eq(bo.getReleaseTime() != null, ErrataLink::getReleaseTime, bo.getReleaseTime());
        lqw.eq(bo.getEndDate() != null, ErrataLink::getEndDate, bo.getEndDate());
        return lqw;
    }

    @Override
    public Boolean insertByBo(ErrataLinkBo bo) {
        ErrataLink add = BeanUtil.toBean(bo, ErrataLink.class);
        validEntityBeforeSave(add);
        boolean flag = save(add);
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    @Override
    public Boolean updateByBo(ErrataLinkBo bo) {
        ErrataLink update = BeanUtil.toBean(bo, ErrataLink.class);
        validEntityBeforeSave(update);
        return updateById(update);
    }

    /**
     * 保存前的数据校验
     *
     * @param entity 实体类数据
     */
    private void validEntityBeforeSave(ErrataLink entity){
        //TODO 做一些数据校验,如唯一约束
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return removeByIds(ids);
    }
}

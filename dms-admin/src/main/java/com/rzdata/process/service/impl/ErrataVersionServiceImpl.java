package com.rzdata.process.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rzdata.es.service.ElasticsearchService;
import com.rzdata.framework.constant.ProcessStatusConstants;
import com.rzdata.framework.utils.PageUtils;
import com.rzdata.framework.core.page.PagePlus;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.utils.SecurityUtils;
import com.rzdata.framework.utils.StringUtils;
import com.rzdata.process.domain.*;
import com.rzdata.process.domain.vo.DocLinkLogVo;
import com.rzdata.process.enums.LinkTypeEnum;
import com.rzdata.process.service.IDocLinkLogService;
import com.rzdata.process.service.IErrataLinkService;
import com.rzdata.process.service.IVersionService;
import com.rzdata.system.service.ISysUserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.rzdata.framework.core.mybatisplus.core.ServicePlusImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.rzdata.process.domain.bo.ErrataVersionBo;
import com.rzdata.process.domain.vo.ErrataVersionVo;
import com.rzdata.process.mapper.ErrataVersionMapper;
import com.rzdata.process.service.IErrataVersionService;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 勘误文件版本记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-08
 */
@Slf4j
@Service
public class ErrataVersionServiceImpl extends ServicePlusImpl<ErrataVersionMapper, ErrataVersion, ErrataVersionVo> implements IErrataVersionService {

    @Autowired
    private IVersionService iVersionService;

    @Autowired
    private IErrataLinkService iErrataLinkService;

    @Autowired
    private IDocLinkLogService iDocLinkLogService;

    @Autowired
    private ElasticsearchService elasticsearchService;

    @Override
    public ErrataVersionVo queryById(String id){
        return getVoById(id);
    }

    @Override
    public ErrataVersionVo queryPrev(ErrataVersionBo bo) {
        return getVoOne(buildQueryWrapper(bo).orderByDesc(ErrataVersion::getSort).last("limit 1"));
    }

    @Override
    public TableDataInfo<ErrataVersionVo> queryPageList(ErrataVersionBo bo) {
        Page<ErrataVersionVo> result = baseMapper.queryPageList(PageUtils.buildPage(), bo,buildQueryWrapper(bo));
        return PageUtils.buildDataInfo(result);
    }

    @Override
    public List<ErrataVersionVo> queryList(ErrataVersionBo bo) {
        return baseMapper.queryPageList(PageUtils.buildPage(), bo,buildQueryWrapper(bo)).getRecords();
    }

    private LambdaQueryWrapper<ErrataVersion> buildQueryWrapper(ErrataVersionBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<ErrataVersion> lqw = Wrappers.lambdaQuery();
        lqw.lt(bo.getMaxSort() != null,ErrataVersion::getSort, bo.getMaxSort());
        lqw.ne(bo.getNeSort() != null, ErrataVersion::getSort, bo.getNeSort());
        lqw.eq(StringUtils.isNotBlank(bo.getVersionId()), ErrataVersion::getVersionId, bo.getVersionId());
        lqw.eq(bo.getSort() != null, ErrataVersion::getSort, bo.getSort());
        lqw.eq(StringUtils.isNotBlank(bo.getDocClass()), ErrataVersion::getDocClass, bo.getDocClass());
        lqw.eq(StringUtils.isNotBlank(bo.getStandardId()), ErrataVersion::getStandardId, bo.getStandardId());
        lqw.eq(StringUtils.isNotBlank(bo.getDocId()), ErrataVersion::getDocId, bo.getDocId());
        lqw.like(StringUtils.isNotBlank(bo.getDocName()), ErrataVersion::getDocName, bo.getDocName());
        lqw.eq(StringUtils.isNotBlank(bo.getApplyId()), ErrataVersion::getApplyId, bo.getApplyId());
        lqw.eq(StringUtils.isNotBlank(bo.getVersionValue()), ErrataVersion::getVersionValue, bo.getVersionValue());
        lqw.eq(bo.getStartDate() != null, ErrataVersion::getStartDate, bo.getStartDate());
        lqw.eq(bo.getEndDate() != null, ErrataVersion::getEndDate, bo.getEndDate());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), ErrataVersion::getStatus, bo.getStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getReason()), ErrataVersion::getReason, bo.getReason());
        lqw.eq(bo.getReviewTime() != null, ErrataVersion::getReviewTime, bo.getReviewTime());
        lqw.eq(StringUtils.isNotBlank(bo.getContent()), ErrataVersion::getContent, bo.getContent());
        lqw.eq(StringUtils.isNotBlank(bo.getChangeFactor()), ErrataVersion::getChangeFactor, bo.getChangeFactor());
        lqw.eq(StringUtils.isNotBlank(bo.getChangeReason()), ErrataVersion::getChangeReason, bo.getChangeReason());
        lqw.eq(StringUtils.isNotBlank(bo.getTrainDept()), ErrataVersion::getTrainDept, bo.getTrainDept());
        lqw.eq(StringUtils.isNotBlank(bo.getFileId()), ErrataVersion::getFileId, bo.getFileId());
        lqw.eq(StringUtils.isNotBlank(bo.getMergeFileId()), ErrataVersion::getMergeFileId, bo.getMergeFileId());
        lqw.eq(StringUtils.isNotBlank(bo.getEncryptFileId()), ErrataVersion::getEncryptFileId, bo.getEncryptFileId());
        lqw.eq(StringUtils.isNotBlank(bo.getTenantId()), ErrataVersion::getTenantId, bo.getTenantId());
        lqw.eq(StringUtils.isNotBlank(bo.getForever()), ErrataVersion::getForever, bo.getForever());
        lqw.eq(StringUtils.isNotBlank(bo.getDataType()), ErrataVersion::getDataType, bo.getDataType());
        lqw.eq(StringUtils.isNotBlank(bo.getPdfFileId()), ErrataVersion::getPdfFileId, bo.getPdfFileId());
        lqw.eq(bo.getReleaseTime() != null, ErrataVersion::getReleaseTime, bo.getReleaseTime());
        lqw.eq(StringUtils.isNotBlank(bo.getUpVersionId()), ErrataVersion::getUpVersionId, bo.getUpVersionId());
        lqw.eq(StringUtils.isNotBlank(bo.getParentDocId()), ErrataVersion::getParentDocId, bo.getParentDocId());
        lqw.eq(StringUtils.isNotBlank(bo.getClassType()), ErrataVersion::getClassType, bo.getClassType());
        lqw.eq(StringUtils.isNotBlank(bo.getDeptId()), ErrataVersion::getDeptId, bo.getDeptId());
        lqw.like(StringUtils.isNotBlank(bo.getUserName()), ErrataVersion::getUserName, bo.getUserName());
        lqw.eq(StringUtils.isNotBlank(bo.getChangeType()), ErrataVersion::getChangeType, bo.getChangeType());
        lqw.eq(StringUtils.isNotBlank(bo.getInvokeType()), ErrataVersion::getInvokeType, bo.getInvokeType());
        lqw.eq(StringUtils.isNotBlank(bo.getInvokeId()), ErrataVersion::getInvokeId, bo.getInvokeId());
        lqw.eq(StringUtils.isNotBlank(bo.getDistributeType()), ErrataVersion::getDistributeType, bo.getDistributeType());
        lqw.eq(StringUtils.isNotBlank(bo.getWhetherCustomer()), ErrataVersion::getWhetherCustomer, bo.getWhetherCustomer());
        lqw.eq(bo.getApplyTime() != null, ErrataVersion::getApplyTime, bo.getApplyTime());
        lqw.eq(StringUtils.isNotBlank(bo.getInternalDocId()), ErrataVersion::getInternalDocId, bo.getInternalDocId());
        lqw.eq(StringUtils.isNotBlank(bo.getExt1()), ErrataVersion::getExt1, bo.getExt1());
        lqw.eq(StringUtils.isNotBlank(bo.getExt2()), ErrataVersion::getExt2, bo.getExt2());
        lqw.eq(StringUtils.isNotBlank(bo.getExt3()), ErrataVersion::getExt3, bo.getExt3());
        lqw.eq(StringUtils.isNotBlank(bo.getExt4()), ErrataVersion::getExt4, bo.getExt4());
        lqw.eq(StringUtils.isNotBlank(bo.getExt5()), ErrataVersion::getExt5, bo.getExt5());
        lqw.eq(StringUtils.isNotBlank(bo.getExt6()), ErrataVersion::getExt6, bo.getExt6());
        lqw.eq(StringUtils.isNotBlank(bo.getExt7()), ErrataVersion::getExt7, bo.getExt7());
        lqw.eq(StringUtils.isNotBlank(bo.getExt8()), ErrataVersion::getExt8, bo.getExt8());
        lqw.eq(StringUtils.isNotBlank(bo.getExt9()), ErrataVersion::getExt9, bo.getExt9());
        lqw.eq(StringUtils.isNotBlank(bo.getExt10()), ErrataVersion::getExt10, bo.getExt10());
        lqw.eq(StringUtils.isNotBlank(bo.getExt11()), ErrataVersion::getExt11, bo.getExt11());
        lqw.eq(StringUtils.isNotBlank(bo.getExt12()), ErrataVersion::getExt12, bo.getExt12());
        lqw.eq(StringUtils.isNotBlank(bo.getExt13()), ErrataVersion::getExt13, bo.getExt13());
        lqw.eq(StringUtils.isNotBlank(bo.getExt14()), ErrataVersion::getExt14, bo.getExt14());
        lqw.eq(StringUtils.isNotBlank(bo.getExt15()), ErrataVersion::getExt15, bo.getExt15());
        lqw.eq(StringUtils.isNotBlank(bo.getExt16()), ErrataVersion::getExt16, bo.getExt16());
        lqw.eq(StringUtils.isNotBlank(bo.getExt17()), ErrataVersion::getExt17, bo.getExt17());
        lqw.eq(StringUtils.isNotBlank(bo.getExt18()), ErrataVersion::getExt18, bo.getExt18());
        lqw.eq(StringUtils.isNotBlank(bo.getExt19()), ErrataVersion::getExt19, bo.getExt19());
        lqw.eq(StringUtils.isNotBlank(bo.getExt20()), ErrataVersion::getExt20, bo.getExt20());
        lqw.eq(StringUtils.isNotBlank(bo.getCreateName()), ErrataVersion::getCreateName, bo.getCreateName());
        lqw.and(StringUtils.isNotBlank(bo.getSearchValue()), queryWrapper -> {
            queryWrapper.like(ErrataVersion::getDocName, bo.getSearchValue())
                    .or().like(ErrataVersion::getVersionValue, bo.getSearchValue())
                    .or().like(ErrataVersion::getDocId, bo.getSearchValue());
        });
        lqw.between(ObjectUtil.isNotEmpty(params.get("startTime")) && ObjectUtil.isNotEmpty(params.get("endTime")),
                ErrataVersion::getCreateTime ,params.get("startTime"), params.get("endTime"));
        return lqw;
    }

    @Override
    public Boolean insertByBo(ErrataVersionBo bo) throws Exception {
        bo.setId(null);
        ErrataVersion add = BeanUtil.toBean(bo, ErrataVersion.class);
        add.setCreateBy(null);
        add.setCreateTime(null);
        add.setUpdateBy(null);
        add.setUpdateTime(null);
        add.setCreateName(SecurityUtils.getNickname());
        ErrataVersion last = getOne(new LambdaQueryWrapper<ErrataVersion>().eq(ErrataVersion::getVersionId,bo.getVersionId()).orderByDesc(ErrataVersion::getSort).last("limit 1"));
        if (ObjectUtil.isEmpty(last)) {
            Version version = iVersionService.getById(bo.getVersionId());
            List<DocLinkLogVo> list = iDocLinkLogService.queryDocLinkVo(bo.getVersionId(),LinkTypeEnum.APPENDIX.name());
            list.addAll(iDocLinkLogService.queryDocLinkVo(bo.getVersionId(),LinkTypeEnum.DOC.name()));
            List<ErrataLink> linkList = BeanUtil.copyToList(list,ErrataLink.class);
            last = BeanUtil.toBean(version,ErrataVersion.class);
            last.setSort(0);
            last.setDocClass(bo.getDocClass());
            last.setVersionId(version.getId());
            last.setId(null);
            save(last);
            updateLink(linkList,last.getId());
        }
        add.setStandardId(last.getStandardId());
        add.setChangeType(last.getChangeType());
        add.setApplyId(last.getApplyId());
        add.setSort(last.getSort()+1);
        boolean flag = save(add);
        if (flag) {
            bo.setId(add.getId());
            updateLink(bo.getLinkList(),add.getId());
            Version update = BeanUtil.toBean(add, Version.class);
            update.setId(add.getVersionId());
            List<DocLinkLog> linkList = BeanUtil.copyToList(bo.getLinkList(), DocLinkLog.class);
            iVersionService.updateVersionData(update,linkList,bo.getDocClass());
        }
        return flag;
    }

    private void updateLink(List<ErrataLink> linkList,String parentId){
        linkList.forEach(item->{
            item.setId(null);
            item.setParentId(parentId);
        });
        iErrataLinkService.saveBatch(linkList);
    }

    @Override
    public Boolean updateByBo(ErrataVersionBo bo) {
        ErrataVersion update = BeanUtil.toBean(bo, ErrataVersion.class);
        validEntityBeforeSave(update);
        return updateById(update);
    }

    /**
     * 保存前的数据校验
     *
     * @param entity 实体类数据
     */
    private void validEntityBeforeSave(ErrataVersion entity){
        //TODO 做一些数据校验,如唯一约束
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return removeByIds(ids);
    }
}

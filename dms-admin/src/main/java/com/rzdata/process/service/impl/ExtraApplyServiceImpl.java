package com.rzdata.process.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.blueland.bpmclient.model.ProcessInstanceModel;
import com.google.common.eventbus.AllowConcurrentEvents;
import com.google.common.eventbus.Subscribe;
import com.rzdata.config.CustomConfig;
import com.rzdata.config.ProcessConfig;
import com.rzdata.eventbus.ProcessEventBus;
import com.rzdata.eventbus.ProcessResultEvent;
import com.rzdata.framework.constant.Constants;
import com.rzdata.framework.constant.DocMsgConstants;
import com.rzdata.framework.constant.ProcessStatusConstants;
import com.rzdata.framework.core.domain.entity.SysDept;
import com.rzdata.framework.core.domain.entity.SysUser;
import com.rzdata.framework.core.mybatisplus.core.ServicePlusImpl;
import com.rzdata.framework.core.page.PagePlus;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.enums.DistributeTypeEnum;
import com.rzdata.framework.utils.PageUtils;
import com.rzdata.framework.utils.SecurityUtils;
import com.rzdata.framework.utils.StringUtils;
import com.rzdata.process.domain.*;
import com.rzdata.process.domain.bo.ExtraApplyBo;
import com.rzdata.process.domain.vo.ExtraApplyVo;
import com.rzdata.process.domain.vo.WorkflowApplyLogVo;
import com.rzdata.process.enums.*;
import com.rzdata.process.mapper.ExtraApplyMapper;
import com.rzdata.process.service.*;
import com.rzdata.process.utils.MsgTemplateUtils;
import com.rzdata.setting.service.IDocPresetUserService;
import com.rzdata.system.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.*;
import java.util.stream.Collectors;

import static com.rzdata.framework.constant.Constants.PROCESS_KEY;

/**
 * 文件增发申请Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-12-31
 */
@Slf4j
@Service
public class ExtraApplyServiceImpl extends ServicePlusImpl<ExtraApplyMapper, ExtraApply, ExtraApplyVo>
        implements IExtraApplyService {

    @Autowired
    WorkflowService workflowService;

    @Autowired
    ISysConfigService configService;

    @Autowired
    IWorkflowApplyLogService iWorkflowApplyLogService;

    @Autowired
    IExtraApplyItemService iExtraApplyItemService;

    @Autowired
    private IGenerateIdService iGenerateIdService;

    @Autowired
    IDocMessageService docMessageService;

    @Autowired
    private IWorkflowApplyLogService workflowApplyLogService;

    @Autowired
    private IModifyApplyDistributeService modifyApplyDistributeService;

    @Autowired
    IDocDistributeService iDocDistributeService;

    @Autowired
    IVersionService iVersionService;

    @Autowired
    ISysUserService sysUserService;

    @Autowired
    private ISysDeptService sysDeptService;

    @Autowired
    IDocPresetUserService iDocPresetUserService;

    @Autowired
    CustomConfig customConfig;

    @Autowired
    private ISysNotifyLogService sysNotifyLogService;

    @Autowired
    private IMessageSendEntryService messageSendEntryService;

    @Override
    public ExtraApplyVo queryById(String id) {
        return getVoById(id);
    }

    @Override
    public ExtraApplyVo queryByBpmnId(String bpmnId) {
        WorkflowApplyLogVo workflowApplyLog = workflowApplyLogService.getVoOne(
                new LambdaQueryWrapper<WorkflowApplyLog>().eq(WorkflowApplyLog::getProcInstId, bpmnId).last("limit 1"));
        return queryById(workflowApplyLog.getId());
    }

    @Override
    public TableDataInfo<ExtraApplyVo> queryPageList(ExtraApplyBo bo) {
        PagePlus<ExtraApply, ExtraApplyVo> result = pageVo(PageUtils.buildPagePlus(), buildQueryWrapper(bo));
        return PageUtils.buildDataInfo(result);
    }

    @Override
    public List<ExtraApplyVo> queryList(ExtraApplyBo bo) {
        return listVo(buildQueryWrapper(bo));
    }

    private LambdaQueryWrapper<ExtraApply> buildQueryWrapper(ExtraApplyBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<ExtraApply> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getApplyTitle()), ExtraApply::getApplyTitle, bo.getApplyTitle());
        lqw.eq(bo.getDeptId() != null, ExtraApply::getDeptId, bo.getDeptId());
        lqw.like(StringUtils.isNotBlank(bo.getUserName()), ExtraApply::getUserName, bo.getUserName());
        lqw.eq(bo.getApplyTime() != null, ExtraApply::getApplyTime, bo.getApplyTime());
        lqw.eq(StringUtils.isNotBlank(bo.getReason()), ExtraApply::getReason, bo.getReason());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), ExtraApply::getStatus, bo.getStatus());
        return lqw;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ProcessInstanceModel insertByBo(ExtraApplyBo bo) {
        ExtraApply add = BeanUtil.toBean(bo, ExtraApply.class);
        // if (ObjectUtil.isEmpty(bo.getId())) {
        // String applyId = iGenerateIdService.generateApplyId(ApplyTypeEnum.EXTRA,
        // bo.getUserName());
        // add.setId(applyId);
        // }
        if (BooleanUtil.isTrue(bo.getEditStatus())) {
            add.setCreateBy(SecurityUtils.getUsername());
        }
        boolean flag = saveOrUpdate(add);
        // 保存关系数据
        ProcessInstanceModel processInstanceModel = null;
        if (flag) {
            bo.setId(add.getId());
            if (BooleanUtil.isTrue(bo.getEditStatus())) {
                saveApplyItem(bo);
                modifyApplyDistributeService.updateModifyApplyDistribute(bo.getDistributeList(), bo.getId());
            }
            if (BooleanUtil.isTrue(bo.getPresetUserEdit())) {
                iDocPresetUserService.updatePresetUser(bo.getPresetUserList(), bo.getId());
            }
            // 开启流程 调用工作流相关接口
            try {
                bo.getBpmClientInputModel().setStatus(bo.getRecordStatus());
                bo.getBpmClientInputModel().setBizType(ApplyTypeEnum.EXTRA.name());
                if (StringUtils.equals(bo.getRecordStatus(), RecordStatusEnum.DRAFT.getCode())) {
                    processInstanceModel = workflowService.saveExecute(bo.getBpmClientInputModel(), add.getId());
                } else if (StringUtils.equals(bo.getRecordStatus(), RecordStatusEnum.CANCEL.getCode())) {
                    processInstanceModel = workflowService.cancelExecute(bo.getBpmClientInputModel(), add.getId());
                } else {
                    processInstanceModel = workflowService.nextExecute(bo.getBpmClientInputModel(), add.getId());
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return processInstanceModel;
    }

    /**
     * 同步事件监听处理
     */
    @Subscribe
    @AllowConcurrentEvents
    protected void onProcessEvent(ProcessResultEvent event) {
        ProcessConfig processConfig = JSONUtil.toBean(configService.selectConfigByKey(PROCESS_KEY),
                ProcessConfig.class);
        if (!event.getApplyType().contains(processConfig.getProcDefKeyZFSQ())) {
            return;
        }
        ExtraApply extraApply = this.baseMapper.selectById(event.getApplyId());
        if (ObjectUtil.isEmpty(extraApply)) {
            return;
        }
        extraApply.setStatus(event.getStatus());
        baseMapper.updateById(extraApply);
        String status = event.getStatus();
        String actDefName = event.getModel().getWf_nextActDefName();
        QueryWrapper<ExtraApplyItem> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ExtraApplyItem::getApplyId, extraApply.getId());
        List<ExtraApplyItem> itemList = iExtraApplyItemService.list(queryWrapper);
        if (Objects.equals(ProcessStatusConstants.TO_DONE, event.getStatus())) {
            actDefName = "结束";
            if (CollUtil.isEmpty(itemList)) {
                return;
            }
            ExtraApplyItem applyItem = itemList.get(0);
            StringBuilder msgContent = new StringBuilder();
            for (ExtraApplyItem extraApplyItem : itemList) {
                if (StringUtils.isNotEmpty(extraApply.getTrainType())) {
                    String distributeType = DistributeTypeEnum.fuse(extraApplyItem.getDistributeType(),
                            extraApply.getTrainType());
                    iVersionService.update(
                            new LambdaUpdateWrapper<Version>().eq(Version::getId, extraApplyItem.getVersionId())
                                    .set(Version::getDistributeType, distributeType));
                }
                iDocDistributeService.handleDistributeByApply(event.getApplyId(), extraApplyItem.getVersionId(),
                        extraApplyItem.getDocId(),
                        extraApplyItem.getDocName(), extraApplyItem.getDocClass(),
                        extraApplyItem.getDistributeType(), extraApply.getTrainType());
                msgContent.append(String.format(DocMessageEnum.FLOW_EXTRA.getMsg(), extraApplyItem.getDocName(), extraApplyItem.getDocId()));
                msgContent.append(";");
            }

            //有问题怎么处理
            try {
                // 发送企业微信
                docMessageService.saveMsg(
                        applyItem != null ? applyItem.getVersionId() : null,
                        extraApply.getId(),
                        DocMessageEnum.FLOW_EXTRA.getMsg(),
                        applyItem != null ? applyItem.getDocName() : null,
                        applyItem != null ? applyItem.getDocId() : null,
                        DocMessageEnum.FLOW_EXTRA.getCode(),
                        msgContent.toString(), null, null);
            } catch (Exception e) {
                log.error("ExtraApplyServiceImpl-->onProcessEvent--saveMsg--e###", e);
            }

            // 分发人发送打印消息
            sendPrintMsg(extraApply, itemList);
        }
        saveWorkFlowLog(extraApply, itemList, status, event.getProcessInst().getProcInstTitle(), event);
    }

    private void sendPrintMsg(ExtraApply extraApply, List<ExtraApplyItem> itemList) {
        // 1、获取所有的文件
        if (CollUtil.isEmpty(itemList)) {
            log.error("ExtraApplyServiceImpl-->sendPrintMsg----extraApply---itemList is null");
            return;
        }
        // 是否开启分发
        if (StrUtil.isBlank(extraApply.getIsDistribute()) || !Constants.VALUE_Y.equals(extraApply.getIsDistribute())) {
            log.error("ExtraApplyServiceImpl-->sendPrintMsg----extraApply---分发未开启");
            return;
        }

        // 找到所有的版本
        List<String> versionIdList = itemList.stream().map(ExtraApplyItem::getVersionId).collect(Collectors.toList());
        List<Version> versionList = iVersionService
                .list(new LambdaQueryWrapper<Version>().in(Version::getId, versionIdList));
        if (CollUtil.isEmpty(versionList)) {
            log.error("ExtraApplyServiceImpl-->sendPrintMsg----extraApply---版本为空");
            return;
        }
        // 获取所有版本的分发记录
        List<ModifyApplyDistribute> distributeList = modifyApplyDistributeService.list(new LambdaQueryWrapper<ModifyApplyDistribute>()
                .eq(ModifyApplyDistribute::getApplyId, extraApply.getId())
                .eq(ModifyApplyDistribute::getCategory, Constants.DISTRIBUTE_TYPE_PRINT));

        if(CollUtil.isEmpty(distributeList)){
            log.error("ExtraApplyServiceImpl-->sendPrintMsg----extraApply---发送对象为空");
            return;
        }
        // 根据接收人信息去重
        distributeList = distributeList.stream()
                .collect(Collectors.collectingAndThen(
                        Collectors.toMap(
                                distribute -> distribute.getReceiveUserName() + ":" + distribute.getReceiveNickName(),
                                distribute -> distribute,
                                (existing, replacement) -> existing),
                        map -> new ArrayList<>(map.values())));

        for (ModifyApplyDistribute modifyApplyDistribute : distributeList) {
            SysUser user = sysUserService.selectUserByUserName(modifyApplyDistribute.getReceiveUserName());
            if (ObjectUtil.isEmpty(user)) {
                continue;
            }

            Map<String, String> param = new HashMap<>();
            StringBuilder content = new StringBuilder();
            StringBuilder txtContent = new StringBuilder();
            // 遍历版本构建发送消息的内容
            for (Version version : versionList) {
                String linkUrl = MsgTemplateUtils.buildPcFileDetailUrl(version.getDocId(), version.getId());
                String mobileUrl = MsgTemplateUtils.buildMobilFileDetailUrl(version.getDocId(), version.getId());
                content.append(MsgTemplateUtils.printHtmlContent(version.getDocName(), version.getVersionValue(), linkUrl, mobileUrl));
                appendTextContent(txtContent, version);
            }

            if (content.length() == 0) {
                continue;
            }
            param.put("title", "文件增发");
            param.put("content", content.toString());
            txtContent.append("请联系部门文控进行打印。");


            try {
                // 发送邮件
                sysNotifyLogService.sendEmail(SendType.PRINT.getCode(), user.getNickName(), user.getEmail(),
                        user.getUserId(), param);
            } catch (Exception e) {
                log.error("sendPrintMsg--发送回收邮件异常: ", e);
            }

            String msgContent = "【文件增发-打印提醒】" + txtContent.toString();
            for (Version version : versionList) {
                String linkUrl = MsgTemplateUtils.buildPcFileDetailUrl(version.getDocId(), version.getId());
                String mobileUrl = MsgTemplateUtils.buildMobilFileDetailUrl(version.getDocId(), version.getId());
                String contentMsg = "【文件增发-打印提醒】文件名称：" + version.getDocName() + "，版本：" + version.getVersionValue();
                try {
                    // 发送站内信
                    docMessageService.sendInstationMessage(extraApply.getId(), version.getDocId(), null,
                            version.getDocName(), version.getId(), null,
                            extraApply.getDeptId(), extraApply.getDeptName(), user.getUserName(), user.getUserId(), contentMsg,
                            DocMsgConstants.MSG_TYPE_MSG, MsgTypeEnum.PRINT.getType(), linkUrl, mobileUrl,
                            SecurityUtils.getLoginUser().getTenantId());
                } catch (Exception e) {
                    log.error("sendPrintMsg--发送站内信异常: ", e);
                }
            }

            try {
                // 发送企业微信消息
                messageSendEntryService.sendMsgQywx(null, extraApply.getId(),
                        user.getUserName(), msgContent);
            } catch (Exception e) {
                log.error("sendPrintMsg--发送企业微信消息异常: ", e);
            }
        }
    }
    private void appendTextContent(StringBuilder txtContent, Version version) {
        txtContent.append("文件名称：");
        txtContent.append(version.getDocName());
        txtContent.append("，");
        txtContent.append("版本：");
        txtContent.append(version.getVersionValue());
        txtContent.append("; ");
    }

    /**
     * 保存申请记录
     */
    public void saveWorkFlowLog(ExtraApply apply, List<ExtraApplyItem> itemList, String status, String procTitle,
            ProcessResultEvent event) {
        WorkflowApplyLog log = new WorkflowApplyLog();
        log.setId(apply.getId());
        log.setDocId(itemList.stream().map(ExtraApplyItem::getDocId).filter(StringUtils::isNotEmpty)
                .collect(Collectors.joining(Constants.ID_SPLIT_KEY)));
        log.setVersionValue(itemList.stream().map(ExtraApplyItem::getVersionValue)
                .collect(Collectors.joining(Constants.ID_SPLIT_KEY)));
        log.setDocName(procTitle);
        log.setProcStatus(status);
        log.setUserName(apply.getUserName());
        log.setDeptId(apply.getDeptId());
        workflowApplyLogService.updateStatusByBusId(log, event);
    }

    /**
     * 发送培训邮件提醒
     * 1.获取所有的文件
     * 2.获取所有培训的人员,去重
     * 3.发送邮件
     *
     * @param extraApply 增发流程对象
     * @param itemList   所有的增发邮件
     */
    /*public void sendTrainEmail(ExtraApply extraApply, List<ExtraApplyItem> itemList) {
        // 1、获取所有的文件
        if (CollUtil.isEmpty(itemList)) {
            return;
        }
        // 是否开启培训
        if (StrUtil.isBlank(extraApply.getIsTrain()) || !Constants.VALUE_Y.equals(extraApply.getIsTrain())) {
            return;
        }
        // 找到所有的版本
        List<String> versionIdList = itemList.stream().map(ExtraApplyItem::getVersionId).collect(Collectors.toList());
        List<Version> versionList = iVersionService
                .list(new LambdaQueryWrapper<Version>().in(Version::getId, versionIdList));
        if (CollUtil.isEmpty(versionList)) {
            return;
        }
        // 2.获取所有培训的人员
        List<SysUser> sendUserList = getSendUserList(extraApply);
        if (CollUtil.isEmpty(sendUserList)) {
            return;
        }
        // 3.发送邮件
        sendUserEmail(extraApply, versionList, sendUserList);
    }*/

    /**
     * 获取所有培训的人员
     *
     * @param extraApply
     * @return
     */
    private List<SysUser> getSendUserList(ExtraApply extraApply) {
        List<SysUser> sendUserList = new ArrayList<>();
        List<ModifyApplyDistribute> modifyApplyDistributes = modifyApplyDistributeService
                .list(new LambdaQueryWrapper<ModifyApplyDistribute>().eq(ModifyApplyDistribute::getApplyId,
                        extraApply.getId()));

        if (CollUtil.isEmpty(modifyApplyDistributes)) {
            return sendUserList;
        }
        // 部门下的所有用户
        List<SysDept> comSysDeptList = new ArrayList<>();
        List<SysUser> comSysUserList = new ArrayList<>();
        List<SysUser> deptSysUserList = new ArrayList<>();
        List<SysUser> personList = new ArrayList<>();
        List<String> comDeptIdList = modifyApplyDistributes.stream()
                .filter(item -> item.getType().equals(Constants.TYPE_COMPANY))
                .map(ModifyApplyDistribute::getReceiveUserDeptId).collect(Collectors.toList());
        List<String> deptIdList = modifyApplyDistributes.stream()
                .filter(item -> item.getType().equals(Constants.TYPE_DEPT))
                .map(ModifyApplyDistribute::getReceiveUserDeptId).collect(Collectors.toList());
        List<String> userNameList = modifyApplyDistributes.stream()
                .filter(item -> item.getType().equals(Constants.TYPE_PERSON))
                .map(ModifyApplyDistribute::getReceiveUserName).collect(Collectors.toList());
        // 公司
        if (CollUtil.isNotEmpty(comDeptIdList)) {
            comSysDeptList = sysDeptService
                    .list(new LambdaQueryWrapper<SysDept>().in(SysDept::getParentId, comDeptIdList));
            List<String> deptList = comSysDeptList.stream().map(SysDept::getDeptId).collect(Collectors.toList());
            comSysUserList = sysUserService.list(new LambdaQueryWrapper<SysUser>().in(SysUser::getDeptId, deptList));
        }
        // 部门
        if (CollUtil.isNotEmpty(deptIdList)) {
            deptSysUserList = sysUserService.list(new LambdaQueryWrapper<SysUser>().in(SysUser::getDeptId, deptIdList));
        }
        // 个人用户
        if (CollUtil.isNotEmpty(userNameList)) {
            personList = sysUserService.list(new LambdaQueryWrapper<SysUser>().in(SysUser::getUserName, userNameList));
        }

        // 获取所有的用户
        List<SysDept> depts = new ArrayList<>();
        for (ModifyApplyDistribute docDistribute : modifyApplyDistributes) {
            if (Constants.TYPE_COMPANY.equals(docDistribute.getType())) {
                depts.clear();
                // 当为公司的时候，查询公司下的所有用户，在根据用户来查询阅读记录表中是否有数据，有则带上状态和时间
                if (CollUtil.isEmpty(comSysDeptList)) {
                    continue;
                }
                for (SysDept sysDept : comSysDeptList) {
                    if (docDistribute.getReceiveUserDeptId().equals(sysDept.getParentId())) {
                        depts.add(sysDept);
                    }
                }
                if (CollUtil.isEmpty(comSysUserList)) {
                    continue;
                }
                for (SysDept dept : depts) {
                    for (SysUser sysUser : comSysUserList) {
                        if (dept.getDeptId().equals(sysUser.getDeptId())) {
                            sendUserList.add(sysUser);
                        }
                    }
                }
            } else if (Constants.TYPE_DEPT.equals(docDistribute.getType())) {
                // 当为部门的时候，查询部门下所有用户，在根据用户来查询阅读记录表中是否有数据
                if (CollUtil.isEmpty(deptSysUserList)) {
                    continue;
                }
                for (SysUser sysUser : deptSysUserList) {
                    if (docDistribute.getReceiveUserDeptId().equals(sysUser.getDeptId())) {
                        sendUserList.add(sysUser);
                    }
                }

            } else if (Constants.TYPE_PERSON.equals(docDistribute.getType())) {
                if (CollUtil.isEmpty(personList)) {
                    continue;
                }
                for (SysUser sysUser : personList) {
                    if (docDistribute.getReceiveUserName().equals(sysUser.getUserName())) {
                        sendUserList.add(sysUser);
                    }
                }
            }
        }
        if (CollUtil.isEmpty(sendUserList)) {
            return sendUserList;
        }
        // 去重
        sendUserList = sendUserList.stream().distinct().collect(Collectors.toList());
        // 过滤掉没有邮箱的短信
        sendUserList = sendUserList.stream().filter(item -> StrUtil.isNotBlank(item.getEmail()))
                .collect(Collectors.toList());
        return sendUserList;
    }

    /**
     * 给用户发送邮件
     *
     * @param extraApply   增发流程对象
     * @param versionList  文件版本
     * @param sendUserList 需要发送的用户
     */
    /*private void sendUserEmail(ExtraApply extraApply, List<Version> versionList, List<SysUser> sendUserList) {
        // 查询所有的培训邮件
        List<SysNotifyLog> sysNotifyLogList = sysNotifyLogService.list(new LambdaQueryWrapper<SysNotifyLog>()
                .eq(SysNotifyLog::getStatus, Constants.LONG_ONE).eq(SysNotifyLog::getTitle, SendType.XG.getMsg()));

        // 找到所有的培训邮件
        for (SysUser sysUser : sendUserList) {
            Map<String, String> param = new HashMap<>();
            StringBuilder content = new StringBuilder();
            // 变更前内容
            String changeReason = "";
            // 变更后内容
            String changeContent = "";
            for (Version version : versionList) {
                // 过滤掉已经发送的用户和文件
                if (CollUtil.isNotEmpty(sysNotifyLogList)) {
                    String nickName = sysUser.getNickName();
                    String docName = version.getDocName();
                    boolean exists = sysNotifyLogList.stream()
                            .anyMatch(
                                    item -> item.getNickName().equals(nickName) && item.getMessage().contains(docName));
                    if (exists) {
                        continue;
                    }
                }
                String linkUrl = customConfig.getFrontUrl() + "file/detail?versionId=" + version.getId() + "&docId="
                        + version.getDocId();
                content.append("<p>");
                content.append("<span style='font-family:宋体'><a href='" + linkUrl + "'target='_blank'");
                content.append(
                        "<span style='font-family: 宋体; font-size: 14px; color: #548DD4;'>文件名称：<span style='font-size: 14px; font-family: 宋体; text-wrap: wrap;'>");
                content.append(version.getDocName());
                content.append("</span>");
                content.append("，版本：<span style='font-size: 14px; font-family: 宋体; text-wrap: wrap;'>");
                content.append(version.getVersionValue());
                content.append("</span>");
                content.append("</span></a></span>");
                content.append("</p>");

                changeContent = version.getContent();
                changeReason = version.getChangeReason();
            }
            if (StrUtil.isBlank(content.toString())) {
                continue;
            }
            param.put("changeContent", changeContent);
            param.put("changeReason", changeReason);
            param.put("content", content.toString());
            String createDateTime = DateUtil.format(extraApply.getUpdateTime(), Constants.YYYY_MM_DD);
            String afterDateTime = DateUtil.format(DateUtil.offsetMonth(extraApply.getUpdateTime(), 1),
                    Constants.YYYY_MM_DD);
            String dateTimeContent = createDateTime + "至" + afterDateTime + "（1个月）";
            param.put("dateTime", dateTimeContent);
            param.put("userName", sysUser.getNickName());
            sysNotifyLogService.sendEmail(SendType.XG.getCode(), sysUser.getNickName(), sysUser.getEmail(),
                    sysUser.getUserId(), param);
        }
    }*/

    /**
     * 注册事件
     */
    @PostConstruct
    private void registerEventBus() {
        ProcessEventBus.register(this);
    }

    /**
     * 取消注册事件
     */
    @PreDestroy
    private void unregisterEventBus() {
        ProcessEventBus.unregister(this);
    }

    /**
     * 保存文件申请数量
     *
     * @param bo
     */
    private void saveApplyItem(ExtraApplyBo bo) {
        QueryWrapper<ExtraApplyItem> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ExtraApplyItem::getApplyId, bo.getId());
        iExtraApplyItemService.remove(queryWrapper);
        if (ObjectUtil.isNotEmpty(bo.getItemList())) {
            for (ExtraApplyItem extraApplyItem : bo.getItemList()) {
                extraApplyItem.setApplyId(bo.getId());
            }
            iExtraApplyItemService.saveBatch(bo.getItemList());
        }
    }

    @Override
    public Boolean updateByBo(ExtraApplyBo bo) {
        ExtraApply update = BeanUtil.toBean(bo, ExtraApply.class);
        bo.setChangeType(ApplyTypeEnum.EXTRA.toString());
        validEntityBeforeSave(update);
        saveApplyItem(bo);
        modifyApplyDistributeService.updateModifyApplyDistribute(bo.getDistributeList(), bo.getId());
        String procTitle = workflowService.updateFlowTitle(bo.getApplyTitle(), bo.getId());
        if (BooleanUtil.isTrue(bo.getPresetUserEdit())) {
            iDocPresetUserService.updatePresetUser(bo.getPresetUserList(), bo.getId());
        }
        saveWorkFlowLog(update, bo.getItemList(), bo.getRecordStatus(), procTitle, null);
        return updateById(update);
    }

    /**
     * 保存前的数据校验
     *
     * @param entity 实体类数据
     */
    private void validEntityBeforeSave(ExtraApply entity) {
        // TODO 做一些数据校验,如唯一约束
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        if (isValid) {
            // TODO 做一些业务上的校验,判断是否需要校验
        }
        return removeByIds(ids);
    }
}

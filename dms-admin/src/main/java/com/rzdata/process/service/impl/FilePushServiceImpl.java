package com.rzdata.process.service.impl;

import cn.hutool.core.bean.BeanUtil;
    import com.rzdata.framework.utils.PageUtils;
import com.rzdata.framework.core.page.PagePlus;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.utils.StringUtils;
import org.springframework.stereotype.Service;
import com.rzdata.framework.core.mybatisplus.core.ServicePlusImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.rzdata.process.domain.bo.FilePushBo;
import com.rzdata.process.domain.vo.FilePushVo;
import com.rzdata.process.domain.FilePush;
import com.rzdata.process.mapper.FilePushMapper;
import com.rzdata.process.service.IFilePushService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 文件推送Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-05
 */
@Service
public class FilePushServiceImpl extends ServicePlusImpl<FilePushMapper, FilePush, FilePushVo> implements IFilePushService {

    @Override
    public FilePushVo queryById(String id){
        return getVoById(id);
    }

    @Override
    public TableDataInfo<FilePushVo> queryPageList(FilePushBo bo) {
        PagePlus<FilePush, FilePushVo> result = pageVo(PageUtils.buildPagePlus(), buildQueryWrapper(bo));
        return PageUtils.buildDataInfo(result);
    }

    @Override
    public List<FilePushVo> queryList(FilePushBo bo) {
        return listVo(buildQueryWrapper(bo));
    }

    private LambdaQueryWrapper<FilePush> buildQueryWrapper(FilePushBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<FilePush> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getApplyId()), FilePush::getApplyId, bo.getApplyId());
        lqw.eq(StringUtils.isNotBlank(bo.getPushCompanyId()), FilePush::getPushCompanyId, bo.getPushCompanyId());
        lqw.like(StringUtils.isNotBlank(bo.getPushCompanyName()), FilePush::getPushCompanyName, bo.getPushCompanyName());
        lqw.eq(StringUtils.isNotBlank(bo.getPashClassId()), FilePush::getPashClassId, bo.getPashClassId());
        lqw.like(StringUtils.isNotBlank(bo.getPashClassName()), FilePush::getPashClassName, bo.getPashClassName());
        lqw.like(StringUtils.isNotBlank(bo.getReceiveUserName()), FilePush::getReceiveUserName, bo.getReceiveUserName());
        lqw.like(StringUtils.isNotBlank(bo.getReceiveNickName()), FilePush::getReceiveNickName, bo.getReceiveNickName());
        lqw.eq(StringUtils.isNotBlank(bo.getReceiveDeptId()), FilePush::getReceiveDeptId, bo.getReceiveDeptId());
        lqw.like(StringUtils.isNotBlank(bo.getReceiveDeptName()), FilePush::getReceiveDeptName, bo.getReceiveDeptName());
        lqw.eq(StringUtils.isNotBlank(bo.getFileId()), FilePush::getFileId, bo.getFileId());
        lqw.eq(StringUtils.isNotBlank(bo.getPashVersionId()), FilePush::getPashVersionId, bo.getPashVersionId());
        lqw.eq(StringUtils.isNotBlank(bo.getPashDocId()), FilePush::getPashDocId, bo.getPashDocId());
        lqw.like(StringUtils.isNotBlank(bo.getPashDocName()), FilePush::getPashDocName, bo.getPashDocName());
        lqw.eq(StringUtils.isNotBlank(bo.getPashVersionValue()), FilePush::getPashVersionValue, bo.getPashVersionValue());
        lqw.eq(StringUtils.isNotBlank(bo.getVersionId()), FilePush::getVersionId, bo.getVersionId());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), FilePush::getStatus, bo.getStatus());
        return lqw;
    }

    @Override
    public Boolean insertByBo(FilePushBo bo) {
        FilePush add = BeanUtil.toBean(bo, FilePush.class);
        validEntityBeforeSave(add);
        boolean flag = save(add);
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    @Override
    public Boolean updateByBo(FilePushBo bo) {
        FilePush update = BeanUtil.toBean(bo, FilePush.class);
        validEntityBeforeSave(update);
        return updateById(update);
    }

    /**
     * 保存前的数据校验
     *
     * @param entity 实体类数据
     */
    private void validEntityBeforeSave(FilePush entity){
        //TODO 做一些数据校验,如唯一约束
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return removeByIds(ids);
    }
}

package com.rzdata.process.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Maps;
import com.rzdata.framework.constant.CommonI18nConstant;
import com.rzdata.framework.utils.I18nUtils;
import com.rzdata.framework.utils.StringUtils;
import com.rzdata.process.domain.dto.CreateNewNoDTO;
import com.rzdata.process.domain.dto.DocIdNumDTO;
import com.rzdata.process.domain.vo.DocNoVo;
import com.rzdata.process.enums.ApplyTypeEnum;
import com.rzdata.process.enums.ProcessNoRuleEnum;
import com.rzdata.process.service.*;
import com.rzdata.setting.domain.*;
import com.rzdata.setting.service.*;
import com.rzdata.system.service.ISysConfigService;
import com.rzdata.system.service.ISysDictTypeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2022/1/12 上午10:28
 * @Version 1.0
 * @Desc 生成ID
 */
@Slf4j
@Service
public class GenerateIdServiceImpl implements IGenerateIdService {
    @Autowired
    private ICodeRuleLogService iCodeRuleLogService;
    @Autowired
    private ICodeRuleService iCodeRuleService;
    @Autowired
    ISysDictTypeService iSysDictTypeService;

    @Override
    public DocNoVo generateRecordDocId(Map<String,Object> bizMap) throws Exception{
        DocNoVo docNoVo = new DocNoVo();
        String ruleId = iCodeRuleService.getDocRuleId(bizMap.get("docClass").toString());
        CodeRuleLog codeRuleLog = iCodeRuleService.generatorEncodingRule(ruleId,bizMap.get("FileId")!=null?bizMap.get("FileId").toString():"",bizMap);
        CodeRule codeRule = iCodeRuleService.getById(ruleId);
        docNoVo.setDocId(codeRuleLog.getRuleValue());
        docNoVo.setTwo(String.format("%0"+codeRule.getNumberDigit()+"d",codeRuleLog.getNumberValue()));
        docNoVo.setOne(StrUtil.removeSuffix(docNoVo.getDocId(),docNoVo.getTwo()));
        return docNoVo;
    }

    @Override
    public DocNoVo generateDocId(Map<String,Object> bizMap) throws Exception{
        DocNoVo docNoVo = new DocNoVo();
        String ruleId = iCodeRuleService.getDocRuleId(bizMap.get("docClass").toString());
        CodeRuleLog codeRuleLog = iCodeRuleService.generatorEncodingRule(ruleId,bizMap.get("id")!=null?bizMap.get("id").toString():"",bizMap);
        CodeRule codeRule = iCodeRuleService.getById(ruleId);
        docNoVo.setDocId(codeRuleLog.getRuleValue());
        docNoVo.setTwo(String.format("%0"+codeRule.getNumberDigit()+"d",codeRuleLog.getNumberValue()));
        docNoVo.setOne(StrUtil.removeSuffix(docNoVo.getDocId(),docNoVo.getTwo()));
        return docNoVo;
    }
    public DocNoVo generateDocId(String docClass,String projectCode) throws Exception{
        Map<String,Object> bizMap = new HashMap<>();
        bizMap.put("projectCode",projectCode);
        bizMap.put("docClass",docClass);
        DocNoVo docNoVo = new DocNoVo();
        String ruleId = iCodeRuleService.getDocRuleId(docClass);
        CodeRuleLog codeRuleLog = iCodeRuleService.generatorEncodingRule(ruleId,docClass+projectCode,bizMap);
        CodeRule codeRule = iCodeRuleService.getById(ruleId);
        docNoVo.setDocId(codeRuleLog.getRuleValue());
        docNoVo.setTwo(String.format("%0"+codeRule.getNumberDigit()+"d",codeRuleLog.getNumberValue()));
        docNoVo.setOne(StrUtil.removeSuffix(docNoVo.getDocId(),docNoVo.getTwo()));
        return docNoVo;
    }
    @Override
    public boolean saveOldDocId(String buinessId,String docClass,String docId,String oldDocClass,Map<String,Object> map){
        try{
            String newRuleId = "";
            String oldRuleId = "";
            if (StringUtils.equals(docClass,oldDocClass)) {
                oldRuleId = iCodeRuleService.getDocRuleId(oldDocClass);
                newRuleId = oldRuleId;
            }else {
                newRuleId = iCodeRuleService.getDocRuleId(docClass);
                oldRuleId = iCodeRuleService.getDocRuleId(oldDocClass);
            }
            CodeRuleLog log = iCodeRuleLogService.getOne(new LambdaQueryWrapper<CodeRuleLog>().eq(CodeRuleLog::getRuleId,oldRuleId).eq(CodeRuleLog::getBusinessId,buinessId));
            DocIdNumDTO dto = iCodeRuleService.getNumberValueByDocId(docId,log!=null?log.getRuleId():oldRuleId,map);
            CodeRule codeRule = iCodeRuleService.getById(newRuleId);
            if (log==null) {
                log = new CodeRuleLog();
            }
            iCodeRuleService.logSetFileType(codeRule,log,map);
            log.setRuleValue(docId);
            log.setBusinessId(buinessId);
            log.setNumberValue(dto.getValue());
            log.setRuleId(codeRule.getId());
            log.setCreateTime(DateUtil.date());
            iCodeRuleLogService.saveOrUpdate(log);
            return true;
        } catch(Exception ex){
            log.error("saveOldDocId==="+buinessId,ex);
            return false;
        }

    }
    @Override
    public boolean checkDocIdExist(String docId){
        QueryWrapper<CodeRuleLog> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(StringUtils.isNotBlank(docId), CodeRuleLog::getRuleValue, docId);
        return iCodeRuleLogService.count(queryWrapper) > 0 ? true : false;
    }

    @Override
    public Boolean updateDocSerialNumber(CreateNewNoDTO bo) throws Exception{
        return updateSerialNumber(bo);

    }

    @Override
    public Boolean updateRecordSerialNumber(CreateNewNoDTO bo) throws Exception{
        return updateSerialNumber(bo);
    }

    private Boolean updateSerialNumber(CreateNewNoDTO bo)throws Exception{
        if(StrUtil.equalsIgnoreCase(bo.getNewNo(),bo.getOldNo())){
            return true;
        }
        CodeRuleLog codeRuleLog = iCodeRuleLogService.getOne(new QueryWrapper<>(CodeRuleLog.builder().ruleValue(bo.getOldNo()).businessId(bo.getBusId()).build()));
        if(codeRuleLog==null){
            throw new Exception(I18nUtils.getTitle(CommonI18nConstant.FILE_NOT_FOUND_OLD_NUM));
        }
        CodeRule codeRule = iCodeRuleService.getById(codeRuleLog.getRuleId());
        try{
            long numberValue = Long.valueOf(bo.getNum());

            codeRuleLog.setNumberValue(numberValue);
            codeRuleLog.setRuleValue(bo.getNewNo());
        }
        catch (Exception ex){
            throw new Exception(I18nUtils.getTitle(CommonI18nConstant.FILE_STREAM_NUM_NOT_VALUE));
        }
        long maxValue = 9;
        if(codeRule.getNumberDigit()>1){
            maxValue = Long.valueOf(String.format("1%1$0"+(codeRule.getNumberDigit()-1)+"d",0))*10-1;
        }
        if(codeRuleLog.getNumberValue()>maxValue){
            throw new Exception(I18nUtils.getTitle(CommonI18nConstant.FILE_STREAM_NUM_EXCEED_RULE));
        }
        QueryWrapper<CodeRuleLog> queryWrapper = new QueryWrapper<CodeRuleLog>(CodeRuleLog.builder().ruleId(codeRuleLog.getRuleId()).ruleValue(bo.getNewNo()).build());
        if(iCodeRuleLogService.count(queryWrapper)>0){
            throw new Exception(I18nUtils.getTitle(CommonI18nConstant.FILE_NUM_DUPLICATE));
        }

        iCodeRuleLogService.updateById(codeRuleLog);
        return true;
    }
}

package com.rzdata.process.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.blueland.bpmclient.model.ProcessInstanceModel;
import com.google.common.eventbus.AllowConcurrentEvents;
import com.google.common.eventbus.Subscribe;
import com.rzdata.config.ProcessConfig;
import com.rzdata.eventbus.ProcessEventBus;
import com.rzdata.eventbus.ProcessResultEvent;
import com.rzdata.framework.constant.ProcessStatusConstants;
import com.rzdata.framework.core.mybatisplus.core.ServicePlusImpl;
import com.rzdata.framework.core.page.PagePlus;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.utils.PageUtils;
import com.rzdata.framework.utils.SecurityUtils;
import com.rzdata.framework.utils.StringUtils;
import com.rzdata.process.domain.LostApply;
import com.rzdata.process.domain.LostApplyItem;
import com.rzdata.process.domain.WorkflowApplyLog;
import com.rzdata.process.domain.bo.LostApplyBo;
import com.rzdata.process.domain.bo.WorkflowApplyLogBo;
import com.rzdata.process.domain.vo.LostApplyVo;
import com.rzdata.process.domain.vo.WorkflowApplyLogVo;
import com.rzdata.process.enums.ApplyStatusEnum;
import com.rzdata.process.enums.ApplyTypeEnum;
import com.rzdata.process.enums.DocMessageEnum;
import com.rzdata.process.enums.RecordStatusEnum;
import com.rzdata.process.mapper.LostApplyMapper;
import com.rzdata.process.service.*;
import com.rzdata.setting.service.IDocPresetUserService;
import com.rzdata.system.service.ISysConfigService;
import com.rzdata.system.service.WorkflowService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.*;
import java.util.stream.Collectors;

import static com.rzdata.framework.constant.Constants.PROCESS_KEY;

/**
 * 文件丢失申请Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-04-23
 */
@Slf4j
@Service
public class LostApplyServiceImpl extends ServicePlusImpl<LostApplyMapper, LostApply, LostApplyVo> implements ILostApplyService {

    @Autowired
    ISysConfigService configService;

    @Autowired
    IWorkflowApplyLogService iWorkflowApplyLogService;

    @Autowired
    ILostApplyItemService iLostApplyItemService;

    @Autowired
    private IGenerateIdService iGenerateIdService;

    @Autowired
    WorkflowService workflowService;

    @Autowired
    IDocDistributeService iDocDistributeService;

    @Autowired
    IDocMessageService docMessageService;

    @Autowired
    IDocPresetUserService iDocPresetUserService;


    @Override
    public LostApplyVo queryById(String id){
        return getVoById(id);
    }

    @Override
    public LostApplyVo queryByBpmnId(String bpmnId) {
        WorkflowApplyLogVo workflowApplyLog = iWorkflowApplyLogService.getVoOne(new LambdaQueryWrapper<WorkflowApplyLog>().eq(WorkflowApplyLog::getProcInstId,bpmnId).last("limit 1"));
        return queryById(workflowApplyLog.getId());
    }

    @Override
    public TableDataInfo<LostApplyVo> queryPageList(LostApplyBo bo) {
        PagePlus<LostApply, LostApplyVo> result = pageVo(PageUtils.buildPagePlus(), buildQueryWrapper(bo));
        return PageUtils.buildDataInfo(result);
    }

    @Override
    public List<LostApplyVo> queryList(LostApplyBo bo) {
        return listVo(buildQueryWrapper(bo));
    }

    private LambdaQueryWrapper<LostApply> buildQueryWrapper(LostApplyBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<LostApply> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getApplyTitle()), LostApply::getApplyTitle, bo.getApplyTitle());
        lqw.eq(StringUtils.isNotBlank(bo.getApplyType()), LostApply::getApplyType, bo.getApplyType());
        lqw.eq(StringUtils.isNotBlank(bo.getDeptId()), LostApply::getDeptId, bo.getDeptId());
        lqw.like(StringUtils.isNotBlank(bo.getDeptName()), LostApply::getDeptName, bo.getDeptName());
        lqw.like(StringUtils.isNotBlank(bo.getUserName()), LostApply::getUserName, bo.getUserName());
        lqw.like(StringUtils.isNotBlank(bo.getNickName()), LostApply::getNickName, bo.getNickName());
        lqw.eq(StringUtils.isNotBlank(bo.getVersionId()), LostApply::getVersionId, bo.getVersionId());
        lqw.eq(StringUtils.isNotBlank(bo.getDocId()), LostApply::getDocId, bo.getDocId());
        lqw.like(StringUtils.isNotBlank(bo.getDocName()), LostApply::getDocName, bo.getDocName());
        lqw.eq(bo.getApplyTime() != null, LostApply::getApplyTime, bo.getApplyTime());
        lqw.eq(StringUtils.isNotBlank(bo.getVersionValue()), LostApply::getVersionValue, bo.getVersionValue());
        lqw.eq(StringUtils.isNotBlank(bo.getDocClass()), LostApply::getDocClass, bo.getDocClass());
        lqw.eq(StringUtils.isNotBlank(bo.getIsPuttingOut()), LostApply::getIsPuttingOut, bo.getIsPuttingOut());
        lqw.eq(StringUtils.isNotBlank(bo.getReason()), LostApply::getReason, bo.getReason());
        lqw.eq(StringUtils.isNotBlank(bo.getEffect()), LostApply::getEffect, bo.getEffect());
        lqw.eq(StringUtils.isNotBlank(bo.getPrecaution()), LostApply::getPrecaution, bo.getPrecaution());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), LostApply::getStatus, bo.getStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getTenantId()), LostApply::getTenantId, bo.getTenantId());
        return lqw;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ProcessInstanceModel insertByBo(LostApplyBo bo) {
        LostApply add = BeanUtil.toBean(bo, LostApply.class);
//        if (ObjectUtil.isEmpty(bo.getId())) {
//            String applyId = iGenerateIdService.generateApplyId(ApplyTypeEnum.LOST, bo.getUserName());
//            add.setId(applyId);
//        }
        if (BooleanUtil.isTrue(bo.getEditStatus())) {
            add.setCreateBy(SecurityUtils.getUsername());
        }
        boolean flag = saveOrUpdate(add);
        // 保存关系数据
        ProcessInstanceModel processInstanceModel = null;
        if (flag) {
            bo.setId(add.getId());
            if (BooleanUtil.isTrue(bo.getEditStatus())) {
                saveApplyItem(bo);
            }
            if (BooleanUtil.isTrue(bo.getPresetUserEdit())) {
                iDocPresetUserService.updatePresetUser(bo.getPresetUserList(),bo.getId());
            }
            // 开启流程 调用工作流相关接口
            try {
                bo.getBpmClientInputModel().setStatus(bo.getRecordStatus());
                bo.getBpmClientInputModel().setBizType(ApplyTypeEnum.LOST.name());
                if(StringUtils.equals(bo.getRecordStatus(), RecordStatusEnum.DRAFT.getCode())){
                    processInstanceModel = workflowService.saveExecute(bo.getBpmClientInputModel(), add.getId());
                }else if (StringUtils.equals(bo.getRecordStatus(),RecordStatusEnum.CANCEL.getCode())){
                    processInstanceModel = workflowService.cancelExecute(bo.getBpmClientInputModel(), add.getId());
                }else{
                    processInstanceModel = workflowService.nextExecute(bo.getBpmClientInputModel(), add.getId());
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return processInstanceModel;
    }

    private void saveApplyItem(LostApplyBo bo) {
        QueryWrapper<LostApplyItem> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(LostApplyItem:: getApplyId, bo.getId());
        iLostApplyItemService.remove(queryWrapper);
        if (ObjectUtil.isNotEmpty(bo.getItemList())) {
            for (LostApplyItem item : bo.getItemList()) {
                item.setApplyId(bo.getId());
            }
            iLostApplyItemService.saveBatch(bo.getItemList());
        }
    }

    @Override
    public Boolean updateByBo(LostApplyBo bo) {
        LostApply update = BeanUtil.toBean(bo, LostApply.class);
        validEntityBeforeSave(update);
        saveApplyItem(bo);
        if (BooleanUtil.isTrue(bo.getPresetUserEdit())) {
            iDocPresetUserService.updatePresetUser(bo.getPresetUserList(),bo.getId());
        }
        String procTitle = workflowService.updateFlowTitle(bo.getApplyTitle(),bo.getId());
        saveWorkFlowLog(update,bo.getRecordStatus(),procTitle,null);
        return updateById(update);
    }


    /**
     * 保存前的数据校验
     *
     * @param entity 实体类数据
     */
    private void validEntityBeforeSave(LostApply entity){
        //TODO 做一些数据校验,如唯一约束
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return removeByIds(ids);
    }

    /**
     * 注册事件
     */
    @PostConstruct
    private void registerEventBus() {
        ProcessEventBus.register(this);
    }

    /**
     * 取消注册事件
     */
    @PreDestroy
    private void unregisterEventBus() {
        ProcessEventBus.unregister(this);
    }

    /**
     * 同步事件监听处理
     */
    @Subscribe
    @AllowConcurrentEvents
    protected void onProcessEvent(ProcessResultEvent event) {
        ProcessConfig processConfig = JSONUtil.toBean(configService.selectConfigByKey(PROCESS_KEY), ProcessConfig.class);
        if (!event.getApplyType().contains(processConfig.getProcDefKeyDSSQ())) {
            return;
        }
        LostApply lostApply = this.baseMapper.selectById(event.getApplyId());
        if (ObjectUtil.isEmpty(lostApply)) {
            return;
        }
        lostApply.setStatus(event.getStatus());
        this.baseMapper.updateById(lostApply);
        String status = event.getStatus();
        String actDefName = event.getModel().getWf_nextActDefName();
        if (Objects.equals(ProcessStatusConstants.TO_DONE, event.getStatus())) {
            actDefName = "结束";
            List<LostApplyItem> itemList = iLostApplyItemService.list(new LambdaQueryWrapper<LostApplyItem>().eq(LostApplyItem::getApplyId,event.getApplyId()));
            List<String> distributeIdList = itemList.stream().map(LostApplyItem::getDistributeId).collect(Collectors.toList());
            iDocDistributeService.lostByIds(distributeIdList);

            try {
                //保存站内消息
                docMessageService.saveMsg(lostApply.getVersionId(),
                        lostApply.getId(),
                        DocMessageEnum.FLOW_LOST.getMsg(),
                        lostApply.getDocName(),lostApply.getDocId(),DocMessageEnum.FLOW_LOST.getCode(), null, null, null);
            }catch (Exception e){
                log.error("LostApplyServiceImpl-->onProcessEvent----e###", e);
            }
        }
        saveWorkFlowLog(lostApply,status,event.getProcessInst().getProcInstTitle(),event);
    }

    /**
     * 保存申请记录
     */
    public void saveWorkFlowLog(LostApply apply, String status, String procTitle, ProcessResultEvent event) {
        WorkflowApplyLog log = new WorkflowApplyLog();
        log.setId(apply.getId());
        log.setDocId(apply.getDocId());
        log.setVersionValue(apply.getVersionValue());
        log.setDocName(procTitle);
        log.setProcStatus(status);
        log.setUserName(apply.getUserName());
        log.setDeptId(apply.getDeptId());
        iWorkflowApplyLogService.updateStatusByBusId(log,event);
    }
}

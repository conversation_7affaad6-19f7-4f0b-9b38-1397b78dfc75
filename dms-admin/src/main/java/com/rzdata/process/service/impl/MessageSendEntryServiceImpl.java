package com.rzdata.process.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.blueland.bpmclient.model.BpmClientInputModel;
import com.rzdata.config.CustomConfig;
import com.rzdata.framework.constant.Constants;
import com.rzdata.framework.constant.DocMsgConstants;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.utils.StringUtils;
import com.rzdata.plugins.msg.BizMsgDto;
import com.rzdata.plugins.msg.MsgService;
import com.rzdata.process.domain.DocMessage;
import com.rzdata.process.domain.ModifyApply;
import com.rzdata.process.domain.bo.BpmClientInputModelBo;
import com.rzdata.process.domain.vo.WorkflowLogVo;
import com.rzdata.process.enums.DocMessageEnum;
import com.rzdata.process.enums.MsgTypeEnum;
import com.rzdata.process.service.IDocMessageService;
import com.rzdata.process.service.IMessageSendEntryService;
import lombok.extern.slf4j.Slf4j;
import org.apache.pdfbox.pdmodel.interactive.digitalsignature.PDSeedValueMDP;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description:业务活动中如需发送消息，请使用该服务,并通过DocMessageService进行统一发送 通过该服务，将业务与消息发送做解耦
 * @author: gj
 * @date 2024/9/27
 */
@Service
@Slf4j
public class MessageSendEntryServiceImpl implements IMessageSendEntryService {

    @Autowired
    private IDocMessageService docMessageService;

    @Resource
    CustomConfig customConfig;

    @Resource
    MsgService msgService;

    @Autowired
    private ThreadPoolTaskExecutor executor;

    /**
     * 通过企业微信，发送工作流消息
     *
     * @param bo      携带的工作流客户端输入模型
     * @param msgType 消息类型，用于区分不同类型的处理逻辑
     */
    public void sendWorkFlowMessagQywx(BpmClientInputModelBo bo, String msgType) {
        if (ObjectUtil.isNull(bo) || StringUtils.isEmpty(msgType)) {
            return;
        }
        BpmClientInputModel model = bo.getModel();
        try {
            DocMessage docMessage = new DocMessage();
//        docMessage.setDocId();
            docMessage.setMsgType(DocMsgConstants.MSG_TYPE_QYWX);
            docMessage.setDeptId("flow");
            //docMessage.setMsgClass(106);
            docMessage.setVersionValue("default");
            docMessage.setApplyId(model.getWf_procInstId());//流程示例ID
            String msgContent = "<a href='" + customConfig.getTokenRedirectUri() + "'>您有新的流程待办:" + model.getWf_procTitle() + "</a>";
            String msgInfo = "您有新的流程待办" + model.getWf_procTitle();
            docMessage.setMsgClass(MsgTypeEnum.FLOW_TODO_PROMPT.getType());
            docMessage.setMsgInfo(msgInfo);
            docMessage.setHtmlContent(msgContent);
            docMessage.setPcUrl(customConfig.getTokenRedirectUri());
            List<Map<String, Object>> receivers = model.getWf_receivers();
            if (CollectionUtil.isEmpty(receivers)) return;

            StringBuilder builder = new StringBuilder();
            for (int i = 0; i < receivers.size(); i++) {
                String receiverUser = (String) receivers.get(i).get("receiveUserId");
                if (StringUtils.isNotEmpty(receiverUser)) {
                    builder.append(receiverUser);
                    if (i < receivers.size() - 1) {
                        builder.append(",");
                    }
                }
            }
            docMessage.setRecoveryUser(builder.toString());
            String msgJson = JSONUtil.toJsonStr(docMessage);
            log.error("msgJson=====>" + msgJson);
            docMessageService.pushSimpleMsg(msgJson);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 通过企业微信，发送工作流消息
     * @param changeType 流程类型
     * @param docId      文件编号
     * @param applyId    流程ID
     * @param docName    文件名称
     * @param userId     用户ID
     */
    public void sendWorkFlowMessagQywx(String changeType,String docId,String applyId,String docName,String userId) {
        if (ObjectUtil.isNull(changeType) || StringUtils.isEmpty(userId)) {
            return;
        }
        try {
            String msg = "";
            switch (changeType){
                case "ADD":
                    msg = DocMessageEnum.FLOW_ADD.getMsg();
                    break;
                case "UPDATE":
                    msg = DocMessageEnum.FLOW_UPDATE.getMsg();
                    break;
                case "DISUSE":
                    msg = DocMessageEnum.FLOW_DISUSE.getMsg();
                    break;
                case Constants.FLOW_BORROW:
                    msg = DocMessageEnum.FLOW_BORROW.getMsg();
                    break;
                case Constants.FLOW_EXTRA:
                    msg = DocMessageEnum.FLOW_EXTRA.getMsg();
                    break;
                case Constants.FLOW_REISSUE:
                    msg = DocMessageEnum.FLOW_REISSUE.getMsg();
                    break;
                case Constants.FLOW_LOST:
                    msg = DocMessageEnum.FLOW_LOST.getMsg();
                    break;
                case Constants.FLOW_REVIEW:
                    msg = DocMessageEnum.FLOW_REVIEW.getMsg();
                    break;
                case Constants.FLOW_PRINT:
                    msg = DocMessageEnum.FLOW_PRINT.getMsg();
                    break;
            }

            DocMessage docMessage = new DocMessage();
            docMessage.setDocId(docId);
            docMessage.setMsgType(DocMsgConstants.MSG_TYPE_QYWX);
            docMessage.setDeptId("flow");
            //docMessage.setMsgClass(106);
            docMessage.setVersionValue("default");
            docMessage.setApplyId(applyId);//流程示例ID
            String msgContent = String.format(msg, StrUtil.isNotBlank(docName) ? docName : "", StrUtil.isNotBlank(docId) ? docId : "");
            docMessage.setMsgInfo(msgContent);

            docMessage.setRecoveryUser(userId);
            log.error("msgJson=====>" + changeType);
            log.error("msgJson=====>" + JSONUtil.toJsonStr(docMessage));

            BizMsgDto msgDto = this.msgService.convert(docMessage);
            AjaxResult qywxRes = this.msgService.send(msgDto);
            String qywxResJson = JSONObject.toJSONString(qywxRes);
            log.info("send qywx res = "+ qywxResJson);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }



    /**
     * 通过企业微信，发送工作流消息
     * @param docId      文件编号
     * @param bizId    业务id
     * @param userId     用户ID
     * @param msgContent  消息内容
     */
    public void sendMsgQywx(String docId,String bizId, String userId,String msgContent) {
        if (StringUtils.isEmpty(userId)) {
            return;
        }
        executor.execute(() -> {
            try {
                DocMessage docMessage = new DocMessage();
                docMessage.setDocId(docId);
                docMessage.setMsgType(DocMsgConstants.MSG_TYPE_QYWX);
                docMessage.setDeptId("flow");
                //docMessage.setMsgClass(106);
                docMessage.setVersionValue("default");
                docMessage.setApplyId(bizId);//流程示例ID
                docMessage.setMsgInfo(msgContent);
                docMessage.setRecoveryUser(userId);

                BizMsgDto msgDto = this.msgService.convert(docMessage);
                AjaxResult qywxRes = this.msgService.send(msgDto);
                /*String qywxResJson = JSONObject.toJSONString(qywxRes);
                log.info("send qywx res = "+ qywxResJson);*/
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        });
    }

}

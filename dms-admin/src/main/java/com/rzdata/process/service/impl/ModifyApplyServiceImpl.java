package com.rzdata.process.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.blueland.bpmclient.model.ProcessInstanceModel;
import com.google.common.collect.Lists;
import com.google.common.eventbus.AllowConcurrentEvents;
import com.google.common.eventbus.Subscribe;
import com.rzdata.config.CustomConfig;
import com.rzdata.config.ProcessConfig;
import com.rzdata.es.service.ElasticsearchService;
import com.rzdata.eventbus.ProcessEventBus;
import com.rzdata.eventbus.ProcessResultEvent;
import com.rzdata.framework.constant.*;
import com.rzdata.framework.core.domain.entity.SysUser;
import com.rzdata.framework.core.domain.model.LoginUser;
import com.rzdata.framework.core.mybatisplus.core.ServicePlusImpl;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.core.redis.RedisCache;
import com.rzdata.framework.exception.ServiceException;
import com.rzdata.framework.utils.I18nUtils;
import com.rzdata.framework.utils.PageUtils;
import com.rzdata.framework.utils.SecurityUtils;
import com.rzdata.framework.utils.StringUtils;
import com.rzdata.framework.utils.spring.SpringUtils;
import com.rzdata.plugins.filesync.IFileSyncService;
import com.rzdata.process.domain.*;
import com.rzdata.process.domain.bo.*;
import com.rzdata.process.domain.dto.ConfigureDTO;
import com.rzdata.process.domain.dto.CreateNewNoDTO;
import com.rzdata.process.domain.dto.DocIdNumDTO;
import com.rzdata.process.domain.vo.*;
import com.rzdata.process.enums.*;
import com.rzdata.process.listener.ibo.ModifyApplyIBo;
import com.rzdata.process.listener.ivo.ImportIVo;
import com.rzdata.process.mapper.ModifyApplyMapper;
import com.rzdata.process.service.*;
import com.rzdata.process.utils.MsgTemplateUtils;
import com.rzdata.sap.service.SapHelper;
import com.rzdata.setting.domain.CodeRuleLog;
import com.rzdata.setting.domain.DocClass;
import com.rzdata.setting.domain.vo.DocClassVo;
import com.rzdata.setting.service.*;
import com.rzdata.system.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import javax.annotation.Resource;
import java.rmi.ServerException;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.rzdata.framework.constant.Constants.PROCESS_KEY;

/**
 * 文件变更操作申请Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-12-31
 */
@Slf4j
@Service
public class ModifyApplyServiceImpl extends ServicePlusImpl<ModifyApplyMapper, ModifyApply, ModifyApplyVo> implements IModifyApplyService {

    private static final String ID_SPLIT_KEY = ",";
    private Logger logger = LoggerFactory.getLogger(ModifyApplyServiceImpl.class);

    @Autowired
    private WorkflowService workflowService;
    @Autowired
    private IWorkflowApplyLogService workflowApplyLogService;
    @Autowired
    private ISysConfigService configService;
    @Autowired
    private IModifyApplyDistributeService modifyApplyDistributeService;
    @Autowired
    private IModifyApplyLinkService modifyApplyLinkService;

    @Autowired
    private IStandardService standardService;
    @Autowired
    IVersionService versionService;
    @Autowired
    private IDocLinkLogService docLinkLogService;

    @Autowired
    IBasicFileService basicFileService;
    @Autowired
    private IModifyApplyTrainService modifyApplyTrainService;

    @Autowired
    private ISysDeptService sysDeptService;
    @Resource
    private ISysUserService sysUserService;
    @Autowired
    private IGenerateIdService iGenerateIdService;

    @Autowired
    private ICodeRuleService iCodeRuleService;

    @Autowired
    IDocClassFlowService iDocClassFlowService;

    @Autowired
    IDocClassService iDocClassService;

    @Autowired
    IDocMessageService docMessageService;

    @Autowired
    IDocVersionLinkService iDocVersionLinkService;

    @Autowired
    FileSignatureService fileSignatureService;

    @Autowired
    IDocDistributeService iDocDistributeService;

    @Autowired
    ICodeRuleLogService iCodeRuleLogService;

    @Autowired
    IFileSyncService asFileSyncService;

    @Autowired
    IDocPresetUserService iDocPresetUserService;

    @Autowired
    CustomConfig customConfig;

    @Autowired
    private ISysNotifyLogService sysNotifyLogService;

    @Autowired
    private SapHelper sapHelper;

    @Autowired
    private IWorkflowLogService workflowLogService;

    @Autowired
    private IDocDistributeService docDistributeService;

    @Autowired
    private IModifyApplyDistributeService docModifyApplyDistributeService;

    @Autowired
    private IMessageSendEntryService messageSendEntryService;

    @Autowired
    IBasicFilePdfService basicFilePdfService;

    @Autowired
    private ElasticsearchService elasticsearchService;

    @Autowired
    private IDocEditLogService iDocEditLogService;

    @Override
    // @Transactional(rollbackFor = Exception.class)
    public boolean distribute(String id) {
        if (ObjectUtil.isEmpty(id)) {
            return false;
        }
        ModifyApply applyPo = getById(id);
        if (ObjectUtil.isEmpty(applyPo)) {
            return false;
        }
        /**
         * 已经分发
         */
        if (YNEnum.toType(applyPo.getYNDistribute()) == YNEnum.YES) {
            return true;
        }
        switch (ApplyTypeEnum.toType(applyPo.getChangeType())) {
            case ADD:
                handleAddApplySuccess(applyPo, null, null);
                break;
            case UPDATE:
                handleUpdateApplySuccess(applyPo, null, null);
                break;
            case DISUSE:
                handleDisuseApplySuccess(applyPo, null, null);
                break;
            default:
                break;
        }
        return true;
    }

    @Override
    public ModifyApplyVo queryById(String id) {
        ModifyApplyVo applyVo = getVoById(id);
        if (ObjectUtil.isEmpty(applyVo)) {
            return applyVo;
        }
        if (StringUtils.isNotEmpty(applyVo.getParentDocId())) {
            Version upVersion = versionService.getValidVersionByDocId(applyVo.getParentDocId());
            if (upVersion != null) {
                applyVo.setUpDocName(upVersion.getDocName());
                applyVo.setUpVersionId(upVersion.getId());
                applyVo.setParentDocId(upVersion.getDocId());
            } else {
                applyVo.setUpDocName(versionService.getDocNameByVersionId(applyVo.getUpVersionId()));
            }
        }

        // 查询文件历史
        if (StringUtils.isNotEmpty(applyVo.getDocId())) {
            List<VersionVo> versions = versionService.selectVersionListByDocId(applyVo.getDocId());
            if (ObjectUtil.isNotEmpty(versions)) {
                applyVo.setVersions(versions);
            }
        }

        /**
         * 关联
         */

        //不为ADD(新增)则一定是修订或者作废
        if (!ApplyTypeEnum.toType(applyVo.getChangeType()).equals(ApplyTypeEnum.ADD)){
            //把当前生效主文件查出来
            List<DocLinkLogVo> docLinkLogVos = docLinkLogService.queryDocLinkVo(applyVo.getVersionId(), LinkTypeEnum.DOC.name());
            //虽然没有传versionId可能会查出之前多个版本的主文件 但是SQL做了创建时间的倒序排序 所以如果有数据 第一条则是上一次的主文件(因为这个时候流程还没有结束
            // 当前流程的主文件还没有插入到doc_link_log表中 则查到的是一定上次的主文件(也就是当前生效的)
            if (ObjectUtil.isNotEmpty(docLinkLogVos)){
                applyVo.setPreStandardDoc(docLinkLogVos.stream().filter(docLinkLogVo -> Constants.ONE.equals(docLinkLogVo.getStatus())).findFirst().orElse(null));
            }
            //附件同理
            List<DocLinkLogVo> appendixLinkLogVos = docLinkLogService.queryDocLinkVo(applyVo.getVersionId(), LinkTypeEnum.APPENDIX.name());
            applyVo.setPreAppendixes(appendixLinkLogVos);

        }

        //当前变更版本主文件
        List<ModifyApplyLinkVo> modifyApplyLinkVoList = modifyApplyLinkService.queryDocByApplyIdAndType(id,LinkTypeEnum.DOC);
        if (ObjectUtil.isNotEmpty(modifyApplyLinkVoList)) {
            applyVo.setStandardDoc(modifyApplyLinkVoList.get(0));
        }
        //当前变更版本附件
        List<ModifyApplyLinkVo> appendixModifyApplyLinkVoList = modifyApplyLinkService.queryDocByApplyIdAndType(id,LinkTypeEnum.APPENDIX);
        applyVo.setAppendixes(appendixModifyApplyLinkVoList);

        //备注附件
        List<ModifyApplyLinkVo> appendixRemarkModifyApplyLinkVoList = modifyApplyLinkService.queryDocByApplyIdAndType(id,LinkTypeEnum.APPENDIX_REMARK);
        applyVo.setRemarkDoc(appendixRemarkModifyApplyLinkVoList);

        applyVo.setDeptName(sysDeptService.getDeptName(applyVo.getDeptId()));
        applyVo.setNickName(sysUserService.getNickName(applyVo.getUserName()));

        return applyVo;
    }

    @Override
    public ModifyApplyVo queryByBpmnId(String bpmnId) {
        WorkflowApplyLogVo workflowApplyLog = workflowApplyLogService.getVoOne(new LambdaQueryWrapper<WorkflowApplyLog>()
                .eq(WorkflowApplyLog::getProcInstId,bpmnId)
                .select(WorkflowApplyLog::getProcInstId,WorkflowApplyLog::getId,WorkflowApplyLog::getProcStatus)
                .last("limit 1"));
        ModifyApplyVo vo = queryById(workflowApplyLog.getId());
        vo.setProcessStatus(workflowApplyLog.getProcStatus());
        return vo;
    }

    @Override
    public List<ModifyApplyVo> getBatchInfoByBpmnId(String bpmnId) {
        WorkflowApplyLogVo workflowApplyLog = workflowApplyLogService.getVoOne(
                new LambdaQueryWrapper<WorkflowApplyLog>().eq(WorkflowApplyLog::getProcInstId, bpmnId).last("limit 1"));
        List<ModifyApplyVo> list = listVo(new LambdaQueryWrapper<ModifyApply>()
                .eq(ModifyApply::getBatchId, workflowApplyLog.getId()).select(ModifyApply::getId));
        List<ModifyApplyVo> listVo = new ArrayList<>();
        for (ModifyApplyVo item : list) {
            ModifyApplyVo vo = queryById(item.getId());
            vo.setProcessStatus(workflowApplyLog.getProcStatus());
            listVo.add(vo);
        }
        return listVo;
    }

    @Override
    public TableDataInfo<ModifyApplyVo> queryPageList(ModifyApplyBo bo) {
        // 原来的程序写法
        // PagePlus<ModifyApply, ModifyApplyVo> result =
        // pageVo(PageUtils.buildPagePlus(), buildQueryWrapper(bo));
        Page<ModifyApply> page = PageUtils.buildPage();
        Page<ModifyApplyVo> iPage = baseMapper.getApplyModifyList(page, bo, customConfig.getBpmDataSource());
        return PageUtils.buildDataInfo(iPage);
    }

    @Override
    public List<ModifyApplyVo> queryList(ModifyApplyBo bo) {
        return listVo(buildQueryWrapper(bo));
    }

    public static void main(String[] args) {

        String formatValue = String.format("%02d", 9);
        System.out.println("formatValue = " + formatValue);

        System.out.println(Integer.parseInt("02") + "------------------");
    }

    @Override
    public ModifyApplyResultVo insertBoNoBpm(ModifyApplyBo bo) {
        ModifyApplyResultVo resultVo = new ModifyApplyResultVo();
        if (ObjectUtil.isNotEmpty(bo.getId())) {
            if (BooleanUtil.isTrue(bo.getEditStatus())) {
                bo.setCreateBy(SecurityUtils.getUsername());
                updateByBo(bo);
            } else {
                if (BooleanUtil.isTrue(bo.getOnlyEdit())) {
                    updateByBo(bo);
                }
                if (BooleanUtil.isTrue(bo.getPresetUserEdit())) {
                    updatePresetUser(bo);
                }
                if (BooleanUtil.isTrue(bo.getCustomerEdit())) {
                    update(new LambdaUpdateWrapper<ModifyApply>()
                            .set(ModifyApply::getWhetherCustomer, bo.getWhetherCustomer())
                            .eq(ModifyApply::getId, bo.getId()));
                }
            }
            resultVo.setId(bo.getId());
            return resultVo;
        }
        if (StrUtil.isBlank(bo.getUserName())) {
            bo.setUserName(SecurityUtils.getUsername());
        }
        if (ObjectUtil.isNull(bo.getDeptId()) || bo.getDeptId() == "0") {
            bo.setDeptId(SecurityUtils.getDeptId());
        }
        Optional<String> applyIdOpt = insertAllByBo(bo);
        if (applyIdOpt.isPresent()) {
            bo.setId(applyIdOpt.get());
            resultVo.setId(applyIdOpt.get());
        }
        return resultVo;
    }

    @Override
    public ProcessInstanceModel insertByBo(ModifyApplyBo bo) {
        ModifyApplyResultVo resultVo = this.insertBoNoBpm(bo);
        bo.getBpmClientInputModel().setStatus(bo.getRecordStatus());
        bo.getBpmClientInputModel().setBizType(bo.getChangeType());
        if (BooleanUtil.isTrue(bo.getEditStatus())) {
            bo.getBpmClientInputModel().setMark(String.valueOf(DateUtil.current()));
        }
        return workflowExecute(bo.getBpmClientInputModel(), resultVo.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ModifyApplyResultVo insertBatchByBo(ModifyApplyBatchBo bo) {
        ModifyApplyResultVo resultVo = new ModifyApplyResultVo();
        resultVo.setId(bo.getBatchId());
        resultVo.setIdList(updateBatchByBo(bo));
        bo.getBpmClientInputModel().setStatus(bo.getRecordStatus());
        bo.getBpmClientInputModel().setBizType(bo.getChangeType());
        if (BooleanUtil.isTrue(bo.getEditStatus())) {
            bo.getBpmClientInputModel().setMark(String.valueOf(DateUtil.current()));
        }
        resultVo.setProcessInstanceModel(workflowExecute(bo.getBpmClientInputModel(), resultVo.getId()));
        return resultVo;
    }

    public ProcessInstanceModel workflowExecute(BpmClientInputModelBo bpmClientInputModel, String id) {
        try {
            ProcessInstanceModel processInstanceModel = null;
            if (StringUtils.equals(bpmClientInputModel.getStatus(), RecordStatusEnum.DRAFT.getCode())) {
                processInstanceModel = workflowService.saveExecute(bpmClientInputModel, id);
            } else if (StringUtils.equals(bpmClientInputModel.getStatus(), RecordStatusEnum.CANCEL.getCode())) {
                processInstanceModel = workflowService.cancelExecute(bpmClientInputModel, id);
            } else {
                if (BooleanUtil.isTrue(bpmClientInputModel.getJointReview())&&StringUtils.isNotEmpty(bpmClientInputModel.getModel().getWf_curActInstId())) {
                    processInstanceModel = new ProcessInstanceModel();
                    processInstanceModel.setBusinessKey(id);
                    CompletableFuture.supplyAsync(() -> {
                        try {
                            SpringUtils.getBean(RedisCache.class).setCacheObject(bpmClientInputModel.getModel().getWf_curActInstId(), true, 1, TimeUnit.MINUTES);
                            ProcessInstanceModel processInstanceModel1 = workflowService.nextExecute(bpmClientInputModel, id);
                            SpringUtils.getBean(RedisCache.class).deleteObject(bpmClientInputModel.getModel().getWf_curActInstId());
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                        return null;
                    });
                }else {
                    processInstanceModel = workflowService.nextExecute(bpmClientInputModel, id);
                }
            }
            return processInstanceModel;
        } catch (Exception e) {
            log.error("start modify apply fail:", e);
        }
        return null;
    }

    /**
     * 获取工作流定义名称
     */
    private String getProDefKey(String changeType) {
        ProcessConfig processConfig = JSONUtil.toBean(configService.selectConfigByKey(PROCESS_KEY),
                ProcessConfig.class);
        switch (ApplyTypeEnum.toType(changeType)) {
            case ADD:
                return processConfig.getProcDefKeyADD();
            case UPDATE:
                return processConfig.getProcDefKeyUPDATE();
            case DISUSE:
                return processConfig.getProcDefKeyDISUSE();
            default:
                return null;
        }
    }

    /**
     * 保存变更操作数据，包括附件，关联文件，新增或关联记录文件
     *
     * @param bo
     * @return
     */
    // @Transactional(rollbackFor = Exception.class)
    @Override
    public Optional<String> insertAllByBo(ModifyApplyBo bo) {
        // 生成编制正文对应的PDF文件
        if (ObjectUtil.isNotEmpty(bo.getStandardDoc())) {
            bo.setFileId(bo.getStandardDoc().getFileId());
        }
        ModifyApply applyPo = saveModifyApplyData(bo);
        bo.setId(applyPo.getId()).setVersionId(applyPo.getVersionId()).setVersionValue(applyPo.getVersionValue())
                .setCreateTime(new Date());
        /**
         * 更新分发列表
         */
        modifyApplyDistributeService.updateModifyApplyDistribute(bo.getDistributeList(), bo.getId());

        /**
         * 构造关联数据：将编制正文，附件文件，关联文件，新增或关联记录文件统一保存
         * 关联文件或记录时间linkId为versionId,其它为basic_file的id
         */
        updateModifyApplyLink(bo);

        /**
         * 环节预选人员
         */
        updatePresetUser(bo);

        if (StringUtils.isNotEmpty(applyPo.getDocId())) {
            iCodeRuleLogService.updateUseStatus(applyPo.getDocId(), applyPo.getId(), null);
        }
        return Optional.ofNullable(applyPo.getId());
    }

    private void updatePresetUser(ModifyApplyBo bo) {
        iDocPresetUserService.updatePresetUser(bo.getPresetUserList(), bo.getId());
    }

    /**
     * 组装关联记录
     */
    private List<ModifyApplyLink> buildModifyApplyLink(ModifyApplyBo bo) {
        List<ModifyApplyLink> linkList = Lists.newArrayList();
        // 编制正文
        if (ObjectUtil.isNotEmpty(bo.getStandardDoc())) {
            bo.setFileId(bo.getStandardDoc().getFileId());
            ModifyApplyLink linkPo = createModifyApplyLink(bo.getStandardDoc().getId(), bo.getId(), bo.getFileId(),
                    bo.getStandardDoc().getLinkId(), LinkClassEnum.HOST.name(), LinkTypeEnum.DOC.name(),
                    bo.getStandardDoc().getDocName(), bo.getVersionId(), bo.getVersionValue(), null, bo.getDocClass(),
                    NumberConstants.ONE, bo.getDocId(), null, null,bo.getStandardDoc().getProtoFileId());
            linkList.add(linkPo);
        }

        // 备注的附件
        // buildModifyApplyLink(linkList, bo.getRemarkDoc(), bo.getId(),
        // LinkTypeEnum.APPENDIX_REMARK, LinkClassEnum.FILE);
        // 附件文件
        buildModifyApplyLink(linkList, bo.getAppendixes(), bo.getId(), LinkTypeEnum.APPENDIX, LinkClassEnum.FILE);
        // 关联文件
        buildModifyApplyLink(linkList, bo.getDocLinks(), bo.getId(), LinkTypeEnum.REF_DOC, LinkClassEnum.DOC);
        // 新增或关联记录文件
        buildModifyApplyLink(linkList, bo.getRecordLinks(), bo.getId(), LinkTypeEnum.RECORD, LinkClassEnum.LINK);
        // 记录文件 多对多
        buildModifyApplyLink(linkList, bo.getNoteLinks(), bo.getId(), LinkTypeEnum.NOTE, LinkClassEnum.LINK);
        // 主文件
        buildModifyApplyLink(linkList, bo.getNoteDocLinks(), bo.getId(), LinkTypeEnum.NOTE_DOC, LinkClassEnum.LINK);
        return linkList;
    }

    /**
     * 生成多条记录
     */
    private void buildModifyApplyLink(List<ModifyApplyLink> linkList, List<ModifyApplyLinkBo> linkBoList,
            String applyId, LinkTypeEnum linkTypeEnum, LinkClassEnum linkClassEnum) {
        if (ObjectUtil.isNotEmpty(linkBoList)) {
            List<ModifyApplyLink> appendixList = linkBoList.stream()
                    .map(appendixBo -> createModifyApplyLink(appendixBo.getId(), applyId, appendixBo.getFileId(),
                            appendixBo.getLinkId(), linkClassEnum.name(),
                            linkTypeEnum.name(), appendixBo.getDocName(), appendixBo.getVersionId(),
                            appendixBo.getVersionValue(), appendixBo.getIsDeleted(),
                            appendixBo.getDocClass(), appendixBo.getStatus(), appendixBo.getDocId(),
                            appendixBo.getStartDate(), appendixBo.getReleaseTime(), appendixBo.getProtoFileId()))
                    .collect(Collectors.toList());
            linkList.addAll(appendixList);
        }
    }

    /**
     * 创建一条关联记录
     */
    private ModifyApplyLink createModifyApplyLink(String id, String applyId, String fileId, String linkId,
            String linkClass, String linkType, String docName, String versionId, String versionValue, Integer isDeleted,
            String docClass, Integer status, String docId, Date startDate, Date releaseTime, String protoFileId) {
        ModifyApplyLink linkPo = new ModifyApplyLink();
        linkPo.setId(id);
        linkPo.setApplyId(applyId);
        linkPo.setFileId(fileId);
        linkPo.setLinkId(linkId);
        linkPo.setLinkClass(linkClass);
        linkPo.setLinkType(linkType);
        linkPo.setDocName(docName);
        linkPo.setVersionId(versionId);
        linkPo.setVersionValue(versionValue);
        linkPo.setIsDeleted(isDeleted);
        linkPo.setDocClass(docClass);
        linkPo.setStatus(status);
        linkPo.setDocId(docId);
        linkPo.setStartDate(startDate);
        linkPo.setReleaseTime(releaseTime);
        linkPo.setProtoFileId(protoFileId);
        return linkPo;
    }

    @Override
    // @Transactional(rollbackFor = Exception.class)
    public Boolean updateByBo(ModifyApplyBo bo) {
        if (ObjectUtil.isEmpty(bo) || ObjectUtil.isEmpty(bo.getId())) {
            log.error("update with empty id");
            return false;
        }
        // 生成编制正文对应的PDF文件
        if (ObjectUtil.isNotEmpty(bo.getStandardDoc())) {
            bo.setFileId(bo.getStandardDoc().getFileId());
        }
        ModifyApply modifyApply = getOne(
                new LambdaQueryWrapper<ModifyApply>().eq(ModifyApply::getId, bo.getId()).select(ModifyApply::getDocId));
        String docId = modifyApply != null ? modifyApply.getDocId() : null;
        ModifyApply update = BeanUtil.toBean(bo, ModifyApply.class);
        Boolean result = updateById(update);
        if (ObjectUtil.isNotEmpty(result) && result && !BooleanUtil.isTrue(bo.getOnlyEdit())) {
            /**
             * 更新分发列表
             */
            modifyApplyDistributeService.updateModifyApplyDistribute(bo.getDistributeList(), bo.getId());

            /**
             * 更新关联
             */
            updateModifyApplyLink(bo);

            /**
             * 环节预选人员
             */
            updatePresetUser(bo);

            // 编制环节选了编号的话 更改预制表编号的状态
            iCodeRuleLogService.updateUseStatus(bo.getDocId(), bo.getId(), docId);
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<String> updateBatchByBo(ModifyApplyBatchBo bo) {
        List<String> idList = new ArrayList<>();
        for (ModifyApplyBo maBo : bo.getDataList()) {
            maBo.setRecordStatus(bo.getRecordStatus());
            maBo.setEditStatus(bo.getEditStatus());
            maBo.setCustomerEdit(bo.getCustomerEdit());
            maBo.setPresetUserEdit(bo.getPresetUserEdit());
            maBo.setOnlyEdit(bo.getOnlyEdit());
            insertBoNoBpm(maBo);
            if (StringUtils.isNotEmpty(maBo.getId())) {
                idList.add(maBo.getId());
            }
        }
        if (BooleanUtil.isTrue(bo.getEditStatus()) || BooleanUtil.isTrue(bo.getPresetUserEdit())) {
            iDocPresetUserService.updatePresetUser(bo.getPresetUserList(), bo.getBatchId());
        }
        List<ModifyApply> deleteList = baseMapper.selectList(new LambdaQueryWrapper<ModifyApply>()
                .eq(ModifyApply::getBatchId, bo.getBatchId()).notIn(idList.size() > 0, ModifyApply::getId, idList)
                .select(ModifyApply::getId, ModifyApply::getDocId));
        // 需要删除的 如果有编号需要执行编号回收逻辑
        for (ModifyApply ma : deleteList) {
            if (StringUtils.isNotEmpty(ma.getDocId())) {
                iCodeRuleLogService.cancelDocId(ma.getId(), ma.getDocId());
            }
            baseMapper.deleteById(ma.getId());
        }
        return idList;
    }

    /**
     * 更新正文，附件，关联文档，关联或新增附件
     */
    private void updateModifyApplyLink(ModifyApplyBo bo) {
        List<ModifyApplyLink> newLinkList = buildModifyApplyLink(bo);
        List<String> ids = newLinkList.stream().filter(item -> StringUtils.isNotBlank(item.getId()))
                .map(ModifyApplyLink::getId).collect(Collectors.toList());
        // 删除传参没有 而数据库有的数据
        LambdaQueryWrapper<ModifyApplyLink> query = Wrappers.lambdaQuery();
        query.eq(ModifyApplyLink::getApplyId, bo.getId());
        query.notIn(ObjectUtil.isNotEmpty(ids), ModifyApplyLink::getId, ids);

        modifyApplyLinkService.remove(query);
        modifyApplyLinkService.saveOrUpdateAll(newLinkList);
    }

    /**
     * 保存新修废申请记录
     *
     * @param bo
     * @return
     */
    private ModifyApply saveModifyApplyData(ModifyApplyBo bo) {
        ModifyApply add = BeanUtil.toBean(bo, ModifyApply.class);
        validEntityBeforeSave(add);
        LoginUser loginUser = SecurityUtils.getLoginUser();
//        String applyId = iGenerateIdService.generateApplyId(ApplyTypeEnum.toType(bo.getChangeType()), ObjectUtil.isNotEmpty(bo.getUserName()) ? bo.getUserName() : loginUser.getUsername());
        if (ObjectUtil.isEmpty(add.getUserName())) {
            add.setUserName(loginUser.getUsername());
        }
        if (ObjectUtil.isEmpty(add.getDeptId())) {
            add.setDeptId(loginUser.getDeptId());
        }
//        add.setRecordStatus(ApplyStatusEnum.APPLIED.getStatus());
//        add.setId(applyId);
        save(add);
        return add;
    }

    @Override
    public void saveWorkFlowLog(ModifyApply modifyApply, String procTitle, ProcessResultEvent event) {
        WorkflowApplyLog log = workflowApplyLogService.getById(modifyApply.getId());
        if (ObjectUtil.isEmpty(log)) {
            log = new WorkflowApplyLog();
            log.setId(modifyApply.getId());
            log.setProcDefKey(event.getProcessInst().getProcDefId());
            log.setProcInstId(event.getProcessInst().getProcInstId());
            log.setApplyTime(modifyApply.getApplyTime());
            log.setApplyClass(modifyApply.getChangeType().toLowerCase(Locale.ROOT));
            log.setUserName(modifyApply.getUserName());
            log.setDeptId(modifyApply.getDeptId());
            log.setChangeType(event.getApplyType());
        }
        log.setDocName(procTitle);
        log.setDocId(modifyApply.getDocId());
        log.setVersionValue(modifyApply.getVersionValue());
        log.setProcStatus(modifyApply.getRecordStatus());
        log.setUpdateTime(new Date());
        workflowApplyLogService.saveOrUpdate(log);
    }

    /**
     * 保存申请记录
     */
    @Override
    public void saveWorkFlowLog(List<ModifyApply> list, String procTitle, ProcessResultEvent event) {
        ModifyApply modifyApply = list.get(0);
        WorkflowApplyLog log = workflowApplyLogService.getById(modifyApply.getBatchId());
        if (ObjectUtil.isEmpty(log)) {
            log = new WorkflowApplyLog();
            log.setId(modifyApply.getBatchId());
            log.setProcDefKey(event.getProcessInst().getProcDefId());
            log.setProcInstId(event.getProcessInst().getProcInstId());
            log.setApplyTime(modifyApply.getApplyTime());
            log.setApplyClass(modifyApply.getChangeType().toLowerCase(Locale.ROOT));
            log.setUserName(modifyApply.getUserName());
            log.setDeptId(modifyApply.getDeptId());
            log.setChangeType(event.getApplyType());
        }
        log.setDocName(procTitle);
        log.setDocId(list.stream().map(ModifyApply::getDocId).filter(StringUtils::isNotEmpty)
                .collect(Collectors.joining(Constants.ID_SPLIT_KEY)));
        log.setVersionValue(
                list.stream().map(ModifyApply::getVersionValue).collect(Collectors.joining(Constants.ID_SPLIT_KEY)));
        log.setProcStatus(modifyApply.getRecordStatus());
        log.setUpdateTime(new Date());
        workflowApplyLogService.saveOrUpdate(log);
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        if (isValid) {
            // TODO 做一些业务上的校验,判断是否需要校验
        }
        return removeByIds(ids);
    }

    /**
     * 同步事件监听处理
     */
    @Subscribe
    @AllowConcurrentEvents
    // @Transactional(rollbackFor = Exception.class)
    protected void onProcessEvent(ProcessResultEvent event) {
        ProcessConfig processConfig = JSONUtil.toBean(configService.selectConfigByKey(PROCESS_KEY),
                ProcessConfig.class);
        // 如果是文件新增、文件修订、文件作废流程就继续执行后面的代码
        if (!(event.getApplyType().contains(processConfig.getProcDefKeyADD())
                || event.getApplyType().contains(processConfig.getProcDefKeyUPDATE())
                || event.getApplyType().contains(processConfig.getProcDefKeyDISUSE()))) {
            return;
        }
        ModifyApply applyPo = getById(event.getApplyId());
        if (ObjectUtil.isEmpty(applyPo)) {
            List<ModifyApply> list = list(
                    new LambdaQueryWrapper<ModifyApply>().eq(ModifyApply::getBatchId, event.getApplyId()));
            for (ModifyApply ma : list) {
                DataSettlement(ma, event);
            }
            saveWorkFlowLog(list, event.getProcessInst().getProcInstTitle(), event);
        } else {
            DataSettlement(applyPo, event);
            // 保存流程日志
            saveWorkFlowLog(applyPo, event.getProcessInst().getProcInstTitle(), event);
        }
    }

    private void DataSettlement(ModifyApply applyPo, ProcessResultEvent event) {
        applyPo.setStep(event.getStep());

        // 设置
        if (ObjectUtil.isNotEmpty(event.getSetupTime())) {
            applyPo.setSetupTime(event.getSetupTime());
        }

        logger.info("流程状态onProcessEvent返回值:{}", event.toString());
        String status = event.getStatus();
        try {
            if (!ProcessStatusConstants.TO_DRAFT.equals(event.getStatus())&&StringUtils.isNotEmpty(event.getOnlineType())) {
                iDocEditLogService.saveLog(event.getApplyId(), event.getOnlineType(),event.getBpmClientInputModel().getWf_curActDefName());
            }
            // 流程结束
            if (ProcessStatusConstants.TO_DONE.equals(event.getStatus())) {

                switch (ApplyTypeEnum.toType(applyPo.getChangeType())) {
                    case ADD:
                        handleAddApplySuccess(applyPo, status, event.getModel().getWf_nextActDefName());
                        break;
                    case UPDATE:
                        handleUpdateApplySuccess(applyPo, status, event.getModel().getWf_nextActDefName());
                        break;
                    case DISUSE:
                        handleDisuseApplySuccess(applyPo, status, event.getModel().getWf_nextActDefName());
                        break;
                    default:
                        break;
                }
            } else {
                // 流程作废 文件编号在生效后的数据中没有的需要回收掉
                if (RecordStatusEnum.CANCEL.getCode().equals(status)) {
                    iCodeRuleLogService.cancelDocId(applyPo.getId(), applyPo.getDocId());
                }
                applyPo.setRecordStatus(status);
                updateById(applyPo);

                // 待办的时候发送邮件提醒
                if (ProcessStatusConstants.TO_DO.equals(event.getStatus())) {
                    // 流程审批邮件提醒
                    sendFlowEmail(event, applyPo, SendType.TO_DO_REMINDER.getCode());
                }
            }
        } catch (Exception e) {
            logger.error("工作流调用失败：", e);
        }
    }

    private void sendPrintMsg(ModifyApply applyPo, String type) {
        // 获取当前版本
        Version currentVersion = versionService.getById(applyPo.getVersionId());
        if (currentVersion == null) {
            return;
        }

        String msgTitle = "";
        if (type.equals(Constants.CHANGE_TYPE_ADD)) {
            msgTitle = "文件新增";
        } else if (type.equals(Constants.CHANGE_TYPE_UPDATE)) {
            msgTitle = "文件修订";
        }

        // 获取分发记录
        List<DocDistribute> distributeList = docDistributeService.list(new LambdaQueryWrapper<DocDistribute>()
                .eq(DocDistribute::getDocId, currentVersion.getDocId())
                .eq(DocDistribute::getVersionId, currentVersion.getId())
                .eq(DocDistribute::getType, Constants.DISTRIBUTE_TYPE_PRINT));

        if (CollUtil.isEmpty(distributeList)) {
            return;
        }

        String deptName = sysDeptService.getDeptName(currentVersion.getDeptId());
        String pcUrl = MsgTemplateUtils.buildPcFileDetailUrl(currentVersion.getDocId(), currentVersion.getId());
        String mobileUrl = MsgTemplateUtils.buildMobilFileDetailUrl(currentVersion.getDocId(), currentVersion.getId());
        String msgContent = "【" + msgTitle + "-打印提醒】" + String.format(DocMessageEnum.SEND_MSG_TYPE_PRINT.getMsg(),
                StrUtil.isNotBlank(currentVersion.getDocName()) ? currentVersion.getDocName() : "",
                StrUtil.isNotBlank(currentVersion.getVersionValue()) ? currentVersion.getVersionValue() : "");

        // 发送回收邮件
        for (DocDistribute distribute : distributeList) {
            Map<String, String> param = new HashMap<>();
            String content = MsgTemplateUtils.printHtmlContent(distribute.getDocName(),
                    currentVersion.getVersionValue(), pcUrl, mobileUrl);
            if (StrUtil.isBlank(content)) {
                continue;
            }
            param.put("title", msgTitle);
            param.put("content", content);

            SysUser user = sysUserService.selectUserByUserName(distribute.getReceiveUserName());
            if (ObjectUtil.isEmpty(user)) {
                continue;
            }

            try {
                // 发送邮件
                sysNotifyLogService.sendEmail(SendType.PRINT.getCode(), user.getNickName(), user.getEmail(),
                        user.getUserId(), param);
            } catch (Exception e) {
                log.error("sendPrintMsg--发送回收邮件异常: ", e);
            }

            try {
                // 发送站内信
                docMessageService.sendInstationMessage(applyPo.getId(), distribute.getDocId(), distribute.getDocClass(),
                        distribute.getDocName(), distribute.getVersionId(), currentVersion.getVersionValue(),
                        currentVersion.getDeptId(), deptName, user.getUserName(), user.getUserId(), msgContent,
                        DocMsgConstants.MSG_TYPE_MSG, MsgTypeEnum.PRINT.getType(), pcUrl, mobileUrl,
                        SecurityUtils.getLoginUser().getTenantId());
            } catch (Exception e) {
                log.error("sendPrintMsg--发送站内信异常: ", e);
            }

            try {
                // 发送企业微信消息
                messageSendEntryService.sendMsgQywx(currentVersion.getDocId(), currentVersion.getId(),
                        user.getUserName(), msgContent);
            } catch (Exception e) {
                log.error("sendPrintMsg--发送企业微信消息异常: ", e);
            }
        }
    }

    /**
     * 发送回收邮件提醒
     *
     * @param applyPo 流程实例
     */
    private void sendRecycleEmail(ModifyApply applyPo) {
        // 获取当前版本
        Version currentVersion = versionService.getById(applyPo.getVersionId());
        if (currentVersion == null) {
            return;
        }

        // 查询上一个版本
        Version recoverVersion = versionService.getOne(new LambdaQueryWrapper<Version>()
                .eq(Version::getDocId, currentVersion.getDocId())
                .lt(Version::getVersionValue, currentVersion.getVersionValue())
                .orderByDesc(Version::getVersionValue)
                .last("limit 1"));

        if (ObjectUtil.isEmpty(recoverVersion)) {
            return;
        }

        // 获取分发记录
        List<DocDistribute> distributeList = docDistributeService.list(new LambdaQueryWrapper<DocDistribute>()
                .eq(DocDistribute::getDocId, recoverVersion.getDocId())
                .eq(DocDistribute::getVersionId, recoverVersion.getId())
                .eq(DocDistribute::getType, Constants.DISTRIBUTE_TYPE_PRINT));

        if (CollUtil.isEmpty(distributeList)) {
            return;
        }

        String deptName = sysDeptService.getDeptName(recoverVersion.getDeptId());
        String linkUrl = MsgTemplateUtils.buildPcFileDetailUrl(recoverVersion.getDocId(), recoverVersion.getId());
        String mobileUrl = MsgTemplateUtils.buildMobilFileDetailUrl(recoverVersion.getDocId(), recoverVersion.getId());
        String msgContent = String.format(DocMessageEnum.FLOW_RECYCLE.getMsg(),
                StrUtil.isNotBlank(recoverVersion.getDocName()) ? recoverVersion.getDocName() : "",
                StrUtil.isNotBlank(recoverVersion.getVersionValue()) ? recoverVersion.getVersionValue() : "");

        // 发送回收邮件
        for (DocDistribute distribute : distributeList) {
            SysUser user = sysUserService.selectUserByUserName(distribute.getReceiveUserName());
            if (ObjectUtil.isEmpty(user)) {
                continue;
            }

            Map<String, String> param = buildEmailParams(distribute, recoverVersion, deptName, linkUrl, mobileUrl);

            try {
                // 发送邮件
                sysNotifyLogService.sendEmail(SendType.HS.getCode(), user.getNickName(), user.getEmail(),
                        user.getUserId(), param);
            } catch (Exception e) {
                log.error("sendRecycleEmail--发送回收邮件异常: ", e);
            }

            try {
                // 发送站内信
                docMessageService.sendInstationMessage(applyPo.getId(), distribute.getDocId(), distribute.getDocClass(),
                        distribute.getDocName(), distribute.getVersionId(), recoverVersion.getVersionValue(),
                        recoverVersion.getDeptId(), deptName, user.getUserName(), user.getUserId(), msgContent,
                        DocMsgConstants.MSG_TYPE_MSG, MsgTypeEnum.RECEIVE.getType(), linkUrl, mobileUrl,
                        SecurityUtils.getLoginUser().getTenantId());
            } catch (Exception e) {
                log.error("sendRecycleEmail--发送站内信异常: ", e);
            }

            try {
                // 发送企业微信消息
                messageSendEntryService.sendMsgQywx(recoverVersion.getDocId(), recoverVersion.getId(),
                        user.getUserName(), msgContent);
            } catch (Exception e) {
                log.error("sendRecycleEmail--发送企业微信消息异常: ", e);
            }
        }
    }

    /**
     * 构建邮件参数
     */
    private Map<String, String> buildEmailParams(DocDistribute distribute, Version version,
            String deptName, String pcUrl, String mobileUrl) {
        Map<String, String> param = new HashMap<>();
        param.put("pcLink", pcUrl);
        param.put("mobileLink", mobileUrl);
        param.put("docName", distribute.getDocName());
        param.put("version", version.getVersionValue());
        param.put("deptName", deptName);
        param.put("number", "1");
        param.put("docCode", distribute.getDocId());
        return param;
    }

    /**
     * 发送流程待办邮件提醒
     *
     * @param event   流程处理事件的过程
     * @param applyPo 流程实例
     * @param type    发送邮件的类型
     */
    public void sendFlowEmail(ProcessResultEvent event, ModifyApply applyPo, String type) {
        // 发送短信
        Map<String, String> param = new HashMap<>();
        param.put("title", event.getBpmClientInputModel().getWf_procTitle());
        param.put("docName", StrUtil.isNotBlank(applyPo.getDocName()) ? applyPo.getDocName() : "-");
        param.put("version", StrUtil.isNotBlank(applyPo.getVersionValue()) ? applyPo.getVersionValue() : "-");
        param.put("content", StrUtil.isNotBlank(applyPo.getChangeReason()) ? applyPo.getChangeReason() : "-");
        param.put("docCode", StrUtil.isNotBlank(applyPo.getDocId()) ? applyPo.getDocId() : "-");
        param.put("userName", sysUserService.getNickName(event.getBpmClientInputModel().getWf_sendUserId()));
        workflowService.sendPendingEmail(event.getBpmClientInputModel(), event.getProcessInst(), param, type);
    }

    /**
     * 给所有的参会人员发送邮件提醒
     *
     * @param applyPo 流程实例
     * @param type    发送邮件的类型
     * @param param   参数
     */
    private void sendAllParticipantEmail(ModifyApply applyPo, String type, Map<String, String> param) {
        try {
            // 归档邮件提醒
            List<WorkflowLogVo> workflowLogVos = workflowLogService.selectLogByBusinessId(applyPo.getId());
            List<String> userNameList = new ArrayList<>();
            userNameList.addAll(workflowLogVos.stream().map(WorkflowLogVo::getSender).collect(Collectors.toList()));

            // 查询预览权限的人员
            List<ModifyApplyDistribute> modifyApplyList = docModifyApplyDistributeService
                    .list(new LambdaQueryWrapper<ModifyApplyDistribute>()
                            .eq(ModifyApplyDistribute::getApplyId, applyPo.getId())
                            .eq(ModifyApplyDistribute::getCategory, "train"));
            for (ModifyApplyDistribute modifyApplyDistribute : modifyApplyList) {
                if ("dept".equals(modifyApplyDistribute.getType())) {
                    List<SysUser> userList = sysUserService
                            .getUserByDeptId(modifyApplyDistribute.getReceiveUserDeptId());
                    if (userList.size() > 0) {
                        userNameList.addAll(userList.stream().map(SysUser::getUserName).collect(Collectors.toList()));
                    }
                }
                if ("person".equals(modifyApplyDistribute.getType())
                        && StringUtils.isNotEmpty(modifyApplyDistribute.getReceiveUserName())) {
                    userNameList.add(modifyApplyDistribute.getReceiveUserName());
                }
            }

            List<String> uniqueList = userNameList.stream()
                    .distinct()
                    .collect(Collectors.toList());

            List<SysUser> sysUserList = sysUserService.list(
                    new LambdaQueryWrapper<SysUser>().in(SysUser::getUserName, uniqueList).eq(SysUser::getStatus, 0));
            if (CollUtil.isEmpty(sysUserList)) {
                return;
            }
            List<SysUser> sysUsers = sysUserList.stream().filter(item -> StrUtil.isNotBlank(item.getEmail()))
                    .collect(Collectors.toList());
            if (sysUsers.size() > 0) {
                for (SysUser sysUser : sysUsers) {
                    // messageSendEntryService.sendWorkFlowMessagQywx(applyPo.getChangeType(),applyPo.getDocId(),applyPo.getId(),applyPo.getDocName(),sysUser.getUserName());

                    sysNotifyLogService.sendEmail(type, sysUser.getNickName(), sysUser.getEmail(), sysUser.getUserId(),
                            param);
                }
            }
        } catch (Exception e) {
            log.error("ModifyApplyServiceImpl-->sendAllParticipantEmail----e###", e);
        }
    }

    /**
     * 新增文档流程成功后:
     * 1.创建文件，文件版本
     * 2.若新增附件需要创建附件文件和版本
     * 3.编制文件，文件附件，关联文件，关联记录进行关联
     * 4.文件分发到文件签收,文件打印
     * 5.更新申请状态和文件ID
     * 6.更新申请流程日志信息
     */
    private void handleAddApplySuccess(ModifyApply applyPo, String status, String actDefName) {
        // 更新状态
        String docId = applyPo.getDocId();

        Version version = handleCreateVersion(applyPo, docId, ApplyTypeEnum.ADD);
        // 关联
        if (LinkTypeEnum.RECORD.name().equals(applyPo.getClassType())) {
            // 记录文件 给上级文件增加绑定关系
            handApplyLinkAddRecord(applyPo, version.getId());
        }
        // 关联
        handApplyLink(applyPo, version);

        // 按流程中数据分发 打印
        iDocDistributeService.handleDistributeByApply(version.getApplyId(), version.getId(), version.getDocId(),
                version.getDocName(), applyPo.getDocClass());

        // 发布培训文件
        modifyApplyTrainService.handleModifyApplyTrain(applyPo.getId(), version.getId());

        // 更新状态
        handleUpdateApplyStatus(docId, applyPo, version, status, actDefName);

        //发布编辑记录
        iDocEditLogService.handleEditLog(applyPo.getId(),version.getId());

        try {
            // 保存站内消息
            if (StrUtil.isNotBlank(status) && ProcessStatusConstants.TO_DONE.equals(status)) {
                docMessageService.saveMsg(version.getId(), applyPo.getId(), DocMessageEnum.FLOW_ADD.getMsg(),
                        applyPo.getDocName(), applyPo.getDocId(), DocMessageEnum.FLOW_ADD.getCode(), null, null, null);
            }
        } catch (Exception e) {
            log.error("ModifyApplyServiceImpl-->handleAddApplySuccess--saveMsg--e###", e);
        }

        // 同步DMS添加生效文件到爱数文档库
        this.asFileSyncService.add(applyPo.getVersionId());

        // 发送归档和培训邮件以及企业微信提醒
        try {
            if (StrUtil.isNotBlank(status) && ProcessStatusConstants.TO_DONE.equals(status)) {
                sendTrainEmail(applyPo.getId());
            }
        } catch (Exception e) {
            log.error("ModifyApplyServiceImpl-->handleAddApplySuccess--sendTrainEmail--e###", e);
        }

        // 关联文件发送邮件通知
        try {
            if (StrUtil.isNotBlank(status) && ProcessStatusConstants.TO_DONE.equals(status)) {
                sendChangeEmail(applyPo.getId());
            }
        } catch (Exception e) {
            log.error("ModifyApplyServiceImpl-->handleAddApplySuccess--sendChangeEmail--e###", e);
        }

        // 发送打印消息
        try {
            if (StrUtil.isNotBlank(status) && ProcessStatusConstants.TO_DONE.equals(status)) {
                // 发送打印消息
                sendPrintMsg(applyPo, Constants.CHANGE_TYPE_ADD);
            }
        } catch (Exception e) {
            log.error("ModifyApplyServiceImpl-->handleAddApplySuccess--sendPrintMsg--e###", e);
        }

        // 文件保存到ES
        try {
            if (StrUtil.isNotBlank(status) && ProcessStatusConstants.TO_DONE.equals(status)) {
                long l = System.currentTimeMillis();
                log.info("ES开始："+l);
                elasticsearchService.docPublish(version);
                log.info("ES耗时："+(System.currentTimeMillis()-l));
            }
        } catch (Exception e) {
            log.error("ModifyApplyServiceImpl-->handleAddApplySuccess--docPublish--e###", e);
        }

    }

    /**
     * 发送培训邮件提醒
     *
     * @param applyId
     */
    @Override
    public void sendTrainEmail(String applyId) {
        ModifyApply modifyApply = this.getById(applyId);
        if (StrUtil.isBlank(modifyApply.getVersionId())) {
            return;
        }
        Version version = versionService.getById(modifyApply.getVersionId());
        if (ObjectUtil.isEmpty(version)) {
            return;
        }

        // 获取发送人员
        List<SysUser> sendUserList = getSendUserList(modifyApply);
        if (CollUtil.isEmpty(sendUserList)) {
            return;
        }
        String pcUrl = MsgTemplateUtils.buildPcFileDetailUrl(modifyApply.getDocId(), modifyApply.getVersionId());
        String mobileUrl = MsgTemplateUtils.buildMobilFileDetailUrl(modifyApply.getDocId(), modifyApply.getVersionId());
        String xgContent = MsgTemplateUtils.printHtmlContent(modifyApply.getDocName(), modifyApply.getVersionValue(),
                pcUrl, mobileUrl);

        String createDateTime = DateUtil.format(version.getReleaseTime(), Constants.YYYY_MM_DD);
        String afterDateTime = DateUtil.format(DateUtil.offsetMonth(version.getReleaseTime(), 1), Constants.YYYY_MM_DD);
        String dateTimeContent = createDateTime + "至" + afterDateTime + "（1个月）";

        // 获取流程待办人员
        List<WorkflowLogVo> workflowLogVos = workflowLogService.selectLogByBusinessId(applyId);
        List<String> workflowSenders = workflowLogVos.stream().map(WorkflowLogVo::getSender)
                .collect(Collectors.toList());

        for (SysUser sysUser : sendUserList) {
            Map<String, String> param = new HashMap<>();
            param.put("docName", StrUtil.isNotBlank(modifyApply.getDocName()) ? modifyApply.getDocName() : "-");
            param.put("version",
                    StrUtil.isNotBlank(modifyApply.getVersionValue()) ? modifyApply.getVersionValue() : "-");
            param.put("content",
                    StrUtil.isNotBlank(modifyApply.getChangeReason()) ? modifyApply.getChangeReason() : "-");
            param.put("docCode", StrUtil.isNotBlank(modifyApply.getDocId()) ? modifyApply.getDocId() : "-");
            param.put("dateTime", dateTimeContent);
            param.put("userName", sysUser.getNickName());
            param.put("xgContent", xgContent);
            param.put("pcLink", pcUrl);
            param.put("mobileLink", mobileUrl);
            // 发送邮件
            sysNotifyLogService.sendEmail(SendType.GDXG.getCode(), sysUser.getNickName(), sysUser.getEmail(),
                    sysUser.getUserId(), param);
            // 发送企业微信消息提醒,如果不在流程待办人员中才发送
            if (!workflowSenders.contains(sysUser.getUserName())) {
                messageSendEntryService.sendWorkFlowMessagQywx(modifyApply.getChangeType(), modifyApply.getDocId(),
                        modifyApply.getId(), modifyApply.getDocName(), sysUser.getUserName());
            }
        }
    }


    /**
     * 发送邮件变更通知
     *
     * @param applyId
     */
    private void sendChangeEmail(String applyId) {
        // 1.获取关联的附件，类型为REF_DOC、RECORD
        List<ModifyApplyLink> modifyApplyLinks = modifyApplyLinkService.list(new LambdaQueryWrapper<ModifyApplyLink>()
                .eq(ModifyApplyLink::getApplyId, applyId)
                .in(ModifyApplyLink::getLinkType, LinkTypeEnum.REF_DOC, LinkTypeEnum.RECORD, LinkTypeEnum.NOTE,
                        LinkTypeEnum.NOTE_DOC));
        if (CollUtil.isEmpty(modifyApplyLinks)) {
            return;
        }
        List<String> versionIdList = modifyApplyLinks.stream().map(ModifyApplyLink::getVersionId)
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(versionIdList)) {
            return;
        }
        // 2.获取所有的标准文件
        List<Version> versionList = versionService
                .list(new LambdaQueryWrapper<Version>().in(Version::getId, versionIdList));
        // 3.获取编制人
        List<String> userNameList = versionList.stream().filter(item -> StrUtil.isNotBlank(item.getUserName()))
                .map(Version::getUserName).collect(Collectors.toList());
        if (CollUtil.isEmpty(userNameList)) {
            return;
        }
        // 关联记录和关联文件，排除掉发起申请编制人
        ModifyApply modifyApply = this.getById(applyId);
        if (ObjectUtil.isNotEmpty(modifyApply) && StrUtil.isNotBlank(modifyApply.getUserName())) {
            userNameList = userNameList.stream().filter(item -> !item.equals(modifyApply.getUserName()))
                    .collect(Collectors.toList());
        }
        // 4.去重
        List<String> distinctUserNameList = userNameList.stream()
                .distinct()
                .collect(Collectors.toList());

        // 5.获取编制人的用户信息
        if (distinctUserNameList.size() == 0) {
            return;
        }
        List<SysUser> sysUserList = sysUserService
                .list(new LambdaQueryWrapper<SysUser>().in(SysUser::getUserName, distinctUserNameList));
        if (CollUtil.isEmpty(sysUserList)) {
            return;
        }
        Map<String, String> param = new HashMap<>();
        String pcLink = MsgTemplateUtils.buildPcFileDetailUrl(modifyApply.getDocId(), modifyApply.getVersionId());
        String mobileUrl = MsgTemplateUtils.buildMobilFileDetailUrl(modifyApply.getDocId(), modifyApply.getVersionId());
        param.put("pcLink", pcLink);
        param.put("mobileLink", mobileUrl);
        param.put("docCode", modifyApply.getDocId());
        param.put("docName", modifyApply.getDocName());
        for (SysUser sysUser : sysUserList) {
            if (StrUtil.isBlank(sysUser.getEmail())) {
                continue;
            }
            sysNotifyLogService.sendEmail(SendType.GLWJ.getCode(), sysUser.getNickName(), sysUser.getEmail(),
                    sysUser.getUserId(), param);
        }
    }

    /**
     * 获取发送人员数据
     *
     * @param modifyApply
     * @return
     */
    private List<SysUser> getSendUserList(ModifyApply modifyApply) {
        List<String> userNameList = new ArrayList<>();
        // 查询预览权限的人员
        List<DocDistribute> modifyApplyList = docDistributeService
                .list(new LambdaQueryWrapper<DocDistribute>()
                        .eq(DocDistribute::getVersionId, modifyApply.getVersionId())
                        .eq(DocDistribute::getDocId, modifyApply.getDocId())
                        .ne(DocDistribute::getType, Constants.DISTRIBUTE_TYPE_PRINT));
        if (CollUtil.isEmpty(modifyApplyList)) {
            return null;
        }
        for (DocDistribute docDistribute : modifyApplyList) {
            if (Constants.TYPE_DEPT.equals(docDistribute.getType())) {
                List<SysUser> userList = sysUserService.getUserByDeptId(docDistribute.getReceiveUserDeptId());
                if (userList.size() > 0) {
                    userNameList.addAll(userList.stream().map(SysUser::getUserName).collect(Collectors.toList()));
                }
            }
            if (Constants.TYPE_PERSON.equals(docDistribute.getType())
                    && StringUtils.isNotEmpty(docDistribute.getReceiveUserName())) {
                userNameList.add(docDistribute.getReceiveUserName());
            }
        }
        // 去重
        userNameList = userNameList.stream().distinct().collect(Collectors.toList());
        List<SysUser> sysUserList = sysUserService.list(
                new LambdaQueryWrapper<SysUser>().in(SysUser::getUserName, userNameList).eq(SysUser::getStatus, 0));
        return sysUserList;
    }

    /**
     * 修订文档流程成功后:
     * 1.旧版本失效
     * 2.新版本生效
     * 3.线下上交旧版本文件，存储文件回收记录
     * 4.若新增附件需要创建附件文件和版本
     * 5.编制文件，文件附件，关联文件，关联记录进行关联
     * 6.文件分发到文件签收,文件打印
     * 7.更新申请状态和文件ID
     * 8.更新申请流程日志信息
     */
    private void handleUpdateApplySuccess(ModifyApply applyPo, String status, String actDefName) {
        if (ObjectUtil.isEmpty(applyPo) || ObjectUtil.isEmpty(applyPo.getDocId())) {
            log.error("handle update apply error: no id");
            return;
        }
        String docId = applyPo.getDocId();
        // 失效上一个版本
        Version lastVersion = versionService.getById(applyPo.getVersionId());
        if (Constants.VALUE_Y.equals(applyPo.getWhetherRetain())) {
            lastVersion.setStatus(CommonStatusEnum.RETAIN.getStatus());
            lastVersion.setReviewTime(applyPo.getRetainDeadline());
        } else {
            lastVersion.setStatus(CommonStatusEnum.INVALID.getStatus());
        }
        lastVersion.setEndDate(new Date());
        versionService.updateById(lastVersion);
        // 失效上一个版本文件关联记录
        this.fileSignatureService.handleLinkLog(applyPo.getId(), lastVersion);

        // 上一个版本文件（签章作废）
        this.fileSignatureService.signEffectiveCore(lastVersion, "cancel", null, null);
        // 同步DMS添加作废文件到爱数文档库
        this.asFileSyncService.disuse(lastVersion.getId());
        // 生成一个新版本
        Version newVersion = handleCreateVersion(applyPo, applyPo.getDocId(), ApplyTypeEnum.UPDATE);
        // 关联
        if (LinkTypeEnum.RECORD.name().equals(applyPo.getClassType())) {
            // 记录文件发起修订
            handApplyLinkUpdateRecord(applyPo, newVersion.getId());
        } else {
            // 旧版本有效的记录文件转移到 modifyApplyLink表等待下一步生效 主文件不带记录文件修订
            // handApplyLinkUpdateDoc(lastVersion.getId(),applyPo.getId());
        }

        // 关联文件生效
        handApplyLink(applyPo, newVersion);

        // 分发
        // 为空默认分发
        // if (!Constants.VALUE_Y.equals(applyPo.getYNTrain())) {
        // iDocDistributeService.handleDistribute(newVersion,applyPo.getDocClass());
        // }
        // 按流程中数据分发
        iDocDistributeService.handleDistributeByApply(newVersion.getApplyId(), newVersion.getId(),
                newVersion.getDocId(), newVersion.getDocName(), applyPo.getDocClass());

        // 发布培训文件
        modifyApplyTrainService.handleModifyApplyTrain(applyPo.getId(), newVersion.getId());

        // 更新状态
        handleUpdateApplyStatus(docId, applyPo, newVersion, status, actDefName);

        //发布编辑记录
        iDocEditLogService.handleEditLog(applyPo.getId(),newVersion.getId());

        this.baseMapper.updateById(applyPo);
        // 同步DMS添加生效文件到爱数文档库
        this.asFileSyncService.add(newVersion.getId());
        // 如果是bom文件就发消息给bom修改版本号

        try {
            // 保存站内消息
            if (StrUtil.isNotBlank(status) && ProcessStatusConstants.TO_DONE.equals(status)) {
                docMessageService.saveMsg(newVersion.getId(), applyPo.getId(), DocMessageEnum.FLOW_UPDATE.getMsg(),
                        applyPo.getDocName(), applyPo.getDocId(), DocMessageEnum.FLOW_UPDATE.getCode(), null, null,
                        null);
            }
        } catch (Exception e) {
            log.error("ModifyApplyServiceImpl-->handleUpdateApplySuccess----e###", e);
        }

        // 发送归档和培训邮件以及企业微信提醒
        try {
            if (StrUtil.isNotBlank(status) && ProcessStatusConstants.TO_DONE.equals(status)) {
                sendTrainEmail(applyPo.getId());
            }
        } catch (Exception e) {
            log.error("ModifyApplyServiceImpl-->handleUpdateApplySuccess--sendTrainEmail--e###", e);
        }

        // 关联文件发送邮件通知
        try {
            if (StrUtil.isNotBlank(status) && ProcessStatusConstants.TO_DONE.equals(status)) {
                sendChangeEmail(applyPo.getId());
            }
        } catch (Exception e) {
            log.error("ModifyApplyServiceImpl-->handleUpdateApplySuccess--sendChangeEmail--e###", e);
        }

        // 文件回收提醒，查询上一次分发人
        try {
            if (StrUtil.isNotBlank(status) && ProcessStatusConstants.TO_DONE.equals(status)) {
                // 文件回收提醒
                sendRecycleEmail(applyPo);
            }
        } catch (Exception e) {
            log.error("ModifyApplyServiceImpl-->handleUpdateApplySuccess--sendRecycleEmail--e###", e);
        }

        // 发送打印消息
        try {
            if (StrUtil.isNotBlank(status) && ProcessStatusConstants.TO_DONE.equals(status)) {
                // 发送打印消息
                sendPrintMsg(applyPo, Constants.CHANGE_TYPE_ADD);
            }
        } catch (Exception e) {
            log.error("ModifyApplyServiceImpl-->handleUpdateApplySuccess--sendPrintMsg--e###", e);
        }

        // 文件修订到ES
        try {
            if (StrUtil.isNotBlank(status) && ProcessStatusConstants.TO_DONE.equals(status)) {
                long l = System.currentTimeMillis();
                log.info("ES开始："+l);
                elasticsearchService.docEditPublish(newVersion,lastVersion);
                log.info("ES耗时："+(System.currentTimeMillis()-l));
            }
        } catch (Exception e) {
            log.error("ModifyApplyServiceImpl-->handleAddApplySuccess--docEditPublish--e###", e);
        }
    }

    /**
     * 作废文档流程成功后：
     * 1.文件版本失效
     * 2.线下上交旧版本文件,存储文件回收记录
     */
    private void handleDisuseApplySuccess(ModifyApply applyPo, String status, String actDefName) {
        if (ObjectUtil.isEmpty(applyPo) || ObjectUtil.isEmpty(applyPo.getDocId())) {
            log.error("handle disuse apply error: no id");
            return;
        }
        StandardVo standard = standardService.getStandardVoByVersionId(applyPo.getVersionId());
        // 复用申请流程的签章生效文件
        standard.setEncryptFileId(applyPo.getEncryptFileId());
        if (ObjectUtil.isEmpty(standard)) {
            return;
        }

        // 失效当前版本
        // 失效上一个版本
        Version lastVersion = versionService.getById(applyPo.getVersionId());
        if (Constants.VALUE_Y.equals(applyPo.getWhetherRetain())) {
            lastVersion.setStatus(CommonStatusEnum.RETAIN.getStatus());
            lastVersion.setReviewTime(applyPo.getRetainDeadline());
        } else {
            lastVersion.setStatus(CommonStatusEnum.INVALID.getStatus());
        }
        lastVersion.setEndDate(new Date());
        lastVersion.setEncryptFileId(applyPo.getEncryptFileId());
        versionService.updateById(lastVersion);

        this.baseMapper.updateById(applyPo);
        // 失效上一个版本文件关联记录
        fileSignatureService.handleLinkLog(applyPo.getId(), lastVersion);

        fileSignatureService.signEffectiveCore(lastVersion, "cancel", null, null);

        // 关联
        if (LinkTypeEnum.RECORD.name().equals(applyPo.getClassType())) {
            // 记录文件发起作废
            handApplyLinkDisuseRecord(null, applyPo.getDocId());
        }

        // 记录文件根据编制人的选择作废
        handApplyLink(applyPo, lastVersion, false);

        // 同步DMS添加失效文件到爱数文档库
        this.asFileSyncService.disuse(applyPo.getVersionId());

        // 发布培训文件
        modifyApplyTrainService.handleModifyApplyTrain(applyPo.getId(), lastVersion.getId());

        // 更新状态
        handleUpdateApplyStatus(applyPo.getDocId(), applyPo, lastVersion, status, actDefName);

        try {
            // 保存站内消息
            if (StrUtil.isNotBlank(status) && ProcessStatusConstants.TO_DONE.equals(status)) {
                docMessageService.saveMsg(applyPo.getVersionId(), applyPo.getId(), DocMessageEnum.FLOW_DISUSE.getMsg(),
                        applyPo.getDocName(), applyPo.getDocId(), DocMessageEnum.FLOW_DISUSE.getCode(), null, null,
                        null);
            }
        } catch (Exception e) {
            log.error("ModifyApplyServiceImpl-->handleDisuseApplySuccess----e###", e);
        }

        // 发送作废邮件通知
        try {
            if (StrUtil.isNotBlank(status) && ProcessStatusConstants.TO_DONE.equals(status)) {
                sendDisuseEmail(applyPo.getId());
            }
        } catch (Exception e) {
            log.error("ModifyApplyServiceImpl-->handleUpdateApplySuccess--sendTrainEmail--e###", e);
        }

        // 文件修订到ES
        try {
            if (StrUtil.isNotBlank(status) && ProcessStatusConstants.TO_DONE.equals(status)) {
                long l = System.currentTimeMillis();
                log.info("ES开始："+l);
                elasticsearchService.docDelPublish(lastVersion);
                log.info("ES耗时："+(System.currentTimeMillis()-l));
            }
        } catch (Exception e) {
            log.error("ModifyApplyServiceImpl-->handleAddApplySuccess--docDelPublish--e###", e);
        }
    }

    /**
     * 发送作废邮件
     *
     * @param applyId
     */
    private void sendDisuseEmail(String applyId) {
        ModifyApply modifyApply = this.getById(applyId);
        if (StrUtil.isBlank(modifyApply.getVersionId())) {
            return;
        }
        Version version = versionService.getById(modifyApply.getVersionId());
        if (ObjectUtil.isEmpty(version)) {
            return;
        }

        // 获取发送人员
        List<SysUser> sendUserList = getSendUserList(modifyApply);
        if (CollUtil.isEmpty(sendUserList)) {
            return;
        }

        // 获取流程待办人员
        List<WorkflowLogVo> workflowLogVos = workflowLogService.selectLogByBusinessId(applyId);
        List<String> workflowSenders = workflowLogVos.stream().map(WorkflowLogVo::getSender)
                .collect(Collectors.toList());
        String pcUrl = MsgTemplateUtils.buildPcFileDetailUrl(modifyApply.getDocId(), modifyApply.getVersionId());
        String mobileUrl = MsgTemplateUtils.buildMobilFileDetailUrl(modifyApply.getDocId(), modifyApply.getVersionId());
        for (SysUser sysUser : sendUserList) {
            Map<String, String> param = new HashMap<>();
            param.put("docName", StrUtil.isNotBlank(modifyApply.getDocName()) ? modifyApply.getDocName() : "-");
            param.put("version",
                    StrUtil.isNotBlank(modifyApply.getVersionValue()) ? modifyApply.getVersionValue() : "-");
            param.put("pcLink", pcUrl);
            param.put("mobileLink", mobileUrl);
            // 发送邮件
            sysNotifyLogService.sendEmail(SendType.ZF.getCode(), sysUser.getNickName(), sysUser.getEmail(),
                    sysUser.getUserId(), param);
            // 发送企业微信消息提醒,如果不在流程待办人员中才发送
            if (!workflowSenders.contains(sysUser.getUserName())) {
                messageSendEntryService.sendWorkFlowMessagQywx(modifyApply.getChangeType(), modifyApply.getDocId(),
                        modifyApply.getId(), modifyApply.getDocName(), sysUser.getUserName());
            }
        }
    }

    /**
     * 创建一个新的版本
     *
     * @param applyPo
     * @param docId
     * @return
     */
    private Version handleCreateVersion(ModifyApply applyPo, String docId, ApplyTypeEnum applyTypeEnum) {
        Version version = new Version();
        // 文件类型对象
        DocClass classObj = this.iDocClassService.getById(applyPo.getDocClass());
        version.setStartDate(applyPo.getSetupTime());
        DocClassVo docClassConfig = this.getDocClassConfig(applyPo.getDocClass());
        if (docClassConfig.getExpiration() == null) {
            docClassConfig.setOpenPrescription("true");
            docClassConfig.setExpiration(NumberConstants.ZERO.longValue());
        }
        long expiration = ObjectUtil.isNotEmpty(applyPo.getExpiration()) &&
                applyPo.getExpiration() > 0 ? applyPo.getExpiration() : docClassConfig.getExpiration();
        if ("'true'".equals(docClassConfig.getOpenPrescription())) {
            // 文件失效日期为永久时
            if (NumberConstants.ZERO.equals(docClassConfig.getExpiration().intValue())) {
                version.setForever(String.valueOf(NumberConstants.ONE));
            } else {
                // 根据生效日期往后移文件类型设置的有效期限减一天为失效日期
                Date date = DateUtil.offset(version.getStartDate(), DateField.MONTH,
                        docClassConfig.getExpiration().intValue());
                version.setEndDate((DateUtil.offset(date, DateField.DAY_OF_MONTH, -1)));
                version.setForever(String.valueOf(NumberConstants.ZERO));
            }
        }
        version.setApplyTime(applyPo.getApplyTime());
        version.setApplyId(applyPo.getId()).setDocId(docId);
        version.setVersionValue(applyPo.getVersionValue()).setStatus(CommonStatusEnum.VALID.getStatus());
        version.setTrainDept(applyPo.getTrainDept());
        version.setContent(applyPo.getContent());
        version.setRemark(applyPo.getRemark());
        version.setChangeFactor(applyPo.getChangeFactor());
        version.setChangeReason(applyPo.getChangeReason());
        version.setDeptId(applyPo.getDeptId());
        version.setUserName(applyPo.getUserName());
        version.setChangeType(applyPo.getChangeType());
        version.setInvokeType(applyPo.getInvokeType());
        version.setInvokeId(applyPo.getInvokeId());
        version.setDocName(applyPo.getDocName());
        version.setCompliance(applyPo.getCompliance());
        version.setWhetherCustomer(applyPo.getWhetherCustomer());
        version.setInternalDocId(applyPo.getInternalDocId());
        /** 扩展字段 赋值 **/
        version.setExt1(applyPo.getExt1());
        version.setExt2(applyPo.getExt2());
        version.setExt3(applyPo.getExt3());
        version.setExt4(applyPo.getExt4());
        version.setExt5(applyPo.getExt5());
        version.setExt6(applyPo.getExt6());
        version.setExt7(applyPo.getExt7());
        version.setExt8(applyPo.getExt8());
        version.setExt9(applyPo.getExt9());
        version.setExt10(applyPo.getExt10());
        version.setExt11(applyPo.getExt11());
        version.setExt12(applyPo.getExt12());
        version.setExt13(applyPo.getExt13());
        version.setExt14(applyPo.getExt14());
        version.setExt15(applyPo.getExt15());
        version.setExt16(applyPo.getExt16());
        version.setExt17(applyPo.getExt17());
        version.setExt18(applyPo.getExt18());
        version.setExt19(applyPo.getExt19());
        version.setExt20(applyPo.getExt20());
        version.setCreateBy(applyPo.getCreateBy());
        if (StringUtils.isNotEmpty(applyPo.getParentDocId())) {
            // 获取上级文件最新版本的id
            QueryWrapper<Version> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(Version::getDocId, applyPo.getParentDocId());
            queryWrapper.lambda().eq(Version::getStatus, NumberConstants.ONE);
            queryWrapper.lambda().select(Version::getId);
            Version lastVersion = versionService.getOne(queryWrapper);
            if (lastVersion != null) {
                version.setUpVersionId(lastVersion.getId());
                applyPo.setUpVersionId(lastVersion.getId());
            }
        }
        version.setParentDocId(applyPo.getParentDocId());
        version.setClassType(applyPo.getClassType());

        // if (Constants.VALUE_Y.equals(applyPo.getYNTrain())) {
        // //培训模块设置的分发权限
        version.setDistributeType(applyPo.getTrainType());
        // }else {
        // //没设置默认分发权限为公司
        // version.setDistributeType(Constants.TYPE_COMPANY);
        // }

        // 流程走到这里 获取从DocSignatureUtils.download方法中传入的缓存值 拿到签章之后的文件id
        String encryptFileId = null;
        if (ObjectUtil.isNotEmpty(docId)) {
            encryptFileId = SpringUtils.getBean(RedisCache.class).getCacheObject(docId);
            version.setEncryptFileId(encryptFileId);
        }
        // 获取文件所属分类的顶级分类对象
        DocClass docClassConf = this.iDocClassService.getClassType(applyPo.getDocClass());
        if ("'true'".equals(docClassConf.getOpenReview()) && docClassConf.getReviewCycle() != null) {
            // 当开启复审时 设当前时间往后移文件类型设置的周期减一天为下次复审的时间
            version.setReviewTime(
                    DateUtil.offset(DateUtils.addMonths(new Date(), docClassConf.getReviewCycle().intValue()),
                            DateField.DAY_OF_MONTH, -1));
        }
        List<ModifyApplyLinkVo> linkVoList = modifyApplyLinkService.queryDocByApplyIdAndType(applyPo.getId(),
                LinkTypeEnum.DOC);
        // 创建standard文件
        if (ApplyTypeEnum.ADD.equals(applyTypeEnum)) {
            // 新增文件则插入数据
            Standard standard = BeanUtil.toBean(applyPo, Standard.class);
            standard.setId(null);
            standard.setExpiration(Long.valueOf(expiration));
            standard.setChangeFactor(applyPo.getChangeFactor());
            standard.setChangeReason(applyPo.getChangeReason());
            standard.setEncryptFileId(encryptFileId);
            standard.setEncryptFileId(applyPo.getEncryptFileId());
            if (ObjectUtil.isNotEmpty(linkVoList)) {
                standard.setFileId(linkVoList.get(0).getFileId());
                version.setFileId(linkVoList.get(0).getFileId());
            }
            // 设置数据类型
            standard.setDataType(classObj.getDataType());
            standardService.save(standard);
            // 更新doc_version 表的standardId
            version.setStandardId(standard.getId());
        } else if (ApplyTypeEnum.UPDATE.equals(applyTypeEnum)) {
            VersionVo voById = versionService.getVoById(applyPo.getVersionId());
            version.setStandardId(voById.getStandardId());
            if (ObjectUtil.isNotEmpty(linkVoList)) {
                version.setFileId(linkVoList.get(0).getFileId());
            }

        }
        // 设置文件版本数据类型
        version.setDataType(classObj.getDataType());
        // 发布时间
        version.setReleaseTime(DateUtil.date());
        versionService.save(version);
        return version;
    }

    private DocClassVo getDocClassConfig(String docClass) {
        DocClassVo voById = iDocClassService.getVoById(docClass);
        return voById;
    }

    private void handApplyLinkAddRecord(ModifyApply applyPo, String versionId) {
        DocLinkLog docLinkLog = new DocLinkLog();
        docLinkLog.setLinkCode(applyPo.getDocId());
        docLinkLog.setFileName(applyPo.getDocName());
        docLinkLog.setFileId(applyPo.getFileId());
        docLinkLog.setVersionValue(applyPo.getVersionValue());
        docLinkLog.setDocClass(applyPo.getDocClass());
        docLinkLog.setLinkType(LinkTypeEnum.RECORD.name());
        docLinkLog.setStartDate(applyPo.getSetupTime());
        docLinkLog.setReleaseTime(DateUtil.date());
        docLinkLog.setStatus(Constants.ONE);
        docLinkLog.setVersionId(versionId);
        docLinkLogService.save(docLinkLog);
        // 插入关联记录表
        DocVersionLink docVersionLink = new DocVersionLink();
        docVersionLink.setLinkId(docLinkLog.getId());
        docVersionLink.setVersionId(applyPo.getUpVersionId());
        iDocVersionLinkService.save(docVersionLink);
    }

    private void handApplyLinkUpdateRecord(ModifyApply applyPo, String versionId) {
        handApplyLinkDisuseRecord(null, applyPo.getDocId());
        handApplyLinkAddRecord(applyPo, versionId);
    }

    private void handApplyLinkDisuseRecord(String versionId, String docId) {
        // 将同一上级版本、同样文件编号的作废
        docLinkLogService.disuseValidLinkLog(versionId, docId);
    }

    private void handApplyLinkUpdateDoc(String oldVersionId, String applyId) {
        ModifyApplyLinkBo linkBo = new ModifyApplyLinkBo();
        linkBo.setVersionId(oldVersionId);
        linkBo.setLinkType(LinkTypeEnum.RECORD.name());
        linkBo.setStatus(NumberConstants.ONE);
        // 获取旧版本有效的
        List<ModifyApplyLinkVo> recordLinksVo = modifyApplyLinkService.selectListByVersionLink(linkBo);
        List<ModifyApplyLink> recordLinks = BeanUtil.copyToList(recordLinksVo, ModifyApplyLink.class);
        for (ModifyApplyLink modifyApplyLink : recordLinks) {
            modifyApplyLink.setApplyId(applyId);
            modifyApplyLink.setLinkClass(LinkClassEnum.LINK.name());
        }
        modifyApplyLinkService.saveAll(recordLinks);
    }

    /**
     * 处理关联
     *
     * @param applyPo 流程
     * @param version 新版本
     */
    private void handApplyLink(ModifyApply applyPo, Version version) {
        handApplyLink(applyPo, version, true);
    }

    private void handApplyLink(ModifyApply applyPo, Version version, boolean add) {
        List<ModifyApplyLink> list = modifyApplyLinkService
                .list(new LambdaQueryWrapper<ModifyApplyLink>().eq(ModifyApplyLink::getApplyId, applyPo.getId()));
        Standard standard = standardService.getById(version.getStandardId());
        for (ModifyApplyLink malVo : list) {
            if (NumberUtil.equals(malVo.getStatus(), NumberConstants.ONE) && add) {
                // 待生效
                if (NumberUtil.equals(malVo.getIsDeleted(), NumberConstants.ZERO)) {
                    // 新增的
                    if (LinkTypeEnum.RECORD.name().equals(malVo.getLinkType())) {
                        // 新增记录文件入台账
                        Version recordVersion = versionService.saveCopyByLink(standard, version, malVo);
                        malVo.setVersionId(recordVersion.getId());
                        // 自身附件
                        docLinkLogService.handApplyLinkAdd(recordVersion, malVo.getDocClass(), LinkTypeEnum.DOC.name(),
                                recordVersion.getId(),recordVersion.getFileId());
                        // 为空默认分发
                        // if (!Constants.VALUE_Y.equals(applyPo.getYNTrain())) {
                        // iDocDistributeService.handleDistribute(recordVersion,malVo.getDocClass());
                        // }
                        // 分发
                        iDocDistributeService.handleDistributeByApply(version.getApplyId(), recordVersion.getId(),
                                recordVersion.getDocId(), recordVersion.getDocName(), malVo.getDocClass());
                    } else if (LinkTypeEnum.NOTE.name().equals(malVo.getLinkType())) {
                        // 给关联记录(多对多)同步新增 主文件记录
                        docLinkLogService.handApplyLinkAdd(version, standard.getDocClass(),
                                LinkTypeEnum.NOTE_DOC.name(), malVo.getVersionId(),version.getFileId());
                    } else if (LinkTypeEnum.NOTE_DOC.name().equals(malVo.getLinkType())) {
                        // 给主文件记录同步新增 关联记录(多对多)
                        docLinkLogService.handApplyLinkAdd(version, standard.getDocClass(), LinkTypeEnum.NOTE.name(),
                                malVo.getVersionId(),version.getFileId());
                    }
                } else {
                    // 未做变动
                    if (LinkTypeEnum.RECORD.name().equals(malVo.getLinkType())) {
                        // 记录文件未做变动 记录文件绑定主文件id同步变更
                        versionService
                                .update(new LambdaUpdateWrapper<Version>().set(Version::getUpVersionId, version.getId())
                                        .eq(Version::getId, malVo.getVersionId()));
                    } else if (LinkTypeEnum.NOTE.name().equals(malVo.getLinkType())) {
                        // 关联记录(多对多)未做变动 需同步变更相对的主文件记录版本
                        handApplyLinkDisuseRecord(malVo.getVersionId(), version.getDocId());
                        docLinkLogService.handApplyLinkAdd(version, standard.getDocClass(),
                                LinkTypeEnum.NOTE_DOC.name(), malVo.getVersionId(),version.getFileId());
                    } else if (LinkTypeEnum.NOTE_DOC.name().equals(malVo.getLinkType())) {
                        // 主文件记录未做变动 需同步变更相对的关联记录(多对多)版本
                        handApplyLinkDisuseRecord(malVo.getVersionId(), version.getDocId());
                        docLinkLogService.handApplyLinkAdd(version, standard.getDocClass(), LinkTypeEnum.NOTE.name(),
                                malVo.getVersionId(),version.getFileId());
                    }
                }
                if (LinkTypeEnum.DOC.name().equals(malVo.getLinkType())
                        || LinkTypeEnum.APPENDIX.name().equals(malVo.getLinkType())) {
                    // 主文件和附件之前没有当前版本数据 现在补充 都存主文件的版本数据
                    malVo.setVersionId(version.getId());
                    malVo.setDocClass(standard.getDocClass());
                    malVo.setStartDate(version.getStartDate());
                    malVo.setReleaseTime(version.getReleaseTime());
                    malVo.setDocId(version.getDocId());
                    malVo.setVersionValue(version.getVersionValue());
                    malVo.setProtoFileId(malVo.getProtoFileId());
                }
                DocLinkLog docLinkLog = docLinkLogService.handApplyLinkAdd(malVo, malVo.getLinkType(), version.getId());
                // 更新linkId
                malVo.setLinkId(docLinkLog.getId());
                modifyApplyLinkService.updateById(malVo);
            } else if (NumberUtil.equals(malVo.getStatus(), NumberConstants.TWO)) {
                // 作废的
                if (LinkTypeEnum.RECORD.name().equals(malVo.getLinkType())) {
                    // 修订前的记录 只作废版本
                    versionService.update(new LambdaUpdateWrapper<Version>()
                            .set(Version::getStatus, CommonStatusEnum.INVALID.getStatus())
                            .set(Version::getEndDate, new Date())
                            .eq(Version::getId, malVo.getVersionId()));
                    handApplyLinkDisuseRecord(malVo.getVersionId(), malVo.getDocId());
                } else if (LinkTypeEnum.NOTE.name().equals(malVo.getLinkType())
                        || LinkTypeEnum.NOTE_DOC.name().equals(malVo.getLinkType())) {
                    // 给关联记录(多对多)同步作废 主文件记录 或 给主文件同步作废 关联记录(多对多)
                    handApplyLinkDisuseRecord(malVo.getVersionId(), version.getDocId());
                }
            }
        }
    }

    /**
     * 流程数据更新状态和docId
     *
     * @param docId
     * @param applyPo
     */
    private void handleUpdateApplyStatus(String docId, ModifyApply applyPo, Version version, String status,
            String actDefName) {
        if (ObjectUtil.isNotEmpty(applyPo)) {
            if (ObjectUtil.isNotEmpty(status)) {
                applyPo.setRecordStatus(status);
            }
            if (ObjectUtil.isNotEmpty(version)) {
                applyPo.setDocId(docId);
                applyPo.setVersionId(version.getId());
                applyPo.setVersionValue(version.getVersionValue());
            }
            updateById(applyPo);
        }
    }

    /**
     * TODO: 根据文档类型获取有效年限
     *
     * @param docClass
     * @return
     */
    @Override
    public Long getDocExpiration(String docClass) {
        DocClassVo voById = iDocClassService.getVoById(docClass);
        if (ObjectUtil.isNotEmpty(voById)) {
            return voById.getExpiration();
        } else {
            return 1L;
        }
    }

    /**
     * 注册事件
     */
    @PostConstruct
    private void registerEventBus() {
        ProcessEventBus.register(this);
    }

    /**
     * 取消注册事件
     */
    @PreDestroy
    private void unregisterEventBus() {
        ProcessEventBus.unregister(this);
    }

    /**
     * 保存前的数据校验
     *
     * @param entity 实体类数据
     */
    private void validEntityBeforeSave(ModifyApply entity) {
        // TODO 做一些数据校验,如唯一约束
    }

    private LambdaQueryWrapper<ModifyApply> buildQueryWrapper(ModifyApplyBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<ModifyApply> lqw = Wrappers.lambdaQuery();

        lqw.eq(ModifyApply::getUserName, SecurityUtils.getUsername());
        lqw.eq(StringUtils.isNotBlank(bo.getChangeType()), ModifyApply::getChangeType, bo.getChangeType());
        lqw.eq(StringUtils.isNotBlank(bo.getDocClass()), ModifyApply::getDocClass, bo.getDocClass());
        lqw.like(StringUtils.isNotBlank(bo.getDocName()), ModifyApply::getDocName, bo.getDocName());
        lqw.eq(StringUtils.isNotBlank(bo.getDocId()), ModifyApply::getDocId, bo.getDocId());
        lqw.eq(bo.getDeptId() != null, ModifyApply::getDeptId, bo.getDeptId());
        lqw.eq(bo.getApplyTime() != null, ModifyApply::getApplyTime, bo.getApplyTime());
        lqw.eq(bo.getExpiration() != null, ModifyApply::getExpiration, bo.getExpiration());
        lqw.eq(StringUtils.isNotBlank(bo.getVersionId()), ModifyApply::getVersionId, bo.getVersionId());
        lqw.eq(StringUtils.isNotBlank(bo.getVersionValue()), ModifyApply::getVersionValue, bo.getVersionValue());
        lqw.eq(StringUtils.isNotBlank(bo.getYNTrain()), ModifyApply::getYNTrain, bo.getYNTrain());
        lqw.eq(StringUtils.isNotBlank(bo.getTrainDept()), ModifyApply::getTrainDept, bo.getTrainDept());
        lqw.eq(StringUtils.isNotBlank(bo.getYNMergeDraft()), ModifyApply::getYNMergeDraft, bo.getYNMergeDraft());
        lqw.eq(StringUtils.isNotBlank(bo.getYNEncrypt()), ModifyApply::getYNEncrypt, bo.getYNEncrypt());
        lqw.eq(StringUtils.isNotBlank(bo.getYNDistribute()), ModifyApply::getYNDistribute, bo.getYNDistribute());
        lqw.eq(StringUtils.isNotBlank(bo.getFileId()), ModifyApply::getFileId, bo.getFileId());
        lqw.eq(StringUtils.isNotBlank(bo.getRecordStatus()), ModifyApply::getRecordStatus, bo.getRecordStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getChangeReason()), ModifyApply::getChangeReason, bo.getChangeReason());
        lqw.eq(StringUtils.isNotBlank(bo.getContent()), ModifyApply::getContent, bo.getContent());
        lqw.eq(StringUtils.isNotBlank(bo.getBatchId()), ModifyApply::getBatchId, bo.getBatchId());
        lqw.orderByDesc(ModifyApply::getApplyTime);
        lqw.orderByAsc(ModifyApply::getId);
        return lqw;
    }

    /**
     * 生成新的文档ID
     *
     * @return
     */

    @Override
    public DocNoVo getDocNoByApplyId(String applyId) throws Exception {
        ModifyApply applyPo = getById(applyId);
        if (StringUtils.isNotEmpty(applyPo.getDocId())) {
            DocNoVo docNoVo = new DocNoVo();
            docNoVo.setDocId(applyPo.getDocId());
            docNoVo.setDocName(applyPo.getDocName());
            docNoVo.setBusId(applyId);
            return docNoVo;
        } else {
            Map<String, Object> bizMap = BeanUtil.beanToMap(applyPo);
            DocNoVo docNoVo = iGenerateIdService.generateDocId(bizMap);
            applyPo.setDocId(docNoVo.getDocId());
            this.baseMapper.updateById(applyPo);
            docNoVo.setDocName(applyPo.getDocName());
            docNoVo.setBusId(applyId);
            return docNoVo;
        }
    }

    @Override
    public Boolean updateDocId(CreateNewNoDTO dto) throws ServerException {
        if (!dto.getNewNo().equals(dto.getOldNo())) {
            CodeRuleLog codeRuleLog = iCodeRuleLogService.docIdExist(dto.getNewNo());
            if (ObjectUtil.isNotEmpty(codeRuleLog)) {
                if (dto.getBusId().equals(codeRuleLog.getBusinessId())) {
                    return true;
                } else {
                    throw new ServiceException(
                            dto.getNewNo() + I18nUtils.getTitle(CommonI18nConstant.FILE_MODIFY_NUM_EXIST));
                }
            } else {
                workflowApplyLogService.update(new LambdaUpdateWrapper<WorkflowApplyLog>()
                        .eq(WorkflowApplyLog::getId, dto.getBusId()).set(WorkflowApplyLog::getDocId, dto.getNewNo()));
                updateData(dto);
            }
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateDocIdList(List<CreateNewNoDTO> list) throws ServerException {
        String busId = null;
        for (CreateNewNoDTO dto : list) {
            CodeRuleLog codeRuleLog = iCodeRuleLogService.docIdExist(dto.getNewNo());
            if (ObjectUtil.isNotEmpty(codeRuleLog)
                    && list.stream().noneMatch(cnd -> !dto.getBusId().equals(cnd.getBusId())
                            && cnd.getBusId().equals(codeRuleLog.getBusinessId()))) {
                if (!dto.getBusId().equals(codeRuleLog.getBusinessId())) {
                    throw new ServiceException(
                            dto.getNewNo() + I18nUtils.getTitle(CommonI18nConstant.FILE_MODIFY_NUM_EXIST));
                }
            } else {
                busId = updateData(dto);
            }
        }
        if (busId != null) {
            workflowApplyLogService.update(new LambdaUpdateWrapper<WorkflowApplyLog>()
                    .eq(WorkflowApplyLog::getId, busId).set(WorkflowApplyLog::getDocId, list.stream()
                            .map(CreateNewNoDTO::getNewNo).collect(Collectors.joining(Constants.ID_SPLIT_KEY))));
        }
    }

    private String updateData(CreateNewNoDTO dto) throws ServerException {
        // 获取文件类型
        ModifyApply modifyApply = getOne(new LambdaQueryWrapper<ModifyApply>().eq(ModifyApply::getId, dto.getBusId()));
        // 获取编号规则
        String ruleId = iCodeRuleService.getDocRuleId(modifyApply.getDocClass());
        Map<String, Object> map = BeanUtil.beanToMap(modifyApply);
        // 获取新编号流水号
        DocIdNumDTO numDto = iCodeRuleService.getNumberValueByDocId(dto.getNewNo(), ruleId, map);
        // 更新编号生成日志表
        iCodeRuleLogService.update(new LambdaUpdateWrapper<CodeRuleLog>()
                .eq(CodeRuleLog::getBusinessId, dto.getBusId()).eq(CodeRuleLog::getRuleId, ruleId)
                .set(CodeRuleLog::getRuleValue, dto.getNewNo()).set(CodeRuleLog::getNumberValue, numDto.getValue()));
        update(new LambdaUpdateWrapper<ModifyApply>().eq(ModifyApply::getId, dto.getBusId()).set(ModifyApply::getDocId,
                dto.getNewNo()));
        return modifyApply.getBatchId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateRecordDocId(List<CreateNewNoDTO> dtoList) throws ServerException {
        for (CreateNewNoDTO dto : dtoList) {
            if (!dto.getNewNo().equals(dto.getOldNo())) {
                CodeRuleLog codeRuleLog = iCodeRuleLogService.docIdExist(dto.getNewNo());
                if (ObjectUtil.isNotEmpty(codeRuleLog)) {
                    throw new ServiceException(
                            dto.getNewNo() + I18nUtils.getTitle(CommonI18nConstant.FILE_MODIFY_NUM_EXIST));
                } else {
                    ModifyApplyLink modifyApplyLink = modifyApplyLinkService.getOne(
                            new LambdaQueryWrapper<ModifyApplyLink>().eq(ModifyApplyLink::getFileId, dto.getBusId())
                                    .select(ModifyApplyLink::getDocClass));
                    String ruleId = iCodeRuleService.getDocRuleId(modifyApplyLink.getDocClass());
                    Map<String, Object> map = new HashMap<String, Object>();
                    map.put("docClass", modifyApplyLink.getDocClass());
                    DocIdNumDTO numDto = iCodeRuleService.getNumberValueByDocId(dto.getNewNo(), ruleId, map);
                    iCodeRuleLogService.update(new LambdaUpdateWrapper<CodeRuleLog>()
                            .eq(CodeRuleLog::getBusinessId, dto.getBusId()).eq(CodeRuleLog::getRuleId, ruleId)
                            .set(CodeRuleLog::getRuleValue, dto.getNewNo())
                            .eq(CodeRuleLog::getNumberValue, numDto.getValue()));
                    modifyApplyLinkService.update(
                            new LambdaUpdateWrapper<ModifyApplyLink>().eq(ModifyApplyLink::getFileId, dto.getBusId())
                                    .set(ModifyApplyLink::getDocId, dto.getNewNo()));
                }
            }
        }
        return true;
    }

    /**
     * 生产记录文件的文件编号
     *
     * @param requestMap 关联记录文件id
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<DocNoVo> getRecordDocNoByLinkId(Map<String, String> requestMap) throws Exception {
        String applyId = requestMap.get("applyId");
        String fileIds = requestMap.get("fileIds");
        String docId = requestMap.get("docId");
        ModifyApply applyPo = getById(applyId);
        List<String> linkList = StrUtil.split(fileIds, ',');
        List<DocNoVo> list = new ArrayList<>();
        if (linkList.size() > 0) {
            for (String fileId : linkList) {
                ModifyApplyLink applyLink = this.modifyApplyLinkService.getOne(new LambdaQueryWrapper<ModifyApplyLink>()
                        .eq(ModifyApplyLink::getFileId, fileId).eq(ModifyApplyLink::getApplyId, applyId));
                if (ObjectUtil.isNotEmpty(applyLink)) {
                    if (StringUtils.isNotEmpty(applyLink.getDocId())) {
                        DocNoVo docNoVo = new DocNoVo();
                        docNoVo.setDocId(applyLink.getDocId());
                        docNoVo.setLinkId(applyLink.getFileId());
                        docNoVo.setDocName(applyLink.getDocName());
                        list.add(docNoVo);
                    } else {
                        applyLink.setParentDocId(docId);
                        Map<String, Object> bizMap = BeanUtil.beanToMap(applyLink);
                        bizMap.put("parentDocId", applyPo.getDocId());
                        bizMap.put("deptId", applyPo.getDeptId());
                        DocNoVo docNoVo = iGenerateIdService.generateRecordDocId(bizMap);
                        applyLink.setDocId(docNoVo.getDocId());
                        this.modifyApplyLinkService.updateById(applyLink);
                        docNoVo.setLinkId(fileId);
                        docNoVo.setDocName(applyLink.getDocName());
                        list.add(docNoVo);
                    }
                }
            }
        }
        return list;
    }

    @Override
    public boolean checkDocIdExist(CreateNewNoDTO dto) {
        CodeRuleLog codeRuleLog = iCodeRuleLogService.docIdExist(dto.getNewNo());
        return ObjectUtil.isNotEmpty(codeRuleLog) && !dto.getBusId().equals(codeRuleLog.getBusinessId());
    }

    @Override
    public Boolean updateDocSerialNumber(CreateNewNoDTO bo) throws Exception {
        return iGenerateIdService.updateDocSerialNumber(bo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateRecordSerialNumber(List<CreateNewNoDTO> listBo) throws Exception {
        for (CreateNewNoDTO bo : listBo) {
            iGenerateIdService.updateRecordSerialNumber(bo);
        }
        return true;
    }

    @Override
    public Boolean updateByImport(ModifyApplyIBo ibo, int num, ImportIVo ivo, ConfigureDTO configure) {
        if (StringUtils.isBlank(ibo.getId())) {
            ivo.initFileMsgAdd(num, I18nUtils.getTitle(CommonI18nConstant.SERVICE_ID_NOT_EXIST));
            return false;
        }

        return baseMapper.updateById(BeanUtil.toBean(ibo, ModifyApply.class)) > 0;
    }

    @Override
    public String handleCompareFile(ModifyApplyBo modifyApplyBo) throws Exception{
        String basicFileId1 = "";
        String basicFileId2 = "";
        // 查询历史生效版本
        Version version = versionService.list(new LambdaQueryWrapper<Version>()
                .ne(StringUtils.isNotEmpty(modifyApplyBo.getVersionId()), Version::getId, modifyApplyBo.getVersionId())
                .eq(Version::getDocId, modifyApplyBo.getDocId()).orderByDesc(Version::getStartDate)).stream().findFirst().orElse(null);
        basicFileId2 = version.getFileId();
        if(ObjectUtil.isNotEmpty(modifyApplyBo.getFileId())){
            basicFileId1 = modifyApplyBo.getFileId();
        }else{
            ModifyApplyLink modifyApplyLink1 = modifyApplyLinkService.getOne(new LambdaQueryWrapper<ModifyApplyLink>().eq(ModifyApplyLink::getApplyId,modifyApplyBo.getId()).eq(ModifyApplyLink::getLinkType,"DOC"));
            if(ObjectUtil.isNotEmpty(modifyApplyLink1)){
                basicFileId1 = modifyApplyLink1.getFileId();
            }
        }
        if(ObjectUtil.isNotEmpty(basicFileId1) && ObjectUtil.isNotEmpty(basicFileId2)){
            return basicFilePdfService.fileCompareToPdf(basicFileId1,basicFileId2,modifyApplyBo.getId());
        }
        return null;
    }
}

package com.rzdata.process.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.rzdata.framework.constant.Constants;
import com.rzdata.framework.core.domain.entity.SysUser;
import com.rzdata.framework.core.mybatisplus.core.ServicePlusImpl;
import com.rzdata.framework.core.page.PagePlus;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.utils.PageUtils;
import com.rzdata.framework.utils.StringUtils;
import com.rzdata.process.domain.DocMessage;
import com.rzdata.process.domain.ModifyApplyTrain;
import com.rzdata.process.domain.bo.ModifyApplyTrainBo;
import com.rzdata.process.domain.dto.ModifyApplyTrainDTO;
import com.rzdata.process.domain.vo.BasicFileVo;
import com.rzdata.process.domain.vo.ModifyApplyTrainVo;
import com.rzdata.process.mapper.ModifyApplyTrainMapper;
import com.rzdata.process.service.IBasicFileService;
import com.rzdata.process.service.IDocMessageService;
import com.rzdata.process.service.IModifyApplyTrainService;
import com.rzdata.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 文件变更操作申请培训记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-01-08
 */
@Service
public class ModifyApplyTrainServiceImpl extends ServicePlusImpl<ModifyApplyTrainMapper, ModifyApplyTrain, ModifyApplyTrainVo> implements IModifyApplyTrainService {


    @Autowired
    private IBasicFileService basicFileService;

    @Autowired
    private ISysUserService iSysUserService;

    @Autowired
    private IDocMessageService idocMessageService;

    @Override
    public ModifyApplyTrainVo queryById(String id){
        return getVoById(id);
    }

    @Override
    public TableDataInfo<ModifyApplyTrainVo> queryPageList(ModifyApplyTrainBo bo) {
        PagePlus<ModifyApplyTrain, ModifyApplyTrainVo> result = pageVo(PageUtils.buildPagePlus(), buildQueryWrapper(bo));
        return PageUtils.buildDataInfo(result);
    }

    @Override
    public List<ModifyApplyTrainVo> queryList(ModifyApplyTrainBo bo) {
        return listVo(buildQueryWrapper(bo));
    }

    @Override
    public List<ModifyApplyTrainVo> queryModifyApplyTrain(ModifyApplyTrainBo bo){
        List<ModifyApplyTrainVo> trainVoList = queryList(bo);

        List<String> basicFileIdLinks = new ArrayList<>();

        if (CollectionUtil.isNotEmpty(trainVoList)) {
            for (ModifyApplyTrainVo trainVo : trainVoList) {
                if (ObjectUtil.isNotEmpty(trainVo.getFileIds())) {
                    basicFileIdLinks.addAll(Arrays.asList(trainVo.getFileIds().split(Constants.ID_SPLIT_KEY)));
                }
            }
        }

        Map<String, BasicFileVo> idFileNameMap = basicFileService.queryByIdList(basicFileIdLinks);


        if (CollectionUtil.isNotEmpty(trainVoList)) {
            for (ModifyApplyTrainVo trainVo : trainVoList) {
                if (ObjectUtil.isNotEmpty(trainVo.getFileIds())) {
                    List<String> fileIdList = Arrays.asList(trainVo.getFileIds().split(Constants.ID_SPLIT_KEY));
                    List<BasicFileVo> fileVoList = new ArrayList<>();
                    for (String fileId : fileIdList) {
                        BasicFileVo fileVo = idFileNameMap.get(fileId);
                        if (ObjectUtil.isNotEmpty(fileVo)) {
                            fileVoList.add(fileVo);
                        }
                    }
                    if (CollectionUtil.isNotEmpty(fileVoList)) {
                        trainVo.setFiles(fileVoList);
                    }
                }
            }
        }

        return trainVoList;
    }

    private LambdaQueryWrapper<ModifyApplyTrain> buildQueryWrapper(ModifyApplyTrainBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<ModifyApplyTrain> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getApplyId()), ModifyApplyTrain::getApplyId, bo.getApplyId());
        lqw.eq(StringUtils.isNotBlank(bo.getVersionId()), ModifyApplyTrain::getVersionId, bo.getVersionId());
        lqw.eq(StringUtils.isNotBlank(bo.getType()), ModifyApplyTrain::getType, bo.getType());
        lqw.eq(StringUtils.isNotBlank(bo.getDocId()), ModifyApplyTrain::getDocId, bo.getDocId());
        lqw.eq(bo.getIsDeleted()!=null, ModifyApplyTrain::getIsDeleted, bo.getIsDeleted());
        return lqw;
    }

    @Override
    public Boolean insertByBo(ModifyApplyTrainBo bo) {
        ModifyApplyTrain add = BeanUtil.toBean(bo, ModifyApplyTrain.class);
        validEntityBeforeSave(add);
        boolean flag = save(add);
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateModifyApplyTrainList(ModifyApplyTrainDTO bo) {
        //培训记录每上传一条记录或删除一条记录都会触发这个方法 但数据都没有id
        List<String> fileIdList = new ArrayList<>();
        if (bo.getTrains().size()>0) {
            //list为不含删除记录 的所有记录
            for (ModifyApplyTrain item:bo.getTrains()) {
                //查找当前记录是否数据库存在
                long count=count(new LambdaQueryWrapper<ModifyApplyTrain>()
                        .eq(ModifyApplyTrain::getFileIds,item.getFileIds())
                        .eq(ModifyApplyTrain::getType,item.getType())
                        .eq(StringUtils.isNotEmpty(bo.getApplyId()),ModifyApplyTrain::getApplyId,bo.getApplyId())
                        .eq(StringUtils.isNotEmpty(bo.getVersionId()),ModifyApplyTrain::getVersionId,bo.getVersionId()));
                if (count==0){
                    //不存在 则新增 并发送消息给文控
                    save(item);
                    //美好创亿不发消息
//                    sendMsg(item.getVersionId(),item.getDocId());
                }
                fileIdList.add(item.getFileIds());
            }
        }
        if (StringUtils.isNotEmpty(bo.getVersionId())||StringUtils.isNotEmpty(bo.getApplyId())) {
            //不在list中的数据 就是要删除的数据
            remove(new LambdaQueryWrapper<ModifyApplyTrain>()
                    .eq(StringUtils.isNotEmpty(bo.getVersionId()),ModifyApplyTrain::getVersionId,bo.getVersionId())
                    .eq(StringUtils.isNotEmpty(bo.getApplyId()),ModifyApplyTrain::getApplyId,bo.getApplyId())
                    .eq(ModifyApplyTrain::getType,bo.getType())
                    .notIn(fileIdList.size()>0,ModifyApplyTrain::getFileIds,fileIdList));
        }
        return true;
    }

    @Override
    public List<ModifyApplyTrainVo> getModifyApplyTrainList(String versionId) {
        List<ModifyApplyTrainVo> list=listVo(new LambdaQueryWrapper<ModifyApplyTrain>().eq(ModifyApplyTrain::getVersionId,versionId));
        for (ModifyApplyTrainVo vo : list) {
            vo.setFiles(basicFileService.listVoByIds(Arrays.asList(vo.getFileIds().split(Constants.ID_SPLIT_KEY))));
        }
        return list;
    }

    @Override
    public Boolean updateByBo(ModifyApplyTrainBo bo) {
        ModifyApplyTrain update = BeanUtil.toBean(bo, ModifyApplyTrain.class);
        validEntityBeforeSave(update);
        return updateById(update);
    }

    /**
     * 保存前的数据校验
     *
     * @param entity 实体类数据
     */
    private void validEntityBeforeSave(ModifyApplyTrain entity){
        //TODO 做一些数据校验,如唯一约束
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return removeByIds(ids);
    }

    @Override
    public void handleModifyApplyTrain(String applyId,String versionId){
        update(new LambdaUpdateWrapper<ModifyApplyTrain>().eq(ModifyApplyTrain::getApplyId,applyId).set(ModifyApplyTrain::getVersionId,versionId));
    }

    @Override
    public List<String> validateRequired(ModifyApplyTrainBo bo){
        List<ModifyApplyTrainVo> list = baseMapper.validateRequired(bo);
        return list.stream().filter(item->item.getNum()<1).map(ModifyApplyTrainVo::getDocName).collect(Collectors.toList());
    }
}

package com.rzdata.process.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import com.rzdata.framework.utils.PageUtils;
import com.rzdata.framework.core.page.PagePlus;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.utils.SecurityUtils;
import com.rzdata.framework.utils.StringUtils;
import com.rzdata.process.domain.BasicFile;
import com.rzdata.process.domain.BasicFilePdf;
import com.rzdata.process.domain.PrintFileDetail;
import com.rzdata.process.domain.vo.PrintFileDetailVo;
import com.rzdata.process.mapper.PrintFileDetailMapper;
import com.rzdata.process.service.IBasicFilePdfService;
import com.rzdata.process.service.IBasicFileService;
import com.rzdata.process.service.IPrintFileDetailService;
import groovy.lang.Lazy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.rzdata.framework.core.mybatisplus.core.ServicePlusImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 文件打印明细Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-04-28
 */
@Service
public class PrintFileDetailServiceImpl extends ServicePlusImpl<PrintFileDetailMapper, PrintFileDetail, PrintFileDetailVo> implements IPrintFileDetailService {

    @Autowired
    private IBasicFilePdfService basicFilePdfService ;

    @Autowired
    private IBasicFileService basicFileService;

    @Autowired
    PrintFileDetailMapper printFileDetailMapper;

    @Override
    public void addPrintFileDetail(String pdfId) {
        PrintFileDetail printFileDetail = new PrintFileDetail();
        BasicFilePdf basicFilePdf = basicFilePdfService.getOne(new LambdaQueryWrapper<BasicFilePdf>().eq(BasicFilePdf::getPdfId,pdfId));
        if(ObjectUtil.isNotEmpty(basicFilePdf)){
            printFileDetail.setBizId(basicFilePdf.getId());
            BasicFile basicFile = basicFileService.getById(basicFilePdf.getFileId());
            if(ObjectUtil.isNotEmpty(basicFile)){
                printFileDetail.setFileName(basicFile.getFileName());
            }
        }
        printFileDetail.setPrintUserName(SecurityUtils.getLoginUser().getUsername());
        printFileDetail.setPrintUserNickName(SecurityUtils.getLoginUser().getNickName());
        printFileDetail.setCreateTime(new Date());
        this.save(printFileDetail);
    }

    @Override
    public List<PrintFileDetailVo> getListBydistributeId(String distributeId) {
        return printFileDetailMapper.getListBydistributeId(distributeId);
    }
}

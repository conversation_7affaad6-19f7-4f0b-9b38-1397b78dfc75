package com.rzdata.process.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.rzdata.framework.constant.Constants;
import com.rzdata.framework.exception.ServiceException;
import com.rzdata.framework.core.mybatisplus.core.ServicePlusImpl;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.utils.SecurityUtils;
import com.rzdata.framework.utils.StringUtils;
import com.rzdata.process.domain.DocDistribute;
import com.rzdata.process.domain.PrintTask;
import com.rzdata.process.domain.bo.PrintTaskBo;
import com.rzdata.process.domain.vo.DocDistributeVo;
import com.rzdata.process.domain.vo.PrintTaskVo;
import com.rzdata.process.mapper.PrintTaskMapper;
import com.rzdata.process.service.IDocDistributeService;
import com.rzdata.process.service.IPrintTaskService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 打印任务Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-01-18
 */
@Service
public class PrintTaskServiceImpl extends ServicePlusImpl<PrintTaskMapper, PrintTask,PrintTaskVo> implements IPrintTaskService {

    @Autowired
    private IDocDistributeService docDistributeService;

    @Autowired
    private IDocDistributeService iDocDistributeService;

    private boolean isAvailable = true; // 控制线程执行顺序的标志

    @Override
    public TableDataInfo<PrintTaskVo> queryPageList(PrintTaskBo bo) {
        Page<PrintTask> page = new Page<>(bo.getPageNum(), bo.getPageSize());
        LambdaQueryWrapper<PrintTask> lqw = buildQueryWrapper(bo);
        Page<PrintTask> result = page(page, lqw);
        return new TableDataInfo<>(
            result.getRecords().stream()
                .map(entity -> BeanUtil.toBean(entity, PrintTaskVo.class))
                .collect(Collectors.toList()),
            result.getTotal()
        );
    }

    /**
     * 查询打印任务列表
     */
    @Override
    public List<PrintTaskVo> queryList(PrintTaskBo bo) {
        LambdaQueryWrapper<PrintTask> lqw = buildQueryWrapper(bo);
        return list(lqw).stream()
            .map(entity -> BeanUtil.toBean(entity, PrintTaskVo.class))
            .collect(Collectors.toList());
    }

    private LambdaQueryWrapper<PrintTask> buildQueryWrapper(PrintTaskBo bo) {
        LambdaQueryWrapper<PrintTask> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getDocName()), PrintTask::getDocName, bo.getDocName());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), PrintTask::getStatus, bo.getStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getDocType()), PrintTask::getDocType, bo.getDocType());

        if (StringUtils.isNotEmpty(bo.getKeyword())) {
            //文件名称/文件编号/分发号
            lqw.and((wrapper) -> {
                wrapper.like(PrintTask::getDocName, bo.getKeyword())
                        .or().like(PrintTask::getDocId, bo.getKeyword())
                        .or().like(PrintTask::getClassificationNo, bo.getKeyword())
                ;
            });
        }

        if (StringUtils.isNotEmpty(bo.getFileNameOrPrinter())) {
            //电子文件名称/打印人
            lqw.and((wrapper) -> {
                wrapper.like(PrintTask::getElectronicFileName, bo.getFileNameOrPrinter())
                        .or().like(PrintTask::getCreateName, bo.getFileNameOrPrinter())
                ;
            });
        }
        lqw.ge(StringUtils.isNotEmpty(bo.getStartTime()), PrintTask::getCreateTime,bo.getStartTime()+" 00:00:00");
        lqw.le(StringUtils.isNotEmpty(bo.getEndTime()),PrintTask::getCreateTime,bo.getEndTime()+" 23:59:59");

        lqw.orderByDesc(PrintTask::getCreateTime);
        return lqw;
    }

    @Override
    public Boolean insertByBo(PrintTaskBo bo){
        System.out.println("========================================================");
        PrintTask add = BeanUtil.toBean(bo, PrintTask.class);
        checkPrint(add);
        add.setCreateName(SecurityUtils.getNickname());
        validEntityBeforeSave(add);
        //默认是打印成功
        add.setStatus(Constants.PATROL_TASK_STATUS_COMPLETED);
        boolean result = save(add);
        if(result){
            bo.setId(add.getId());

            updatePrintNumAdd(bo);

        }
        return result;
    }

    public synchronized void updatePrintNumAdd(PrintTaskBo bo) {
        while (!isAvailable) {
            try {
                wait(); // 等待其他线程释放锁
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }

        isAvailable = false; // 标记为不可用，阻止其他线程执行

        if(StringUtils.isNotEmpty(bo.getDocDistributeId())){
            DocDistribute docDistribute = iDocDistributeService.getById(bo.getDocDistributeId());
            if(docDistribute!=null){
                //打印数据+1
                docDistribute.setPrintNums(docDistribute.getPrintNums()+1);
                iDocDistributeService.updateById(docDistribute);
            }
        }

        isAvailable = true; // 标记为可用，允许其他线程执行
        notifyAll(); // 唤醒所有等待的线程
    }

    //判断当前用户是否对当前文件有打印权限
    //判断当前用户是否有对应的角色权限
    private void checkPrint(PrintTask printTask){

        DocDistributeVo printAuthById = docDistributeService.getPrintAuthById(printTask.getDocDistributeId(), printTask.getFileId());
        if(printAuthById == null){
            throw new ServiceException("暂无权限或已打印过！");
        }

    }

    @Override
    public Boolean updateByBo(PrintTaskBo bo) {
        PrintTask update = BeanUtil.toBean(bo, PrintTask.class);
        validEntityBeforeSave(update);
        return updateById(update);
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(PrintTask entity) {
        // 默认未执行
        if (StringUtils.isEmpty(entity.getStatus())) {
            entity.setStatus("0");
        }
    }

    @Override
    public Boolean deleteWithValidByIds(List<String> ids, Boolean isValid) {
        if (isValid) {
            // TODO 做一些业务上的校验,判断是否允许删除
        }
        return removeByIds(ids);
    }

    @Override
    public Boolean executePrintTask(String id) {
        PrintTask task = getById(id);
        if (task != null) {
            task.setStatus("1"); // 设置为已执行
            task.setUpdateTime(new Date()); // 设置更新时间
            return updateById(task);
        }
        return false;
    }
} 
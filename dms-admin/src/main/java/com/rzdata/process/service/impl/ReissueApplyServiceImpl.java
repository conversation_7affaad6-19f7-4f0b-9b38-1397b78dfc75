package com.rzdata.process.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.blueland.bpmclient.model.ProcessInstanceModel;
import com.google.common.eventbus.AllowConcurrentEvents;
import com.google.common.eventbus.Subscribe;
import com.rzdata.config.CustomConfig;
import com.rzdata.config.ProcessConfig;
import com.rzdata.eventbus.ProcessEventBus;
import com.rzdata.eventbus.ProcessResultEvent;
import com.rzdata.framework.constant.Constants;
import com.rzdata.framework.constant.DocMsgConstants;
import com.rzdata.framework.constant.ProcessStatusConstants;
import com.rzdata.framework.core.domain.entity.SysUser;
import com.rzdata.framework.core.mybatisplus.core.ServicePlusImpl;
import com.rzdata.framework.core.page.PagePlus;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.utils.PageUtils;
import com.rzdata.framework.utils.SecurityUtils;
import com.rzdata.framework.utils.StringUtils;
import com.rzdata.process.domain.*;
import com.rzdata.process.domain.bo.ReissueApplyBo;
import com.rzdata.process.domain.vo.ReissueApplyVo;
import com.rzdata.process.domain.vo.WorkflowApplyLogVo;
import com.rzdata.process.enums.*;
import com.rzdata.process.mapper.ReissueApplyMapper;
import com.rzdata.process.service.*;
import com.rzdata.process.utils.MsgTemplateUtils;
import com.rzdata.setting.service.ICodeRuleService;
import com.rzdata.setting.service.IDocPresetUserService;
import com.rzdata.system.service.ISysConfigService;
import com.rzdata.system.service.ISysNotifyLogService;
import com.rzdata.system.service.ISysUserService;
import com.rzdata.system.service.WorkflowService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.*;
import java.util.stream.Collectors;

import static com.rzdata.framework.constant.Constants.PROCESS_KEY;

/**
 * 文件补发申请Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-12-31
 */
@Slf4j
@Service
public class ReissueApplyServiceImpl extends ServicePlusImpl<ReissueApplyMapper, ReissueApply, ReissueApplyVo> implements IReissueApplyService {

    @Autowired
    WorkflowService workflowService;

    @Autowired
    ISysConfigService configService;

    @Autowired
    IWorkflowApplyLogService iWorkflowApplyLogService;

    @Autowired
    ICodeRuleService iCodeRuleService;

    @Autowired
    private  IGenerateIdService iGenerateIdService;

    @Autowired
    private  IReissueApplyItemService iReissueApplyItemService;

    @Autowired
    IDocDistributeService iDocDistributeService;

    @Autowired
    IDocMessageService docMessageService;

    @Autowired
    IDocPresetUserService iDocPresetUserService;

    @Autowired
    ISysUserService sysUserService;

    @Autowired
    private ISysNotifyLogService sysNotifyLogService;

    @Autowired
    private IMessageSendEntryService messageSendEntryService;

    @Autowired
    CustomConfig customConfig;

    @Autowired
    IVersionService iVersionService;

    @Override
    public ReissueApplyVo queryById(String id) {
        return getVoById(id);
    }

    @Override
    public ReissueApplyVo queryByBpmnId(String bpmnId) {
        WorkflowApplyLogVo workflowApplyLog = iWorkflowApplyLogService.getVoOne(new LambdaQueryWrapper<WorkflowApplyLog>().eq(WorkflowApplyLog::getProcInstId,bpmnId).last("limit 1"));
        return queryById(workflowApplyLog.getId());
    }

    @Override
    public TableDataInfo<ReissueApplyVo> queryPageList(ReissueApplyBo bo) {
        PagePlus<ReissueApply, ReissueApplyVo> result = pageVo(PageUtils.buildPagePlus(), buildQueryWrapper(bo));
        return PageUtils.buildDataInfo(result);
    }

    @Override
    public List<ReissueApplyVo> queryList(ReissueApplyBo bo) {
        return listVo(buildQueryWrapper(bo));
    }

    private LambdaQueryWrapper<ReissueApply> buildQueryWrapper(ReissueApplyBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<ReissueApply> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getApplyTitle()), ReissueApply::getApplyTitle, bo.getApplyTitle());
        lqw.eq(StringUtils.isNotBlank(bo.getDocId()), ReissueApply::getDocId, bo.getDocId());
        lqw.eq(bo.getDeptId() != null, ReissueApply::getDeptId, bo.getDeptId());
        lqw.like(StringUtils.isNotBlank(bo.getUserName()), ReissueApply::getUserName, bo.getUserName());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), ReissueApply::getStatus, bo.getStatus());
        return lqw;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ProcessInstanceModel insertByBo(ReissueApplyBo bo) {
        ReissueApply add = BeanUtil.toBean(bo, ReissueApply.class);
//        if (ObjectUtil.isEmpty(bo.getId())) {
//            String applyId = iGenerateIdService.generateApplyId(ApplyTypeEnum.REISSUE, bo.getUserName());
//            add.setId(applyId);
//        }
        if (BooleanUtil.isTrue(bo.getEditStatus())) {
            add.setCreateBy(SecurityUtils.getUsername());
        }
        boolean flag = saveOrUpdate(add);
        // 保存关系数据
        ProcessInstanceModel processInstanceModel = null;
        if (flag) {
            bo.setId(add.getId());
            if (BooleanUtil.isTrue(bo.getEditStatus())) {
                saveApplyItem(bo);
            }
            if (BooleanUtil.isTrue(bo.getPresetUserEdit())) {
                iDocPresetUserService.updatePresetUser(bo.getPresetUserList(),bo.getId());
            }
            // 开启流程 调用工作流相关接口
            try {
                bo.getBpmClientInputModel().setStatus(bo.getRecordStatus());
                bo.getBpmClientInputModel().setBizType(ApplyTypeEnum.REISSUE.name());
                if(StringUtils.equals(bo.getRecordStatus(), RecordStatusEnum.DRAFT.getCode())){
                    processInstanceModel = workflowService.saveExecute(bo.getBpmClientInputModel(), add.getId());
                }else if (StringUtils.equals(bo.getRecordStatus(),RecordStatusEnum.CANCEL.getCode())){
                    processInstanceModel = workflowService.cancelExecute(bo.getBpmClientInputModel(), add.getId());
                }else{
                    processInstanceModel = workflowService.nextExecute(bo.getBpmClientInputModel(), add.getId());
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return processInstanceModel;
    }

    private void saveApplyItem(ReissueApplyBo bo) {
        QueryWrapper<ReissueApplyItem> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ReissueApplyItem:: getApplyId, bo.getId());
        iReissueApplyItemService.remove(queryWrapper);
        if (ObjectUtil.isNotEmpty(bo.getItemList())) {
            for (ReissueApplyItem item : bo.getItemList()) {
                item.setId(null);
                item.setApplyId(bo.getId());
            }
            iReissueApplyItemService.saveBatch(bo.getItemList());
        }
    }

    /**
     * 同步事件监听处理
     */
    @Subscribe
    @AllowConcurrentEvents
    protected void onProcessEvent(ProcessResultEvent event) {
        ProcessConfig processConfig = JSONUtil.toBean(configService.selectConfigByKey(PROCESS_KEY), ProcessConfig.class);
        if (!event.getApplyType().contains(processConfig.getProcDefKeyBFSQ())) {
            return;
        }
        ReissueApply reissueApply = this.baseMapper.selectById(event.getApplyId());
        if (ObjectUtil.isEmpty(reissueApply)) {
            return;
        }
        reissueApply.setStatus(event.getStatus());
        this.baseMapper.updateById(reissueApply);
        String status = event.getStatus();
        String actDefName = event.getModel().getWf_nextActDefName();
        if (Objects.equals(ProcessStatusConstants.TO_DONE, event.getStatus())) {
            actDefName = "结束";
            List<ReissueApplyItem> itemList = iReissueApplyItemService.list(new LambdaQueryWrapper<ReissueApplyItem>().eq(ReissueApplyItem::getApplyId,event.getApplyId()));
            Integer code = iDocDistributeService.getMaxCodeByVersionId(reissueApply.getVersionId());
            List<DocDistribute> list = new ArrayList<>();
            for (ReissueApplyItem item: itemList) {
                DocDistribute distribute = new DocDistribute();
                distribute.setVersionId(reissueApply.getVersionId());
                distribute.setDocId(reissueApply.getDocId());
                distribute.setDocClass(reissueApply.getDocClass());
                distribute.setDocName(reissueApply.getDocName());
                distribute.setReceiveUserDeptId(item.getReceiveUserDeptId());
                distribute.setReceiveUserDept(item.getReceiveUserDept());
                distribute.setReceiveUserName(item.getReceiveUserName());
                distribute.setReceiveNickName(item.getReceiveNickName());
                distribute.setType(Constants.DISTRIBUTE_TYPE_PRINT);
                distribute.setCode(++code);
                list.add(distribute);
            }
            iDocDistributeService.saveBatch(list);

            try {
                //保存站内消息
                docMessageService.saveMsg(reissueApply.getVersionId(),
                        reissueApply.getId(),
                        DocMessageEnum.FLOW_REISSUE.getMsg(),
                        reissueApply.getDocName(),
                        reissueApply.getDocId(),
                        DocMessageEnum.FLOW_REISSUE.getCode() , null, null, null);
            }catch (Exception e){
                log.error("ReissueApplyServiceImpl-->onProcessEvent--saveMsg--e###", e);
            }

            //发送打印消息
            sendPrintMsg(reissueApply, list);

        }
        saveWorkFlowLog(reissueApply,status,event.getProcessInst().getProcInstTitle(),event);
    }


    /**
     * 发送打印消息
     * @param reissueApply    申请人员
     * @param distributeList  分配人员
     */
    private void sendPrintMsg(ReissueApply reissueApply, List<DocDistribute> distributeList) {
        List<Version> versionList = iVersionService.list(new LambdaQueryWrapper<Version>().
                in(Version::getId, distributeList.stream().map(DocDistribute::getVersionId).collect(Collectors.toList())));

        for (DocDistribute distribute : distributeList) {
            Map<String, String> param = new HashMap<>();
            Version version = versionList.stream().filter(item -> distribute.getVersionId().equals(item.getId())).findFirst().orElse(null);
            if(ObjectUtil.isEmpty(version)){
                continue;
            }

            String linkUrl = MsgTemplateUtils.buildPcFileDetailUrl(distribute.getDocId(), version.getId());
            String mobileUrl = MsgTemplateUtils.buildMobilFileDetailUrl(distribute.getDocId(), version.getId());
            String content = MsgTemplateUtils.printHtmlContent(distribute.getDocName(), version.getVersionValue(), linkUrl, mobileUrl);
            if(StrUtil.isBlank(content)){
                continue;
            }
            param.put("title", "文件补发");
            param.put("content", content);

            SysUser user = sysUserService.selectUserByUserName(distribute.getReceiveUserName());
            if (ObjectUtil.isEmpty(user)) {
                continue;
            }

            try {
                // 发送邮件
                sysNotifyLogService.sendEmail(SendType.PRINT.getCode(), user.getNickName(), user.getEmail(),
                        user.getUserId(), param);
            } catch (Exception e) {
                log.error("sendPrintMsg--发送回收邮件异常: ", e);
            }

            String msgContent = "【文件补发-打印提醒】" + String.format("文件名称：%s，版本：%s，请联系部门文控进行打印", distribute.getDocName(), version.getVersionValue());

            try {
                // 发送站内信
                docMessageService.sendInstationMessage(reissueApply.getId(), distribute.getDocId(), distribute.getDocClass(),
                        distribute.getDocName(), distribute.getVersionId(), null,
                        reissueApply.getDeptId(), reissueApply.getDeptName(), user.getUserName(), user.getUserId(), msgContent,
                        DocMsgConstants.MSG_TYPE_MSG, MsgTypeEnum.PRINT.getType(), linkUrl, mobileUrl,
                        SecurityUtils.getLoginUser().getTenantId());
            } catch (Exception e) {
                log.error("sendPrintMsg--发送站内信异常: ", e);
            }

            try {
                // 发送企业微信消息
                messageSendEntryService.sendMsgQywx(distribute.getDocId(), reissueApply.getId(),
                        user.getUserName(), msgContent);
            } catch (Exception e) {
                log.error("sendPrintMsg--发送企业微信消息异常: ", e);
            }
        }
    }

    /**
     * 保存申请记录
     */
    public void saveWorkFlowLog(ReissueApply apply, String status, String procTitle, ProcessResultEvent event) {
        WorkflowApplyLog log = new WorkflowApplyLog();
        log.setId(apply.getId());
        log.setDocId(apply.getDocId());
        log.setVersionValue(apply.getVersionValue());
        log.setDocName(procTitle);
        log.setProcStatus(status);
        log.setUserName(apply.getUserName());
        log.setDeptId(apply.getDeptId());
        iWorkflowApplyLogService.updateStatusByBusId(log,event);
    }

    /**
     * 注册事件
     */
    @PostConstruct
    private void registerEventBus() {
        ProcessEventBus.register(this);
    }

    /**
     * 取消注册事件
     */
    @PreDestroy
    private void unregisterEventBus() {
        ProcessEventBus.unregister(this);
    }

    @Override
    public Boolean updateByBo(ReissueApplyBo bo) {
        ReissueApply update = BeanUtil.toBean(bo, ReissueApply.class);
        validEntityBeforeSave(update);
        saveApplyItem(bo);
        if (BooleanUtil.isTrue(bo.getPresetUserEdit())) {
            iDocPresetUserService.updatePresetUser(bo.getPresetUserList(),bo.getId());
        }
        String procTitle = workflowService.updateFlowTitle(bo.getApplyTitle(),bo.getId());
        saveWorkFlowLog(update,bo.getRecordStatus(),procTitle,null);
        return updateById(update);
    }

    /**
     * 保存前的数据校验
     *
     * @param entity 实体类数据
     */
    private void validEntityBeforeSave(ReissueApply entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return removeByIds(ids);
    }

    @Override
    public List<ReissueApply> listByDisItemId(String disItemId) {
        return this.baseMapper.listByDisItemId(disItemId);
    }
}

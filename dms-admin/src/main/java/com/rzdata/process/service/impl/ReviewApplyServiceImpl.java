package com.rzdata.process.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.blueland.bpmclient.model.PageResultModel;
import com.blueland.bpmclient.model.ProcessInstanceModel;
import com.blueland.bpmclient.model.SearchQuery;
import com.google.common.eventbus.AllowConcurrentEvents;
import com.google.common.eventbus.Subscribe;
import com.rzdata.config.ProcessConfig;
import com.rzdata.eventbus.ProcessEventBus;
import com.rzdata.eventbus.ProcessResultEvent;
import com.rzdata.framework.constant.Constants;
import com.rzdata.framework.constant.NumberConstants;
import com.rzdata.framework.constant.ProcessStatusConstants;
import com.rzdata.framework.core.domain.entity.SysUser;
import com.rzdata.framework.core.mybatisplus.core.ServicePlusImpl;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.utils.DateUtils;
import com.rzdata.framework.utils.PageUtils;
import com.rzdata.framework.utils.SecurityUtils;
import com.rzdata.framework.utils.StringUtils;
import com.rzdata.process.api.WorkflowApiController;
import com.rzdata.process.domain.*;
import com.rzdata.process.domain.bo.ModifyApplyBo;
import com.rzdata.process.domain.bo.ReviewApplyBo;
import com.rzdata.process.domain.bo.ReviewApplyItemBo;
import com.rzdata.process.domain.vo.ReviewApplyItemVo;
import com.rzdata.process.domain.vo.ReviewApplyVo;
import com.rzdata.process.domain.vo.WorkflowApplyLogVo;
import com.rzdata.process.enums.*;
import com.rzdata.process.mapper.ReviewApplyMapper;
import com.rzdata.process.service.*;
import com.rzdata.setting.domain.DocClass;
import com.rzdata.setting.service.IDocClassService;
import com.rzdata.setting.service.IDocPresetUserService;
import com.rzdata.system.service.ISysConfigService;
import com.rzdata.system.service.ISysDeptService;
import com.rzdata.system.service.ISysUserService;
import com.rzdata.system.service.WorkflowService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.*;
import java.util.stream.Collectors;

import static com.rzdata.framework.constant.Constants.PROCESS_KEY;

/**
 * 文件复审申请Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-12-30
 */
@Service
@Slf4j
public class ReviewApplyServiceImpl extends ServicePlusImpl<ReviewApplyMapper, ReviewApply, ReviewApplyVo> implements IReviewApplyService {

    @Autowired
    WorkflowService workflowService;

    @Autowired
    ISysConfigService configService;

    @Autowired
    IWorkflowApplyLogService iWorkflowApplyLogService;

    @Autowired
    IDocMessageService docMessageService;

    @Autowired
    IVersionService versionServices;

    @Autowired
    IStandardService standardService;

    @Autowired
    IDocClassService iDocClassService;

    @Autowired
    IReviewApplyItemService applyItemService;

    @Autowired
    private IWorkflowApplyLogService workflowApplyLogService;

    @Autowired
    private ISysDeptService iSysDeptService;

    @Autowired
    private ISysUserService iSysUserService;

    @Autowired
    private WorkflowApiController workflowApiController;

    @Autowired
    private IDocPresetUserService iDocPresetUserService;

    @Autowired
    FileSignatureService fileSignatureService;

    @Override
    public ReviewApplyVo queryById(String id) {
        ReviewApplyVo vo = getVoById(id);
        vo.setNickName(iSysUserService.getNickName(vo.getUserName()));
        vo.setDeptName(iSysDeptService.getDeptName(vo.getDeptId()));
        List<ReviewApplyItemVo> itemList= applyItemService.listVo(new LambdaQueryWrapper<ReviewApplyItem>().eq(ReviewApplyItem::getApplyId,vo.getId()));
        itemList.forEach(item->{
            item.setNickName(iSysUserService.getNickName(item.getUserName()));
            item.setDeptName(iSysDeptService.getDeptName(item.getDeptId()));
        });
        vo.setItemList(itemList);
        return vo;
    }

    @Override
    public ReviewApplyVo queryByBpmnId(String bpmnId) {
        WorkflowApplyLogVo workflowApplyLog = workflowApplyLogService.getVoOne(new LambdaQueryWrapper<WorkflowApplyLog>().eq(WorkflowApplyLog::getProcInstId,bpmnId).last("limit 1"));
        return queryById(workflowApplyLog.getId());
    }

    @Override
    public TableDataInfo<ReviewApplyVo> queryPageList(ReviewApplyBo bo) {
        SearchQuery searchQuery = new SearchQuery();
        searchQuery.setPageNumber(bo.getPageNum());
        searchQuery.setPageSize(bo.getPageSize());
        searchQuery.setStatus(Integer.valueOf(bo.getStatus()));
        searchQuery.setRecUserId(SecurityUtils.getUsername());
        searchQuery.setProcDefKey(bo.getProcDefKey());
        PageResultModel pageResultModel = workflowService.findRecordList(searchQuery);
        List<String> ids = new ArrayList<>();
        List<WorkFlowInfo> workFlowInfos = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(pageResultModel) && pageResultModel.getTotalCount() > 0) {
            workFlowInfos = JSONUtil.toList(JSONUtil.toJsonStr(pageResultModel.getResult()), WorkFlowInfo.class);
            ids = workFlowInfos.stream().map(x -> x.getProcInstId()).collect(Collectors.toList());
        }
        Page<ReviewApplyVo> page = new Page<>(bo.getPageNum(), bo.getPageSize());
        Page<ReviewApplyVo> iPage = this.baseMapper.selectReviewApplyPage(page, bo, ids);
        return PageUtils.buildDataInfo(iPage);
    }

    @Override
    public List<ReviewApplyVo> queryList(ReviewApplyBo bo) {
        return listVo(buildQueryWrapper(bo));
    }

    private LambdaQueryWrapper<ReviewApply> buildQueryWrapper(ReviewApplyBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<ReviewApply> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getApplyTitle()), ReviewApply::getApplyTitle, bo.getApplyTitle());
        lqw.eq(StringUtils.isNotBlank(bo.getApplyTitle()), ReviewApply::getApplyTitle, bo.getApplyTitle());
        lqw.eq(bo.getDeptId() != null, ReviewApply::getDeptId, bo.getDeptId());
        lqw.like(StringUtils.isNotBlank(bo.getUserName()), ReviewApply::getUserName, bo.getUserName());
        lqw.eq(StringUtils.isNotBlank(bo.getReason()), ReviewApply::getReason, bo.getReason());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), ReviewApply::getStatus, bo.getStatus());
        return lqw;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ProcessInstanceModel insertByBo(ReviewApplyBo bo) {
        ReviewApply add = BeanUtil.toBean(bo, ReviewApply.class);
        validEntityBeforeSave(add);
//        if (ObjectUtil.isEmpty(bo.getId())) {
//            String applyId = iGenerateIdService.generateApplyId(ApplyTypeEnum.REVIEW, bo.getUserName());
//            add.setId(applyId);
//        }
        if (BooleanUtil.isTrue(bo.getEditStatus())) {
            add.setCreateBy(SecurityUtils.getUsername());
        }
        boolean flag = saveOrUpdate(add);
        if (BooleanUtil.isTrue(bo.getPresetUserEdit())) {
            iDocPresetUserService.updatePresetUser(bo.getPresetUserList(),bo.getId());
        }
        if (BooleanUtil.isTrue(bo.getEditStatus())) {
            updateReviewApplyItem(bo,add.getId());
        }
        ProcessInstanceModel processInstanceModel = null;
        if (flag) {
            bo.setId(add.getId());
            try {
                bo.getBpmClientInputModel().setStatus(bo.getRecordStatus());
                bo.getBpmClientInputModel().setBizType(bo.getApplyType());
                if(StringUtils.equals(bo.getRecordStatus(),RecordStatusEnum.DRAFT.getCode())){
                    processInstanceModel = workflowService.saveExecute(bo.getBpmClientInputModel(), add.getId());
                }else if (StringUtils.equals(bo.getRecordStatus(),RecordStatusEnum.CANCEL.getCode())){
                    processInstanceModel = workflowService.cancelExecute(bo.getBpmClientInputModel(), add.getId());
                }else{
                    processInstanceModel = workflowService.nextExecute(bo.getBpmClientInputModel(), add.getId());
                }
            } catch (Exception e) {
                log.error("start doc review apply fail:", e);
            }
        }
        if (CollUtil.isNotEmpty(bo.getPresetUserList())) {
            iDocPresetUserService.updatePresetUser(bo.getPresetUserList(),add.getId());
        }
        return processInstanceModel;
    }

    /**
     * 新增文件复审清单
     *
     * @param bo bo对象
     * @param id 复审id
     */

    private List<ReviewApplyItem> updateReviewApplyItem(ReviewApplyBo bo, String id) {
        List<ReviewApplyItem> items = new ArrayList<>();
        List<String> idList = new ArrayList<>();
        for (ReviewApplyItemBo itemBo : bo.getItemList()){
            ReviewApplyItem item = BeanUtil.toBean(itemBo, ReviewApplyItem.class);
            item.setApplyId(id);
            if (StringUtils.isNotEmpty(item.getId())) {
                idList.add(item.getId());
            }
            items.add(item);
        }
        applyItemService.remove(new LambdaQueryWrapper<ReviewApplyItem>().eq(ReviewApplyItem::getApplyId,id).notIn(idList.size()>0,ReviewApplyItem::getId,idList));
        applyItemService.saveOrUpdateAll(items);
        return items;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateByBo(ReviewApplyBo bo) {
        ReviewApply update = BeanUtil.toBean(bo, ReviewApply.class);
        List<ReviewApplyItem> itemList = updateReviewApplyItem(bo,update.getId());
        validEntityBeforeSave(update);
        if (BooleanUtil.isTrue(bo.getPresetUserEdit())) {
            iDocPresetUserService.updatePresetUser(bo.getPresetUserList(),bo.getId());
        }
        String procTitle = workflowService.updateFlowTitle(bo.getApplyTitle(),bo.getId());
        saveWorkFlowLog(update,itemList,bo.getRecordStatus(),procTitle, null);
        return updateById(update);
    }

    /**
     * 同步事件监听处理
     */
    @Subscribe
    @AllowConcurrentEvents
    protected void onProcessEvent(ProcessResultEvent event) {
        ProcessConfig processConfig = JSONUtil.toBean(configService.selectConfigByKey(PROCESS_KEY), ProcessConfig.class);
        if (!(event.getApplyType().contains(processConfig.getProcDefKeyFSSQ())||event.getApplyType().contains(processConfig.getProcDefKeyLYSQ()))) {
            return;
        }
        ReviewApply reviewApply = this.baseMapper.selectById(event.getApplyId());
        if (ObjectUtil.isEmpty(reviewApply)) {
            return;
        }
        String status = event.getStatus();
        reviewApply.setStatus(status);
        this.baseMapper.updateById(reviewApply);

        String actDefName = event.getModel().getWf_nextActDefName();
        //流程结束
        List<ReviewApplyItem> itemList = applyItemService.list(new LambdaQueryWrapper<ReviewApplyItem>().eq(ReviewApplyItem::getApplyId,event.getApplyId()));

        //流程结束
        if (Objects.equals(ProcessStatusConstants.TO_DONE, status)) {
            itemList.forEach(item->{
                if(ApplyStatusEnum.EXTENSION.getCode().equals(item.getReviewAction())
                        || ApplyStatusEnum.KEEP.getCode().equals(item.getReviewAction()) ){
                    // 延长有效期和保持现状 场景
                    if (ApplyTypeEnum.RETAIN.name().equalsIgnoreCase(reviewApply.getApplyType())) {
                        Version lastVersion = versionServices.getById(item.getVersionId());
                        lastVersion.setReviewTime(item.getSetupTime());
                        versionServices.updateById(lastVersion);
                        this.fileSignatureService.signEffectiveCore(lastVersion,"cancel",null,null);
                    }else {
                        updateDocVersion(item);
                    }
                } else if(ApplyStatusEnum.UPDATE.getCode().equals(item.getReviewAction())||ApplyStatusEnum.DISUSE.getCode().equals(item.getReviewAction())){
                    // 修订和作废 场景
                    //startModifyApply(item,reviewApply.getDataType(),event.getModel().getWf_procDefKey());
                    startModifyApply(item,reviewApply.getDataType(),event.getApplyType(),reviewApply.getApplyTitle());
                    if (!ApplyTypeEnum.RETAIN.name().equalsIgnoreCase(reviewApply.getApplyType())) {
                        // 设置文件版本的下次文件复审时间
                        item.setReviewAction(ApplyStatusEnum.KEEP.getCode());
                        updateDocVersion(item);
                    }
                }
            });
//            event.setMsgInfo(ApplyStatusEnum.getMsg(event.getApplyStatus()));
//            docMessageService.insertMessage(event, MsgTypeEnum.REVIEW);
            actDefName = "结束";

            ReviewApplyItem reviewApplyItem = null;
            if (CollUtil.isNotEmpty(itemList)) {
                reviewApplyItem = itemList.get(0);
            }
            try {
                // 保存站内消息
                docMessageService.saveMsg(
                        reviewApplyItem != null ? reviewApplyItem.getVersionId() : null,
                        reviewApply.getId(),
                        DocMessageEnum.FLOW_REVIEW.getMsg(),
                        reviewApplyItem != null ? reviewApplyItem.getDocName() : null,
                        reviewApplyItem != null ? reviewApplyItem.getDocId() : null,
                        DocMessageEnum.FLOW_REVIEW.getCode() , null, null, null
                );
            } catch (Exception e) {
                log.error("ReviewApplyServiceImpl-->onProcessEvent--saveMsg--e###", e);
            }
        }else{
            //待办的时候发送邮件提醒
            if(CollUtil.isNotEmpty(itemList)){
                List<String> userNameList = itemList.stream().map(ReviewApplyItem::getUserName).collect(Collectors.toList());
                List<SysUser> sysUserList = null;
                if(CollUtil.isNotEmpty(userNameList)){
                    userNameList = userNameList.stream().distinct().collect(Collectors.toList());
                    sysUserList = iSysUserService.list(new LambdaQueryWrapper<SysUser>().in(SysUser::getUserName, userNameList));
                }

                //年 月 日至 年 月 日
                Date createTime = reviewApply.getCreateTime();

                String createDateTime = DateUtil.format(reviewApply.getCreateTime(), Constants.YYYY_MM_DD);
                String afterDateTime = DateUtil.format(DateUtil.offsetMonth(createTime, 1), Constants.YYYY_MM_DD);


                Map<String, String> param = new HashMap<>();
                StringBuilder content = new StringBuilder();
                for (ReviewApplyItem reviewApplyItem : itemList) {
                    content.append("<p><span style='font-family: 宋体;font-size: 14px'>");
                    content.append("文件名称：");
                    content.append(StrUtil.isNotBlank(reviewApplyItem.getDocName()) ? reviewApplyItem.getDocName() : "-");
                    content.append(",");
                    content.append("版本：");
                    content.append(StrUtil.isNotBlank(reviewApplyItem.getVersionValue()) ? reviewApplyItem.getVersionValue() : "-");
                    content.append(",");
                    content.append("回顾期限：");
                    content.append(createDateTime);
                    content.append("至");
                    content.append(afterDateTime);
                    content.append("（1个月）");
                    content.append(",");
                    content.append("文件编号：");
                    content.append(StrUtil.isNotBlank(reviewApplyItem.getDocId()) ? reviewApplyItem.getDocId() : "-");
                    content.append(",");
                    content.append("编制人：");
                    if(CollUtil.isNotEmpty(sysUserList)){
                        boolean indexFlag = true;
                        for (SysUser sysUser : sysUserList) {
                            if(sysUser.getUserName().equals(reviewApplyItem.getUserName())){
                                content.append(sysUser.getNickName());
                                indexFlag = false;
                                break;
                            }
                        }
                        if(indexFlag){
                            content.append("-");
                        }
                    }
                    content.append("</span></p>");
                }
                param.put("title", event.getBpmClientInputModel().getWf_procTitle());
                param.put("content", content.toString());

                workflowService.sendPendingEmail(event.getBpmClientInputModel(), event.getProcessInst(), param, SendType.FS.getCode());
            }
        }
        saveWorkFlowLog(reviewApply,itemList,status,event.getProcessInst().getProcInstTitle(), event);
    }

    public void saveWorkFlowLog(ReviewApply reviewApply,List<ReviewApplyItem> itemList,String status,String procTitle, ProcessResultEvent event) {
        WorkflowApplyLog log = new WorkflowApplyLog();
        log.setId(reviewApply.getId());
        log.setDocId(itemList.stream().map(ReviewApplyItem::getDocId).filter(StringUtils::isNotEmpty).collect(Collectors.joining(Constants.ID_SPLIT_KEY)));
        log.setVersionValue(itemList.stream().map(ReviewApplyItem::getVersionValue).collect(Collectors.joining(Constants.ID_SPLIT_KEY)));
        log.setDocName(procTitle);
        log.setProcStatus(status);
        log.setUserName(reviewApply.getUserName());
        log.setDeptId(reviewApply.getDeptId());
        iWorkflowApplyLogService.updateStatusByBusId(log,event);
    }
    private void startModifyApply(ReviewApplyItem applyItem,String dateType,String invokeType,String applyTitle) {
        ModifyApplyBo modifyApplyBo = BeanUtil.toBean(applyItem, ModifyApplyBo.class);
        modifyApplyBo.setId(null);
        modifyApplyBo.setDataType(dateType);
        modifyApplyBo.setRetainDeadline(applyItem.getSetupTime());
        modifyApplyBo.setInvokeType(invokeType);
        modifyApplyBo.setInvokeId(applyItem.getApplyId());
        modifyApplyBo.setChangeType(applyItem.getReviewAction());
        modifyApplyBo.setPreChangeCode(applyTitle);
        modifyApplyBo.setChangeId(applyItem.getApplyId());
        modifyApplyBo.setUserName(applyItem.getUserName());
        modifyApplyBo.setDeptId(applyItem.getDeptId());
        if(log.isDebugEnabled()){
            log.debug("startModifyApply====1==="+modifyApplyBo);
        }
        workflowApiController.saveOne(modifyApplyBo);
    }
    /**
     * 复审标准为：文件延长有效期时
     * @param applyItem 文件复审对象
     */
    private void updateDocVersion(ReviewApplyItem applyItem) {
        // 修改复审文件清单
        Version version = versionServices.getById(applyItem.getVersionId());
        // 获取顶级分类对象
        DocClass docClass = iDocClassService.getClassType(applyItem.getDocClass());
        // 当文件有效期和复审时间启用时才修改
        if(ApplyStatusEnum.EXTENSION.getCode().equals(applyItem.getReviewAction())) {
            // 延长有效期或者 保持现状
            if (StringUtils.equals(docClass.getOpenPrescription(), "'true'")&&docClass.getExpiration()!=null) {
                if (docClass.getExpiration().intValue() == NumberConstants.ZERO) {
                    //文件类型设置的有效期限0为永久
                    version.setEndDate(null);
                    version.setForever(String.valueOf(NumberConstants.ONE));
                }else {
                    //根据当前时间往后移文件类型设置的有效期限减一天为值 刷新失效日期
                    version.setEndDate(DateUtil.offset(DateUtils.addMonths(DateUtils.getNowDate(), docClass.getExpiration().intValue()), DateField.DAY_OF_MONTH,-1));
                    version.setForever(String.valueOf(NumberConstants.ZERO));
                }
            }
        }
        if(ApplyStatusEnum.EXTENSION.getCode().equals(applyItem.getReviewAction())
                || ApplyStatusEnum.KEEP.getCode().equals(applyItem.getReviewAction()) ) {
            // 延长有效期或者 保持现状
            if (StringUtils.equals(docClass.getOpenReview(), "'true'")&&docClass.getReviewCycle()!=null) {
                //根据当前时间往后移文件类型设置的周期减一天为值 刷新下次复审的时间
                version.setReviewTime(DateUtil.offset(DateUtils.addMonths(DateUtils.getNowDate(), docClass.getReviewCycle().intValue()),DateField.DAY_OF_MONTH,-1));
            } else {
                version.setReviewTime(null);
            }
        }
        versionServices.updateById(version);
    }

    /**
     * 注册事件
     */
    @PostConstruct
    private void registerEventBus() {
        ProcessEventBus.register(this);
    }

    /**
     * 取消注册事件
     */
    @PreDestroy
    private void unregisterEventBus() {
        ProcessEventBus.unregister(this);
    }

    /**
     * 保存前的数据校验
     *
     * @param entity 实体类数据
     */
    private void validEntityBeforeSave(ReviewApply entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return removeByIds(ids);
    }

    @Override
    public List<ReviewApplyVo> selectUndueList(List<String> statusList) {
        return baseMapper.selectUndueList(statusList);
    }

    /**
     * 查询待复审的文件，即时发送消息提醒（剩余多少天复审）
     *
     * @param days
     * @return
     */
    @Override
    public List<HashMap> selectUnReviewList(int days, String fileType) {
        return this.baseMapper.selectUnReviewList(days, fileType);
    }

    @Override
    public ReviewApplyVo selectDocStatus(String versionId){
        return baseMapper.selectDocStatus(versionId);
    }
}

package com.rzdata.process.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.rzdata.framework.constant.*;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.core.domain.entity.SysDept;
import com.rzdata.framework.core.domain.entity.SysDictData;
import com.rzdata.framework.core.domain.entity.SysUser;
import com.rzdata.framework.core.mybatisplus.core.ServicePlusImpl;
import com.rzdata.framework.core.page.PagePlus;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.utils.*;
import com.rzdata.process.domain.*;
import com.rzdata.process.domain.bo.DocLinkLogBo;
import com.rzdata.process.domain.bo.DocVersionLinkBo;
import com.rzdata.process.domain.bo.StandardBo;
import com.rzdata.process.domain.dto.ConfigureDTO;
import com.rzdata.process.domain.vo.*;
import com.rzdata.process.enums.LinkTypeEnum;
import com.rzdata.process.listener.ibo.LinkLogIBo;
import com.rzdata.process.listener.ibo.StandardIBo;
import com.rzdata.process.listener.ivo.ImportIVo;
import com.rzdata.process.mapper.StandardMapper;
import com.rzdata.process.mapper.WorkflowApplyLogMapper;
import com.rzdata.process.service.*;
import com.rzdata.setting.domain.CodeRuleLog;
import com.rzdata.setting.domain.DocClass;
import com.rzdata.setting.domain.vo.DocClassVo;
import com.rzdata.setting.service.ICodeRuleLogService;
import com.rzdata.setting.service.ICodeRuleService;
import com.rzdata.setting.service.IDocClassService;
import com.rzdata.system.domain.vo.ProjectInfoVo;
import com.rzdata.system.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.web.multipart.MultipartFile;

import java.rmi.ServerException;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 标准文件Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-12-30
 */
@Service
@Slf4j
public class StandardServiceImpl extends ServicePlusImpl<StandardMapper, Standard, StandardVo> implements IStandardService {

    @Autowired
    private IVersionService versionService;
    @Autowired
    private IDocLinkLogService docLinkLogService;
    /** 附件，service **/
    @Autowired
    private IBasicFileService basicFileService;
    /** 附件转换pdf，service **/
    @Autowired
    private IBasicFilePdfService basicFilePdfService;
    @Autowired
    private ISysDeptService sysDeptService;
    @Autowired
    private ISysUserService sysUserService;
    /** 文件分发明细表 **/
    @Autowired
    private IDocDistributeService docDistributeService;
    /** 文件分发-回收详情记录条目表 **/
    @Autowired
    private IModifyApplyTrainService modifyApplyTrainService;

    @Autowired
    private IDocLinkLogService iDocLinkLogService;

    @Autowired
    private IWorkflowApplyLogService workflowApplyLogService;

    @Autowired
    private IDocClassService iDocClassService;

    @Autowired
    private IGenerateIdService iGenerateIdService;

    @Autowired
    private IDocVersionLinkService docVersionLinkService;
    @Autowired
    private ICodeRuleService iCodeRuleService;

    @Autowired
    private WorkflowApplyLogMapper workflowApplyLogMapper;
    @Autowired
    private ICodeRuleLogService iCodeRuleLogService;
    @Autowired
    IDocDistributeService iDocDistributeService;
    @Autowired
    ISysDictDataService iSysDictDataService;

    @Autowired
    IProjectInfoService projectInfoService;

    @Autowired
    RedisUtils redisUtils;

    @Autowired
    private StandardMapper standardMapper;

    @Override
    public Map<String, StandardVo> queryByIdList(List<String> ids) {
        if (CollectionUtil.isEmpty(ids)) {
            return new HashMap<>();
        }
        return Optional.ofNullable(this.listVoByIds(ids)).orElse(Lists.newArrayList())
                .stream().collect(Collectors.toMap(StandardVo::getId, Function.identity()));
    }

    @Override
    public StandardVo queryById(String id) {
        StandardVo vo = getVoById(id);
        Version version = versionService.queryOneByStandardId(vo.getId());
        if (ObjectUtil.isNotNull(version)) {
            vo.setDeptId(version.getDeptId());
            vo.setDeptName(sysDeptService.getDeptName(version.getDeptId()));
            vo.setUserName(version.getUserName());
            vo.setNickName(sysUserService.getNickName(version.getUserName()));
            vo.setStartDate(version.getStartDate());
            vo.setDocId(version.getDocId());
            vo.setEndDate(version.getEndDate());
            vo.setReviewTime(version.getReviewTime());
            vo.setCurrentVersion(version.getVersionValue());
            vo.setForever(version.getForever());
            vo.setVersionId(version.getId());
            vo.setReleaseTime(version.getReleaseTime());
            vo.setFileId(version.getFileId());
            vo.setDocName(version.getDocName());
            //当前版本的附件
            List<DocLinkLogVo> appendixLinkLogVoList = docLinkLogService.queryDocLinkVo(version.getId(),LinkTypeEnum.APPENDIX.name());
            vo.setPreAppendixes(appendixLinkLogVoList);
            BasicFileVo basicFileVo = basicFileService.queryById(version.getFileId());
            if (basicFileVo != null) {
                vo.setFileName(basicFileVo.getFileName());
            }
        }
        return vo;
    }

    @Override
    public VersionVo queryByDocIdAndVersion(String docId, String versionValue) {
        VersionVo version = versionService.queryByDocIdAndVersion(docId,versionValue);
        if (ObjectUtil.isNotEmpty(version)) {
            StandardVo vo = getVoById(version.getStandardId());
            version.setStandardStatus(vo.getStatus());
            version.setDocClass(vo.getDocClass());
            return version;
        }
        return null;
    }

    @Override
    public StandardVo queryDetail(String versionId,String flag) {
        VersionVo vVo= versionService.getVoById(versionId);
        String docId = vVo.getDocId();
        StandardVo standardVo = getVoById(vVo.getStandardId());
        standardVo.setStatus(vVo.getStatus());
        standardVo.setDocId(vVo.getDocId());
        standardVo.setDocName(vVo.getDocName());
        standardVo.setApplyTime(vVo.getApplyTime());
        standardVo.setVersionId(vVo.getId());
        standardVo.setChangeReason(vVo.getChangeReason());
        standardVo.setDistributeType(vVo.getDistributeType());
        standardVo.setContent(vVo.getContent());
        standardVo.setProductLine(vVo.getProductLine());
        standardVo.setCompliance(vVo.getCompliance());

        standardVo.setStartDate(vVo.getStartDate());
        standardVo.setReleaseTime(vVo.getReleaseTime());
        standardVo.setEndDate(vVo.getEndDate());
        standardVo.setWhetherCustomer(vVo.getWhetherCustomer());
        /** 扩展字段 赋值**/
        standardVo.setExt1(vVo.getExt1());
        standardVo.setExt2(vVo.getExt2());
        standardVo.setExt3(vVo.getExt3());
        standardVo.setExt4(vVo.getExt4());
        standardVo.setExt5(vVo.getExt5());
        standardVo.setExt6(vVo.getExt6());
        standardVo.setExt7(vVo.getExt7());
        standardVo.setExt8(vVo.getExt8());
        standardVo.setExt9(vVo.getExt9());
        standardVo.setExt10(vVo.getExt10());
        standardVo.setExt11(vVo.getExt11());
        standardVo.setExt12(vVo.getExt12());
        standardVo.setExt13(vVo.getExt13());
        standardVo.setExt14(vVo.getExt14());
        standardVo.setExt15(vVo.getExt15());
        standardVo.setExt16(vVo.getExt16());
        standardVo.setExt17(vVo.getExt17());
        standardVo.setExt18(vVo.getExt18());
        standardVo.setExt19(vVo.getExt19());
        standardVo.setExt20(vVo.getExt20());
        standardVo.setInternalDocId(vVo.getInternalDocId());
        standardVo.setRemark(vVo.getRemark());
        if (StringUtils.isNotEmpty(vVo.getParentDocId())) {
            Version upVersion = versionService.getValidVersionByDocId(vVo.getParentDocId());
            if (upVersion!=null) {
                standardVo.setUpVersionId(upVersion.getId());
                standardVo.setUpDocName(upVersion.getDocName());
                standardVo.setParentDocId(upVersion.getDocId());
            }else {
                //主文件已经失效
                standardVo.setUpVersionId(vVo.getUpVersionId());
                standardVo.setUpDocName(versionService.getDocNameByVersionId(vVo.getUpVersionId()));
                standardVo.setParentDocId(vVo.getParentDocId());
            }
        }

        standardVo.setClassType(vVo.getClassType());
//        standardVo.setSecDeptId(sysDeptService.getSecDeptId(vVo.getDeptId()));
        //查询流程
        if (ObjectUtil.isNotEmpty(vVo.getApplyId())) {
            WorkflowApplyLog workflowApplyLog = workflowApplyLogService.getById(vVo.getApplyId());
            if (ObjectUtil.isNotEmpty(workflowApplyLog))  {
                standardVo.setProcInstId(workflowApplyLog.getProcInstId());
            }
        }

        standardVo.setVersionValue(vVo.getVersionValue());
        //查询当前文件的流程状态
        String changeType = this.workflowApplyLogService.queryProcessStatus(docId);
        standardVo.setChangeType(changeType);

        //文件历史
        if (StringUtils.isNotEmpty(docId)) {
            List<VersionVo> versionVoList = versionService.selectVersionListByDocId(docId);
            standardVo.setVersions(versionVoList);
        }

        //截止日期减少一天
        if(!vVo.getForever().equals(Constants.ONE)) {
            standardVo.setRecheckDate(vVo.getEndDate());
            //文件有效期
            standardVo.setExpirationDate(DateUtil.formatDate(vVo.getEndDate()));

        }
        //复审时间
        if(ObjectUtil.isNotEmpty(vVo.getReviewTime())) {
            standardVo.setReviewTime(vVo.getReviewTime());
        }

        //当前文件版本
        standardVo.setCurrentVersion(vVo.getVersionValue());

        /**
         * 查询文档关联的编制文件，附件，关联文档和关联记录
         */
        //DocLinkLogBo linkLogBo = new DocLinkLogBo();
        //linkLogBo.setVersionId(versionId);
        //查出当前文件的主文件 主文件只允许上传1个 所以List中如果有值 也会只有一条数据 因为方法复用 就不单独写一个SQL方法了
        List<DocLinkLogVo> docLinkLogVoList = docLinkLogService.queryDocLinkVo(versionId,LinkTypeEnum.DOC.name());
        if (ObjectUtil.isNotEmpty(docLinkLogVoList)) {
            standardVo.setPreStandardDoc(docLinkLogVoList.get(0));
        }
        //当前版本的附件
        List<DocLinkLogVo> appendixLinkLogVoList = docLinkLogService.queryDocLinkVo(versionId,LinkTypeEnum.APPENDIX.name());
        standardVo.setPreAppendixes(appendixLinkLogVoList);

        //文件备注附件
//        List<DocLinkLogVo> remakeAppendixList = docLinkLogService.queryDocLinkVo(docId,versionId,LinkTypeEnum.APPENDIX_REMARK);
//        standardVo.setRemakeAppendixes(remakeAppendixList);

        /**
         * 设置编制人昵称，部门名称
         */
        standardVo.setDeptId(vVo.getDeptId());
        standardVo.setDeptName(sysDeptService.getDeptName(vVo.getDeptId()));
        standardVo.setUserName(vVo.getUserName());
        standardVo.setNickName(sysUserService.getNickName(vVo.getUserName()));

        if("1".equals(flag)) {
            //修订时删除部分信息
            standardVo.setRemarkDoc(null);
            standardVo.setRemark("");
            standardVo.setChangeFactor("");
            standardVo.setChangeReason("");
            standardVo.setContent("");
        }
        return standardVo;
    }

    @Override
    public TableDataInfo<StandardVo> queryPageList(StandardBo bo) {
        PagePlus<Standard, StandardVo> result = pageVo(PageUtils.buildPagePlus(), buildQueryWrapper(bo));
        return PageUtils.buildDataInfo(result);
    }

    @Override
    public TableDataInfo<StandardVo> selectPageList(StandardBo bo) {
        Page<StandardVo> page = new Page<>(bo.getPageNum(), bo.getPageSize());
        Page<StandardVo> iPage = baseMapper.selectPageList(page, bo);
        return PageUtils.buildDataInfo(iPage);
    }

    /**
     * 查询文件台账列表
     *
     * @param bo
     */
    @Override
    public TableDataInfo<StandardVo> selectAccountList(StandardBo bo) {
        Page<StandardVo> page = new Page<>(bo.getPageNum(), bo.getPageSize());
        Page<StandardVo> iPage = baseMapper.selectAccountList(page, bo);
        return PageUtils.buildDataInfo(iPage);
    }

    @Override
    public List<DocLinkLogVo> exportLinkLog(StandardBo bo,List<String> ids) {
        return baseMapper.exportLinkLog(bo,ids);
    }


    @Override
    public List<StandardVo> selectList(StandardBo bo) {
        Page<StandardVo> page = new Page<>(PageUtils.DEFAULT_PAGE_NUM,PageUtils.DEFAULT_PAGE_SIZE);
        return baseMapper.selectPageList(page,bo).getRecords();
    }

    @Override
    public List<StandardVo> queryList(StandardBo bo) {
        return listVo(buildQueryWrapper(bo));
    }

    private LambdaQueryWrapper<Standard> buildQueryWrapper(StandardBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<Standard> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getDocClass()), Standard::getDocClass, bo.getDocClass());
        lqw.like(StringUtils.isNotBlank(bo.getDocName()), Standard::getDocName, bo.getDocName());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), Standard::getStatus, bo.getStatus());
        lqw.eq(bo.getExpiration() != null, Standard::getExpiration, bo.getExpiration());
        lqw.eq(StringUtils.isNotBlank(bo.getCurrentVersion()), Standard::getCurrentVersion, bo.getCurrentVersion());
        lqw.eq(StringUtils.isNotBlank(bo.getFileId()), Standard::getFileId, bo.getFileId());
        lqw.eq(StringUtils.isNotBlank(bo.getEncryptFileId()), Standard::getEncryptFileId, bo.getEncryptFileId());
        lqw.eq(StringUtils.isNotBlank(bo.getContent()), Standard::getContent, bo.getContent());
        if (ObjectUtil.isNotEmpty(bo.getSearchValue())) {
            lqw.like(Standard:: getDocName, bo.getSearchValue()).or().like(Standard:: getId, bo.getSearchValue())
                    .or().like(Standard::getCurrentVersion, bo.getSearchValue());
        }
        return lqw;
    }

    @Override
    public Boolean insertByBo(StandardBo bo) {
        Standard add = BeanUtil.toBean(bo, Standard.class);
        validEntityBeforeSave(add);
        boolean flag = save(add);
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean join(StandardBo bo) {
        // 加入关联记录及关联文件 根据linkType区分
        //主文件标准文件
        StandardVo vo = getVoById(bo.getId());
        //主文件版本
        Version docVersion = versionService.queryOneByStandardId(vo.getId());
        //关联的版本
        Version version = versionService.getById(bo.getVersionId());
        docLinkLogService.handApplyLinkAdd(version,bo.getDocClass(),bo.getLinkType(),docVersion.getId(),version.getFileId());
        if(LinkTypeEnum.RECORD.name().equals(bo.getLinkType())) {
            // 更新被关联记录文件版本的上级文件版本ID和对应的文件台账编号
            versionService.update(new LambdaUpdateWrapper<Version>()
                    .eq(Version::getId,bo.getVersionId())
                    .set(Version::getUpVersionId,version.getId())
                    .set(Version::getParentDocId,version.getDocId()));
        } else if(LinkTypeEnum.NOTE.name().equals(bo.getLinkType())){
            docLinkLogService.handApplyLinkAdd(docVersion,vo.getDocClass(),LinkTypeEnum.NOTE_DOC.name(),version.getId(),docVersion.getFileId());
        }
        return true;
    }

    @Override
    public Boolean updateByBo(StandardBo bo) {
        Standard update = BeanUtil.toBean(bo, Standard.class);
        validEntityBeforeSave(update);
        return updateById(update);
    }

    @Override
    public Boolean updateByImport(StandardIBo ibo, int num, ImportIVo ivo, ConfigureDTO configure) {
        //校验必填项
        boolean flag = validateImport(ibo, num, ivo, configure);
        if(!flag){
            return false;
        }
        Standard standardOld= getById(ibo.getId());
        if(standardOld==null){
            ivo.initFileMsgAdd(num,I18nUtils.getTitle(CommonI18nConstant.SERVICE_STANDARD_FILE_ALREADY_DELETE));
            return false;
        }
        String oldDocClass = standardOld.getDocClass();
        Version version = versionService.queryOneByStandardId(ibo.getId());
        if (ObjectUtil.isNull(version)) {
            version = new Version();
        }
        Standard standard = new Standard();
        standard.setId(ibo.getId());
        //Optional<DocClass> docOptional = configure.getDcList().stream().filter(i -> i.getClassName().equals(ibo.getDocClass())).findFirst();
        DocClass docClass = null;
        for(DocClass itme:configure.getDcList()){
            log.error("文件分类:"+ibo.getDocClass()+"====="+itme.getClassName()+"====");
            if(itme.getClassName().equals(ibo.getDocClass())){
                docClass = itme;
            }
        }
        if (docClass== null) {
            ivo.initFileMsgAdd(num,"文件类型不存在:"+ibo.getDocClass());
            return false;
        }
        //DocClass docClass = docOptional.get();
        // 所属分类
        standard.setDocClass(docClass.getId());
        // 分类所属类型 DOC文件台账、RECORD记录台账、FOREIGN外来文件
        version.setClassType(docClass.getClassType());



        standard.setCurrentVersion(ibo.getCurrentVersion());
        version.setVersionValue(ibo.getCurrentVersion());
        standard.setDocName(ibo.getDocName());
        version.setDocName(ibo.getDocName());
        standard.setStatus(Constants.ZERO);

        if (StringUtils.isNotBlank(ibo.getChangeReason())){
            standard.setChangeReason(ibo.getChangeReason());
            version.setReason(ibo.getChangeReason());
        }else {
            standard.setChangeReason(Constants.VALUE_INIT);
            version.setReason(Constants.VALUE_INIT);
        }
        if (StringUtils.isNotBlank(ibo.getContent())){
            standard.setContent(ibo.getContent());
        }else {
            standard.setContent(Constants.VALUE_INIT);
        }
        //存部门 不验证编制人员
        if (StringUtils.isNotBlank(ibo.getDeptId())) {
            List<SysDept> deptList = configure.findDept(ibo.getDeptId());
            if (deptList.size()==1) {
                version.setDeptId(deptList.get(0).getDeptId());
            } else if(deptList.size()<1) {
                ivo.initFileMsgAdd(num,"编制部门不存在");
                return false;
            } else {
                ivo.initFileMsgAdd(num,"编制部门匹配到多条");
                return false;
            }
        } else {
            ivo.initFileMsgAdd(num,"编制部门不能为空");
            return false;
        }
        //根据编制人员存部门
        if (StringUtils.isNotBlank(ibo.getUserName())) {
            Optional<SysUser> userOptional = configure.getUserList().stream().filter(i -> ibo.getUserName().equals(i.getNickName())).findFirst();
            if (userOptional.isPresent()) {
                version.setUserName(userOptional.get().getUserName());
            } else {
                ivo.initFileMsgAdd(num,I18nUtils.getTitle(CommonI18nConstant.SERVICE_STANDARD_ORG_USER_NOT_EXIST));
                return false;
            }
        }
        Date applyTime = Convert.toDate(ibo.getApplyTime());
        if (ObjectUtil.isNotEmpty(applyTime)) {
            version.setApplyTime(applyTime);
        }else {
            version.setApplyTime(DateUtil.date());
        }
        /*CodeRuleLog codeRuleLog= iCodeRuleLogService.docIdExist(ibo.getDocId());
        if(ObjectUtil.isNotEmpty(codeRuleLog) && !StringUtils.equals(ibo.getId(),codeRuleLog.getBusinessId())){
            ivo.initFileMsgAdd(num,I18nUtils.getTitle(CommonI18nConstant.FILE_DOC_LINK_NUM_EXIST));
            return false;
        }*/
        //扩展字段数据处理
//        DocExt ext = new DocExt();
//        BeanUtil.copyProperties(ibo,ext,true);
//        Map<String,Object> extMap = BeanUtil.beanToMap(ext);
//        for (String key : extMap.keySet()) {
//            //扩展字段有值就插入
//            if (ObjectUtil.isNotEmpty(extMap.get(key))) {
//                //扩展字段对应的字段属性
//                Optional<SysDictData> formOptional = configure.getFormList().stream().filter(item-> key.equals(item.getDictValue())).findFirst();
//                if (formOptional.isPresent()) {
//                    String [] keys = formOptional.get().getRemark().split("@");
//                    if (GenConstants.HTML_SELECT.equals(keys[0])) {
//                        //下拉框的为字典数据 转换成字典值
//                        List<SysDictData> dataList= configure.getDictList().get(keys[1]);
//                        Optional<SysDictData> extOptional = dataList.stream().filter(item->item.getDictLabel().equals(extMap.get(key))).findFirst();
//                        if (extOptional.isPresent()) {
//                            extMap.put(key,extOptional.get().getDictValue());
//                        }else {
//                            ivo.initFileMsgAdd(num,formOptional.get().getDictLabel()+"不存在");
//                            return false;
//                        }
//                    } else if (GenConstants.TPL_TREE.equals(keys[0])) {
//                        //树表暂时只支持部门 转换成部门id
//                        List<SysDept> deptList = configure.findDept((String) extMap.get(key));
//                        if (deptList.size()==1) {
//                            extMap.put(key,deptList.get(0).getDeptId());
//                        } else if(deptList.size()<1) {
//                            ivo.initFileMsgAdd(num,formOptional.get().getDictLabel()+"不存在");
//                            return false;
//                        } else {
//                            ivo.initFileMsgAdd(num,formOptional.get().getDictLabel()+"匹配到多条");
//                            return false;
//                        }
//                    }
//                }
//            }
//        }
//        //扩展字段数据插入
//        BeanUtil.fillBeanWithMap(extMap,version,false);

        version.setDocId(ibo.getDocId());
        Date startDate = Convert.toDate(ibo.getStartDate());
        version.setStartDate(startDate);
        Date releaseTime = Convert.toDate(ibo.getReleaseTime());
        version.setReleaseTime(releaseTime);
        Date endDate = Convert.toDate(ibo.getEndDate());
        if (ObjectUtil.isNotEmpty(endDate)) {
            version.setEndDate(endDate);
        }
        standard.setInitFile(Constants.ONE);

        version.setContent(ibo.getContent());
        version.setChangeReason(ibo.getChangeReason());
        version.setStandardId(ibo.getId());
        version.setStatus(Constants.ONE);
        version.setForever(Constants.ONE);
        version.setDataType(standardOld.getDataType());
        version.setFileId(standardOld.getFileId());
        // 获取文件所属分类的顶级分类对象
        if("'true'".equals(docClass.getOpenReview())&&docClass.getReviewCycle()!=null) {
            // 当开启复审时 设当前发布时间往后移文件类型设置的周期减一天为下次复审的时间
            version.setReviewTime(DateUtil.offset(DateUtils.addMonths(version.getReleaseTime(),docClass.getReviewCycle().intValue()), DateField.DAY_OF_MONTH,-1));
        }
        /**
         * 保存版本表：料号、 保管部门（分类是记录台账的才有）、 保存期限（分类是记录台账的才有）
         *
         * 外来文件字段导入；文件生效日期、修订日期、合规性
         */

        if (StringUtils.isNotBlank(ibo.getCompliance())) {
            version.setCompliance(ibo.getCompliance());
        }

        // 保存文件版本数据
        versionService.saveOrUpdate(version);
        boolean bool = saveOrUpdate(standard);
        if (bool) {
            Map<String,Object> map = new HashMap<String,Object>();
            map.putAll(BeanUtil.beanToMap(standard));
            map.putAll(BeanUtil.beanToMap(version));
            iGenerateIdService.saveOldDocId(standard.getId(),standard.getDocClass(),version.getDocId(),oldDocClass,map);
        }
        //默认分发部门
//        distribute.setVersionId(version.getId());
//        distribute.setDocId(version.getDocId());
//        distribute.setDocClass(standard.getDocClass());
//        distribute.setDocName(version.getDocName());
//        distribute.setReceiveUserDeptId(version.getDeptId());
//        distribute.setReceiveUserDept(ibo.getDeptId());
//        distribute.setType(Constants.TYPE_DEPT);
//        iDocDistributeService.saveOrUpdate(distribute);
        //更新被关联的
        iDocLinkLogService.update(new LambdaUpdateWrapper<DocLinkLog>()
                .set(DocLinkLog::getLinkCode,version.getDocId())
                .set(DocLinkLog::getVersionValue,version.getVersionValue())
                .set(DocLinkLog::getDocClass,docClass.getId())
                .set(DocLinkLog::getStartDate,version.getStartDate())
                .set(DocLinkLog::getReleaseTime,version.getReleaseTime())
                .eq(DocLinkLog::getVersionId,version.getId()));
        return bool;
    }


    /**
     * 校验文件名是否重复
     * @param bo
     * @return
     */
    private Boolean checkDocName(StandardIBo bo) {
        Standard standard = this.getById(bo.getId());
        if(ObjectUtil.isNotNull(standard) && !StringUtils.equals(bo.getDocName(),standard.getDocName())) {
            QueryWrapper<Standard> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(Standard::getDocName, StringUtils.trim(bo.getDocName()));
            long count = this.count(queryWrapper);
            if (count > 0) {
                return Boolean.TRUE;
            }
        }
        return Boolean.FALSE;
    }

    /**
     * 校验是否产生了新数据
     * @param ibo
     * @return
     */
    private Boolean checkProcessMsg(StandardIBo ibo) {
        StandardVo standardVo = queryInfoById(ibo.getId());
        if(ObjectUtil.isNotNull(standardVo)){
            QueryWrapper<WorkflowApplyLog> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(WorkflowApplyLog::getDocId, standardVo.getDocId());
            queryWrapper.lambda().eq(WorkflowApplyLog::getVersionId, standardVo.getVersionId());
            Long count = workflowApplyLogMapper.selectCount(queryWrapper);
            if (count > 0) {
                return Boolean.TRUE;
            }
        }
        return Boolean.FALSE;
    }

    @Override
    public Boolean updateLinkLogByImport(LinkLogIBo ibo, String linkType, int num, ImportIVo ivo) {
        if (StringUtils.isBlank(ibo.getDocId())) {
            ivo.linkFileMsgMsgAdd(linkType,num,"主-文件编号不能为空");
            return true;
        }
        if (StringUtils.isBlank(ibo.getLinkCode())) {
            ivo.linkFileMsgMsgAdd(linkType,num,"关联-文件编号不能为空");
            return false;
        }
        if (StringUtils.isBlank(ibo.getCurrentVersion())) {
            ivo.linkFileMsgMsgAdd(linkType,num,"主-文件版本不能为空");
            return false;
        }
        if (StringUtils.isBlank(ibo.getVersionValue())) {
            ivo.linkFileMsgMsgAdd(linkType,num,"关联-文件版本不能为空");
            return false;
        }
        // 查询主文件
        VersionVo doc = queryByDocIdAndVersion(ibo.getDocId(),ibo.getCurrentVersion());
        // 查询关联文件
        VersionVo link = queryByDocIdAndVersion(ibo.getLinkCode(),ibo.getVersionValue());

        if (ObjectUtil.isNull(doc)) {
            ivo.linkFileMsgMsgAdd(linkType,num,"主-文件不存在");
            return false;
        } else if (!doc.getStandardStatus().equals(Constants.ONE)) {
            ivo.linkFileMsgMsgAdd(linkType,num,"主-文件不是有效文件");
            return false;
        }
        if (ObjectUtil.isNull(link)) {
            ivo.linkFileMsgMsgAdd(linkType,num,"关联-文件不存在");
            return false;
        } else if (!link.getStandardStatus().equals(Constants.ONE)) {
            ivo.linkFileMsgMsgAdd(linkType,num,"关联-文件不是有效文件");
            return false;
        }
        //记录文件修改主文件信息
        if (LinkTypeEnum.RECORD.name().equals(linkType)){
            versionService.update(new LambdaUpdateWrapper<Version>()
                    .eq(Version::getId,link.getId())
                    .set(Version::getUpVersionId,doc.getId())
                    .set(Version::getParentDocId,doc.getDocId()));
        } else if(LinkTypeEnum.NOTE.name().equals(linkType)){
            docLinkLogService.deleteLinkLog(LinkTypeEnum.NOTE_DOC.name(),doc.getId(),link.getId());
            docLinkLogService.handApplyLinkAdd(BeanUtil.toBean(doc,Version.class),doc.getDocClass(),LinkTypeEnum.NOTE_DOC.name(),link.getId(),doc.getFileId());
        }
        docLinkLogService.deleteLinkLog(linkType,link.getId(),doc.getId());
        docLinkLogService.handApplyLinkAdd(BeanUtil.toBean(link,Version.class),link.getDocClass(),linkType,doc.getId(),link.getFileId());
        return true;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateBoAndVersion(StandardBo bo) {
        StandardVo standardVo = this.getVoById(bo.getId());
        Version version = versionService.queryOneByStandardId(bo.getId());
        //判断文件是否有变更，有变更删除分发和附件数据
        if(!standardVo.getFileId().equals(bo.getFileId())){
            handleStandardAndFile(standardVo, version);
        }
        String docClass = getDocClass(bo.getId());
        if (ObjectUtil.isNull(version)) {
            version = new Version();
        }
        version.setApplyTime(bo.getApplyTime());
        version.setDeptId(StrUtil.isNotBlank(bo.getDeptId()) ? bo.getDeptId() : null);
        version.setUserName(StrUtil.isNotBlank(bo.getUserName()) ? bo.getUserName() : null);
        version.setApplyTime(bo.getApplyTime());
        version.setStandardId(bo.getId());
        version.setDocId(bo.getDocId());
        version.setDocName(bo.getDocName());
        version.setVersionValue(bo.getCurrentVersion());
        version.setStartDate(bo.getStartDate());
        version.setEndDate(bo.getEndDate());
        version.setStatus(Constants.ONE);
        version.setFileId(bo.getFileId());
        version.setReason(bo.getChangeReason());
        version.setReviewTime(bo.getReviewTime());
        version.setForever(bo.getForever());
        version.setReleaseTime(bo.getReleaseTime());
        versionService.saveOrUpdate(version);
        bo.setStatus(Constants.ZERO);
        boolean bool = updateByBo(bo);
        if (bool) {
            Map<String,Object> map = new HashMap<String,Object>();
            map.putAll(BeanUtil.beanToMap(bo));
            map.putAll(BeanUtil.beanToMap(version));
            iGenerateIdService.saveOldDocId(bo.getId(),bo.getDocClass(),bo.getDocId(),docClass,map);
        }
        //先删除后新增，文件关联记录，类型为正文
        Boolean docResult = delAndSaveDocLinkLog(bo, version);

        //关联附件
        //删除已关联的附件
        List<DocLinkLogVo> appendixLinkLogVoList = docLinkLogService.queryDocLinkVo(version.getId(),LinkTypeEnum.APPENDIX.name());
        List<String> deleteIdList= appendixLinkLogVoList.stream().map(DocLinkLogVo::getId).collect(Collectors.toList());
        if(!deleteIdList.isEmpty()){
            iDocLinkLogService.removeByIds(deleteIdList);
            docVersionLinkService.remove(new LambdaUpdateWrapper<DocVersionLink>().in(DocVersionLink::getLinkId,deleteIdList));
        }
        List<DocLinkLogBo> list= bo.getAppendixes();
        List<DocLinkLog> saveLinkList=new ArrayList<>();
        List<DocVersionLink> saveVersionLinkList=new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            DocLinkLogBo docLinkLogbo=list.get(i);
            DocLinkLog docLinkLog=new DocLinkLog();
            docLinkLog.setId(IdWorker.getIdStr());
            docLinkLog.setLinkCode(bo.getDocId());
            docLinkLog.setLinkType(LinkTypeEnum.APPENDIX.name());
            docLinkLog.setFileName(bo.getDocName());
            docLinkLog.setDocClass(bo.getDocClass());
            docLinkLog.setVersionValue(version.getVersionValue());
            docLinkLog.setCreateTime(DateUtil.date());
            docLinkLog.setStatus(Constants.ONE);
            docLinkLog.setFileId(docLinkLogbo.getFileId()) ;
            saveLinkList.add(docLinkLog);
            // 新增 文件关联记录-关联表
            DocVersionLink dvlBo = new DocVersionLink();
            dvlBo.setLinkId(docLinkLog.getId());
            dvlBo.setVersionId(version.getId());
            saveVersionLinkList.add(dvlBo);
        }
        Boolean insertdll =saveLinkList.size()==0?true : iDocLinkLogService.saveAll(saveLinkList);
        Boolean insertDvl =saveVersionLinkList.size()==0?true : docVersionLinkService.saveAll(saveVersionLinkList);
        return bool && insertdll && insertDvl && docResult;
    }


    /**
     * 删除分发和附件数据
     * 1、查询是否有分发的数据，有则删除
     * 2、删除basic_file、basic_file_pdf表数据
     * @param standardVo
     */
    public void handleStandardAndFile(StandardVo standardVo, Version version) {
        if(ObjectUtil.isNotEmpty(version) && StringUtils.isNotEmpty(version.getId())){
            docDistributeService.remove(new LambdaQueryWrapper<DocDistribute>().eq(DocDistribute::getVersionId, version.getId()));
        }

        if(ObjectUtil.isEmpty(standardVo)){
            log.error("StandardServiceImpl-->handleStandardAndFile----standardVo is null###");
            return;
        }
        if(StrUtil.isBlank(standardVo.getFileId())){
            log.error("StandardServiceImpl-->handleStandardAndFile----standardVo.getFileId() is null###");
            return;
        }
        BasicFile basicFile = basicFileService.getById(standardVo.getFileId());
        if(ObjectUtil.isNotEmpty(basicFile)){
            basicFileService.removeById(basicFile.getId());
        }
        List<BasicFilePdf> basicFilePdfList = basicFilePdfService.list(new LambdaQueryWrapper<BasicFilePdf>().eq(BasicFilePdf::getFileId, standardVo.getFileId()));
        if(CollUtil.isNotEmpty(basicFilePdfList)){
            List<String> basicFilePdfIdList = basicFilePdfList.stream()
                    .map(BasicFilePdf::getId)
                    .collect(Collectors.toList());
            basicFilePdfService.removeByIds(basicFilePdfIdList);
        }
    }

    /**
     * 先删除后新增，文件关联记录对象正文
     * @param bo
     * @param version
     * @return
     */
    private Boolean delAndSaveDocLinkLog(StandardBo bo,Version version) {
        //关联附件
        //删除已关联的附件
        List<DocLinkLogVo> appendixLinkLogVoList = docLinkLogService.queryDocLinkVo(version.getId(),LinkTypeEnum.DOC.name());
        List<String> deleteIdList= appendixLinkLogVoList.stream().map(DocLinkLogVo::getId).collect(Collectors.toList());
        if(CollUtil.isNotEmpty(deleteIdList)){
            iDocLinkLogService.removeByIds(deleteIdList);
            docVersionLinkService.remove(new LambdaUpdateWrapper<DocVersionLink>().in(DocVersionLink::getLinkId,deleteIdList));
        }

        //关联主文件
        DocLinkLogBo docLinkLog = new DocLinkLogBo();
        docLinkLog.setLinkCode(version.getDocId());
        docLinkLog.setLinkType(LinkTypeEnum.DOC.name());
        docLinkLog.setFileName(bo.getDocName());
        docLinkLog.setFileId(version.getFileId());
        docLinkLog.setDocClass(bo.getDocClass());
        docLinkLog.setVersionValue(version.getVersionValue());
        docLinkLog.setCreateTime(DateUtil.date());
        docLinkLog.setStatus(Constants.ONE);
        Boolean docLinkLogResult = iDocLinkLogService.insertByBo(docLinkLog);
        // 新增 文件关联记录-关联表
        DocVersionLinkBo dvlBo = new DocVersionLinkBo();
        dvlBo.setLinkId(docLinkLog.getId());
        dvlBo.setVersionId(version.getId());
        Boolean dvlResult = docVersionLinkService.insertByBo(dvlBo);
        return docLinkLogResult && dvlResult;
    }

    @Override
//    @Transactional(rollbackFor = Exception.class
    @Transactional(propagation = Propagation.REQUIRED,rollbackFor = Exception.class)
    public void deleteList(List<String> idList) throws ServerException {
        for (int i = 0; i < idList.size(); i++) {
            String id=idList.get(i);
            StandardVo standard = this.queryById(id);
            if(StringUtils.isNotBlank(standard.getVersionId()) && StringUtils.isNotBlank(standard.getDocId())) {
                QueryWrapper<WorkflowApplyLog> queryWrapper = new QueryWrapper<>();
                queryWrapper.lambda().eq(WorkflowApplyLog::getDocId, standard.getDocId());
                queryWrapper.lambda().eq(WorkflowApplyLog::getVersionId, standard.getVersionId());
                Long count = workflowApplyLogMapper.selectCount(queryWrapper);
                if (count > 0) {
                    TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                    throw new ServerException(standard.getDocName()+I18nUtils.getTitle(CommonI18nConstant.SERVICE_STANDARD_NEW_DATA_NOT_DELETE));
                }
            }
            StandardBo bo=new StandardBo();
            bo.setId(id);
            bo.setVersionId(standard.getVersionId());
            boolean delete = this.deleteByBo(bo);
            if(Boolean.FALSE.equals(delete)){
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                throw new ServerException(standard.getDocName()+ I18nUtils.getTitle(CommonI18nConstant.SERVICE_STANDARD_DELETE_FAIL));
            }
        }
    }
    /**
     * 保存前的数据校验
     *
     * @param entity 实体类数据
     */
    private void validEntityBeforeSave(Standard entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return removeByIds(ids);
    }



    @Override
    public List<StandardVo> selectValidFile() {
        return baseMapper.selectValidFile();
    }

    @Override
    public StandardVo getStandardVoByVersionId(String versionId) {
        return this.baseMapper.getStandardVoByVersionId(versionId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteByBo(StandardBo bo) {
        //删除关联的
        List<DocVersionLink> vlList=docVersionLinkService.list(new LambdaQueryWrapper<DocVersionLink>().eq(DocVersionLink::getVersionId,bo.getVersionId()));
        if (vlList.size()>0) {
            List<String> vlIdList = vlList.stream().map(DocVersionLink::getId).collect(Collectors.toList());
            List<String> linkIdList = vlList.stream().map(DocVersionLink::getLinkId).collect(Collectors.toList());
            docVersionLinkService.removeByIds(vlIdList);
            docLinkLogService.removeByIds(linkIdList);
        }
        //删除被关联的
        List<DocLinkLog> dlList = docLinkLogService.list(new LambdaQueryWrapper<DocLinkLog>().eq(DocLinkLog::getVersionId,bo.getVersionId()));
        if (dlList.size()>0) {
            List<String> dlIdList = dlList.stream().map(DocLinkLog::getId).collect(Collectors.toList());
            docVersionLinkService.remove(new LambdaQueryWrapper<DocVersionLink>().in(DocVersionLink::getLinkId,dlIdList));
            docLinkLogService.removeByIds(dlIdList);
        }
        versionService.removeById(bo.getVersionId());
        iCodeRuleLogService.remove(new LambdaQueryWrapper<CodeRuleLog>().eq(CodeRuleLog::getRuleValue,bo.getDocId()));
        docDistributeService.remove(new LambdaQueryWrapper<DocDistribute>().eq(DocDistribute::getVersionId,bo.getVersionId()));
        return this.removeById(bo.getId());
    }

    @Override
    public StandardVo queryInfoById(String id) {
        return this.baseMapper.queryInfoById(id);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean addRecord(StandardBo bo)throws Exception {
        Standard standard= getById(bo.getId());
        Version version= versionService.getById(bo.getVersionId());
        if(version==null){
            throw new ServerException(I18nUtils.getTitle(CommonI18nConstant.SERVICE_STANDARD_MAINTAIN_FILE_FIRST));
        }
        DocLinkLogBo dllBo=new DocLinkLogBo();
        dllBo.setLinkType(LinkTypeEnum.RECORD.name());
        dllBo.setFileName(bo.getDocName());
        dllBo.setVersionId(version.getId());
        if (iDocLinkLogService.getLinkCount(dllBo)>0){
            throw new ServerException(bo.getDocName()+I18nUtils.getTitle(CommonI18nConstant.SERVICE_STANDARD_FILE_NAME_REPEAT));
        }
        DocLinkLog docLinkLog=new DocLinkLog();
        docLinkLog.setId(IdWorker.getIdStr());
        String docClass="stdd".equals(standard.getDataType())?"STDD-R":"PROJECT-R";
        String configValue = iCodeRuleService.getDocRuleId(docClass);
        Map<String,Object> bizMap =  new HashMap<>();
        bizMap.put("parentDocId",version.getDocId());
        CodeRuleLog codeRuleLog = iCodeRuleService.generatorEncodingRule(configValue,bo.getFileId(),bizMap);
        docLinkLog.setLinkCode(codeRuleLog.getRuleValue());
        docLinkLog.setLinkType(LinkTypeEnum.RECORD.name());
        docLinkLog.setFileName(bo.getDocName());
        docLinkLog.setDocClass(docClass);
        docLinkLog.setVersionValue("A0");
        docLinkLog.setCreateTime(DateUtil.date());
        docLinkLog.setStatus(Constants.ONE);
        docLinkLog.setFileId(bo.getFileId()) ;
        // 新增 文件关联记录-关联表
        DocVersionLink dvlBo = new DocVersionLink();
        dvlBo.setLinkId(docLinkLog.getId());
        dvlBo.setVersionId(version.getId());
        Boolean insertdll =  iDocLinkLogService.save(docLinkLog);
        Boolean insertDvl =docVersionLinkService.save(dvlBo);
        return insertdll && insertDvl;
    }

    @Override
    public String getDocClass(String id) {
        if (StringUtils.isNotEmpty(id)) {
            Standard standard= getOne(new LambdaQueryWrapper<Standard>().eq(Standard::getId,id).select(Standard::getDocClass));
            return standard!=null?standard.getDocClass():null;
        }else {
            return null;
        }
    }


    /**
     * 1、替换文件
     * 2、并更新为草稿状态
     * @param file        附件
     * @param id        StandardBId
     * @param versionId 版本id
     * @param docId        文件id
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult<Void> replaceFile(MultipartFile file, String id, String versionId, String docId) {
        if(StringUtils.isNotBlank(versionId)) {
            QueryWrapper<WorkflowApplyLog> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(WorkflowApplyLog::getDocId, docId);
            queryWrapper.lambda().eq(WorkflowApplyLog::getVersionId, versionId);
            Long count = workflowApplyLogMapper.selectCount(queryWrapper);
            if (count > 0) {
                return AjaxResult.error(I18nUtils.getTitle(CommonI18nConstant.SERVICE_STANDARD_NEW_DATA_CANNOT_REPLACE));
            }
        }
        String filename = file.getOriginalFilename();
        if(StringUtils.contains(filename,Constants.SLASH)){
            filename = filename.substring(filename.lastIndexOf(Constants.SLASH)+1);
        }
        BasicFile basicFile;
        try {
            basicFile = basicFilePdfService.uploading(file);
        } catch (Exception e) {
            log.error("StandardServiceImpl-->replaceFile----e###", e);
            return AjaxResult.error(filename + I18nUtils.getTitle(CommonI18nConstant.SERVICE_STANDARD_FILE_UPLOAD_ERR));
        }
        StandardVo standardVo = this.getVoById(id);
        Version version = versionService.queryOneByStandardId(id);
        //删除分发和附件数据
        handleStandardAndFile(standardVo, version);
        StandardBo standardBo = new StandardBo();
        BeanUtil.copyProperties(standardVo, standardBo);
        standardBo.setFileId(basicFile.getId());
        boolean docResult = true;
        boolean updateVersionIs = true;
        if(ObjectUtil.isNotEmpty(version)){
            version.setFileId(basicFile.getId());
            iDocLinkLogService.update(new LambdaUpdateWrapper<DocLinkLog>()
                    .set(DocLinkLog::getFileId,basicFile.getId())
                    .set(DocLinkLog::getFileName,basicFile.getFileName())
                    .eq(DocLinkLog::getLinkType,LinkTypeEnum.DOC.name())
                    .eq(DocLinkLog::getVersionId,version.getId()));
            updateVersionIs = versionService.updateById(version);
        }
        //更新为草稿状态
        boolean update = this.update(new LambdaUpdateWrapper<Standard>()
            .set(Standard::getStatus, DocConstants.STATUS_ZERO)
            .set(Standard::getFileId, basicFile.getId())
            .eq(Standard::getId, id)
        );
        return update && docResult && updateVersionIs ? AjaxResult.success() : AjaxResult.error();
    }

    /**
     * 校验导入必填项
     * @param ibo       标准文件对象
     * @param num       文件序号位置
     * @param ivo       标准文件Vo
     * @param configure
     * @return
     */
    public Boolean validateImport(StandardIBo ibo, int num, ImportIVo ivo, ConfigureDTO configure) {
        // 校验是否产生了八大流程数据
        if(checkProcessMsg(ibo)){
            ivo.initFileMsgAdd(num,I18nUtils.getTitle(CommonI18nConstant.SERVICE_STANDARD_NEW_DATA_NOT_EDIT));
            return false;
        }
        // 校验名称是否重复
//        if(checkDocName(ibo)){
//            ivo.initFileMsgAdd(num,I18nUtils.getTitle(CommonI18nConstant.SERVICE_STANDARD_FILE_NAME_REPEAT));
//            return false;
//        }

        if (StrUtil.isBlank(ibo.getDocClass())) {
            ivo.initFileMsgAdd(num,I18nUtils.getTitle(CommonI18nConstant.SERVICE_STANDARD_FILE_TYPE_NOT_NULL));
            return false;
        }

        if (StrUtil.isBlank(ibo.getChangeType())) {
            ivo.initFileMsgAdd(num,I18nUtils.getTitle(CommonI18nConstant.SERVICE_STANDARD_CHANGE_TYPE_NOT_NULL));
            return false;
        }

        if (StrUtil.isBlank(ibo.getDeptId())) {
            ivo.initFileMsgAdd(num,I18nUtils.getTitle(CommonI18nConstant.SERVICE_STANDARD_ORG_DEPT_NOT_NULL));
            return false;
        }

        if (StrUtil.isBlank(ibo.getCurrentVersion())) {
            ivo.initFileMsgAdd(num,I18nUtils.getTitle(CommonI18nConstant.SERVICE_STANDARD_FILE_VER_NOT_NULL));
            return false;
        }
        if (StrUtil.isBlank(ibo.getDocName())) {
            ivo.initFileMsgAdd(num,I18nUtils.getTitle(CommonI18nConstant.FILE_DOC_EXTERNAL_NAME_NOT_NULL));
            return false;
        }

        if (StrUtil.isBlank(ibo.getDocId())) {
            ivo.initFileMsgAdd(num,I18nUtils.getTitle(CommonI18nConstant.FILE_DOC_EXTERNAL_SERIAL_NUM_NOT_NULL));
            return false;
        }
        Date startDate = Convert.toDate(ibo.getStartDate());
        if (ObjectUtil.isEmpty(startDate)) {
            ivo.initFileMsgAdd(num,I18nUtils.getTitle(CommonI18nConstant.SERVICE_STANDARD_EFFECT_DATE_READ_ERR));
            return false;
        }
        /*Date releaseTime = Convert.toDate(ibo.getReleaseTime());
        if (ObjectUtil.isEmpty(releaseTime)) {
            ivo.initFileMsgAdd(num,I18nUtils.getTitle(CommonI18nConstant.SERVICE_STANDARD_RELEASE_DATE_READ_ERR));
            return false;
        }*/
        return true;
    }

    private List<ModifyApply> handleApplyList(List<ModifyApply> modifyApplyList) {
        List<ModifyApply> modifyApplies = new ArrayList<>();
        if (CollUtil.isEmpty(modifyApplyList)) {
           return modifyApplies;
        }
        // 版本为空的情况
        modifyApplies.addAll(modifyApplyList.stream()
                .filter(item -> StrUtil.isBlank(item.getVersionId())).collect(Collectors.toList()));

        // 版本不为空的情况
        List<ModifyApply> notVersionModifyApplyList = modifyApplyList.stream()
                .filter(item -> StrUtil.isNotBlank(item.getVersionId()))
                .collect(Collectors.toList());

        if (CollUtil.isNotEmpty(notVersionModifyApplyList)) {
            // 获取版本ID列表
            List<String> versionIdList = notVersionModifyApplyList.stream()
                    .map(ModifyApply::getVersionId)
                    .collect(Collectors.toList());

            // 查询失效状态的版本
            List<Version> versionList = versionService.list(new LambdaQueryWrapper<Version>()
                    .in(Version::getId, versionIdList)
                    .eq(Version::getStatus, Constants.TWO));

            if (CollUtil.isNotEmpty(versionList)) {
                // 查找是否有ModifyApply的VersionId不在失效状态的版本中
                List<ModifyApply> modifyApplies1 = notVersionModifyApplyList.stream()
                        .filter(modifyApply -> versionList.stream()
                                .noneMatch(version -> modifyApply.getVersionId().equals(version.getId()))).collect(Collectors.toList());
                modifyApplies.addAll(modifyApplies1);
            }
        }
        return modifyApplies;
    }

    @Override
    public void saveFileAndVersion(String fileName, String docClass,
                                   String projectId, String dataType,
                                   String fileId, String fileNameOld) throws ServerException {
        if (!fileName.contains("附录")) {
            String key = fileName.toUpperCase().split(fileNameOld.toUpperCase())[0];
            String redisKey = SecureUtil.md5(key);
            // 保存台账表数据
            StandardBo bo1 = new StandardBo();
            bo1.setApplyTime(DateUtil.date());
            bo1.setDocClass(docClass);
            bo1.setStatus(Constants.ZERO);
            if(StringUtils.contains(fileName,Constants.SLASH)){
                fileName = fileName.substring(fileName.lastIndexOf(Constants.SLASH)+1);
                bo1.setDocName(fileName);
            }
            bo1.setFileId(fileId);
            bo1.setInitFile(Constants.ONE);
            bo1.setDataType(dataType);
            bo1.setProjectId(projectId);
            ProjectInfoVo projectInfoVo= projectInfoService.getVoById(projectId);
            if(projectInfoVo!=null){
                bo1.setProjectName(projectInfoVo.getName());
            }
            insertByBo(bo1);
            Version version = versionService.queryOneByStandardId(bo1.getId());
            if (ObjectUtil.isNull(version)) {
                version = new Version();
            }
            DocClassVo docClassVo = iDocClassService.queryById(docClass);
            //version.setDocId(bo1.getId());
            version.setClassType(docClassVo.getClassType());
            version.setVersionValue("V0");
            version.setDocName(bo1.getDocName());
            version.setApplyTime(DateUtil.date());
            version.setStartDate(new Date());
            version.setStandardId(bo1.getId());
            version.setStatus(Constants.ONE);
            version.setForever(Constants.ONE);
            version.setDistributeType("company");
            version.setDataType(dataType);
            version.setFileId(fileId);
            version.setReason(Constants.VALUE_INIT);
            versionService.saveOrUpdate(version);
            docLinkLogService.handApplyLinkAdd(version,docClass,LinkTypeEnum.DOC.name(),version.getId(),version.getFileId());
            // 保存redis数据
            redisUtils.set(redisKey, bo1.getId());
        } else {
            String key = fileName.toUpperCase().split("附录")[0];
            String redisKey = SecureUtil.md5(key);
            if (ObjectUtil.isNotEmpty(redisUtils.get(redisKey))) {
                String standardId = redisUtils.get(redisKey)+"";
                Version version = versionService.queryOneByStandardId(standardId);
                DocLinkLogBo dllBo=new DocLinkLogBo();
                dllBo.setLinkType(LinkTypeEnum.APPENDIX.name());
                dllBo.setFileName(fileNameOld);
                dllBo.setVersionId(version.getId());
                if (iDocLinkLogService.getLinkCount(dllBo)>0){
                    throw new ServerException(fileNameOld+"文件名称重复");
                }
                //版本附件用主文件的数据
                version.setFileId(fileId);
                version.setDocName(fileNameOld);
                docLinkLogService.handApplyLinkAdd(version,docClass,LinkTypeEnum.APPENDIX.name(),version.getId(),version.getFileId());
            }
        }
    }


    /**
     * 根据标准文件主键查询文件类型
     * @param standardId
     * @return
     */
    @Override
    public String getDocClassName(String standardId) {
        return standardMapper.getDocClassName(standardId);
    }

}

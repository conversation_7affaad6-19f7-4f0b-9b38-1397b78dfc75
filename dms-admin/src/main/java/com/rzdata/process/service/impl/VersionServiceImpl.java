package com.rzdata.process.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rzdata.framework.annotation.DataScope;
import com.rzdata.framework.constant.Constants;
import com.rzdata.framework.constant.DictConstants;
import com.rzdata.framework.constant.DocConstants;
import com.rzdata.framework.core.domain.entity.SysDictData;
import com.rzdata.framework.core.domain.model.LoginUser;
import com.rzdata.framework.core.mybatisplus.core.ServicePlusImpl;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.utils.PageUtils;
import com.rzdata.framework.utils.SecurityUtils;
import com.rzdata.framework.utils.StringUtils;
import com.rzdata.framework.utils.poi.ExcelUtil;
import com.rzdata.process.controller.VersionController;
import com.rzdata.process.domain.*;
import com.rzdata.process.domain.bo.ModifyApplyTrainBo;
import com.rzdata.process.domain.bo.VersionBo;
import com.rzdata.process.domain.dto.VersionDTO;
import com.rzdata.process.domain.vo.*;
import com.rzdata.process.enums.LinkTypeEnum;
import com.rzdata.process.mapper.VersionMapper;
import com.rzdata.process.service.*;
import com.rzdata.setting.domain.DocClass;
import com.rzdata.setting.domain.DocClassSetting;
import com.rzdata.setting.service.IDocClassService;
import com.rzdata.setting.service.IDocClassSettingService;
import com.rzdata.system.service.ISysConfigService;
import com.rzdata.system.service.ISysDeptService;
import com.rzdata.system.service.ISysDictTypeService;
import com.rzdata.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 文件版本记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-12-30
 */
@Service
public class VersionServiceImpl extends ServicePlusImpl<VersionMapper, Version, VersionVo> implements IVersionService {

    @Autowired
    ISysDeptService iSysDeptService;

    @Resource
    ISysUserService sysUserService;

    @Autowired
    IDocVersionFavoritesService iDocVersionFavoritesService;
    @Autowired
    private IModifyApplyLinkService modifyApplyLinkService;
    @Autowired
    private IDocLinkLogService docLinkLogService;
    @Autowired
    private IDocClassService iDocClassService;
    @Autowired
    private IBorrowApplyItemService iBorrowApplyItemService;

    @Autowired
    VersionController versionController;

    @Autowired
    IModifyApplyService modifyApplyService;

    @Autowired
    IDocClassSettingService docClassSettingService;

    @Autowired
    private IStandardService standardService;

    @Autowired
    private ISysDictTypeService dictTypeService;

    @Autowired
    private IModifyApplyTrainService modifyApplyTrainService;
    @Autowired
    private ISysConfigService iSysConfigService;


    @Override
    public VersionVo queryById(String id){
        return getVoById(id);
    }

    @Override
    public Version queryOneByStandardId(String standardId) {
        return getOne(new LambdaQueryWrapper<Version>().eq(Version::getStandardId,standardId).eq(Version::getStatus, Constants.ONE));
    }

    @Override
    public Version queryByDocIdAndVersion(String docId, String versionValue) {
        return getOne(new LambdaQueryWrapper<Version>().eq(Version::getDocId,docId).eq(Version::getVersionValue,versionValue));
    }

    @Override
    public VersionVo getDetail(String docId, String versionValue) {
        LambdaQueryWrapper<Version> wrapper = new LambdaQueryWrapper<Version>()
            .eq(Version::getDocId, docId)
            .eq(Version::getVersionValue, versionValue);
        VersionVo versionVo = getVoOne(wrapper);
        if (versionVo != null) {

            // 获取关联记录 - record
            List<DocLinkLogVo> recordLinks = docLinkLogService.queryDocLinkVo(versionVo.getDocId(), versionVo.getId(), LinkTypeEnum.RECORD.name());

            // 获取关联文件 - refDoc
            List<DocLinkLogVo> docLinks = docLinkLogService.queryDocLinkVo(versionVo.getDocId(), versionVo.getId(), LinkTypeEnum.REF_DOC.name());

            // 获取文件历史版本 - versions
            List<VersionVo> historyVersions = selectVersionListByDocId(versionVo.getDocId());

            // 获取主文件对象 - preStandardDoc
            List<DocLinkLogVo> docList = docLinkLogService.queryDocLinkVo(versionVo.getDocId(), versionVo.getId(), LinkTypeEnum.DOC.name());
            DocLinkLogVo mainFile = docList != null && !docList.isEmpty() ? docList.get(0) : null;

            // 获取版本附件对象 - preAppendixes
            List<DocLinkLogVo> attachments = docLinkLogService.queryDocLinkVo(versionVo.getDocId(), versionVo.getId(), LinkTypeEnum.APPENDIX.name());

            // 获取培训记录 - trains
            ModifyApplyTrainBo modifyApplyTrainBo = new ModifyApplyTrainBo();
            modifyApplyTrainBo.setVersionId(versionVo.getId());
            List<ModifyApplyTrainVo> trainVoList = modifyApplyTrainService.queryModifyApplyTrain(modifyApplyTrainBo);

            // 设置到扩展属性中，使用与queryDetail一致的键名
            versionVo.setExtend(new HashMap<String, Object>(6) {{
                put("versions", historyVersions);
                put("preStandardDoc", mainFile);
                put("preAppendixes", attachments);
                put("recordLinks", recordLinks);
                put("docLinks", docLinks);
                put("trains", trainVoList);
            }});

            // 补充用户和部门名称信息
            versionVo.setNickName(sysUserService.getNickName(versionVo.getUserName()));
            versionVo.setCustodyDeptName(iSysDeptService.getDeptName(versionVo.getCustodyDeptId()));
            versionVo.setDeptName(iSysDeptService.getDeptName(versionVo.getDeptId()));
            return versionVo;
        }
        return null;
    }

    @Override
    @DataScope(deptAlias = "dl")
    public TableDataInfo<VersionVo> queryPageList(VersionBo bo) {
        Page<VersionVo> page = PageUtils.buildPage(bo.getPageNum(),bo.getPageSize(),bo.getOrderByColumn(),bo.getIsAsc());
        if ("file".equals(bo.getType())) {
            // 工程文件id
            String projectFileId = iSysConfigService.selectConfigByKey(Constants.PROJECT_FILE_ID);
            if (StrUtil.isNotBlank(projectFileId)) {
                List<String> allChildrenIds = iDocClassService.findAllChildrenIdsById(projectFileId);
                bo.setProjectFileDocClassList(allChildrenIds);
            }

        }
        Page<VersionVo> iPage = this.baseMapper.selectVersionPage(page, bo);
        LoginUser user = SecurityUtils.getLoginUser();
        //判断当前登录用户是不是公司文件管理员
        boolean isQa = sysUserService.isQa(user.getUsername());
        iPage.getRecords().forEach(versionVo -> {
            versionVo.setNickName(sysUserService.getNickName(versionVo.getUserName()));
            versionVo.setCustodyDeptName(iSysDeptService.getDeptName(versionVo.getCustodyDeptId()));
            versionVo.setDeptName(iSysDeptService.getDeptName(versionVo.getDeptId()));
            Date reviewTime = versionVo.getReviewTime();
            //     临时文件、工程样板
            if (StrUtil.containsAny(versionVo.getIsFileType(), Constants.FILE_Y, Constants.FILE_G)) {
                String shelfLife = versionVo.getShelfLife();
                if (ObjectUtil.isNotEmpty(shelfLife)) {
                    versionVo.setExpiredStatus(DateUtil.compare(DateUtil.parseDate(shelfLife), DateUtil.beginOfDay(DateUtil.date())) >= 0 ? Constants.NORMAL : Constants.FAILURE);
                }
            } else {
                if (ObjectUtil.isNotNull(reviewTime)) {
                    // 使用hutool判断复审时间是否超过当前时间：超过为normal，否则为failure
                    versionVo.setExpiredStatus(DateUtil.compare(DateUtil.beginOfDay(reviewTime), DateUtil.beginOfDay(DateUtil.date())) >= 0 ? Constants.NORMAL : Constants.FAILURE);
                }
            }
            checkPerms(versionVo,isQa,user);
        });
        return PageUtils.buildDataInfo(iPage);
    }

    @Override
    public void checkPerms(VersionVo versionVo,boolean isQa,LoginUser user){
        if (isQa || SecurityUtils.isAdmin(SecurityUtils.getUserId()) || user.getUsername().equals(versionVo.getCreateBy()) || user.getUsername().equals(versionVo.getUserName()) || user.getAncestors().contains(versionVo.getDeptId())) {
            //是编制部门的 或者 文件分发类型是公司的 或者是公司文件管理员 有权限查看 或者 自己发起的文件自己也能查看
            versionVo.setHasPerms(true);
        }else{
            versionVo.setHasPerms(baseMapper.checkAuthByDis(versionVo.getId(), user.getUsername(), user.getDeptId()) > 0);
        }
        if (!versionVo.getHasPerms()) {
            versionVo.setIsBorrow(iBorrowApplyItemService.isValidBorrowByVersionId(versionVo.getId(),user.getUsername()));
        }
    }

    @Override
    public TableDataInfo<VersionVo> queryPageOtherDeptList(VersionBo bo) {
        Page<VersionVo> page = PageUtils.buildPage(bo.getPageNum(),bo.getPageSize(),bo.getOrderByColumn(),bo.getIsAsc());
        Page<VersionVo> iPage = this.baseMapper.queryPageOtherDeptList(page, bo);
        iPage.getRecords().forEach(versionVo -> {
            Date reviewTime = versionVo.getReviewTime();
            //     临时文件、工程样板
            if (StrUtil.containsAny(versionVo.getIsFileType(), Constants.FILE_G, Constants.FILE_Y)) {
                String shelfLife = versionVo.getShelfLife();
                if (ObjectUtil.isNotEmpty(shelfLife)) {
                    versionVo.setExpiredStatus(DateUtil.compare(DateUtil.parseDate(shelfLife), DateUtil.date()) > 0 ? Constants.NORMAL : Constants.FAILURE);
                }
            } else {
                if (ObjectUtil.isNotNull(reviewTime)) {
                    // 使用hutool判断复审时间是否超过当前时间：超过为normal，否则为failure
                    versionVo.setExpiredStatus(DateUtil.compare(reviewTime, DateUtil.date()) > 0 ? Constants.NORMAL : Constants.FAILURE);
                }
            }
        });
        return PageUtils.buildDataInfo(iPage);
    }

    public List<VersionVo> queryVersionPartList(VersionBo bo){
        return this.baseMapper.queryVersionPartList(bo);
    }

    @Override
    public Boolean validateUniqueness(VersionBo bo) {
        //物料编码-物料描述-工厂开关
        List<DocClassSetting> docClassSettingList = docClassSettingService.list(new LambdaQueryWrapper<DocClassSetting>().in(DocClassSetting::getType, Constants.TYPE_FORMSHOW, Constants.TYPE_CUSTOMERSHOW, Constants.TYPE_DEVICESHOW));
        if(CollUtil.isEmpty(docClassSettingList)){
            return false;
        }
        boolean openFlag =  false;
        for (DocClassSetting docClassSetting : docClassSettingList) {
            if(Constants.VALUE_Y.equals(docClassSetting.getOpenFlag())){
                openFlag = true;
                break;
            }
        }
        if(!openFlag){
            return false;
        }

        if(CollUtil.isNotEmpty(this.baseMapper.queryVersionPartList(bo))){
            return true;
        }

        //查询正在走流程的数据
        LambdaQueryWrapper<ModifyApply> lqw = Wrappers.lambdaQuery();
        if (StrUtil.isNotBlank(bo.getPartNumber()) && StrUtil.isNotBlank(bo.getPartRemark())) {
            lqw.eq(ModifyApply::getDocClass, bo.getDocClass());
            lqw.ne(StringUtils.isNotBlank(bo.getApplyId()), ModifyApply::getId, bo.getApplyId());
            lqw.ne(StringUtils.isNotBlank(bo.getNeVersionId()), ModifyApply::getVersionId, bo.getNeVersionId());
            lqw.eq(ModifyApply::getPartNumber, bo.getPartNumber());
            lqw.eq(ModifyApply::getPartRemark, bo.getPartRemark());
            lqw.ne(ModifyApply::getRecordStatus, "cancel");
        }

        if (StrUtil.isNotBlank(bo.getDeviceCode()) && StrUtil.isNotBlank(bo.getDeviceName())) {
            lqw.or(lqw1 -> lqw1.eq(ModifyApply::getDocClass, bo.getDocClass())
                    .eq(ModifyApply::getDeviceCode, bo.getDeviceCode())
                    .eq(ModifyApply::getDeviceName, bo.getDeviceName())
                    .ne(StringUtils.isNotBlank(bo.getApplyId()), ModifyApply::getId, bo.getApplyId())
                    .ne(StringUtils.isNotBlank(bo.getNeVersionId()), ModifyApply::getVersionId, bo.getNeVersionId())
                    .ne(ModifyApply::getRecordStatus, "cancel")
            );
        }
        List<ModifyApply> modifyApplyList = modifyApplyService.list(lqw);

        if (CollUtil.isNotEmpty(modifyApplyList)) {
            // 版本为空的情况
            boolean hasEmptyVersion = modifyApplyList.stream()
                    .anyMatch(item -> StrUtil.isBlank(item.getVersionId()));
            if (hasEmptyVersion) {
                return true;
            }

            // 版本不为空的情况
            List<ModifyApply> notVersionModifyApplyList = modifyApplyList.stream()
                    .filter(item -> StrUtil.isNotBlank(item.getVersionId()))
                    .collect(Collectors.toList());

            if (CollUtil.isNotEmpty(notVersionModifyApplyList)) {
                // 获取版本ID列表
                List<String> versionIdList = notVersionModifyApplyList.stream()
                        .map(ModifyApply::getVersionId)
                        .collect(Collectors.toList());

                // 查询失效状态的版本
                List<Version> versionList = this.list(new LambdaQueryWrapper<Version>()
                        .in(Version::getId, versionIdList)
                        .eq(Version::getStatus, Constants.TWO));

                if (CollUtil.isNotEmpty(versionList)) {
                    // 查找是否有ModifyApply的VersionId不在失效状态的版本中
                    boolean isValid = notVersionModifyApplyList.stream()
                            .anyMatch(modifyApply -> versionList.stream()
                                    .noneMatch(version -> modifyApply.getVersionId().equals(version.getId())));
                    return isValid;
                } else {
                    return true;
                }
            }
        }
        return false;
    }

    @Override
    public List<VersionVo> queryList(VersionBo bo) {
        return listVo(buildQueryWrapper(bo));
    }

    private LambdaQueryWrapper<Version> buildQueryWrapper(VersionBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<Version> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getStandardId()), Version::getStandardId, bo.getStandardId());
        lqw.eq(StringUtils.isNotBlank(bo.getDocId()), Version::getDocId, bo.getDocId());
        lqw.eq(StringUtils.isNotBlank(bo.getApplyId()), Version::getApplyId, bo.getApplyId());
        lqw.eq(StringUtils.isNotBlank(bo.getVersionValue()), Version::getVersionValue, bo.getVersionValue());
        lqw.eq(bo.getStartDate() != null, Version::getStartDate, bo.getStartDate());
        lqw.eq(bo.getEndDate() != null, Version::getEndDate, bo.getEndDate());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), Version::getStatus, bo.getStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getReason()), Version::getReason, bo.getReason());
        lqw.eq(StringUtils.isNotBlank(bo.getPartNumber()), Version::getPartNumber, bo.getPartNumber());
        lqw.eq(StringUtils.isNotBlank(bo.getPartRemark()), Version::getPartRemark, bo.getPartRemark());
        lqw.eq(StringUtils.isNotBlank(bo.getFactorys()), Version::getFactorys, bo.getFactorys());
        lqw.eq(StringUtils.isNotBlank(bo.getProductVersion()), Version::getProductVersion, bo.getProductVersion());
        lqw.orderByAsc(Version::getApplyTime);

        return lqw;
    }

    @Override
    public Boolean insertByBo(VersionBo bo) {
        Version add = BeanUtil.toBean(bo, Version.class);
        validEntityBeforeSave(add);
        boolean flag = save(add);
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    @Override
    public Boolean updateByBo(VersionBo bo) {
        Version update = BeanUtil.toBean(bo, Version.class);
        validEntityBeforeSave(update);
        return updateById(update);
    }

    /**
     * 保存前的数据校验
     *
     * @param entity 实体类数据
     */
    private void validEntityBeforeSave(Version entity){
        //TODO 做一些数据校验,如唯一约束
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return removeByIds(ids);
    }

    @Override
    public List<VersionVo> selectVersionListByAppId(String applyId) {
        return this.baseMapper.selectVersionListByAppId(applyId);
    }

    @Override
    public List<VersionVo> selectVersionList(List<String> applyId) {
        return this.baseMapper.selectVersionList(applyId);
    }

    @Override
    public List<VersionVo> selectVersionListByDocId(String docId) {
        return listVo(new LambdaQueryWrapper<Version>().eq(Version::getDocId,docId).orderByAsc(Version::getReleaseTime));
    }

    @Override
    public VersionVo selectVersionByDocId(String docId){
        return this.baseMapper.selectVersionByDocId(docId);
    }

    @Override
    public List<VersionVo> selectVersionListByDocIdAndVersionId(String docId, String versionId) {
        return this.baseMapper.selectVersionListByDocIdAndVersionId(docId,versionId);
    }


    @Override
    public TableDataInfo<VersionVo> selectRecordFile(VersionBo bo) {
        Page<VersionVo> page = PageUtils.buildPage();
        Page<VersionVo> iPage = this.baseMapper.selectRecordFile(page, bo);
        for (VersionVo vVo:iPage.getRecords()) {
            vVo.setCompileDeptName(iSysDeptService.getById(vVo.getDeptId()).getDeptName());
            vVo.setInFavorites(iDocVersionFavoritesService.inFavorites(vVo.getId()));
        }
        return PageUtils.buildDataInfo(iPage);
    }

    @Override
    public TableDataInfo<VersionVo> selectRecordFileCompany(VersionBo bo) {
        Page<VersionVo> page = PageUtils.buildPage();
        Page<VersionVo> iPage = this.baseMapper.selectRecordFileCompany(page, bo);
        for (VersionVo vVo:iPage.getRecords()) {
            vVo.setDeptName(iSysDeptService.getDeptName(vVo.getDeptId()));
            vVo.setNickName(sysUserService.getNickName(vVo.getUserName()));
        }
        String deptId = SecurityUtils.getDeptId();
        String userName = SecurityUtils.getUsername();
        //判断当前登录用户是不是公司文件管理员
        boolean isQa = sysUserService.isQa(SecurityUtils.getUsername());
        iPage.getRecords().forEach(versionVo -> {
            if (isQa) {
                //是编制部门的 或者 文件分发类型是公司的 或者是公司文件管理员 有权限查看
                versionVo.setHasPerms(true);
            }else{
                versionVo.setHasPerms(baseMapper.checkAuthByDis(versionVo.getId(), userName, deptId) > 0);
            }
        });
        return PageUtils.buildDataInfo(iPage);
    }

    @Override
    public TableDataInfo<VersionVo> selectRecordFileCompanyOtherDept(VersionBo bo) {
        Page<VersionVo> page = PageUtils.buildPage();
        Page<VersionVo> iPage = this.baseMapper.selectRecordFileCompanyOtherDept(page, bo);
        for (VersionVo vVo:iPage.getRecords()) {
            vVo.setDeptName(iSysDeptService.getDeptName(vVo.getDeptId()));
            vVo.setNickName(sysUserService.getNickName(vVo.getUserName()));
        }
        return PageUtils.buildDataInfo(iPage);
    }

    @Override
    public TableDataInfo<VersionVo> selectDeptFile(VersionBo bo) {
        Page<VersionVo> page = new Page<>(bo.getPageNum(), bo.getPageSize());
        Page<VersionVo> iPage = this.baseMapper.selectDeptFile(page, bo);
        for (VersionVo vVo:iPage.getRecords()) {
            vVo.setDeptName(iSysDeptService.getById(vVo.getDeptId()).getDeptName());
        }
        return PageUtils.buildDataInfo(iPage);
    }
    @Override
    public boolean docIdExist(String docId){
        LambdaQueryWrapper<Version> lqw=new LambdaQueryWrapper<>();
        lqw.eq(Version::getDocId,docId);
        long sum = this.count(lqw);
        LambdaQueryWrapper<ModifyApplyLink> lqwLink=new LambdaQueryWrapper<>();
        lqwLink.eq(ModifyApplyLink::getDocId,docId);
        long sumLink = modifyApplyLinkService.count(lqwLink);
        LambdaQueryWrapper<DocLinkLog> lqwLinkLog=new LambdaQueryWrapper<>();
        lqwLinkLog.eq(DocLinkLog::getLinkCode,docId);
        long sumLinkLog = docLinkLogService.count(lqwLinkLog);
        return sum>0 || sumLink>0 || sumLinkLog>0;
    }

    @Override
    public boolean isbom(String id) {
        Version version=getOne(new LambdaQueryWrapper<Version>().select(Version::getIsBom).eq(Version::getId,id));
        return version != null && version.getIsBom();
    }

    @Override
    public boolean checkAuthByDis(String versionId,String userName,String deptId){
        return baseMapper.checkAuthByDis(versionId, userName, deptId) > 0;
    }

    @Override
    public List<Version> versionAndStandardList(List<String> standardIds,String status) {
        return this.baseMapper.versionAndStandardList(standardIds,status);
    }

    @Override
    public String getDocNameByVersionId(String versionId) {
        Version version= getOne(new LambdaQueryWrapper<Version>().eq(Version::getId,versionId).select(Version::getDocName));
        if (version!=null){
            return version.getDocName();
        }
        return null;
    }

    @Override
    public Version getValidVersionByDocId(String docId) {
       return getOne(new LambdaQueryWrapper<Version>()
                .eq(Version::getDocId,docId).eq(Version::getStatus,Constants.ONE)
                .select(Version::getDocName,Version::getDocId,Version::getId));
    }

    @Override
    public List<Version> selectRecordVersionList(String versionId) {
        return baseMapper.selectRecordVersionList(versionId);
    }

    @Override
    public List<VersionExportVo> transformData(List<VersionExportVo> listVo) {
        return transformVersionExportData(listVo);
    }


    public List<VersionPartExportVo> transformVersionPartData(List<VersionPartExportVo> listVo) {
        return transformVersionExportData(listVo);
    }


    /**
     * 不是记录
     * @param listVo
     * @param <T>
     * @return
     */
    public <T extends VersionExportVo> List<T> transformVersionExportData(List<T> listVo) {
        List<DocClass> docClassList = iDocClassService.list(new LambdaQueryWrapper<DocClass>().eq(DocClass::getClassStatus, DocConstants.STATUS_ONE));
        List<SysDictData> factoryDictList = dictTypeService.selectDictDataByType(DictConstants.TENANT_LIST);

        for (T versionExportVo : listVo) {
            versionExportVo.setStatus(Constants.ONE.equals(versionExportVo.getStatus()) ? Constants.IN_EFFECT : Constants.INVALID);
            docClassList.forEach(item -> {
                if (item.getId().equals(versionExportVo.getDocClass())) {
                    versionExportVo.setDocClassName(item.getClassName());
                }
            });
            if(StrUtil.isBlank(versionExportVo.getFactorys())){
                continue;
            }
            factoryDictList.forEach(item -> {
                if (item.getDictValue().equals(versionExportVo.getFactorys())) {
                    versionExportVo.setFactorys(item.getDictLabel());
                }
            });
        }
        return listVo;
    }


    /**
     * 外来文件转换
     * @param listVo
     * @return
     */
    public List<VersionForeignExportVo> transformForeignData(List<VersionForeignExportVo> listVo) {
        return transformVersionForeignExportVoData(listVo);
    }


    /**
     * 外来文件
     * @param listVo
     * @param <T>
     * @return
     */
    public <T extends VersionForeignExportVo> List<T> transformVersionForeignExportVoData(List<T> listVo) {
        List<DocClass> docClassList = iDocClassService.list(new LambdaQueryWrapper<DocClass>().eq(DocClass::getClassStatus, DocConstants.STATUS_ONE));
        List<SysDictData> factoryDictList = dictTypeService.selectDictDataByType(DictConstants.TENANT_LIST);
        Map<String, String> regulationStandardStatusMap = dictTypeService.selectDictDataByType(DictConstants.REGULATION_STANDARD_STATUS)
                .stream()
                .collect(Collectors.toMap(SysDictData::getDictValue, SysDictData::getDictLabel, (k1, k2) -> k1));

        for (T versionExportVo : listVo) {
            versionExportVo.setStatus(Constants.ONE.equals(versionExportVo.getStatus()) ? Constants.IN_EFFECT : Constants.INVALID);
            docClassList.forEach(item -> {
                if (item.getId().equals(versionExportVo.getDocClass())) {
                    versionExportVo.setDocClassName(item.getClassName());
                }
            });
            String regulationStandardStatus = versionExportVo.getRegulationStandardStatus();
            if(StrUtil.isNotBlank(regulationStandardStatus)){
                versionExportVo.setRegulationStandardStatus(regulationStandardStatusMap.get(regulationStandardStatus));
            }
            if(StrUtil.isBlank(versionExportVo.getFactorys())){
                continue;
            }
            factoryDictList.forEach(item -> {
                if (item.getDictValue().equals(versionExportVo.getFactorys())) {
                    versionExportVo.setFactorys(item.getDictLabel());
                }
            });
        }
        return listVo;
    }

    /**
     * 记录文件
     * @param listVo
     * @param <T>
     * @return
     */
    public <T extends VersionRecordExportVo> List<T> transformVersionRecordExportVoData(List<T> listVo) {
        List<DocClass> docClassList = iDocClassService.list(new LambdaQueryWrapper<DocClass>().eq(DocClass::getClassStatus, DocConstants.STATUS_ONE));

        for (T versionExportVo : listVo) {
            versionExportVo.setStatus(Constants.ONE.equals(versionExportVo.getStatus()) ? Constants.IN_EFFECT : Constants.INVALID);
            docClassList.forEach(item -> {
                if (item.getId().equals(versionExportVo.getDocClass())) {
                    versionExportVo.setDocClassName(item.getClassName());
                }
            });
        }
        return listVo;
    }



    public List<VersionRecordExportVo> transformRecordData(List<VersionRecordExportVo> listVo) {
        return transformVersionRecordExportVoData(listVo);
    }


    public List<VersionRecordPartExportVo> transformRecordPartData(List<VersionRecordPartExportVo> listVo) {
        return transformVersionRecordExportVoData(listVo);
    }

    /**
     * 导出
     * @param bo
     * @param response
     * @param exportName		导出名称
     * @param invokerQueryList	来源查询类型  listOtherDept调用查询listOtherDept，list调用list
     */
    @Override
    public void exportForList(VersionBo bo, HttpServletResponse response, String exportName, String invokerQueryList) {
        if(Constants.VERSION_QUERY_FOREIGN_LIST.equals(invokerQueryList)){
            //外来文件
            List<VersionForeignExportVo> versionForeignExportVoList = BeanUtil.copyToList(versionController.list(bo).getRows(), VersionForeignExportVo.class);
            versionForeignExportVoList = this.transformForeignData(versionForeignExportVoList);
            ExcelUtil.exportExcel(versionForeignExportVoList, exportName, VersionForeignExportVo.class, response);
            return;
        }

        List<VersionExportVo> listVo = getVersionPartListVo(invokerQueryList, bo, VersionExportVo.class);
        listVo = this.transformData(listVo);
        ExcelUtil.exportExcel(listVo, exportName, VersionExportVo.class, response);
        return;
    }

    /**
     * 根据关联记录新增台账版本数据
     * @param standard
     * @param version
     * @param applyLink
     * @return
     */
    @Override
    public Version saveCopyByLink(Standard standard, Version version,ModifyApplyLink applyLink) {
        Standard recordStandard = new Standard();
        BeanUtil.copyProperties(standard,recordStandard);
        recordStandard.setId(null);
        recordStandard.setFileId(applyLink.getFileId());
        recordStandard.setDocClass(applyLink.getDocClass());
        recordStandard.setDocName(applyLink.getDocName());
        recordStandard.setDocClassType(iDocClassService.getClassType(applyLink.getDocClass()).getId());
        standardService.save(recordStandard);
        //版本
        Version recordVersion = new Version();
        BeanUtil.copyProperties(version,recordVersion);
        recordVersion.setId(null);
        recordVersion.setStandardId(recordStandard.getId());
        recordVersion.setDocId(applyLink.getDocId());
        recordVersion.setDocName(applyLink.getDocName());
        recordVersion.setVersionValue(applyLink.getVersionValue());
        recordVersion.setFileId(applyLink.getFileId());
        recordVersion.setUpVersionId(version.getId());
        recordVersion.setParentDocId(version.getDocId());
        recordVersion.setClassType(LinkTypeEnum.RECORD.name());
        save(recordVersion);
        return recordVersion;
    }

    private <T> List<T> getVersionPartListVo(String invokerQueryList, VersionBo bo, Class<T> clazz) {
        if(Constants.VERSION_QUERY_OTHER_DEPT.equals(invokerQueryList)){
            return BeanUtil.copyToList(versionController.listOtherDept(bo).getRows(),clazz);
        }else if(Constants.VERSION_QUERY_LIST.equals(invokerQueryList)){
            return BeanUtil.copyToList(versionController.list(bo).getRows(),clazz);
        }
        return Collections.emptyList();
    }


    private <T> List<T> getVersionRecordPartListVo(String invokerQueryList, VersionBo bo, Class<T> clazz) {
        if (Constants.VERSION_QUERY_OTHER_DEPT.equals(invokerQueryList)) {
            // 本部门
            return BeanUtil.copyToList(versionController.listOtherDept(bo).getRows(), clazz);
        } else if (Constants.VERSION_QUERY_LIST.equals(invokerQueryList)) {
            // 公司
            return BeanUtil.copyToList(versionController.list(bo).getRows(), clazz);
        }
        return Collections.emptyList();
    }

    @Override
    public TableDataInfo<VersionDTO> pageApi(VersionBo bo,String limitDocClass) {
        Page<VersionDTO> page = PageUtils.buildPage();
        Page<VersionDTO> iPage = baseMapper.pageApi(page, bo,limitDocClass);
        return PageUtils.buildDataInfo(iPage);
    }

    @Override
    public Boolean updateReviewRecordFileIdsByIds(String reviewRecordFileIds, Set<String> versionIds) {
        return lambdaUpdate()
                .set(Version::getReviewRecordFileIds, reviewRecordFileIds)
                .in(Version::getId, versionIds)
                .update();
    }
}

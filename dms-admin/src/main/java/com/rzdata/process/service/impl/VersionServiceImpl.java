package com.rzdata.process.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rzdata.es.service.ElasticsearchService;
import com.rzdata.framework.annotation.DataScope;
import com.rzdata.framework.constant.*;
import com.rzdata.framework.core.domain.entity.SysDept;
import com.rzdata.framework.core.domain.entity.SysDictData;
import com.rzdata.framework.core.domain.model.LoginUser;
import com.rzdata.framework.core.mybatisplus.core.ServicePlusImpl;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.utils.*;
import com.rzdata.framework.utils.poi.ExcelUtil;
import com.rzdata.process.domain.*;
import com.rzdata.process.domain.bo.DocLinkLogBo;
import com.rzdata.process.domain.bo.StandardBo;
import com.rzdata.process.domain.bo.VersionBo;
import com.rzdata.process.domain.dto.VersionDTO;
import com.rzdata.process.domain.vo.*;
import com.rzdata.process.enums.ApplyTypeEnum;
import com.rzdata.process.enums.CommonStatusEnum;
import com.rzdata.process.enums.LinkTypeEnum;
import com.rzdata.process.enums.OnlineTypeEnum;
import com.rzdata.process.mapper.ModifyApplyMapper;
import com.rzdata.process.mapper.StandardMapper;
import com.rzdata.process.mapper.VersionMapper;
import com.rzdata.process.service.*;
import com.rzdata.setting.domain.DocClass;
import com.rzdata.setting.domain.DocClassSetting;
import com.rzdata.setting.domain.DocClassWatermarkSettingDetail;
import com.rzdata.setting.domain.bo.WatermarkParamBo;
import com.rzdata.setting.service.IBasicFormRuleService;
import com.rzdata.setting.service.IDocClassService;
import com.rzdata.setting.service.IDocClassSettingService;
import com.rzdata.setting.service.IDocClassWatermarkSettingService;
import com.rzdata.system.service.ISysDeptService;
import com.rzdata.system.service.ISysDictTypeService;
import com.rzdata.system.service.ISysUserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 文件版本记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-12-30
 */
@Slf4j
@Service
public class VersionServiceImpl extends ServicePlusImpl<VersionMapper, Version, VersionVo> implements IVersionService {

    @Autowired
    ISysDeptService iSysDeptService;

    @Resource
    ISysUserService sysUserService;

    @Autowired
    IDocVersionFavoritesService iDocVersionFavoritesService;
    @Autowired
    private IModifyApplyLinkService modifyApplyLinkService;
    @Autowired
    private IDocLinkLogService docLinkLogService;
    @Autowired
    private IDocClassService iDocClassService;
    @Autowired
    private IBorrowApplyItemService iBorrowApplyItemService;

    @Autowired
    ModifyApplyMapper modifyApplyMapper;

    @Autowired
    IDocClassSettingService docClassSettingService;

    @Autowired
    private StandardMapper standardMapper;

    @Autowired
    private ISysDictTypeService dictTypeService;
    @Autowired
    private IBasicFormRuleService basicFormRuleService;

    @Autowired
    private FileSignatureService fileSignatureService;

    @Autowired
    private IBasicFilePdfService basicFilePdfService;

    @Autowired
    private IBasicFileService iBasicFileService;

    @Autowired
    private IDocClassWatermarkSettingService docClassWatermarkSettingService;

    @Autowired
    private ElasticsearchService elasticsearchService;

    @Autowired
    private IDocEditLogService iDocEditLogService;

    @Autowired
    private IFileAdviseService iFileAdviseService;

    @Override
    public VersionVo queryById(String id) {
        return getVoById(id);
    }

    @Override
    public Version queryOneByStandardId(String standardId) {
        return getOne(new LambdaQueryWrapper<Version>().eq(Version::getStandardId, standardId).eq(Version::getStatus, Constants.ONE));
    }

    @Override
    public VersionVo queryByDocIdAndVersion(String docId, String versionValue) {
        return getVoOne(new LambdaQueryWrapper<Version>().eq(Version::getDocId, docId).eq(Version::getVersionValue, versionValue));
    }

    @Override
    @DataScope(deptAlias = "dl")
    public TableDataInfo<VersionVo> queryPageList(VersionBo bo) {
        Page<VersionVo> page = PageUtils.buildPage(bo.getPageNum(), bo.getPageSize(), bo.getOrderByColumn(), bo.getIsAsc());
        Page<VersionVo> iPage = this.baseMapper.selectVersionPage(page, bo);
        // 获取人员组织对应的二级部门ID
        LoginUser user = SecurityUtils.getLoginUser();
        //判断当前登录用户是不是公司文件管理员
        boolean isQa = sysUserService.isQa(user.getUsername());
        iPage.getRecords().forEach(versionVo -> {
            SysDept sysDept = iSysDeptService.getById(versionVo.getDeptId());
            if (isQa || SecurityUtils.isAdmin(SecurityUtils.getUserId()) || user.getUsername().equals(versionVo.getCreateBy()) || user.getUsername().equals(versionVo.getUserName()) || sysDept.getDeptFullPathId().contains(user.getDeptId())) {
                //是编制部门的 或者 文件分发类型是公司的 或者是公司文件管理员 有权限查看 或者 自己发起的文件自己也能查看
                versionVo.setHasPerms(true);
            } else {
                versionVo.setHasPerms(baseMapper.checkAuthByDis(versionVo.getId(), user.getUsername(), user.getDeptId()) > 0);
            }
            if (!versionVo.getHasPerms()) {
                versionVo.setIsBorrow(iBorrowApplyItemService.isValidBorrowByVersionId(versionVo.getId(), user.getUsername()));
            }
            // 查询文件建议数量
            versionVo.setAdviseCount(iFileAdviseService.list(new LambdaQueryWrapper<FileAdvise>().eq(FileAdvise::getVersionId, versionVo.getId())).size());
        });
        return PageUtils.buildDataInfo(iPage);
    }


    @Override
    public TableDataInfo<VersionVo> queryPageOtherDeptList(VersionBo bo) {
        Page<VersionVo> page = PageUtils.buildPage(bo.getPageNum(), bo.getPageSize(), bo.getOrderByColumn(), bo.getIsAsc());
        Page<VersionVo> iPage = this.baseMapper.queryPageOtherDeptList(page, bo);
        return PageUtils.buildDataInfo(iPage);
    }

    public List<VersionVo> queryVersionPartList(VersionBo bo) {
        return this.baseMapper.queryVersionPartList(bo);
    }

    @Override
    public Boolean validateUniqueness(VersionBo bo) {
        //物料编码-物料描述-工厂开关
        List<DocClassSetting> docClassSettingList = docClassSettingService.list(new LambdaQueryWrapper<DocClassSetting>().in(DocClassSetting::getType, Constants.TYPE_FORMSHOW, Constants.TYPE_CUSTOMERSHOW, Constants.TYPE_DEVICESHOW));
        if (CollUtil.isEmpty(docClassSettingList)) {
            return false;
        }
        boolean openFlag = false;
        for (DocClassSetting docClassSetting : docClassSettingList) {
            if (Constants.VALUE_Y.equals(docClassSetting.getOpenFlag())) {
                openFlag = true;
                break;
            }
        }
        if (!openFlag) {
            return false;
        }

        if (CollUtil.isNotEmpty(this.baseMapper.queryVersionPartList(bo))) {
            return true;
        }

        //查询正在走流程的数据
        LambdaQueryWrapper<ModifyApply> lqw = Wrappers.lambdaQuery();
        if (StrUtil.isNotBlank(bo.getPartNumber()) && StrUtil.isNotBlank(bo.getPartRemark())) {
            lqw.eq(ModifyApply::getDocClass, bo.getDocClass());
            lqw.ne(StringUtils.isNotBlank(bo.getApplyId()), ModifyApply::getId, bo.getApplyId());
            lqw.ne(StringUtils.isNotBlank(bo.getNeVersionId()), ModifyApply::getVersionId, bo.getNeVersionId());
            lqw.ne(ModifyApply::getRecordStatus, "cancel");
        }

        if (StrUtil.isNotBlank(bo.getDeviceCode()) && StrUtil.isNotBlank(bo.getDeviceName())) {
            lqw.or(lqw1 -> lqw1.eq(ModifyApply::getDocClass, bo.getDocClass())
                    .ne(StringUtils.isNotBlank(bo.getApplyId()), ModifyApply::getId, bo.getApplyId())
                    .ne(StringUtils.isNotBlank(bo.getNeVersionId()), ModifyApply::getVersionId, bo.getNeVersionId())
                    .ne(ModifyApply::getRecordStatus, "cancel")
            );
        }
        List<ModifyApply> modifyApplyList = modifyApplyMapper.selectList(lqw);

        if (CollUtil.isNotEmpty(modifyApplyList)) {
            // 版本为空的情况
            boolean hasEmptyVersion = modifyApplyList.stream()
                    .anyMatch(item -> StrUtil.isBlank(item.getVersionId()));
            if (hasEmptyVersion) {
                return true;
            }

            // 版本不为空的情况
            List<ModifyApply> notVersionModifyApplyList = modifyApplyList.stream()
                    .filter(item -> StrUtil.isNotBlank(item.getVersionId()))
                    .collect(Collectors.toList());

            if (CollUtil.isNotEmpty(notVersionModifyApplyList)) {
                // 获取版本ID列表
                List<String> versionIdList = notVersionModifyApplyList.stream()
                        .map(ModifyApply::getVersionId)
                        .collect(Collectors.toList());

                // 查询失效状态的版本
                List<Version> versionList = this.list(new LambdaQueryWrapper<Version>()
                        .in(Version::getId, versionIdList)
                        .eq(Version::getStatus, Constants.TWO));

                if (CollUtil.isNotEmpty(versionList)) {
                    // 查找是否有ModifyApply的VersionId不在失效状态的版本中
                    boolean isValid = notVersionModifyApplyList.stream()
                            .anyMatch(modifyApply -> versionList.stream()
                                    .noneMatch(version -> modifyApply.getVersionId().equals(version.getId())));
                    return isValid;
                } else {
                    return true;
                }
            }
        }
        return false;
    }

    @Override
    public List<VersionVo> queryList(VersionBo bo) {
        return listVo(buildQueryWrapper(bo));
    }

    private LambdaQueryWrapper<Version> buildQueryWrapper(VersionBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<Version> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getStandardId()), Version::getStandardId, bo.getStandardId());
        lqw.eq(StringUtils.isNotBlank(bo.getDocId()), Version::getDocId, bo.getDocId());
        lqw.eq(StringUtils.isNotBlank(bo.getApplyId()), Version::getApplyId, bo.getApplyId());
        lqw.eq(StringUtils.isNotBlank(bo.getVersionValue()), Version::getVersionValue, bo.getVersionValue());
        lqw.eq(bo.getStartDate() != null, Version::getStartDate, bo.getStartDate());
        lqw.eq(bo.getEndDate() != null, Version::getEndDate, bo.getEndDate());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), Version::getStatus, bo.getStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getReason()), Version::getReason, bo.getReason());
        lqw.orderByAsc(Version::getApplyTime);

        return lqw;
    }

    @Override
    public Boolean insertByBo(VersionBo bo) {
        Version add = BeanUtil.toBean(bo, Version.class);
        validEntityBeforeSave(add);
        boolean flag = save(add);
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    @Override
    public Boolean updateByBo(VersionBo bo) {
        Version update = BeanUtil.toBean(bo, Version.class);
        validEntityBeforeSave(update);
        return updateById(update);
    }

    /**
     * 保存前的数据校验
     *
     * @param entity 实体类数据
     */
    private void validEntityBeforeSave(Version entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return removeByIds(ids);
    }

    @Override
    public List<VersionVo> selectVersionListByAppId(String applyId) {
        return this.baseMapper.selectVersionListByAppId(applyId);
    }

    @Override
    public List<VersionVo> selectVersionList(List<String> applyId) {
        return this.baseMapper.selectVersionList(applyId);
    }

    @Override
    public List<VersionVo> selectVersionListByDocId(String docId) {
        return listVo(new LambdaQueryWrapper<Version>().eq(Version::getDocId, docId).orderByAsc(Version::getReleaseTime));
    }

    @Override
    public List<VersionVo> selectVersionListByDocIdAndVersionId(String docId, String versionId) {
        return this.baseMapper.selectVersionListByDocIdAndVersionId(docId, versionId);
    }


    @Override
    public TableDataInfo<VersionVo> selectRecordFileCompany(VersionBo bo) {
        Page<VersionVo> page = PageUtils.buildPage();
        Page<VersionVo> iPage = this.baseMapper.selectRecordFileCompany(page, bo);
        for (VersionVo vVo : iPage.getRecords()) {
            vVo.setDeptName(iSysDeptService.getDeptName(vVo.getDeptId()));
            vVo.setNickName(sysUserService.getNickName(vVo.getUserName()));
        }
        String deptId = SecurityUtils.getDeptId();
        String userName = SecurityUtils.getUsername();
        //判断当前登录用户是不是公司文件管理员
        boolean isQa = sysUserService.isQa(SecurityUtils.getUsername());
        iPage.getRecords().forEach(versionVo -> {
            if (isQa) {
                //是编制部门的 或者 文件分发类型是公司的 或者是公司文件管理员 有权限查看
                versionVo.setHasPerms(true);
            } else {
                versionVo.setHasPerms(baseMapper.checkAuthByDis(versionVo.getId(), userName, deptId) > 0);
            }
        });
        return PageUtils.buildDataInfo(iPage);
    }

    @Override
    public TableDataInfo<VersionVo> selectRecordFileCompanyOtherDept(VersionBo bo) {
        Page<VersionVo> page = PageUtils.buildPage();
        Page<VersionVo> iPage = this.baseMapper.selectRecordFileCompanyOtherDept(page, bo);
        for (VersionVo vVo : iPage.getRecords()) {
            vVo.setDeptName(iSysDeptService.getDeptName(vVo.getDeptId()));
            vVo.setNickName(sysUserService.getNickName(vVo.getUserName()));
        }
        return PageUtils.buildDataInfo(iPage);
    }

    @Override
    public boolean docIdExist(String docId) {
        LambdaQueryWrapper<Version> lqw = new LambdaQueryWrapper<>();
        lqw.eq(Version::getDocId, docId);
        long sum = this.count(lqw);
        LambdaQueryWrapper<ModifyApplyLink> lqwLink = new LambdaQueryWrapper<>();
        lqwLink.eq(ModifyApplyLink::getDocId, docId);
        long sumLink = modifyApplyLinkService.count(lqwLink);
        LambdaQueryWrapper<DocLinkLog> lqwLinkLog = new LambdaQueryWrapper<>();
        lqwLinkLog.eq(DocLinkLog::getLinkCode, docId);
        long sumLinkLog = docLinkLogService.count(lqwLinkLog);
        return sum > 0 || sumLink > 0 || sumLinkLog > 0;
    }

    @Override
    public boolean isbom(String id) {
        return false;
    }

    @Override
    public boolean checkAuthByDis(String versionId, String userName, String deptId) {
        return baseMapper.checkAuthByDis(versionId, userName, deptId) > 0;
    }

    @Override
    public List<VersionVo> versionAndStandardList(StandardBo bo) {
        return this.baseMapper.versionAndStandardList(bo);
    }

    @Override
    public String getDocNameByVersionId(String versionId) {
        return baseMapper.getDocNameByVersionId(versionId);
    }

    @Override
    public Version getValidVersionByDocId(String docId) {
        return getOne(new LambdaQueryWrapper<Version>()
                .eq(Version::getDocId, docId).eq(Version::getStatus, Constants.ONE)
                .select(Version::getDocName, Version::getDocId, Version::getId, Version::getVersionValue, Version::getDeptId));
    }

    @Override
    public List<Version> selectRecordVersionList(String versionId) {
        return baseMapper.selectRecordVersionList(versionId);
    }

    @Override
    public List<VersionExportVo> transformData(List<VersionExportVo> listVo) {
        return transformVersionExportData(listVo);
    }


    public List<VersionPartExportVo> transformVersionPartData(List<VersionPartExportVo> listVo) {
        return transformVersionExportData(listVo);
    }


    /**
     * 不是记录
     *
     * @param listVo
     * @param <T>
     * @return
     */
    public <T extends VersionExportVo> List<T> transformVersionExportData(List<T> listVo) {
        List<DocClass> docClassList = iDocClassService.list(new LambdaQueryWrapper<DocClass>().eq(DocClass::getClassStatus, DocConstants.STATUS_ONE));
        List<SysDictData> factoryDictList = dictTypeService.selectDictDataByType(DictConstants.APPLY_FACTORY_TYPE);

        for (T versionExportVo : listVo) {
            //versionExportVo.setStatus(Constants.ONE.equals(versionExportVo.getStatus()) ? Constants.IN_EFFECT : Constants.INVALID);
            docClassList.forEach(item -> {
                if (item.getId().equals(versionExportVo.getDocClass())) {
                    versionExportVo.setDocClassName(item.getClassName());
                }
            });

        }
        return listVo;
    }


    /**
     * 外来文件转换
     *
     * @param listVo
     * @return
     */
    public List<VersionForeignExportVo> transformForeignData(List<VersionForeignExportVo> listVo) {
        return transformVersionForeignExportVoData(listVo);
    }


    /**
     * 外来文件
     *
     * @param listVo
     * @param <T>
     * @return
     */
    public <T extends VersionForeignExportVo> List<T> transformVersionForeignExportVoData(List<T> listVo) {
        List<DocClass> docClassList = iDocClassService.list(new LambdaQueryWrapper<DocClass>().eq(DocClass::getClassStatus, DocConstants.STATUS_ONE));
        List<SysDictData> factoryDictList = dictTypeService.selectDictDataByType(DictConstants.APPLY_FACTORY_TYPE);

        for (T versionExportVo : listVo) {
            versionExportVo.setStatus(Constants.ONE.equals(versionExportVo.getStatus()) ? Constants.IN_EFFECT : Constants.INVALID);
            docClassList.forEach(item -> {
                if (item.getId().equals(versionExportVo.getDocClass())) {
                    versionExportVo.setDocClassName(item.getClassName());
                }
            });

        }
        return listVo;
    }

    /**
     * 记录文件
     *
     * @param listVo
     * @param <T>
     * @return
     */
    public <T extends VersionRecordExportVo> List<T> transformVersionRecordExportVoData(List<T> listVo) {
        List<DocClass> docClassList = iDocClassService.list(new LambdaQueryWrapper<DocClass>().eq(DocClass::getClassStatus, DocConstants.STATUS_ONE));

        for (T versionExportVo : listVo) {
            versionExportVo.setStatus(Constants.ONE.equals(versionExportVo.getStatus()) ? Constants.IN_EFFECT : Constants.INVALID);
            docClassList.forEach(item -> {
                if (item.getId().equals(versionExportVo.getDocClass())) {
                    versionExportVo.setDocClassName(item.getClassName());
                }
            });
        }
        return listVo;
    }


    public List<VersionRecordExportVo> transformRecordData(List<VersionRecordExportVo> listVo) {
        return transformVersionRecordExportVoData(listVo);
    }


    public List<VersionRecordPartExportVo> transformRecordPartData(List<VersionRecordPartExportVo> listVo) {
        return transformVersionRecordExportVoData(listVo);
    }

    /**
     * 导出
     *
     * @param bo
     * @param response
     * @param exportName       导出名称
     * @param invokerQueryList 来源查询类型  listOtherDept调用查询listOtherDept，list调用list
     */
    @Override
    public void exportForList(VersionBo bo, HttpServletResponse response, String exportName, String invokerQueryList) {
        List<String> excludeFieldList = basicFormRuleService.getHiddenColumns(bo.getDocClass());
        List<String> docClassList = bo.getDocClassList();
        if (docClassList != null) {
            docClassList.add(bo.getDocClass());
            bo.setDocClass(null);
            bo.setDocClassList(docClassList);
        }
        if (Constants.VERSION_QUERY_FOREIGN_LIST.equals(invokerQueryList)) {
            // 外来文件去掉复审日期
            excludeFieldList.add("reviewTime");
            excludeFieldList.add("endDate");
            //外来文件
            List<VersionForeignExportVo> versionForeignExportVoList = BeanUtil.copyToList(queryPageList(bo).getRows(), VersionForeignExportVo.class);
            versionForeignExportVoList = this.transformForeignData(versionForeignExportVoList);
            ExcelUtil.exportExcel(versionForeignExportVoList, exportName, VersionForeignExportVo.class, response, excludeFieldList);
            return;
        } else {
            // 记录文件去掉修订日期
            excludeFieldList.add("revisionDate");
            excludeFieldList.add("endDate");
        }

        List<VersionExportVo> listVo = getVersionPartListVo(invokerQueryList, bo, VersionExportVo.class);
        listVo = this.transformData(listVo);
        ExcelUtil.exportExcel(listVo, exportName, VersionExportVo.class, response, excludeFieldList);
        return;
    }

    /**
     * 根据关联记录新增台账版本数据
     *
     * @param standard
     * @param version
     * @param applyLink
     * @return
     */
    @Override
    public Version saveCopyByLink(Standard standard, Version version, ModifyApplyLink applyLink) {
        Standard recordStandard = new Standard();
        BeanUtil.copyProperties(standard, recordStandard);
        recordStandard.setId(null);
        recordStandard.setFileId(applyLink.getFileId());
        recordStandard.setDocClass(applyLink.getDocClass());
        recordStandard.setDocName(applyLink.getDocName());
        recordStandard.setDocClassType(iDocClassService.getClassType(applyLink.getDocClass()).getId());
        standardMapper.insert(recordStandard);
        //版本
        Version recordVersion = new Version();
        BeanUtil.copyProperties(version, recordVersion);
        recordVersion.setId(null);
        recordVersion.setStandardId(recordStandard.getId());
        recordVersion.setDocId(applyLink.getDocId());
        recordVersion.setDocName(applyLink.getDocName());
        recordVersion.setVersionValue(applyLink.getVersionValue());
        recordVersion.setFileId(applyLink.getFileId());
        recordVersion.setUpVersionId(version.getId());
        recordVersion.setParentDocId(version.getDocId());
        recordVersion.setClassType(LinkTypeEnum.RECORD.name());
        save(recordVersion);
        return recordVersion;
    }

    private <T> List<T> getVersionPartListVo(String invokerQueryList, VersionBo bo, Class<T> clazz) {
        if (Constants.VERSION_QUERY_OTHER_DEPT.equals(invokerQueryList)) {
            // 对应的二级部门
            bo.setDeptId(SecurityUtils.getLoginUser().getDeptId());
            bo.setUserName(SecurityUtils.getUsername());
            return BeanUtil.copyToList(queryPageOtherDeptList(bo).getRows(), clazz);
        } else if (Constants.VERSION_QUERY_LIST.equals(invokerQueryList)) {
            return BeanUtil.copyToList(queryPageList(bo).getRows(), clazz);
        }
        return Collections.emptyList();
    }


    @Override
    public TableDataInfo<VersionDTO> pageApi(VersionBo bo, String limitDocClass) {
        Page<VersionDTO> page = PageUtils.buildPage();
        Page<VersionDTO> iPage = baseMapper.pageApi(page, bo, limitDocClass);
        return PageUtils.buildDataInfo(iPage);
    }

    @Override
    public TableDataInfo<VersionVo> queryPageListAll(VersionBo bo) {
        Page<VersionVo> page = PageUtils.buildPage(bo.getPageNum(), bo.getPageSize(), bo.getOrderByColumn(), bo.getIsAsc());
        Page<VersionVo> iPage = baseMapper.selectVersionPageAll(page, bo);
        return PageUtils.buildDataInfo(iPage);
    }

    @Override
    public void exportListAll(VersionBo bo, HttpServletResponse response, String exportName) {
        List<String> excludeFieldList = basicFormRuleService.getHiddenColumns(bo.getDocClass());
        List<String> docClassList = bo.getDocClassList();
        if (docClassList != null) {
            docClassList.add(bo.getDocClass());
            bo.setDocClass(null);
            bo.setDocClassList(docClassList);
        }
        // 记录文件去掉修订日期
        excludeFieldList.add("revisionDate");
        excludeFieldList.add("endDate");
        List<VersionExportVo> listVo = BeanUtil.copyToList(queryPageListAll(bo).getRows(), VersionExportVo.class);
        listVo = this.transformData(listVo);
        ExcelUtil.exportExcel(listVo, exportName, VersionExportVo.class, response, excludeFieldList);
    }

    @Override
    public void updateVersionData(Version version, List<DocLinkLog> linkList, String docClass) throws Exception {
        //主文件
        Optional<DocLinkLog> optional = linkList.stream().filter(item -> LinkTypeEnum.DOC.name().equals(item.getLinkType())).findFirst();
        if (optional.isPresent()) {
            DocLinkLog docLinkLog = optional.get();
            //查询所有关联了该主文件的记录 (除了自己引用，其他主文件可能也会关联)
            DocLinkLogBo bo = new DocLinkLogBo();
            bo.setVersionId(version.getId());
            bo.setNeLinkType(LinkTypeEnum.APPENDIX.name());
            List<DocLinkLog> docLinkList = docLinkLogService.queryLink(bo);
            //替换附件
            docLinkList.forEach(item -> {
                item.setFileId(docLinkLog.getFileId());
                item.setFileName(docLinkLog.getFileName());
            });
            //更新
            docLinkLogService.updateBatchById(docLinkList);
            version.setFileId(docLinkLog.getFileId());
            iDocEditLogService.saveLogByVersionId(version.getId(), docLinkLog.getFileId(), docLinkLog.getFileName(), docLinkLog.getProtoFileId(), OnlineTypeEnum.ERRATA.getCode(), I18nUtils.getTitle(CommonI18nConstant.ONLINE_TYPE_ENUM_ERRATA));
        }
        updateById(version);
        //版本附件
        List<DocLinkLog> appendixLinkList = linkList.stream().filter(item -> LinkTypeEnum.APPENDIX.name().equals(item.getLinkType())).collect(Collectors.toList());
        //将原先的进行删除
        docLinkLogService.deleteLinkLog(LinkTypeEnum.APPENDIX.name(), null, version.getId());
        //增加新增的
        Version appendix = BeanUtil.copyProperties(version,Version.class);
        appendixLinkList.forEach(item -> {
            appendix.setFileId(item.getFileId());
            appendix.setDocName(item.getFileName());
            docLinkLogService.handApplyLinkAdd(appendix, item.getDocClass(), LinkTypeEnum.APPENDIX.name(), appendix.getId(),item.getProtoFileId());
            iDocEditLogService.saveLogByVersionId(version.getId(), item.getFileId(), item.getFileName(), item.getProtoFileId(), OnlineTypeEnum.ERRATA.getCode(), I18nUtils.getTitle(CommonI18nConstant.ONLINE_TYPE_ENUM_ERRATA));
        });

        //勘误 操作重新保存ES文件
        try {
            elasticsearchService.docEditPublish(version,version);
        } catch (Exception e) {
            log.error("ES保存失败", e);
        }

        //走过流程的 生成封面
        if (StringUtils.isNotEmpty(version.getApplyId())) {
            fileSignatureService.coverEffectiveVersion(version,docClass);
        }
        //签章
        fileSignatureService.refreshSignature(version, docClass, CommonStatusEnum.RETAIN.getStatus().equals(version.getStatus())?ApplyTypeEnum.RETAIN.name():ApplyTypeEnum.toType(version.getChangeType()).name());
    }

    @Override
    public void refreshSignature(Version version, String docClass,String bizType) throws Exception {
        fileSignatureService.refreshSignature(version, docClass,bizType);
    }

    @Override
    public void checkPerms(VersionVo versionVo,boolean isQa,LoginUser user){
        if (isQa || SecurityUtils.isAdmin(SecurityUtils.getUserId()) || user.getUsername().equals(versionVo.getCreateBy()) || user.getUsername().equals(versionVo.getUserName()) || user.getAncestors().contains(versionVo.getDeptId())) {
            //是编制部门的 或者 文件分发类型是公司的 或者是公司文件管理员 有权限查看 或者 自己发起的文件自己也能查看
            versionVo.setHasPerms(true);
        }else{
            versionVo.setHasPerms(baseMapper.checkAuthByDis(versionVo.getId(), user.getUsername(), user.getDeptId()) > 0);
        }
        if (!versionVo.getHasPerms()) {
            versionVo.setIsBorrow(iBorrowApplyItemService.isValidBorrowByVersionId(versionVo.getId(),user.getUsername()));
        }
    }
}

package com.rzdata.process.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rzdata.config.CustomConfig;
import com.rzdata.eventbus.ProcessResultEvent;
import com.rzdata.framework.constant.CommonI18nConstant;
import com.rzdata.framework.constant.Constants;
import com.rzdata.framework.constant.NumberConstants;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.core.domain.entity.SysDept;
import com.rzdata.framework.core.domain.entity.SysUser;
import com.rzdata.framework.core.domain.model.LoginUser;
import com.rzdata.framework.core.mybatisplus.core.ServicePlusImpl;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.utils.I18nUtils;
import com.rzdata.framework.utils.PageUtils;
import com.rzdata.framework.utils.SecurityUtils;
import com.rzdata.framework.utils.StringUtils;
import com.rzdata.process.domain.*;
import com.rzdata.process.domain.bo.ReviewApplyItemBo;
import com.rzdata.process.domain.bo.WorkflowApplyLogBo;
import com.rzdata.process.domain.vo.*;
import com.rzdata.process.enums.ApplyStatusEnum;
import com.rzdata.process.enums.ApplyTypeEnum;
import com.rzdata.process.enums.LinkTypeEnum;
import com.rzdata.process.enums.RecordStatusEnum;
import com.rzdata.process.mapper.*;
import com.rzdata.process.service.*;
import com.rzdata.process.utils.DateUtils;
import com.rzdata.system.service.ISysDeptService;
import com.rzdata.system.service.ISysUserService;
import com.rzdata.system.service.WorkflowService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.text.DecimalFormat;
import java.text.Format;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 流程申请记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-01-05
 */
@Service
public class WorkflowApplyLogServiceImpl extends ServicePlusImpl<WorkflowApplyLogMapper, WorkflowApplyLog, WorkflowApplyLogVo> implements IWorkflowApplyLogService {

    @Autowired
    StandardMapper standardMapper;

    @Autowired
    VersionMapper versionMapper;

    @Autowired
    private ISysUserService iSysUserService;

    @Autowired
    ModifyApplyMapper modifyApplyMapper;

    @Autowired
    private CustomConfig customConfig;

    @Autowired
    private ISysDeptService sysDeptService;

    @Autowired
    private WorkflowService workflowService;

    @Autowired
    IReviewApplyService iReviewApplyService;

    @Override
    public WorkflowApplyLogVo queryById(String id){
        return getVoById(id);
    }

    @Override
    public TableDataInfo<WorkflowApplyLogVo> queryPageList(WorkflowApplyLogBo bo) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        Set<String> authorities = loginUser.getPermissions();
        //可以查看同部门申请的流程，管理人员文控或管理者角色可以查看所有数据；
        if(!(authorities.contains(Constants.DOCUMENT_APPLY_DATA_ALL)||SecurityUtils.isAdmin(loginUser.getUserId()))){
            bo.setDeptId(loginUser.getDeptId());
        }
        Page<WorkflowApplyLogVo> iPage = baseMapper.queryPageList(PageUtils.buildPage(), bo,customConfig.getBpmDataSource());
        // 获取所有用户名和部门ID以便批量查询
        List<String> userNames = iPage.getRecords().stream()
                .map(WorkflowApplyLogVo::getUserName)
                .distinct()
                .collect(Collectors.toList());

        List<String> deptIds = iPage.getRecords().stream()
                .map(WorkflowApplyLogVo::getDeptId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());


        // 批量查询用户信息并转换为 Map<userName, nickName>
        Map<String, String> nickNameMap = CollUtil.isNotEmpty(userNames)
                ? iSysUserService.list(
                        new LambdaQueryWrapper<SysUser>().in(SysUser::getUserName, userNames))
                .stream()
                .collect(Collectors.toMap(SysUser::getUserName, SysUser::getNickName, (existing, replacement) -> existing))
                : Collections.emptyMap(); // 如果 userNames 为空，返回空的 Map

        // 批量查询部门信息并转换为 Map<deptId, deptName>
        Map<String, String> deptNameMap = CollUtil.isNotEmpty(deptIds)
                ? sysDeptService.list(
                        new LambdaQueryWrapper<SysDept>().in(SysDept::getDeptId, deptIds))
                .stream()
                .collect(Collectors.toMap(SysDept::getDeptId, SysDept::getDeptName, (existing, replacement) -> existing))
                : Collections.emptyMap(); // 如果 deptIds 为空，返回空的 Map

        // 填充数据
        iPage.getRecords().forEach(item -> {
            if(CollUtil.isNotEmpty(nickNameMap)){
                item.setNickName(nickNameMap.getOrDefault(item.getUserName(), "未知用户"));
            }
            if(CollUtil.isNotEmpty(deptNameMap)){
                item.setDeptName(deptNameMap.getOrDefault(item.getDeptId(), "未知部门"));
            }
        });
        return PageUtils.buildDataInfo(iPage);
    }

    @Override
    public List<WorkflowApplyLogVo> queryList(WorkflowApplyLogBo bo) {
        return listVo(buildQueryWrapper(bo));
    }

    private LambdaQueryWrapper<WorkflowApplyLog> buildQueryWrapper(WorkflowApplyLogBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<WorkflowApplyLog> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getApplyClass()), WorkflowApplyLog::getApplyClass, bo.getApplyClass());
        lqw.eq(StringUtils.isNotBlank(bo.getApplyStatus()), WorkflowApplyLog::getApplyStatus, bo.getApplyStatus());
        lqw.like(StringUtils.isNotBlank(bo.getDocName()), WorkflowApplyLog::getDocName, bo.getDocName());
        lqw.eq(StringUtils.isNotBlank(bo.getDocId()), WorkflowApplyLog::getDocId, bo.getDocId());
        lqw.eq(StringUtils.isNotBlank(bo.getDocClass()), WorkflowApplyLog::getDocClass, bo.getDocClass());
        lqw.eq(StringUtils.isNotBlank(bo.getVersionId()), WorkflowApplyLog::getVersionId, bo.getVersionId());
        lqw.eq(StringUtils.isNotBlank(bo.getVersionValue()), WorkflowApplyLog::getVersionValue, bo.getVersionValue());
        lqw.eq(bo.getDeptId() != null, WorkflowApplyLog::getDeptId, bo.getDeptId());
        lqw.eq(StringUtils.isNotBlank(bo.getProcInstId()), WorkflowApplyLog::getProcInstId, bo.getProcInstId());
        lqw.eq(StringUtils.isNotBlank(bo.getProcDefKey()), WorkflowApplyLog::getProcDefKey, bo.getProcDefKey());
        lqw.eq(bo.getApplyTime() != null, WorkflowApplyLog::getApplyTime, bo.getApplyTime());
        lqw.eq(WorkflowApplyLog::getSender, SecurityUtils.getUsername());
        lqw.orderByDesc(WorkflowApplyLog:: getApplyTime);
        return lqw;
    }

    @Override
    public Boolean insertByBo(WorkflowApplyLogBo bo) {
        WorkflowApplyLog add = BeanUtil.toBean(bo, WorkflowApplyLog.class);
        LoginUser loginUser = null;
        try {
            // 设置当前登录用户
            loginUser = SecurityUtils.getLoginUser();
            add.setSender(loginUser.getUsername());
            add.setDeptId(loginUser.getDeptId());
        } catch (Exception e) {
            // 兼容XXJOB自动发起流程场景
            add.setSender(bo.getSender());
            add.setDeptId(bo.getDeptId());
        }
        add.setApplyTime(new Date());
//        add.setApplyStatus(NumberConstants.ZERO+"");
        if (ObjectUtil.isEmpty(add.getDocId())) {
            add.setDocId(bo.getDocIds());
        }
        if (ObjectUtil.isNotEmpty(bo.getDocId()) || ObjectUtil.isNotEmpty(bo.getDocIds())) {
            QueryWrapper<Standard> standardQueryWrapper = new QueryWrapper<>();
            if (ObjectUtil.isNotEmpty(bo.getDocId())) {
                standardQueryWrapper.lambda().eq(Standard:: getId, bo.getDocId());
            }
            if (ObjectUtil.isNotEmpty(bo.getDocIds())) {
                standardQueryWrapper.lambda().in(Standard:: getId, bo.getDocIds().split(","));
            }
            standardQueryWrapper.lambda().eq(Standard:: getStatus, NumberConstants.ONE);
            List<Standard> standardList = standardMapper.selectList(standardQueryWrapper);
            if (ObjectUtil.isNotEmpty(standardList)) {
                String docClass = String.join(",", standardList.stream().map(x -> x.getDocClass()).distinct().collect(Collectors.toList()));
                String docName = String.join(",", standardList.stream().map(x -> x.getDocName()).distinct().collect(Collectors.toList()));
                add.setDocClass(docClass);
                add.setDocName(docName);
            }
            QueryWrapper<Version> versionQueryWrapper = new QueryWrapper<>();
            if (ObjectUtil.isNotEmpty(bo.getDocId())) {
                versionQueryWrapper.lambda().eq(Version:: getDocId, bo.getDocId());
            }
            if (ObjectUtil.isNotEmpty(bo.getDocIds())) {
                versionQueryWrapper.lambda().in(Version:: getDocId, bo.getDocIds().split(","));
            }
            versionQueryWrapper.lambda().eq(Version:: getStatus, NumberConstants.ONE);
            List<Version> versionList = versionMapper.selectList(versionQueryWrapper);
            if (ObjectUtil.isNotEmpty(versionList)) {
                String versionId = String.join(",", versionList.stream().map(x -> x.getId()).distinct().collect(Collectors.toList()));
                String versionValue = String.join(",", versionList.stream().map(x -> x.getVersionValue()).distinct().collect(Collectors.toList()));
                add.setVersionId(versionId);
                add.setVersionValue(versionValue);
            }
        }
        validEntityBeforeSave(add);
        boolean flag = save(add);
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    @Override
    public Boolean updateByBo(WorkflowApplyLogBo bo) {
        WorkflowApplyLog update = BeanUtil.toBean(bo, WorkflowApplyLog.class);
        validEntityBeforeSave(update);
        return updateById(update);
    }

    /**
     * 保存前的数据校验
     *
     * @param entity 实体类数据
     */
    private void validEntityBeforeSave(WorkflowApplyLog entity){
        //TODO 做一些数据校验,如唯一约束
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return removeByIds(ids);
    }

    @Override
    public AjaxResult<String> selectStatus(String versionId,boolean notInDraft){
        AjaxResult<String> result = selectStatusByDocId(versionId, notInDraft);
        if ("0".equals(result.getData())) {
            return selectStatusRecord(versionId);
        }
        return result;
    }

    @Override
    public AjaxResult<String> selectStatusByDocId(String versionId,boolean notInDraft) {
        QueryWrapper<ModifyApply> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ModifyApply:: getVersionId, versionId);
        queryWrapper.lambda().ne(ModifyApply:: getRecordStatus, RecordStatusEnum.DONE.getCode());
        queryWrapper.lambda().ne(ModifyApply:: getRecordStatus, RecordStatusEnum.CANCEL.getCode());
        queryWrapper.lambda().ne(notInDraft,ModifyApply:: getRecordStatus, RecordStatusEnum.DRAFT.getCode());
        queryWrapper.lambda().last("limit 1");
        queryWrapper.lambda().select(ModifyApply::getVersionId,ModifyApply::getChangeType,ModifyApply::getUserName);
        ModifyApply modifyApply = modifyApplyMapper.selectOne(queryWrapper);
        if (modifyApply!=null) {
            //因为修订时文件名称可以修改 modifyApply保存的是修改后的 只能重新拿取
            String docName = versionMapper.getDocNameByVersionId(versionId);
            String nickName = iSysUserService.getNickName(modifyApply.getUserName());
            return AjaxResult.success("【"+docName+"】"+ I18nUtils.getTitle(CommonI18nConstant.WORKFLOW_APPLY_ALREADY)+workflowService.addPrefix("",modifyApply.getChangeType())+I18nUtils.getTitle(CommonI18nConstant.WORKFLOW_APPLY_OCCUPY_TEXT)+"【"+nickName+"】","1");
        }else {
            ReviewApplyVo rVo = iReviewApplyService.selectDocStatus(versionId);
            if (rVo!=null) {
                //因为修订时文件名称可以修改 modifyApply保存的是修改后的 只能重新拿取
                String docName = versionMapper.getDocNameByVersionId(versionId);
                String nickName = iSysUserService.getNickName(rVo.getCreateBy());
                return AjaxResult.success("【"+docName+"】"+ I18nUtils.getTitle(CommonI18nConstant.WORKFLOW_APPLY_ALREADY)+workflowService.addPrefix("",rVo.getApplyType())+I18nUtils.getTitle(CommonI18nConstant.WORKFLOW_APPLY_OCCUPY_TEXT)+"【"+nickName+"】",rVo.getId());
            }
        }
        return AjaxResult.success(I18nUtils.getTitle(CommonI18nConstant.WORKFLOW_APPLY_PERMIT_ACCESS), "0");
    }

    @Override
    public AjaxResult<String> selectStatusRecord(String versionId) {
        Version version= versionMapper.selectOne(new LambdaQueryWrapper<Version>().eq(Version::getId,versionId)
                .select(Version::getId,Version::getStandardId,Version::getUpVersionId,Version::getClassType,Version::getDocName));
        String docName = version.getDocName();
        if(LinkTypeEnum.RECORD.name().equals(version.getClassType())){
            //是记录文件 主文件不能在流程中
            AjaxResult<String> ajaxResult=selectStatusByDocId(version.getUpVersionId(),true);
            if ("0".equals(ajaxResult.getData())) {
                ajaxResult.setMsg("【"+docName+"】"+I18nUtils.getTitle(CommonI18nConstant.WORKFLOW_APPLY_SUPERIOR_FILE) + ajaxResult.getMsg());
                ajaxResult.setData("3");
            }
            return ajaxResult;
        }else{
            //是主文件 记录不能在流程中
            LambdaQueryWrapper<Version> lambdaQueryWrapper= new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(Version::getUpVersionId,versionId);
            lambdaQueryWrapper.eq(Version::getStatus, Constants.ONE);
            lambdaQueryWrapper.select(Version::getId,Version::getDocName,Version::getUserName);
            List<Version> list = versionMapper.selectList(lambdaQueryWrapper);
            if (list!=null&&list.size()>0) {
                String msg = "";
                for (Version v:list) {
                    AjaxResult<String> ajaxResult=selectStatusByDocId(v.getId(),false);
                    if (!"0".equals(ajaxResult.getData())) {
                        msg += ajaxResult.getMsg();
                    }
                }
                if (StringUtils.isNotEmpty(msg)) {
                    msg = "【"+docName+"】"+I18nUtils.getTitle(CommonI18nConstant.WORKFLOW_APPLY_SUB_RECORD_FILE)+msg;
                    return AjaxResult.success(msg,"4");
                }
            }
        }
        return AjaxResult.success(I18nUtils.getTitle(CommonI18nConstant.WORKFLOW_APPLY_PERMIT_ACCESS), "0");
    }

    @Override
    public String queryProcessStatus(String docId) {
        return this.baseMapper.queryProcessStatus(docId);
    }

    @Override
    public WorkflowApplyLog queryByProcInstId(String procInstId) {
        QueryWrapper<WorkflowApplyLog> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(WorkflowApplyLog:: getProcInstId, procInstId);
        WorkflowApplyLog obj = this.baseMapper.selectOne(queryWrapper);
        return obj;
    }

    /**
     * 根据类别查询流水号
     * @param applyClass
     * @return
     */
    @Override
    public String selectApplySerial(String applyClass) {
        Format fm3 = new DecimalFormat("00");
        String encodeSp1 = DateUtil.format(new Date(), "yyyyMMdd");
        String encodeSp2 = fm3.format(0);
        String applyTime = DateUtils.getDate();
        int number = baseMapper.selectApplySerial(applyClass, applyTime);
        encodeSp2 = fm3.format(number + 1);
        return  StrUtil.format("{}{}", encodeSp1, encodeSp2);
    }

    @Override
    public void updateStatusByBusId(WorkflowApplyLog waLog, ProcessResultEvent event) {
        WorkflowApplyLog log= getById(waLog.getId());
        if (ObjectUtil.isEmpty(log)) {
            log = new WorkflowApplyLog();
            BeanUtil.copyProperties(waLog,log);
            log.setId(event.getApplyId());
            log.setProcDefKey(event.getProcessInst().getProcDefId());
            log.setProcInstId(event.getProcessInst().getProcInstId());
            log.setApplyClass(event.getBizType().toLowerCase(Locale.ROOT));
            log.setChangeType(event.getApplyType());
            log.setApplyTime(DateUtil.date());
            save(log);
        }else {
            updateById(waLog);
        }
    }
}

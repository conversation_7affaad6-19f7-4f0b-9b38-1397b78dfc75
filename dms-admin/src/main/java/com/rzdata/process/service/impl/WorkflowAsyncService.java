package com.rzdata.process.service.impl;

import com.blueland.bpmclient.model.BpmClientInputModel;
import com.blueland.bpmclient.model.ProcessInstanceModel;
import com.rzdata.config.CustomConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import com.rzdata.system.mapper.WorkflowMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class WorkflowAsyncService {

    @Resource
    private WorkflowMapper workflowMapper;

    @Autowired
    private CustomConfig customConfig;
    /**
     * @description 异步修改流程标题
     * <AUTHOR>
     * @param bpmClientInputModel bpmClientInputModel
     * @param processInst processInst
     * @updateTime 2024/11/27
     */
    @Async
    public void updateProcTitleAsync(BpmClientInputModel bpmClientInputModel, ProcessInstanceModel processInst){
        try {
            Thread.sleep(2000);
            workflowMapper.updateTaskTitle(bpmClientInputModel.getWf_procTitle(),processInst.getProcInstId(), customConfig.getBpmDataSource());
            workflowMapper.updateFlowTitle(bpmClientInputModel.getWf_procTitle(),processInst.getProcInstId(), customConfig.getBpmDataSource());
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
    }
}

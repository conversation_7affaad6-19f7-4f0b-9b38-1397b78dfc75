package com.rzdata.process.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.blueland.bpmclient.BpmClient;
import com.blueland.bpmclient.model.*;
import com.rzdata.config.CustomConfig;
import com.rzdata.framework.constant.CommonI18nConstant;
import com.rzdata.framework.constant.DocMsgConstants;
import com.rzdata.framework.core.domain.entity.SysUser;
import com.rzdata.framework.core.mybatisplus.core.ServicePlusImpl;
import com.rzdata.framework.core.page.PagePlus;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.utils.I18nUtils;
import com.rzdata.framework.utils.PageUtils;
import com.rzdata.framework.utils.StringUtils;
import com.rzdata.process.domain.DocWorkflowLogItem;
import com.rzdata.process.domain.ModifyApply;
import com.rzdata.process.domain.WorkflowLog;
import com.rzdata.process.domain.bo.BpmClientInputModelBo;
import com.rzdata.process.domain.bo.ProcessWorkFlowBo;
import com.rzdata.process.domain.bo.WorkflowLogBo;
import com.rzdata.process.domain.dto.UniteWorkTaskDTO;
import com.rzdata.process.domain.dto.UserDTO;
import com.rzdata.process.domain.vo.WorkflowLogVo;
import com.rzdata.process.enums.RecordStatusEnum;
import com.rzdata.process.mapper.ModifyApplyMapper;
import com.rzdata.process.mapper.WorkflowLogMapper;
import com.rzdata.process.service.IDocWorkflowLogItemService;
import com.rzdata.process.service.IMessageSendEntryService;
import com.rzdata.process.service.IWorkflowLogService;
import com.rzdata.setting.domain.DistributeGroupDetail;
import com.rzdata.setting.domain.DocClassFlowNode;
import com.rzdata.setting.domain.DocClassFlowNodeDetail;
import com.rzdata.setting.domain.DocPresetUser;
import com.rzdata.setting.domain.dto.FunConditionDTO;
import com.rzdata.setting.domain.vo.DocClassFlowNodeVo;
import com.rzdata.setting.domain.vo.DocClassFlowVo;
import com.rzdata.setting.mapper.DistributeGroupDetailMapper;
import com.rzdata.setting.service.IDocClassFlowNodeDetailService;
import com.rzdata.setting.service.IDocClassFlowNodeService;
import com.rzdata.setting.service.IDocClassFlowService;
import com.rzdata.setting.service.IDocPresetUserService;

import com.rzdata.system.service.ISysDeptService;
import com.rzdata.system.service.ISysUserService;
import com.rzdata.system.service.WorkflowService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 流程审批记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-12-30
 */
@Service
public class WorkflowLogServiceImpl extends ServicePlusImpl<WorkflowLogMapper, WorkflowLog, WorkflowLogVo> implements IWorkflowLogService {

    @Autowired
    private IDocClassFlowService iDocClassFlowService;

    @Autowired
    private ISysUserService iSysUserService;

    @Autowired
    private ISysDeptService iSysDeptService;

    @Autowired
    private ModifyApplyMapper modifyApplyMapper;

    @Autowired
    private IDocClassFlowNodeService iDocClassFlowNodeService;

    @Autowired
    private IDocClassFlowNodeDetailService iDocClassFlowNodeDetailService;

    @Autowired
    private CustomConfig customConfig;

    @Autowired
    private IDocPresetUserService iDocPresetUserService;

    @Resource
    private DistributeGroupDetailMapper distributeGroupDetailMapper;

    @Autowired
    private IDocWorkflowLogItemService docWorkflowLogItemService;

    @Autowired
    private IMessageSendEntryService messageSendEntryService;



    @Override
    public WorkflowLogVo queryById(String id) {
        return getVoById(id);
    }

    @Override
    public TableDataInfo<WorkflowLogVo> queryPageList(WorkflowLogBo bo) {
        PagePlus<WorkflowLog, WorkflowLogVo> result = pageVo(PageUtils.buildPagePlus(), buildQueryWrapper(bo));
        if (BooleanUtil.isTrue(bo.getHavaDetail())) {
            result.getRecordsVo().forEach(item -> {
                item.setNickName(iSysUserService.getNickName(item.getSender()));
                item.setDeptName(iSysDeptService.getDeptName(item.getSenderDeptId()));
            });
        }
        return PageUtils.buildDataInfo(result);
    }

    @Override
    public List<WorkflowLogVo> queryList(WorkflowLogBo bo) {
        return listVo(buildQueryWrapper(bo));
    }

    private LambdaQueryWrapper<WorkflowLog> buildQueryWrapper(WorkflowLogBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<WorkflowLog> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getProcInstId()), WorkflowLog::getProcInstId, bo.getProcInstId());
        lqw.eq(StringUtils.isNotBlank(bo.getBusinessId()), WorkflowLog::getBusinessId, bo.getBusinessId());
        lqw.eq(StringUtils.isNotBlank(bo.getActDefId()), WorkflowLog::getActDefId, bo.getActDefId());
        lqw.eq(StringUtils.isNotBlank(bo.getNextDefId()), WorkflowLog::getNextDefId, bo.getNextDefId());
        lqw.eq(StringUtils.isNotBlank(bo.getBatch()), WorkflowLog::getBatch, bo.getBatch());
        lqw.like(StringUtils.isNotBlank(bo.getActDefName()), WorkflowLog::getActDefName, bo.getActDefName());
        lqw.eq(StringUtils.isNotBlank(bo.getActInstId()), WorkflowLog::getActInstId, bo.getActInstId());
        lqw.eq(StringUtils.isNotBlank(bo.getOpinion()), WorkflowLog::getOpinion, bo.getOpinion());
        lqw.eq(StringUtils.isNotBlank(bo.getSender()), WorkflowLog::getSender, bo.getSender());
        lqw.eq(StringUtils.isNotBlank(bo.getReceiver()), WorkflowLog::getReceiver, bo.getReceiver());
        lqw.eq(StringUtils.isNotBlank(bo.getActStatus()), WorkflowLog::getActStatus, bo.getActStatus());
        lqw.eq(bo.getTransfer() != null, WorkflowLog::getTransfer, bo.getTransfer());
        return lqw;
    }

    @Override
    public Boolean insertByBo(WorkflowLogBo bo) {
        WorkflowLog add = BeanUtil.toBean(bo, WorkflowLog.class);
        validEntityBeforeSave(add);
        add.setCreateTime(new Date());
        boolean flag = save(add);
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    @Override
    public Boolean updateByBo(WorkflowLogBo bo) {
        WorkflowLog update = BeanUtil.toBean(bo, WorkflowLog.class);
        validEntityBeforeSave(update);
        return updateById(update);
    }

    /**
     * 保存前的数据校验
     *
     * @param entity 实体类数据
     */
    private void validEntityBeforeSave(WorkflowLog entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return removeByIds(ids);
    }

    @Override
    public List<WorkflowLogVo> selectLogByBusinessId(String businessId) {
        return this.baseMapper.selectLogByBusinessId(businessId);
    }

    @Override
    public List<WorkflowLogVo> shlkSelectLogByProcInstId(String procInstId) {
        return this.baseMapper.shlkSelectLogByProcInstId(procInstId);
    }

    @Override
    public String queryRecentLog(String id) {
        return this.baseMapper.queryRecentLog(id);
    }

    @Override
    public Page<WorkflowLogVo> queryToDoList(Page<WorkflowLogVo> page, ProcessWorkFlowBo processBo) {
        return this.baseMapper.queryToDoList(page, processBo);
    }

    @Override
    public List<WorkflowLogVo> queryApprovalRecord(String applyId, String defActName) {
        return this.baseMapper.queryApprovalRecord(applyId, defActName);
    }

    @Override
    public List<WorkflowLogVo> getRedirectDefId(WorkflowLogBo bo) {
        List<DocClassFlowNodeVo> nodeList = iDocClassFlowService.getNodeList(bo.getDocClass(), bo.getBizType(), bo.getCode());
        if (nodeList.size() > 0) {
            List<String> defIdList = nodeList.stream().map(DocClassFlowNodeVo::getNodeCode).collect(Collectors.toList());
            List<WorkflowLogVo> list = listVo(new LambdaQueryWrapper<WorkflowLog>().eq(WorkflowLog::getBatch, bo.getBatch()).in(WorkflowLog::getNextDefId, defIdList));
            if (ObjectUtil.isNotEmpty(list)) {
                list.forEach(item -> {
                    Optional<DocClassFlowNodeVo> optional = nodeList.stream().filter(node -> item.getNextDefId().equals(node.getNodeCode())).findFirst();
                    optional.ifPresent(docClassFlowNodeVo -> item.setActDefOrder(docClassFlowNodeVo.getSort()));
                });
                return list;
            }
        }
        return null;
    }

    @Override
    public Boolean updateTransferStatus(String curActInstId) {
        return update(new LambdaUpdateWrapper<WorkflowLog>()
                .set(WorkflowLog::getTransfer, false)
                .eq(WorkflowLog::getActInstId, curActInstId).eq(WorkflowLog::getTransfer, true));
    }


    //只支持新增修订作废流程
    @Override
    public void automaticSubmit(BpmClientInputModelBo bpmClientInputModelBo, BpmClient bpmClient) throws Exception {
        //往前走不是办结的流程才往下执行
        if (!BooleanUtil.isTrue(bpmClientInputModelBo.getDirection()) || StringUtils.equals(bpmClientInputModelBo.getStatus(), RecordStatusEnum.DONE.getCode())) {
            return;
        }
        BpmClientInputModel bpmClientInputModel = bpmClientInputModelBo.getModel();
        //获取当前环节所有待办
        List<UniteWorkTaskDTO> nextActInsts = getRecordbyPorcInstId(bpmClientInputModelBo.getProcessInst().getProcInstId(), bpmClient);
        //当前待办和上一个处理的待办环节id相同 代表会审还没完全走完 结束自动提交逻辑
        if (nextActInsts.get(0).getCurActDefId().equals(bpmClientInputModel.getWf_curActDefId())) {
            return;
        }
        //只支持新增修订作废流程 其他流程direction没传值不会执行到这里
        ModifyApply modifyApply = modifyApplyMapper.selectOne(new LambdaQueryWrapper<ModifyApply>()
                .eq(ModifyApply::getId, bpmClientInputModel.getWf_businessKey())
                .or().eq(ModifyApply::getBatchId, bpmClientInputModel.getWf_businessKey())
                .select(ModifyApply::getDocClass,
                        ModifyApply::getChangeType,
                        ModifyApply::getId,
                        ModifyApply::getUserName,
                        ModifyApply::getYNTrain,
                        ModifyApply::getWhetherCustomer)
                .last("limit 1"));
        DocClassFlowVo dcfVo = iDocClassFlowService.getByUpDocClassAndBizType(modifyApply.getDocClass(), modifyApply.getChangeType(), "");
        // 获取当前环节
        DocClassFlowNode dcfn1 = iDocClassFlowNodeService.getOne(new LambdaQueryWrapper<DocClassFlowNode>()
                .eq(DocClassFlowNode::getFlowId, dcfVo.getId())
                .eq(DocClassFlowNode::getNodeCode, nextActInsts.get(0).getCurActDefId()));
        //获取当前环节配置的功能详情
        List<DocClassFlowNodeDetail> nodeDetailList = iDocClassFlowNodeDetailService.list(new LambdaQueryWrapper<DocClassFlowNodeDetail>().eq(DocClassFlowNodeDetail::getNodeId, dcfn1.getId()));
        List<String> nodeDetail = nodeDetailList.stream().map(DocClassFlowNodeDetail::getCode).collect(Collectors.toList());
        List<Map<String, Object>> receivers = new ArrayList<>();
        boolean bool = true;
        ActivityDefinitionModel nextAct;
        //是否审核环节
        bpmClientInputModelBo.setReview(nodeDetail.contains("shenhe") || nodeDetail.contains("pizhun"));
        //是否配置 系统处理
        if (nodeDetail.contains("system_process") && nextActInsts.size() == 1) {
            UniteWorkTaskDTO curAct = nextActInsts.get(0);
            //获取这一轮审批记录中是否有不同意的
            long LogCount = count(new LambdaQueryWrapper<WorkflowLog>()
                    .eq(WorkflowLog::getBusinessId, bpmClientInputModel.getWf_businessKey())
                    .eq(WorkflowLog::getReview, true)
                    .eq(WorkflowLog::getPass, false)
                    .eq(WorkflowLog::getBatch, bpmClientInputModelBo.getBatch()));
            // 获取下一个环节
            SearchQuery searchQuery = new SearchQuery();
            searchQuery.setProcDefId(bpmClientInputModel.getWf_procDefId());
            searchQuery.setCurActDefId(curAct.getCurActDefId());
            List<ActivityDefinitionModel> nextActs = bpmClient.getNextacts(searchQuery);
            //有不同意的日志 流程跳转到排序最小的 没有 流程跳转到排序最大的
            if (LogCount > 0) {
                nextAct = nextActs.stream().min(Comparator.comparingInt(ActivityDefinitionModel::getActDefOrder)).orElse(null);
                if (nextAct == null) {
                    return;
                }
                SearchQuery sq = new SearchQuery();
                sq.setTenantId(customConfig.getBpmTenantId());
                sq.setUserId(curAct.getRecUserId());
                sq.setUserOrgId(curAct.getRecOrgId());
                sq.setDestActDefId(nextAct.getActDefId());
                sq.setCurActInstId(curAct.getCurActInstId());
                List<ActivityResourceModel> armList = bpmClient.getNextactUsers(sq);
                armList = armList.stream().filter(item -> "USER".equals(item.getType())).collect(Collectors.toList());
                if (armList.size() < 1) {
                    return;
                }
                armList.forEach(item -> {
                    Map<String, Object> map = new HashMap<>();
                    map.put("receiveUserId", item.getId());
                    map.put("receiveUserOrgId", item.getParentId());
                    receivers.add(map);
                });
            } else {
                nextAct = nextActs.stream().max(Comparator.comparingInt(ActivityDefinitionModel::getActDefOrder)).orElse(null);
                if (nextAct == null) {
                    return;
                }
                //获取预设的环节人员
                DocPresetUser dpu = iDocPresetUserService.getOne(new LambdaQueryWrapper<DocPresetUser>()
                        .eq(DocPresetUser::getBizId, bpmClientInputModel.getWf_businessKey())
                        .eq(DocPresetUser::getNodeCode, nextAct.getActDefId())
                        .orderByDesc(DocPresetUser::getCreateTime).last("limit 1"));
                if (dpu != null) {
                    List<UserDTO> users = JSONUtil.toList(dpu.getUsers(), UserDTO.class);
                    if (users.size() < 1) {
                        return;
                    }
                    users.forEach(user -> {
                        Map<String, Object> map = new HashMap<>();
                        map.put("receiveUserId", user.getUserName());
                        map.put("receiveUserOrgId", iSysUserService.getDeptByUserName(user.getUserName()));
                        receivers.add(map);
                    });
                }
            }
            //必须有接收人才往下执行
            if (receivers.size() == 0) {
                return;
            }

            bpmClientInputModelBo.setIsLast(nextActInsts.size()==1);
            submitFlow(bpmClientInputModelBo, curAct, nextAct, receivers, null, I18nUtils.getTitle(CommonI18nConstant.WORKFLOW_API_DMS_SYS_AUTOMATIC_PROCESS), bpmClient);
        }
        //是否配置 审核重复自动提交
        else if (nodeDetail.contains("automatic_submit")) {
            //获取预设的环节人员
            List<DocPresetUser> dpuList = iDocPresetUserService.list(new LambdaQueryWrapper<DocPresetUser>()
                    .eq(DocPresetUser::getBizId, bpmClientInputModel.getWf_businessKey()));
            //会审重定向 会审环节审批批次标记
            if (nodeDetail.contains("hscdx") || nodeDetail.contains("wgcdx")) {
                bpmClientInputModelBo.setBatch(bpmClientInputModelBo.getBatch());
            } else {
                bpmClientInputModelBo.setBatch(null);
            }
            //日志标签
            if (nodeDetail.contains("log_title")) {
                FunConditionDTO funCondition = nodeFunCondition(nodeDetailList, "log_title");
                if (StringUtils.isNotEmpty(funCondition.getLimitValue())) {
                    bpmClientInputModelBo.setTitle(funCondition.getLimitValue());
                }
            }
            //预设会签人员
            if (nodeDetail.contains("preset_countersign")) {
                FunConditionDTO funCondition = nodeFunCondition(nodeDetailList, "preset_countersign");
                if (StringUtils.isNotEmpty(funCondition.getGroupId()) && ArrayUtil.isNotEmpty(funCondition.getNodeCode())) {
                    List<UserDTO> users = new ArrayList<>();
                    distributeGroupDetailMapper.selectList(new LambdaQueryWrapper<DistributeGroupDetail>()
                            .eq(DistributeGroupDetail::getGroupId, funCondition.getGroupId())).forEach(item -> {
                        UserDTO user = new UserDTO();
                        user.setUserName(item.getReceiveUserName());
                        user.setNickName(item.getReceiveNickName());
                        user.setDeptId(item.getReceiveUserDeptId());
                        user.setDeptName(item.getReceiveUserDept());
                        users.add(user);
                    });
                    updateDpuList(funCondition, users, dpuList);
                }
            }
            //下个环节预选直属部门领导
            //只能配置在单人环节 配置在多人环节 会导致部门领导模糊 除非是同一个部门领导
            if (nodeDetail.contains("next_set_leader")) {
                FunConditionDTO funCondition = nodeFunCondition(nodeDetailList, "next_set_leader");
                if (ArrayUtil.isNotEmpty(funCondition.getNodeCode())) {
                    List<UserDTO> users = new ArrayList<>();
                    String deptId;
                    if (BooleanUtil.isTrue(funCondition.getValidate())) {
                        deptId = modifyApply.getDeptId();
                    } else {
                        deptId = nextActInsts.get(0).getRecOrgId();
                    }
                    SysUser su = iSysUserService.getLeader(nextActInsts.get(0).getRecUserId(), deptId);
                    if (su != null) {
                        UserDTO user = new UserDTO();
                        user.setUserName(su.getUserName());
                        user.setNickName(su.getNickName());
                        user.setDeptId(su.getDeptId());
                        user.setDeptName(su.getDept().getDeptName());
                        users.add(user);
                    }
                    updateDpuList(funCondition, users, dpuList);
                }
            }
            if (nodeDetail.contains("next_set_division_leader")) {
                FunConditionDTO funCondition = nodeFunCondition(nodeDetailList, "next_set_division_leader");
                if (ArrayUtil.isNotEmpty(funCondition.getNodeCode())) {
                    List<UserDTO> users = new ArrayList<>();
                    String deptId;
                    if (BooleanUtil.isTrue(funCondition.getValidate())) {
                        deptId = modifyApply.getDeptId();
                    } else {
                        deptId = nextActInsts.get(0).getRecOrgId();
                    }
                    SysUser su = iSysUserService.getDivisionLeader(deptId);
                    if (su != null) {
                        UserDTO user = new UserDTO();
                        user.setUserName(su.getUserName());
                        user.setNickName(su.getNickName());
                        user.setDeptId(su.getDeptId());
                        user.setDeptName(su.getDept().getDeptName());
                        users.add(user);
                    }
                    updateDpuList(funCondition, users, dpuList);
                }
            }
            if (nodeDetail.contains("set_flow_select_list")) {
                FunConditionDTO funCondition = nodeFunCondition(nodeDetailList, "set_flow_select_list");
                if (ArrayUtil.isNotEmpty(funCondition.getNodeCode())) {
                    List<UserDTO> users = new ArrayList<>();
                    String deptId;
                    if (BooleanUtil.isTrue(funCondition.getValidate())) {
                        deptId = modifyApply.getDeptId();
                    } else {
                        deptId = nextActInsts.get(0).getRecOrgId();
                    }
                    List<SysUser> suList = iSysUserService.getDocManagersByDeptId(deptId);
                    if (suList != null) {
                        suList.forEach(su -> {
                            UserDTO user = new UserDTO();
                            user.setUserName(su.getUserName());
                            user.setNickName(su.getNickName());
                            user.setDeptId(su.getDeptId());
                            user.setDeptName(su.getDept().getDeptName());
                            users.add(user);
                        });
                    }
                    updateDpuList(funCondition, users, dpuList);
                }
            }
            //下一环节未预选人员隐藏
            List<String> hideNodeCode = new ArrayList<>();
            if (nodeDetail.contains("xyhjwyxryyc")) {
                FunConditionDTO funCondition = nodeFunCondition(nodeDetailList, "xyhjwyxryyc");
                if (ArrayUtil.isNotEmpty(funCondition.getNodeCode())) {
                    int length = funCondition.getNodeCode().size();
                    //下一环节隐藏范围 过滤出没有预选人员的环节
                    hideNodeCode = funCondition.getNodeCode().stream().filter(item ->
                            dpuList.stream().noneMatch(dpu ->
                                    item.equals(dpu.getNodeCode()) && StringUtils.isNotEmpty(dpu.getUsers()) && JSONUtil.toList(dpu.getUsers(), UserDTO.class).size()>0
                            )).collect(Collectors.toList());
                    //配置了反向节点 隐藏范围数量没变吗，都没预选人员 隐藏范围去掉反向节点
                    if (ArrayUtil.isNotEmpty(funCondition.getNeNodeCode()) && hideNodeCode.size() == length) {
                        hideNodeCode.removeIf(code -> funCondition.getNeNodeCode().contains(code));
                    }
                    //填写了限定值 只能显示最多限定的数量
                    if (StringUtils.isNotEmpty(funCondition.getLimitValue())) {
                        int limitValue = Integer.parseInt(funCondition.getLimitValue());
                        //总数-隐藏数=显示数 显示数>限定数量
                        if ((length - hideNodeCode.size()) > limitValue) {
                            //倒叙再插回隐藏范围
                            for (int i = funCondition.getNodeCode().size() - 1; i >= 0; i--) {
                                String nodeCode = funCondition.getNodeCode().get(i);
                                if (!hideNodeCode.contains(nodeCode)) {
                                    hideNodeCode.add(nodeCode);
                                }
                                //数量到了跳出循环
                                if ((length - hideNodeCode.size()) <= limitValue) {
                                    break;
                                }
                            }
                        }
                    }
                    //配置了反向节点 隐藏范围环节内都有预选人员 增加反向节点
                    if (ArrayUtil.isNotEmpty(funCondition.getNeNodeCode()) && hideNodeCode.size() != length && hideNodeCode.size() == 0) {
                        hideNodeCode.addAll(funCondition.getNeNodeCode());
                    }
                }
            }
            // 获取下一个环节
            SearchQuery searchQuery = new SearchQuery();
            searchQuery.set("isTrain", modifyApply.getYNTrain());
            searchQuery.set("isCustomer", modifyApply.getWhetherCustomer());
            searchQuery.setProcDefId(bpmClientInputModel.getWf_procDefId());
            searchQuery.setCurActDefId(nextActInsts.get(0).getCurActDefId());
            List<ActivityDefinitionModel> nextActs = bpmClient.getNextacts(searchQuery);
            //过滤出往前走的 和 不在隐藏范围的
            List<String> finalHideNodeCode = hideNodeCode;
            nextActs = nextActs.stream().filter(item ->
                    ("endEvent".equals(item.getActDefType()) || "inclusiveGateway".equals(item.getActDefType())
                            || (item.getActDefOrder() != null && item.getActDefOrder() > Integer.parseInt(bpmClientInputModelBo.getOrder())))
                            && !finalHideNodeCode.contains(item.getActDefId())
            ).collect(Collectors.toList());
            //得到的下个环节只有一个时 才自动提交
            if (nextActs.size() != 1) {
                return;
            }
            nextAct = nextActs.get(0);
            //获取这一轮审批记录中向前走的
            List<WorkflowLog> logList = list(new LambdaQueryWrapper<WorkflowLog>()
                    .eq(WorkflowLog::getBusinessId, bpmClientInputModel.getWf_businessKey())
                    .eq(WorkflowLog::getMark, bpmClientInputModelBo.getMark())
                    .eq(WorkflowLog::getReview, true)
                    .eq(WorkflowLog::getDirection, true));
            // 下个环节预选人员
            ActivityDefinitionModel finalNextAct = nextAct;
            DocPresetUser dpu = dpuList.stream().filter(item -> item.getNodeCode().equals(finalNextAct.getActDefId())).findFirst().orElse(new DocPresetUser());
            //设置网关接收人
            Map<String, Object> fields = new HashMap<>();
            for (UniteWorkTaskDTO curAct : nextActInsts) {
                //审批记录中没这个接收人 跳过
                if (logList.stream().noneMatch(wfLog -> wfLog.getSender().equals(curAct.getRecUserId()))) {
                    bool = false;
                    continue;
                }
                List<UserDTO> users = JSONUtil.toList(dpu.getUsers(), UserDTO.class);
                if ("inclusiveGateway".equals(nextAct.getActDefType())) {
                    // 获取网关下一个环节
                    SearchQuery searchQuery1 = new SearchQuery();
                    searchQuery1.setProcDefId(curAct.getProcDefId());
                    searchQuery1.setProcInstId(curAct.getProcInstId());
                    searchQuery1.setCurActDefId(nextAct.getActDefId());
                    List<ActivityDefinitionModel> gatewayActs = bpmClient.getNextacts(searchQuery1);
                    gatewayActs.forEach(item -> {
                        DocPresetUser presetUser = dpuList.stream().filter(dpu1 -> item.getActDefId().equals(dpu1.getNodeCode())).findFirst().orElse(null);
                        if (presetUser != null) {
                            List<UserDTO> users1 = JSONUtil.toList(presetUser.getUsers(), UserDTO.class);
                            if (users1.size() > 0) {
                                fields.put(item.getActDefId() + "_user", users1.stream().map(dpu1 -> dpu1.getUserName() + "#" + dpu1.getDeptId()).collect(Collectors.joining(",")));
                                fields.put(item.getActDefId() + "_turnover", true);
                            } else {
                                fields.put(item.getActDefId() + "_turnover", false);
                            }
                        } else {
                            fields.put(item.getActDefId() + "_turnover", false);
                        }
                    });
                    //必须有接收人才往下执行
                    if (fields.size() == 0) {
                        bool = false;
                        continue;
                    }
                    //网关没有序号，将后面环节的给他
                    nextAct.setActDefOrder(gatewayActs.get(0).getActDefOrder());
                } else {
                    if (users.size() > 0) {
                        if (BooleanUtil.isTrue(nextAct.isMulti())) {
                            users.forEach(user -> {
                                Map<String, Object> map = new HashMap<>();
                                map.put("receiveUserId", user.getUserName());
                                map.put("receiveUserOrgId", iSysUserService.getDeptByUserName(user.getUserName()));
                                receivers.add(map);
                            });
                        } else {
                            Map<String, Object> map = new HashMap<>();
                            map.put("receiveUserId", users.get(users.size() - 1).getUserName());
                            map.put("receiveUserOrgId", iSysUserService.getDeptByUserName(users.get(users.size() - 1).getUserName()));
                            receivers.add(map);
                        }
                    } else {
                        SearchQuery sq = new SearchQuery();
                        sq.setTenantId(customConfig.getBpmTenantId());
                        sq.setUserId(curAct.getRecUserId());
                        sq.setUserOrgId(curAct.getRecOrgId());
                        sq.setDestActDefId(nextAct.getActDefId());
                        sq.setCurActInstId(curAct.getCurActInstId());
                        List<ActivityResourceModel> armList = bpmClient.getNextactUsers(sq);
                        armList = armList.stream().filter(item -> "USER".equals(item.getType())).collect(Collectors.toList());
                        if (armList.size() == 1) {
                            Map<String, Object> map = new HashMap<>();
                            map.put("receiveUserId", armList.get(0).getId());
                            map.put("receiveUserOrgId", armList.get(0).getParentId());
                            receivers.add(map);
                        } else {
                            if (nodeDetail.contains("default_selected")) {
                                if (BooleanUtil.isTrue(nextAct.isMulti())) {
                                    armList.forEach(item -> {
                                        Map<String, Object> map = new HashMap<>();
                                        map.put("receiveUserId", item.getId());
                                        map.put("receiveUserOrgId", item.getParentId());
                                        receivers.add(map);
                                    });
                                } else {
                                    Map<String, Object> map = new HashMap<>();
                                    map.put("receiveUserId", armList.get(0).getId());
                                    map.put("receiveUserOrgId", armList.get(0).getParentId());
                                    receivers.add(map);
                                }
                            }
                        }
                    }

                    //必须有接收人才往下执行
                    if (receivers.size() == 0) {
                        bool = false;
                        continue;
                    }
                }
                if (nodeDetail.contains("preset_countersign") || nodeDetail.contains("next_set_leader") || nodeDetail.contains("next_set_division_leader") || nodeDetail.contains("set_flow_select_list")) {
                    iDocPresetUserService.updatePresetUser(dpuList, bpmClientInputModel.getWf_businessKey());
                }
                bpmClientInputModelBo.setIsLast(nextActInsts.size()==1);
                submitFlow(bpmClientInputModelBo, curAct, nextAct, receivers, fields, I18nUtils.getTitle(CommonI18nConstant.WORKFLOW_API_DMS_SYS_AUTOMATIC_SUBMIT), bpmClient);

            }
        } else {
            return;
        }

        //当前待办全部执行了 才连续跳
        if (bool) {
            automaticSubmit(bpmClientInputModelBo, bpmClient);
        }
    }

    private void submitFlow(BpmClientInputModelBo bpmClientInputModelBo, UniteWorkTaskDTO curAct, ActivityDefinitionModel nextAct, List<Map<String, Object>> receivers, Map<String, Object> fields, String curComment, BpmClient bpmClient) throws Exception {
        BpmClientInputModel bpmClientInputModel = bpmClientInputModelBo.getModel();
        String url = this.customConfig.getWorkflowUniteWorkUrl() + "?businessKey=" + bpmClientInputModel.getWf_businessKey() +
                "&type=" + bpmClientInputModelBo.getType() + "&order=" + nextAct.getActDefOrder() + "&mark=" + bpmClientInputModelBo.getMark();
        if (BooleanUtil.isTrue(nextAct.isMulti())||"inclusiveGateway".equals(nextAct.getActDefType())) {
            url = url + "&batch=" + DateUtil.current();
        } else if (StringUtils.isNotEmpty(bpmClientInputModelBo.getBatch())) {
            url = url + "&batch=" + bpmClientInputModelBo.getBatch();
        }
        bpmClientInputModel.setWf_curActInstId(curAct.getCurActInstId());
        bpmClientInputModel.setWf_sendUserId(curAct.getRecUserId());
        bpmClientInputModel.setWf_sendUserOrgId(curAct.getRecOrgId());
        bpmClientInputModel.setWf_uniteworkUrl(url);
        bpmClientInputModel.setWf_receivers(receivers);
        bpmClientInputModel.setWf_nextActDefId(nextAct.getActDefId());
        bpmClientInputModel.setWf_nextActDefName(nextAct.getActDefName());
        bpmClientInputModel.setWf_curComment(curComment);
        bpmClientInputModel.setWf_curActDefId(curAct.getCurActDefId());
        bpmClientInputModel.setWf_curActDefName(curAct.getCurActDefName());
        bpmClientInputModel.setFields(fields);
        ProcessInstanceModel processInst = bpmClient.submitFlowInstance(bpmClientInputModel);
        saveProcLog(bpmClientInputModelBo, processInst, WorkflowService.ENUM_ACTION.submit.name());
        // 通过企业微信发送流程待办消息
        if (bpmClientInputModelBo.getIsLast() == null || bpmClientInputModelBo.getIsLast()) {
            messageSendEntryService.sendWorkFlowMessagQywx(bpmClientInputModelBo, DocMsgConstants.MSG_TYPE_QYWX);
        }
    }

    private void updateDpuList(FunConditionDTO funCondition, List<UserDTO> users, List<DocPresetUser> dpuList) {
        if (users.size() > 0) {
            funCondition.getNodeCode().forEach(nodeCode -> {
                Optional<DocPresetUser> optional = dpuList.stream().filter(item -> nodeCode.equals(item.getNodeCode())).findFirst();
                if (optional.isPresent()) {
                    optional.get().setUsers(JSONUtil.toJsonStr(users));
                } else {
                    DocPresetUser dpu = new DocPresetUser();
                    dpu.setNodeCode(nodeCode);
                    dpu.setUsers(JSONUtil.toJsonStr(users));
                    dpuList.add(dpu);
                }
            });
        } else {
            funCondition.getNodeCode().forEach(nodeCode -> {
                dpuList.removeIf(item -> item.getNodeCode().equals(nodeCode));
            });
        }
    }

    private FunConditionDTO nodeFunCondition(List<DocClassFlowNodeDetail> nodeDetailList, String code) {
        Optional<DocClassFlowNodeDetail> optional = nodeDetailList.stream().filter(item -> code.equals(item.getCode())).findFirst();
        return optional.map(docClassFlowNodeDetail -> JSONUtil.toBean(docClassFlowNodeDetail.getFunCondition(), FunConditionDTO.class)).orElse(new FunConditionDTO());
    }

    /**
     * 保存流程日志
     *
     * @param bpmClientInputModelBo
     * @param processInst
     * @param action
     */
    @Override
    public void saveProcLog(BpmClientInputModelBo bpmClientInputModelBo, ProcessInstanceModel processInst, String action) {
        BpmClientInputModel bpmClientInputModel = bpmClientInputModelBo.getModel();
        WorkflowLogBo bo = new WorkflowLogBo();
        bo.setPass(bpmClientInputModelBo.getApplyStatus());
        bo.setProcInstId(processInst.getProcInstId());
        bo.setBusinessId(processInst.getBusinessKey());
        bo.setActDefId(bpmClientInputModel.getWf_curActDefId());
        bo.setActDefName(bpmClientInputModel.getWf_curActDefName());
        bo.setNextDefId(bpmClientInputModel.getWf_nextActDefId());
        bo.setNextDefName(bpmClientInputModel.getWf_nextActDefName());
        bo.setActInstId(bpmClientInputModel.getWf_curActInstId());
        bo.setOpinion(bpmClientInputModel.getWf_curComment());
        bo.setSender(bpmClientInputModel.getWf_sendUserId());
        bo.setSenderDeptId(bpmClientInputModel.getWf_sendUserOrgId());
        bo.setReceiver(JSONUtil.toJsonStr(bpmClientInputModel.getWf_receivers()));
        bo.setActStatus(action);
        bo.setProcDefKey(bpmClientInputModel.getWf_procDefKey());
        bo.setReview(bpmClientInputModelBo.getReview());
        bo.setBatch(bpmClientInputModelBo.getBatch());
        bo.setTitle(bpmClientInputModelBo.getTitle());
        bo.setTransfer(bpmClientInputModelBo.getTransfer());
        bo.setMark(bpmClientInputModelBo.getMark());
        bo.setDirection(BooleanUtil.isTrue(bpmClientInputModelBo.getDirection()));
        if ("transfer".equals(action) && !BooleanUtil.isTrue(bpmClientInputModelBo.getTransfer())) {
            updateTransferStatus(bpmClientInputModel.getWf_curActInstId());
        }
        insertByBo(bo);

        if (bpmClientInputModel.getWf_receivers() != null) {
            //设置接收人和接收组织
            DocWorkflowLogItem docWorkflowLogItem;
            for (Map<String, Object> receiver : bpmClientInputModel.getWf_receivers()) {
                docWorkflowLogItem = new DocWorkflowLogItem();
                docWorkflowLogItem.setWorkflowLogId(bo.getId());
                docWorkflowLogItem.setReceiverMan((String) receiver.get("receiveUserId"));
                docWorkflowLogItem.setReceiverOrg((String) receiver.get("receiveUserOrgId"));
                docWorkflowLogItemService.save(docWorkflowLogItem);
            }
        }
    }

    @Override
    public List<UniteWorkTaskDTO> getRecordbyPorcInstId(String porcInstId, BpmClient bpmClient) throws Exception {
        WorkflowConfig workflowConfig = new WorkflowConfig();
        String url = String.format("%s/%s?procInstId=%s", customConfig.getWorkflowServiceUrl(), workflowConfig.getRecordQueryUrl(), porcInstId);
        return JSONUtil.toList(bpmClient.get(url), UniteWorkTaskDTO.class);
    }
}

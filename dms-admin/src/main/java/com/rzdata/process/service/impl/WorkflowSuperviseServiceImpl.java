package com.rzdata.process.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.rzdata.config.CustomConfig;
import com.rzdata.framework.core.mybatisplus.core.ServicePlusImpl;
import com.rzdata.framework.core.page.PagePlus;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.utils.PageUtils;
import com.rzdata.framework.utils.SecurityUtils;
import com.rzdata.framework.utils.StringUtils;
import com.rzdata.process.domain.WorkflowSupervise;
import com.rzdata.process.domain.bo.UniteworkTaskBo;
import com.rzdata.process.domain.bo.WorkflowSuperviseBo;
import com.rzdata.process.domain.vo.UniteworkTaskVo;
import com.rzdata.process.domain.vo.WorkflowSuperviseVo;
import com.rzdata.process.mapper.WorkflowSuperviseMapper;
import com.rzdata.process.service.IWorkflowSuperviseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * 流程督办Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-11-14
 */
@Service
public class WorkflowSuperviseServiceImpl
        extends ServicePlusImpl<WorkflowSuperviseMapper, WorkflowSupervise, WorkflowSuperviseVo>
        implements IWorkflowSuperviseService {

    @Autowired
    CustomConfig customConfig;

    @Override
    public WorkflowSuperviseVo queryById(String id) {
        return getVoById(id);
    }

    @Override
    public TableDataInfo<WorkflowSuperviseVo> queryPageList(WorkflowSuperviseBo bo) {
        PagePlus<WorkflowSupervise, WorkflowSuperviseVo> result = pageVo(PageUtils.buildPagePlus(),
                buildQueryWrapper(bo));
        return PageUtils.buildDataInfo(result);
    }

    private LambdaQueryWrapper<WorkflowSupervise> buildQueryWrapper(WorkflowSuperviseBo bo) {
        LambdaQueryWrapper<WorkflowSupervise> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getProcDefKey()), WorkflowSupervise::getProcDefKey, bo.getProcDefKey());
        lqw.eq(StringUtils.isNotBlank(bo.getProcDefId()), WorkflowSupervise::getProcDefId, bo.getProcDefId());
        lqw.like(StringUtils.isNotBlank(bo.getProcDefName()), WorkflowSupervise::getProcDefName, bo.getProcDefName());
        lqw.eq(StringUtils.isNotBlank(bo.getSupervise()), WorkflowSupervise::getSupervise, bo.getSupervise());
        return lqw;
    }

    @Override
    public void sync() {
        try {
            List<WorkflowSuperviseVo> actReProcDefList = this.baseMapper
                    .queryActReProcDefList(customConfig.getBpmTenantId(), customConfig.getBpmDataSource());
            if (null != actReProcDefList && actReProcDefList.size() > 0) {
                for (WorkflowSuperviseVo workflowSuperviseVo : actReProcDefList) {
                    WorkflowSupervise workflowSupervise = this.getOne(new LambdaQueryWrapper<WorkflowSupervise>()
                            .eq(WorkflowSupervise::getProcDefKey, workflowSuperviseVo.getProcDefKey()));
                    if (null != workflowSupervise) {
                        BeanUtil.copyProperties(workflowSuperviseVo, workflowSupervise);
                        updateById(workflowSupervise);
                    } else {
                        workflowSupervise = new WorkflowSupervise();
                        BeanUtil.copyProperties(workflowSuperviseVo, workflowSupervise);
                        workflowSupervise.setSupervise("0");
                        workflowSupervise.setCreateBy(SecurityUtils.getUserId());
                        save(workflowSupervise);
                    }
                }
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public Boolean insertByBo(WorkflowSuperviseBo bo) {
        WorkflowSupervise add = BeanUtil.toBean(bo, WorkflowSupervise.class);
        add.setCreateTime(new Date());
        boolean flag = save(add);
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    @Override
    public Boolean updateByBo(WorkflowSuperviseBo bo) {
        bo.setUpdateBy(SecurityUtils.getUserId());
        WorkflowSupervise update = BeanUtil.toBean(bo, WorkflowSupervise.class);
        return updateById(update);
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        if (isValid) {
            // TODO 做一些业务上的校验,判断是否需要校验
        }
        return removeByIds(ids);
    }

    @Override
    public List<UniteworkTaskVo> queryUniteworkTaskList(UniteworkTaskBo bo) {
        return this.baseMapper.queryUniteworkTaskList(bo, customConfig.getBpmDataSource());
    }

}

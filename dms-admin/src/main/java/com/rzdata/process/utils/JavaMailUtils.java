package com.rzdata.process.utils;

import java.io.UnsupportedEncodingException;
import java.util.Properties;

import javax.mail.*;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeMessage;

/**
 * <AUTHOR>
 */
public final class JavaMailUtils {
    private JavaMailUtils() {}

    /**
     *  邮箱发送账号
     */
    public static final String USERNAME = "<EMAIL>";
    /**
     *  邮箱授权码
     */
    public static final String PASSWORD = "pweuHi55jZYToX39";

    public static Session createSession() {
        //	创建一个配置文件
        Properties props = new Properties();
        //	SMTP服务器连接信息
        //	SMTP主机名
        props.put("mail.smtp.host", "smtp.exmail.qq.com");
        //	主机端口号
        props.put("mail.smtp.port", "465");
        //	是否需要用户认证
        props.put("mail.smtp.auth", true);
        props.put("mail.smtp.ssl.enable", true);

        Session session = Session.getInstance(props,new Authenticator() {
            @Override
            protected PasswordAuthentication getPasswordAuthentication() {
                // TODO Auto-generated method stub
                return new PasswordAuthentication(USERNAME,PASSWORD);
            }
        });

        //  控制台打印调试信息
        session.setDebug(true);
        return session;

    }

    public static void main(String[] ags) throws UnsupportedEncodingException, MessagingException {
        Session session = createSession();
        MimeMessage message = new MimeMessage(session);
        message.setSubject("提醒：请各项目负责人完成周报审核！");
        message.setText("请各项目负责人完成周报审核！");
        message.setFrom(new InternetAddress(JavaMailUtils.USERNAME,"系统管理员"));
        Address[] addresses = new InternetAddress[1];
        addresses[0] = new InternetAddress("<EMAIL>","刘康");
        message.setRecipients(Message.RecipientType.TO, addresses);
        //	发送
        Transport.send(message);
        System.out.println(session);
    }
}

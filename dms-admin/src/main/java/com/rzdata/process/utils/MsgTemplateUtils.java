package com.rzdata.process.utils;

import com.rzdata.config.CustomConfig;
import com.rzdata.framework.utils.spring.SpringUtils;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.HashMap;
import java.util.Map;

/**
 * 发送消息工具类
 */
public class MsgTemplateUtils {

    /**
     * 打印Html内容消息格式
     * 
     * @param docName      文件名称
     * @param versionValue 版本号
     * @param pcUrl      PC端链接
     * @return HTML格式的消息内容
     */
    public static String printHtmlContent(String docName, String versionValue, String pcUrl, String mobileUrl) {
        StringBuilder content = new StringBuilder();
        content.append("<p>");
        content.append("<span style='font-family:宋体'><a href='" + pcUrl + "'target='_blank'");
        content.append(
                "<span style='font-family: 宋体; font-size: 14px; color: #548DD4;'>文件名称：<span style='font-size: 14px; font-family: 宋体; text-wrap: wrap;'>");
        content.append(docName);
        content.append("</span>");
        content.append("，版本：<span style='font-size: 14px; font-family: 宋体; text-wrap: wrap;'>");
        content.append(versionValue);
        content.append("</span>");
        content.append("</span></a></span>");
        content.append("</p>");
        return content.toString();
    }

    /**
     * 构建pc端文件详细链接
     * @param docId
     * @param versionId
     * @return
     */
    public static String buildPcFileDetailUrl(String docId, String versionId) {
        CustomConfig customConfig = SpringUtils.getBean("customConfig");
        return customConfig.getFrontUrl() + "file/detail?versionId=" + versionId + "&docId=" + docId + "&invokerForm=systemMsg";
    }

    /**
     * 构建移动端文件详细链接
     * @param docId
     * @param versionId
     * @return
     */
    public static String buildMobilFileDetailUrl(String docId, String versionId) {
        return "/pages/detail/index?versionId=" + versionId + "&docId=" + docId + "&flag=0" + "&invokerForm=systemMsg";
    }

    public static String getParameterValue(String url, String paramName) {
        String[] parts = url.split("\\?");
        if (parts.length < 2) {
            return null; // 没有查询参数
        }

        String query = parts[1];
        String[] pairs = query.split("&");
        Map<String, String> queryParams = new HashMap<>();

        for (String pair : pairs) {
            String[] keyValue = pair.split("=");
            if (keyValue.length > 1) {
                try {
                    queryParams.put(keyValue[0], URLDecoder.decode(keyValue[1], "UTF-8"));
                } catch (UnsupportedEncodingException e) {
                    e.printStackTrace();
                }
            } else {
                queryParams.put(keyValue[0], null);
            }
        }

        return queryParams.get(paramName);
    }
}

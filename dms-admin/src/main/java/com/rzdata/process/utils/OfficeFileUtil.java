package com.rzdata.process.utils;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.io.FileUtil;
import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.config.Configure;
import com.deepoove.poi.config.ConfigureBuilder;
import com.deepoove.poi.plugin.table.LoopRowTableRenderPolicy;
import com.rzdata.framework.utils.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFPatriarch;
import org.apache.poi.hwpf.HWPFDocument;
import org.apache.poi.hwpf.extractor.WordExtractor;

import org.apache.poi.xwpf.usermodel.XWPFComment;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.ddr.poi.html.HtmlRenderPolicy;

import java.io.*;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;

import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.openxml4j.opc.OPCPackage;
import org.apache.poi.xssf.eventusermodel.XSSFReader;
import org.apache.poi.xssf.model.CommentsTable;
import org.apache.poi.xslf.usermodel.XMLSlideShow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFComment;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.hslf.usermodel.HSLFSlideShow;
import org.apache.poi.hslf.usermodel.HSLFSlide;


import org.apache.poi.ss.usermodel.*;
import java.io.IOException;
import javax.xml.stream.XMLInputFactory;
import javax.xml.stream.XMLStreamConstants;
import javax.xml.stream.XMLStreamReader;


public final class OfficeFileUtil {

    /**
     * 校验 Word (.docx) 文件是否存在批注
     */
    public static boolean hasWordComments(BufferedInputStream inputStream) throws Exception {
        try (XWPFDocument document = new XWPFDocument(inputStream)) {
            XWPFComment[] list = document.getComments();
            if(list != null && document.getComments().length > 0){
                return true;
            }
            // 检查修订记录
            if (containsRevisions(document)) {
                return true;
            }

            return false;
        }
    }

    /**
     * 检查 Word 文档中是否存在修订记录
     */
    private static boolean containsRevisions(XWPFDocument document) throws Exception {
        String xmlText = document.getDocument().newCursor().xmlText(); // 获取 XML 字符串

        XMLInputFactory factory = XMLInputFactory.newInstance();
        XMLStreamReader reader = factory.createXMLStreamReader(new StringReader(xmlText));

        while (reader.hasNext()) {
            int event = reader.next();
            if (event == XMLStreamConstants.START_ELEMENT) {
                String localName = reader.getLocalName();
                if ("ins".equals(localName) || "del".equals(localName)) {
                    return true; // 找到插入或删除修订标记
                }
            }
        }

        return false;
    }

    public static boolean hasOldWordComments(BufferedInputStream inputStream) throws Exception {
        try (HWPFDocument document = new HWPFDocument(inputStream);) {
            WordExtractor extractor = new WordExtractor(document);
            String[] text = extractor.getCommentsText();
            if(text.length>0){
                boolean status = false ;
                for(String t: text){
                    if(StringUtils.isNotEmpty(t)){
                        status=true;
                        break;
                    }
                }
                return status;
            }
            return false;
        }
    }

    public static void main(String[] args) {
        String path = "E:\\SJ-MX-201B 清洗液（Z1)材料明细表 A3.xlsx";
        File file = new File(path);
        BufferedInputStream inputStream= FileUtil.getInputStream(file);
        try {
            boolean status= hasExcelComments(inputStream);
            System.out.println(status);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 校验 Excel (.xls) 文件是否存在批注
     */
    public static boolean hasOldExcelComments(BufferedInputStream inputStream) throws Exception {
        try (HSSFWorkbook workbook = new HSSFWorkbook(inputStream)) {
            for (int i = 0; i < workbook.getNumberOfSheets(); i++) {
                HSSFSheet sheet = workbook.getSheetAt(i);
                HSSFPatriarch drawingPatriarch = sheet.getDrawingPatriarch();
                if (drawingPatriarch != null) {
                    // 检查是否有批注
                    for (Object obj : drawingPatriarch.getChildren()) {
                        if (obj instanceof HSSFComment) {
                            return true; // 发现批注
                        }
                    }
                }
            }
        }
            return false;
    }

    /**
     * 校验 Excel (.xlsx) 文件是否存在批注
     */
    public static boolean hasExcelComments(BufferedInputStream inputStream) throws Exception {
        try (OPCPackage pkg = OPCPackage.open(inputStream)) {
            XSSFReader reader = new XSSFReader(pkg);

            // 检查每个工作表的批注
            XSSFReader.SheetIterator sheets = (XSSFReader.SheetIterator) reader.getSheetsData();
            while (sheets.hasNext()) {
                InputStream sheetStream = sheets.next();
                CommentsTable commentsTable = sheets.getSheetComments();

                if (commentsTable != null && commentsTable.getNumberOfComments() > 0) {
                    sheetStream.close();
                    return true;
                }
                sheetStream.close();
            }
            return false;
        }
//        try (Workbook workbook = new XSSFWorkbook(inputStream)) {
//            for (int i = 0; i < workbook.getNumberOfSheets(); i++) {
//                Sheet sheet = workbook.getSheetAt(i);
//                if (sheet.getNumMergedRegions() > 0) {
//                    // 可以进一步判断是否包含注释区域
//                    return true;
//                }
//            }
//        }
//        return false;
    }

    private static boolean sheetHasComments(Sheet sheet) {
        // 检查单元格批注
        for (Row row : sheet) {
            for (Cell cell : row) {
                Comment comment = cell.getCellComment();
                if (comment != null) {
                    return true;
                }
            }
        }

        // 检查绘图批注（另一种形式的批注）
        if (sheet instanceof org.apache.poi.xssf.usermodel.XSSFSheet) {
            org.apache.poi.xssf.usermodel.XSSFSheet xssfSheet =
                    (org.apache.poi.xssf.usermodel.XSSFSheet) sheet;
            if (xssfSheet.getCellComments()!= null) {
                return true;
            }
        }

        return false;
    }

    /**
     * 校验 PPT (.ppt) 文件是否存在批注（如备注）
     */
    public static boolean hasOldPPTComments(BufferedInputStream inputStream) throws Exception {
        try (HSLFSlideShow ppt = new HSLFSlideShow(inputStream)) {
            return ppt.getSlides().stream()
                    .map(slide -> (HSLFSlide) slide)
                    .anyMatch(slide -> slide.getNotes() != null);
        }
    }


    /**
     * 校验 PPT (.pptx) 文件是否存在批
     */
    public static boolean hasPPTComments(BufferedInputStream inputStream) throws Exception {
        try (XMLSlideShow ppt = new XMLSlideShow(inputStream)) {
            return ppt.getSlides().stream().anyMatch(slide -> slide.getNotes() != null);
        }
    }


    /**
     * POI模板生成配制
     */
    public static Configure configure(boolean isHtml){
        ConfigureBuilder configureBuilder = Configure.builder();
        configureBuilder.useSpringEL(false);
        configureBuilder.addPlugin('%', new LoopRowTableRenderPolicy());
        if(isHtml){
            // 注册html解析插件
            HtmlRenderPolicy htmlRenderPolicy = new HtmlRenderPolicy();
            configureBuilder.bind("listlabel", htmlRenderPolicy);
        }
        return configureBuilder.build();
    }

    /**
     * 从模板生成Word文件
     * @param templatePath 模板路径
     * @param dataMap 模板数据
     * @return Word文件的二进制流
     * @deprecated {@link OfficeFileUtil#generateDocxFromTl(Path, Map, OutputStream)}
     */
    @Deprecated
    public static ByteArrayOutputStream generateDocxFromTl(Path templatePath, Map<?, ?> dataMap) throws IOException {
        boolean isHtml = dataMap.containsKey("listlabel") && dataMap.get("listlabel").toString().indexOf(">")>=0;
        ByteArrayOutputStream output = new ByteArrayOutputStream();
        XWPFTemplate template = XWPFTemplate.compile(templatePath.toFile(), configure(isHtml)).render(dataMap);
        template.writeAndClose(output);
        return output;
    }

    /**
     * 从模板生成Word文件
     * @param templatePath 模板路径
     * @param dataMap 模板数据
     */
    public static void generateDocxFromTl(Path templatePath, Map<?, ?> dataMap, OutputStream os) throws IOException {
        boolean isHtml = dataMap.containsKey("listlabel") && dataMap.get("listlabel").toString().indexOf(">")>=0;
        XWPFTemplate template = XWPFTemplate.compile(templatePath.toFile(), configure(isHtml)).render(dataMap);
        template.writeAndClose(os);
    }

    /**
     * 从模板生成Word文件
     * @param templatePath 模板路径
     * @param dataMap 模板数据
     */
    public static void generateDocxFromTl(BufferedInputStream templatePath, Map<?, ?> dataMap, OutputStream os) throws IOException {
        boolean isHtml = dataMap.containsKey("listlabel") && dataMap.get("listlabel").toString().indexOf(">")>=0;
        XWPFTemplate template = XWPFTemplate.compile(templatePath, configure(isHtml)).render(dataMap);
        template.writeAndClose(os);
    }

    /**
     * 从模板生成Word文件
     * @param templatePath 模板路径
     * @param dataObject object 模板数据
     * @return Word文件的二进制流
     */
    @Deprecated
    public static ByteArrayOutputStream generateDocxFromTlObject(Path templatePath, Object dataObject) throws IOException {
        ByteArrayOutputStream output = new ByteArrayOutputStream();
        XWPFTemplate template = XWPFTemplate.compile(templatePath.toFile(), configure(false)).render(BeanUtil.beanToMap(dataObject));
        template.writeAndClose(output);
        return output;
    }

    public static void generateDocxFromTlMap(String templatePath, Map<?, ?> dataMap, String outputPath) throws IOException {
        ByteArrayOutputStream docxOutput = new ByteArrayOutputStream();
        Path path = Paths.get(templatePath);
        generateDocxFromTl(path, dataMap, docxOutput);
        try {
            // 将数据写入文件
            try (FileOutputStream fos = new FileOutputStream(outputPath)) {
                docxOutput.writeTo(fos);
            }
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                docxOutput.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    /*public static void main(String[] args) throws IOException {
        ByteArrayOutputStream docxOutput = new ByteArrayOutputStream();
        Map<String, Object> dataMap = new HashMap<String,Object>();
        dataMap.put("versionValue","V0");
        dataMap.put("className","管理手册");
        dataMap.put("docId","SNR/GLSC-QEOM-08");
        dataMap.put("docName","test-0425-01");
        dataMap.put("userNames","邓敏杰，刘礼焕");
        dataMap.put("xUserNames","邓敏杰，刘礼焕");
        dataMap.put("xDepts","开发部门（2），测试部门（1）");
        List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
        for(int i=0;i<20;i++){
            Map<String, Object> map = new HashMap<String,Object>();
            map.put("versionValue","V"+i+"0");
            map.put("releaseTime","2024-01-01");
            map.put("deptName","测试部门");
            map.put("userName","邓敏杰");
            map.put("changeReason","系统开发测试");
            list.add(map);
        }
        dataMap.put("listlabel",list);
        Path path = Paths.get("E:\\SunlordTemplate.docx");
        generateDocxFromTl(path, dataMap, docxOutput);

        try {
            *//*docxOutput.write(docxOutput.getBytes());*//*

            // 将数据写入文件
            String filePath = "E:\\output.docx";
            try (FileOutputStream fos = new FileOutputStream(filePath)) {
                docxOutput.writeTo(fos);
            }
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                docxOutput.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }

    }*/
}

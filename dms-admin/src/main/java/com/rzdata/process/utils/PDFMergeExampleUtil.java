package com.rzdata.process.utils;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import org.apache.pdfbox.multipdf.PDFMergerUtility;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;

import java.io.*;

public class PDFMergeExampleUtil {

    /**
     * PDF文件合稿
     * @param oneSrcPath 第一个pdf文件
     * @param twoSrcPath 第二个pdf文件
     * @param desPath 合成后的pdf文件
     */
    public static void merge(String oneSrcPath, String twoSrcPath, String desPath) throws Exception {
        PDFMergerUtility pdfMerger = new PDFMergerUtility();
        String parentPath = FileUtil.getParent(desPath,1);
        if(!FileUtil.exist(parentPath)){
            FileUtil.mkdir(parentPath);
        }
        // 设置合并后的PDF文件的输出路径
        pdfMerger.setDestinationFileName(desPath);

        // 添加要合并的PDF文件
        pdfMerger.addSource(new File(oneSrcPath));
        pdfMerger.addSource(new File(twoSrcPath));

        // 合并文档
        try {
            try (PDDocument doc1 = PDDocument.load(new File(oneSrcPath));
                 PDDocument doc2 = PDDocument.load(new File(twoSrcPath))) {
                pdfMerger.mergeDocuments(null);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * PDF文件合稿
     * @param oneSrcPath 第一个pdf文件
     * @param twoSrcPath 第二个pdf文件
     * @param desPath 合成后的pdf文件
     */
    public static void merge(InputStream oneSrcPath, InputStream twoSrcPath, String desPath) throws Exception {
        PDFMergerUtility pdfMerger = new PDFMergerUtility();
        // 设置合并后的PDF文件的输出路径
        if (!FileUtil.exist(desPath)) {
            FileUtil.mkParentDirs(desPath);
        }
        pdfMerger.setDestinationFileName(desPath);
        // 添加要合并的PDF文件
        pdfMerger.addSource(oneSrcPath);
        pdfMerger.addSource(twoSrcPath);
        // 合并文档
        try {
            pdfMerger.mergeDocuments(null);
        } catch (IOException e) {
            e.printStackTrace();
        }finally {
            IoUtil.close(oneSrcPath);
            IoUtil.close(twoSrcPath);
        }
    }

    public static void main(String[] args) throws Exception {
        InputStream oneSrcPath = FileUtil.getInputStream("/dms-admin/filedata\\cover\\1851816325862129665_封面.pdf");
        InputStream twoSrcPath = FileUtil.getInputStream("\\dms-admin\\filedata\\doc2pdf\\20241031102533825\\db88d1821fcb43a499639e6c370a09d7.pdf");
        OutputStream desPath = FileUtil.getOutputStream("/dms-admin/filedata\\merge\\\\doc2pdf\\20241031102533825\\db88d1821fcb43a499639e6c370a09d7.pdf");
        merge(oneSrcPath,twoSrcPath, "/dms-admin/filedata\\merge\\\\doc2pdf\\20241031102533825\\db88d1821fcb43a499639e6c370a09d7.pdf");
    }
}

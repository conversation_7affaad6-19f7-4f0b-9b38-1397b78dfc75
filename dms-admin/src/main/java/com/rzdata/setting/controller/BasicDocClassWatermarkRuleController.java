package com.rzdata.setting.controller;

import com.alibaba.fastjson.JSON;
import com.rzdata.framework.annotation.Log;
import com.rzdata.framework.annotation.RepeatSubmit;
import com.rzdata.framework.core.controller.BaseController;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import com.rzdata.framework.core.validate.QueryGroup;
import com.rzdata.framework.enums.BusinessType;
import com.rzdata.plugins.watermark.PdfWaterMarkService;
import com.rzdata.setting.domain.DocClassWatermarkSettingDetail;
import com.rzdata.setting.domain.bo.BasicDocClassWatermarkRuleBo;
import com.rzdata.setting.domain.bo.DocClassWatermarkSettingBo;
import com.rzdata.setting.domain.vo.BasicDocClassWatermarkRuleVo;
import com.rzdata.setting.service.IBasicDocClassWatermarkRuleService;

import cn.hutool.core.io.FileUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import java.io.IOException;
import java.io.InputStream;
import java.util.Arrays;
import java.util.List;

/**
 * 文档水印规则Controller
 *
 * <AUTHOR>
 * @date 2024-01-22
 */
@Validated
@Api(value = "文档水印规则控制器", tags = {"文档水印规则管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/setting/watermarkRule")
public class BasicDocClassWatermarkRuleController extends BaseController {

    private final IBasicDocClassWatermarkRuleService iBasicDocClassWatermarkRuleService;

    @Autowired
    private PdfWaterMarkService pdfWaterMarkService;
    /**
     * 查询文档水印规则列表
     */
    @ApiOperation("查询文档水印规则列表")
    @GetMapping("/list")
    public TableDataInfo<BasicDocClassWatermarkRuleVo> list(@Validated(QueryGroup.class) BasicDocClassWatermarkRuleBo bo) {
        return iBasicDocClassWatermarkRuleService.queryPageList(bo);
    }
    /**
     * 查询文档水印规则列表
     */
    @ApiOperation("查询文档水印规则列表")
    @GetMapping("/listAll")
    public AjaxResult<List<BasicDocClassWatermarkRuleVo>> listAll(@Validated(QueryGroup.class) BasicDocClassWatermarkRuleBo bo) {
        return AjaxResult.success(iBasicDocClassWatermarkRuleService.queryList(bo));
    }

    /**
     * 获取文档水印规则详细信息
     */
    @ApiOperation("获取文档水印规则详细信息")
    @GetMapping("/{id}")
    public AjaxResult<BasicDocClassWatermarkRuleVo> getInfo(@ApiParam("主键")
                                                      @NotNull(message = "主键不能为空")
                                                      @PathVariable("id") String id) {
        return AjaxResult.success(iBasicDocClassWatermarkRuleService.queryById(id));
    }

    /**
     * 新增文档水印规则
     */
    @ApiOperation("新增文档水印规则")
    @Log(title = "文档水印规则", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public AjaxResult<Void> add(@Validated(AddGroup.class) @RequestBody BasicDocClassWatermarkRuleBo bo) {
        return toAjax(iBasicDocClassWatermarkRuleService.insertByBo(bo));
    }

    /**
     * 修改文档水印规则
     */
    @ApiOperation("修改文档水印规则")
    @Log(title = "文档水印规则", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public AjaxResult<Void> edit(@Validated(EditGroup.class) @RequestBody BasicDocClassWatermarkRuleBo bo) {
        return toAjax(iBasicDocClassWatermarkRuleService.updateByBo(bo));
    }

    /**
     * 删除文档水印规则
     */
    @ApiOperation("删除文档水印规则")
    @Log(title = "文档水印规则", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult<Void> remove(@ApiParam("主键串")
                                  @NotEmpty(message = "主键不能为空")
                                  @PathVariable String[] ids) {
        return toAjax(iBasicDocClassWatermarkRuleService.deleteWithValidByIds(Arrays.asList(ids), true));
    }

    /**
     * 检查水印规则是否被使用
     */
    @ApiOperation("检查水印规则是否被使用")
    @GetMapping("/checkUsage/{id}")
    public AjaxResult checkUsage(@PathVariable("id") String id) {
        return AjaxResult.success(iBasicDocClassWatermarkRuleService.checkRuleUsage(id));
    }
    /**
     * 移除水印规则应用
     */
    @PostMapping("/removeUsage")
    public AjaxResult removeUsage(@RequestBody DocClassWatermarkSettingBo bo) {
        return toAjax(iBasicDocClassWatermarkRuleService.removeUsage(bo));
    }

    /**
     * 新增水印规则应用
     */
    @ApiOperation("新增水印规则应用")
    @Log(title = "水印规则应用", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/addUsage")
    public AjaxResult<Void> addUsage(@RequestBody DocClassWatermarkSettingBo bo) {
        return toAjax(iBasicDocClassWatermarkRuleService.addUsage(bo));
    }
    /**
     * 预览水印规则
     * @throws IOException 
     */
    @ApiOperation("预览水印规则")
    @GetMapping("/preview")
    public void preview(String id, HttpServletResponse response) throws IOException {
        BasicDocClassWatermarkRuleVo watermarkRule = iBasicDocClassWatermarkRuleService.queryById(id);
        Resource resource = new ClassPathResource("templates/blank.pdf");
        InputStream inputStream = resource.getInputStream();
        List<DocClassWatermarkSettingDetail> docClassWatermarkSettingDetailList = 
            JSON.parseArray(watermarkRule.getRuleDetails(), DocClassWatermarkSettingDetail.class);
        response.setContentType("application/pdf");
        response.setHeader("Content-Disposition", "inline; filename=watermark.pdf");
        
        try {
            pdfWaterMarkService.preview(inputStream, response.getOutputStream(), docClassWatermarkSettingDetailList);
        } finally {
            if (inputStream != null) {
                inputStream.close();
            }
        }
    }
} 
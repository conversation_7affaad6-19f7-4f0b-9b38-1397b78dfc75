package com.rzdata.setting.controller;

import java.util.List;
import java.util.Arrays;

import lombok.RequiredArgsConstructor;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.rzdata.framework.annotation.RepeatSubmit;
import com.rzdata.framework.annotation.Log;
import com.rzdata.framework.core.controller.BaseController;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import com.rzdata.framework.core.validate.QueryGroup;
import com.rzdata.framework.enums.BusinessType;
import com.rzdata.framework.utils.poi.ExcelUtil;
import com.rzdata.setting.domain.vo.BasicFormRuleVo;
import com.rzdata.setting.domain.bo.BasicFormRuleBo;
import com.rzdata.setting.service.IBasicFormRuleService;
import com.rzdata.framework.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiOperation;

/**
 * 单规则Controller
 *
 * <AUTHOR>
 * @date 2024-09-20
 */
@Validated
@Api(value = "单规则控制器", tags = {"单规则管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/setting/formRule")
public class BasicFormRuleController extends BaseController {

    private final IBasicFormRuleService iBasicFormRuleService;

    /**
     * 查询单规则列表
     */
    @ApiOperation("查询单规则列表")
    @GetMapping("/list")
    public TableDataInfo<BasicFormRuleVo> list(@Validated(QueryGroup.class) BasicFormRuleBo bo) {
        return iBasicFormRuleService.queryPageList(bo);
    }

    /**
     * 导出单规则列表
     */
    @ApiOperation("导出单规则列表")
    @Log(title = "单规则", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public void export(@Validated BasicFormRuleBo bo, HttpServletResponse response) {
        List<BasicFormRuleVo> list = iBasicFormRuleService.queryList(bo);
        ExcelUtil.exportExcel(list, "单规则", BasicFormRuleVo.class, response);
    }

    /**
     * 获取单规则详细信息
     */
    @ApiOperation("获取单规则详细信息")
    @GetMapping("/{id}")
    public AjaxResult<BasicFormRuleVo> getInfo(@ApiParam("主键")
                                                  @NotNull(message = "主键不能为空")
                                                  @PathVariable("id") Long id) {
        return AjaxResult.success(iBasicFormRuleService.queryById(id));
    }

    /**
     * 新增单规则
     */
    @ApiOperation("新增单规则")
    @Log(title = "单规则", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public AjaxResult<Void> add(@Validated(AddGroup.class) @RequestBody BasicFormRuleBo bo) {
        return toAjax(iBasicFormRuleService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改单规则
     */
    @ApiOperation("修改单规则")
    @Log(title = "单规则", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public AjaxResult<Void> edit(@Validated(EditGroup.class) @RequestBody BasicFormRuleBo bo) {
        return toAjax(iBasicFormRuleService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除单规则
     */
    @ApiOperation("删除单规则")
    @Log(title = "单规则" , businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult<Void> remove(@ApiParam("主键串")
                                       @NotEmpty(message = "主键不能为空")
                                       @PathVariable Long[] ids) {
        return toAjax(iBasicFormRuleService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }

    /**
     * 通过递归查找表单规则
     * @return
     */
    @GetMapping("recursive/{docClass}")
    public AjaxResult<BasicFormRuleVo> getFormRuleByRecursive(@PathVariable("docClass")String docClass) {
        BasicFormRuleVo  vo = iBasicFormRuleService.getFormRuleByRecursive(docClass);
        return AjaxResult.success(vo);
    }
}

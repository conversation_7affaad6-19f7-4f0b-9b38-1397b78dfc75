package com.rzdata.setting.controller;

import java.rmi.ServerException;
import java.util.HashMap;
import java.util.List;
import java.util.Arrays;
import java.util.Map;

import com.rzdata.framework.constant.CommonI18nConstant;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.utils.I18nUtils;
import com.rzdata.setting.domain.CodeRuleLog;
import com.rzdata.setting.domain.bo.CodeRuleDetailBo;
import com.rzdata.setting.domain.vo.CodeRuleDetailVo;
import com.rzdata.system.service.ISysConfigService;
import lombok.RequiredArgsConstructor;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.rzdata.framework.annotation.RepeatSubmit;
import com.rzdata.framework.annotation.Log;
import com.rzdata.framework.core.controller.BaseController;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import com.rzdata.framework.core.validate.QueryGroup;
import com.rzdata.framework.enums.BusinessType;
import com.rzdata.framework.utils.poi.ExcelUtil;
import com.rzdata.setting.domain.vo.CodeRuleVo;
import com.rzdata.setting.domain.bo.CodeRuleBo;
import com.rzdata.setting.service.ICodeRuleService;
import com.rzdata.framework.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiOperation;

/**
 * 编号规则Controller
 *
 * <AUTHOR>
 * @date 2021-12-27
 */
@Validated
@Api(value = "编号规则控制器", tags = {"编号规则管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/setting/codeRule")
public class CodeRuleController extends BaseController {

    /**
     * 规则配置参数
     */
    private static final String KEY_RULE_ID_FLOW = "generator.key.";
    private final ICodeRuleService iCodeRuleService;
    private final ISysConfigService configService;
    /**
     * 查询编号规则列表
     */
    @ApiOperation("查询编号规则列表")
    @GetMapping("/list")
    public TableDataInfo<CodeRuleVo> list(@Validated(QueryGroup.class) CodeRuleBo bo) {
        return iCodeRuleService.queryPageList(bo);
    }

    /**
     * 获取编号规则明细详细信息
     */
    @ApiOperation("获取编号规则明细详细信息")
    @GetMapping("/docClass")
    public AjaxResult<List<CodeRuleDetailVo>> getInfoByDocClass(CodeRuleDetailBo bo) throws ServerException {
        return AjaxResult.success(iCodeRuleService.getInfoByDocClass(bo));
    }

    /**
     * 导出编号规则列表
     */
    @ApiOperation("导出编号规则列表")
    @Log(title = "编号规则", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public void export(@Validated CodeRuleBo bo, HttpServletResponse response) {
        List<CodeRuleVo> list = iCodeRuleService.queryList(bo);
        ExcelUtil.exportExcel(list, "编号规则", CodeRuleVo.class, response);
    }

    /**
     * 获取编号规则详细信息
     */
    @ApiOperation("获取编号规则详细信息")
    @GetMapping("/{id}")
    public AjaxResult<CodeRuleVo> getInfo(@ApiParam("主键")
                                                  @NotNull(message = "主键不能为空")
                                                  @PathVariable("id") String id) {
        return AjaxResult.success(iCodeRuleService.queryById(id));
    }

    /**
     * 新增编号规则
     */
    @ApiOperation("新增编号规则")
    @Log(title = "编号规则", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public AjaxResult<String> add(@Validated(AddGroup.class) @RequestBody CodeRuleBo bo) {
        return AjaxResult.success("成功", iCodeRuleService.insertByBo(bo));
    }

    /**
     * 修改编号规则
     */
    @ApiOperation("修改编号规则")
    @Log(title = "编号规则", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public AjaxResult<Void> edit(@Validated(EditGroup.class) @RequestBody CodeRuleBo bo) {
        return toAjax(iCodeRuleService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除编号规则
     */
    @ApiOperation("删除编号规则")
    @Log(title = "编号规则" , businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult<Void> remove(@ApiParam("主键串")
                                       @NotEmpty(message = "主键不能为空")
                                       @PathVariable String[] ids) {
        return toAjax(iCodeRuleService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }



    @ApiOperation("判断编号是否存在，busId：业务主键id，newNo：编号, true:存在，false：不存在")
    @GetMapping("/checkNoIsExist")
    public AjaxResult<Boolean> checkNoIsExist(@RequestParam("busId")String busId, @RequestParam("newNo")String newNo){
        return AjaxResult.success(iCodeRuleService.checkNoIsExist(busId, newNo));
    }

    @ApiOperation("如果生成了编号，但没有使用的时候 删除日志记录")
    @GetMapping("/removeByRule")
    public AjaxResult<Void> removeByRule(@RequestParam("busId")String busId, @RequestParam("newNo")String newNo) {
        iCodeRuleService.removeByRule(busId, newNo);
        return AjaxResult.success();
    }

    @ApiOperation("生成编码")
    @PostMapping("/generatorCode")
    public AjaxResult<String> generatorCode(@RequestBody Map<String, Object> bizMap) {
        String configValue = configService.selectConfigByKey(KEY_RULE_ID_FLOW+bizMap.get("applyType").toString());
        CodeRuleLog codeRuleLog = iCodeRuleService.generatorEncodingRule(configValue,bizMap.get("bizId").toString(),bizMap);
        return AjaxResult.success(I18nUtils.getTitle(CommonI18nConstant.RESULT_AJAX_SUCCESS),codeRuleLog.getRuleValue());
    }


}

package com.rzdata.setting.controller;

import java.util.List;
import java.util.Arrays;

import lombok.RequiredArgsConstructor;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.rzdata.framework.annotation.RepeatSubmit;
import com.rzdata.framework.annotation.Log;
import com.rzdata.framework.core.controller.BaseController;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import com.rzdata.framework.core.validate.QueryGroup;
import com.rzdata.framework.enums.BusinessType;
import com.rzdata.framework.utils.poi.ExcelUtil;
import com.rzdata.setting.domain.vo.CodeRuleDetailVo;
import com.rzdata.setting.domain.bo.CodeRuleDetailBo;
import com.rzdata.setting.service.ICodeRuleDetailService;
import com.rzdata.framework.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiOperation;

/**
 * 编号规则明细Controller
 *
 * <AUTHOR>
 * @date 2021-12-27
 */
@Validated
@Api(value = "编号规则明细控制器", tags = {"编号规则明细管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/setting/codeRuleDetail")
public class CodeRuleDetailController extends BaseController {

    private final ICodeRuleDetailService iCodeRuleDetailService;

    /**
     * 查询编号规则明细列表
     */
    @ApiOperation("查询编号规则明细列表")
    @GetMapping("/list")
    public TableDataInfo<CodeRuleDetailVo> list(@Validated(QueryGroup.class) CodeRuleDetailBo bo) {
        return iCodeRuleDetailService.queryPageList(bo);
    }

    /**
     * 导出编号规则明细列表
     */
    @ApiOperation("导出编号规则明细列表")
    @Log(title = "编号规则明细", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public void export(@Validated CodeRuleDetailBo bo, HttpServletResponse response) {
        List<CodeRuleDetailVo> list = iCodeRuleDetailService.queryList(bo);
        ExcelUtil.exportExcel(list, "编号规则明细", CodeRuleDetailVo.class, response);
    }

    /**
     * 获取编号规则明细详细信息
     */
    @ApiOperation("获取编号规则明细详细信息")
    @GetMapping("/{id}")
    public AjaxResult<CodeRuleDetailVo> getInfo(@ApiParam("主键")
                                                  @NotNull(message = "主键不能为空")
                                                  @PathVariable("id") String id) {
        return AjaxResult.success(iCodeRuleDetailService.queryById(id));
    }

    /**
     * 新增编号规则明细
     */
    @ApiOperation("新增编号规则明细")
    @Log(title = "编号规则明细", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public AjaxResult<Void> add(@Validated(AddGroup.class) @RequestBody CodeRuleDetailBo bo) {
        return toAjax(iCodeRuleDetailService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改编号规则明细
     */
    @ApiOperation("修改编号规则明细")
    @Log(title = "编号规则明细", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public AjaxResult<Void> edit(@Validated(EditGroup.class) @RequestBody CodeRuleDetailBo bo) {
        return toAjax(iCodeRuleDetailService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除编号规则明细
     */
    @ApiOperation("删除编号规则明细")
    @Log(title = "编号规则明细" , businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult<Void> remove(@ApiParam("主键串")
                                       @NotEmpty(message = "主键不能为空")
                                       @PathVariable String[] ids) {
        return toAjax(iCodeRuleDetailService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}

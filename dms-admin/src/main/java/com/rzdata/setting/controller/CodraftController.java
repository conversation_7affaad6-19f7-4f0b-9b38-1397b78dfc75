package com.rzdata.setting.controller;

import com.rzdata.framework.annotation.Log;
import com.rzdata.framework.annotation.RepeatSubmit;
import com.rzdata.framework.core.controller.BaseController;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import com.rzdata.framework.core.validate.QueryGroup;
import com.rzdata.framework.enums.BusinessType;
import com.rzdata.framework.utils.poi.ExcelUtil;
import com.rzdata.setting.domain.bo.CodraftBo;
import com.rzdata.setting.domain.vo.CodraftVo;
import com.rzdata.setting.service.ICodraftService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * 合稿管理Controller
 *
 * <AUTHOR>
 * @date 2022-03-02
 */
@Validated
@Api(value = "合稿管理控制器", tags = {"合稿管理管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/setting/codraft")
public class CodraftController extends BaseController {

    private final ICodraftService iCodraftService;

    /**
     * 查询合稿管理列表
     */
    @ApiOperation("查询合稿管理列表")
    @GetMapping("/list")
    public TableDataInfo<CodraftVo> list(@Validated(QueryGroup.class) CodraftBo bo) {
        return iCodraftService.queryPageList(bo);
    }

    /**
     * 导出合稿管理列表
     */
    @ApiOperation("导出合稿管理列表")
    @Log(title = "合稿管理", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public void export(@Validated CodraftBo bo, HttpServletResponse response) {
        List<CodraftVo> list = iCodraftService.queryList(bo);
        ExcelUtil.exportExcel(list, "合稿管理", CodraftVo.class, response);
    }

    /**
     * 获取合稿管理详细信息
     */
    @ApiOperation("获取合稿管理详细信息")
    @GetMapping("/{id}")
    public AjaxResult<CodraftVo> getInfo(@ApiParam("主键")
                                                  @NotNull(message = "主键不能为空")
                                                  @PathVariable("id") Long id) {
        return AjaxResult.success(iCodraftService.queryById(id));
    }

    /**
     * 新增合稿管理
     */
    @ApiOperation("新增合稿管理")
    @Log(title = "合稿管理", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public AjaxResult<Void> add(@Validated(AddGroup.class) @RequestBody CodraftBo bo) {
        return toAjax(iCodraftService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改合稿管理
     */
    @ApiOperation("修改合稿管理")
    @Log(title = "合稿管理", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public AjaxResult<Void> edit(@Validated(EditGroup.class) @RequestBody CodraftBo bo) {
        return toAjax(iCodraftService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除合稿管理
     */
    @ApiOperation("删除合稿管理")
    @Log(title = "合稿管理" , businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult<Void> remove(@ApiParam("主键串")
                                       @NotEmpty(message = "主键不能为空")
                                       @PathVariable Long[] ids) {
        return toAjax(iCodraftService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}

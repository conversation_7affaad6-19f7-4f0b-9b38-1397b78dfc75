package com.rzdata.setting.controller;

import com.rzdata.framework.annotation.Log;
import com.rzdata.framework.annotation.RepeatSubmit;
import com.rzdata.framework.core.controller.BaseController;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import com.rzdata.framework.core.validate.QueryGroup;
import com.rzdata.framework.enums.BusinessType;
import com.rzdata.framework.utils.poi.ExcelUtil;
import com.rzdata.setting.domain.bo.CombinedFileClassBo;
import com.rzdata.setting.domain.vo.CombinedFileClassVo;
import com.rzdata.setting.service.ICombinedFileClassService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * 合稿文件分类Controller
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Validated
@Api(value = "合稿文件分类控制器", tags = {"合稿文件分类管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/combined/fileClass")
public class CombinedFileClassController extends BaseController {

    private final ICombinedFileClassService iCombinedFileClassService;

    /**
     * 查询合稿文件分类列表
     */
    @ApiOperation("查询合稿文件分类列表")
    @PreAuthorize("@ss.hasPermi('combined:fileClass:list')")
    @GetMapping("/list")
    public TableDataInfo<CombinedFileClassVo> list(@Validated(QueryGroup.class) CombinedFileClassBo bo) {
        return iCombinedFileClassService.queryPageList(bo);
    }

    /**
     * 导出合稿文件分类列表
     */
    @ApiOperation("导出合稿文件分类列表")
    @PreAuthorize("@ss.hasPermi('combined:fileClass:export')")
    @Log(title = "合稿文件分类", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public void export(@Validated CombinedFileClassBo bo, HttpServletResponse response) {
        List<CombinedFileClassVo> list = iCombinedFileClassService.queryList(bo);
        ExcelUtil.exportExcel(list, "合稿文件分类", CombinedFileClassVo.class, response);
    }

    /**
     * 获取合稿文件分类详细信息
     */
    @ApiOperation("获取合稿文件分类详细信息")
    @PreAuthorize("@ss.hasPermi('combined:fileClass:query')")
    @GetMapping("/{id}")
    public AjaxResult<CombinedFileClassVo> getInfo(@ApiParam("主键")
                                                  @NotNull(message = "主键不能为空")
                                                  @PathVariable("id") String id) {
        return AjaxResult.success(iCombinedFileClassService.queryById(id));
    }

    /**
     * 新增合稿文件分类
     */
    @ApiOperation("新增合稿文件分类")
    @PreAuthorize("@ss.hasPermi('combined:fileClass:add')")
    @Log(title = "合稿文件分类", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public AjaxResult<Void> add(@Validated(AddGroup.class) @RequestBody CombinedFileClassBo bo) {
        return toAjax(iCombinedFileClassService.saveCombinedFileClass(bo) ? 1 : 0);
    }

    /**
     * 修改合稿文件分类
     */
    @ApiOperation("修改合稿文件分类")
    @PreAuthorize("@ss.hasPermi('combined:fileClass:edit')")
    @Log(title = "合稿文件分类", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public AjaxResult<Void> edit(@Validated(EditGroup.class) @RequestBody CombinedFileClassBo bo) {
        return toAjax(iCombinedFileClassService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除合稿文件分类
     */
    @ApiOperation("删除合稿文件分类")
    @PreAuthorize("@ss.hasPermi('combined:fileClass:remove')")
    @Log(title = "合稿文件分类" , businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult<Void> remove(@ApiParam("主键串")
                                       @NotEmpty(message = "主键不能为空")
                                       @PathVariable String[] ids) {
        return toAjax(iCombinedFileClassService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}

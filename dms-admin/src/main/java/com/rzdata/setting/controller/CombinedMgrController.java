package com.rzdata.setting.controller;

import java.util.Arrays;
import java.util.List;

import com.rzdata.setting.domain.bo.CombinedMgrBo;
import com.rzdata.setting.domain.vo.CombinedMgrVo;
import com.rzdata.setting.service.ICombinedMgrService;
import lombok.RequiredArgsConstructor;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.rzdata.framework.annotation.RepeatSubmit;
import com.rzdata.framework.annotation.Log;
import com.rzdata.framework.core.controller.BaseController;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import com.rzdata.framework.core.validate.QueryGroup;
import com.rzdata.framework.enums.BusinessType;
import com.rzdata.framework.utils.poi.ExcelUtil;
import com.rzdata.framework.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiOperation;

/**
 * 合稿管理Controller
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Validated
@Api(value = "合稿管理控制器", tags = {"合稿管理管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/combined/mgr")
public class CombinedMgrController extends BaseController {

    private final ICombinedMgrService iCombinedMgrService;

    /**
     * 查询合稿管理列表
     */
    @ApiOperation("查询合稿管理列表")
    @PreAuthorize("@ss.hasPermi('combined:mgr:list')")
    @GetMapping("/list")
    public TableDataInfo<CombinedMgrVo> list(@Validated(QueryGroup.class) CombinedMgrBo bo) {
        return iCombinedMgrService.queryPageList(bo);
    }

    /**
     * 导出合稿管理列表
     */
    @ApiOperation("导出合稿管理列表")
    @PreAuthorize("@ss.hasPermi('combined:mgr:export')")
    @Log(title = "合稿管理", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public void export(@Validated CombinedMgrBo bo, HttpServletResponse response) {
        List<CombinedMgrVo> list = iCombinedMgrService.queryList(bo);
        ExcelUtil.exportExcel(list, "合稿管理", CombinedMgrVo.class, response);
    }

    /**
     * 获取合稿管理详细信息
     */
    @ApiOperation("获取合稿管理详细信息")
    @PreAuthorize("@ss.hasPermi('combined:mgr:query')")
    @GetMapping("/{id}")
    public AjaxResult<CombinedMgrVo> getInfo(@ApiParam("主键")
                                                  @NotNull(message = "主键不能为空")
                                                  @PathVariable("id") String id) {
        return AjaxResult.success(iCombinedMgrService.queryById(id));
    }

    /**
     * 新增合稿管理
     */
    @ApiOperation("新增合稿管理")
    @PreAuthorize("@ss.hasPermi('combined:mgr:add')")
    @Log(title = "合稿管理", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public AjaxResult<Void> add(@Validated(AddGroup.class) @RequestBody CombinedMgrBo bo) {
        return toAjax(iCombinedMgrService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改合稿管理
     */
    @ApiOperation("修改合稿管理")
    @PreAuthorize("@ss.hasPermi('combined:mgr:edit')")
    @Log(title = "合稿管理", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public AjaxResult<Void> edit(@Validated(EditGroup.class) @RequestBody CombinedMgrBo bo) {
        return toAjax(iCombinedMgrService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除合稿管理
     */
    @ApiOperation("删除合稿管理")
    @PreAuthorize("@ss.hasPermi('combined:mgr:remove')")
    @Log(title = "合稿管理" , businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult<Void> remove(@ApiParam("主键串")
                                       @NotEmpty(message = "主键不能为空")
                                       @PathVariable String[] ids) {
        return toAjax(iCombinedMgrService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}

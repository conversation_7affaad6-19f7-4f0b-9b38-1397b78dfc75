package com.rzdata.setting.controller;

import java.util.List;
import java.util.Arrays;

import lombok.RequiredArgsConstructor;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.rzdata.framework.annotation.RepeatSubmit;
import com.rzdata.framework.annotation.Log;
import com.rzdata.framework.core.controller.BaseController;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import com.rzdata.framework.core.validate.QueryGroup;
import com.rzdata.framework.enums.BusinessType;
import com.rzdata.framework.utils.poi.ExcelUtil;
import com.rzdata.setting.domain.vo.DistributeGroupVo;
import com.rzdata.setting.domain.bo.DistributeGroupBo;
import com.rzdata.setting.service.IDistributeGroupService;
import com.rzdata.framework.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiOperation;

/**
 * 预设分组人员Controller
 *
 * <AUTHOR>
 * @date 2024-11-26
 */
@Validated
@Api(value = "预设分组人员控制器", tags = {"预设分组人员管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/setting/distributeGroup")
public class DistributeGroupController extends BaseController {

    private final IDistributeGroupService iDistributeGroupService;

    /**
     * 查询预设分组人员列表
     */
    @ApiOperation("查询预设分组人员列表")
    @GetMapping("/list")
    public TableDataInfo<DistributeGroupVo> list(@Validated(QueryGroup.class) DistributeGroupBo bo) {
        return iDistributeGroupService.queryPageList(bo);
    }

    /**
     * 导出预设分组人员列表
     */
    @ApiOperation("导出预设分组人员列表")
    @Log(title = "预设分组人员", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public void export(@Validated DistributeGroupBo bo, HttpServletResponse response) {
        List<DistributeGroupVo> list = iDistributeGroupService.queryList(bo);
        ExcelUtil.exportExcel(list, "预设分组人员", DistributeGroupVo.class, response);
    }

    /**
     * 获取预设分组人员详细信息
     */
    @ApiOperation("获取预设分组人员详细信息")
    @GetMapping("/{id}")
    public AjaxResult<DistributeGroupVo> getInfo(@ApiParam("主键")
                                                  @NotNull(message = "主键不能为空")
                                                  @PathVariable("id") String id) {
        return AjaxResult.success(iDistributeGroupService.queryById(id));
    }

    /**
     * 新增预设分组人员
     */
    @ApiOperation("新增预设分组人员")
    @Log(title = "预设分组人员", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public AjaxResult<Void> add(@Validated(AddGroup.class) @RequestBody DistributeGroupBo bo) {
        return toAjax(iDistributeGroupService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改预设分组人员
     */
    @ApiOperation("修改预设分组人员")
    @Log(title = "预设分组人员", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public AjaxResult<Void> edit(@Validated(EditGroup.class) @RequestBody DistributeGroupBo bo) {
        return toAjax(iDistributeGroupService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除预设分组人员
     */
    @ApiOperation("删除预设分组人员")
    @Log(title = "预设分组人员" , businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult<Void> remove(@ApiParam("主键串")
                                       @NotEmpty(message = "主键不能为空")
                                       @PathVariable String[] ids) {
        return toAjax(iDistributeGroupService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }

    /**
     * 获取预设分组人员详细信息
     */
    @ApiOperation("获取预设分组人员详细信息")
    @GetMapping("/list/{docClass}")
    public AjaxResult<List<DistributeGroupVo>> listByDocClass(@ApiParam("主键")
                                                 @NotNull(message = "主键不能为空")
                                                 @PathVariable("docClass") String docClass) {
        return AjaxResult.success(iDistributeGroupService.listByDocClass(docClass));
    }
}

package com.rzdata.setting.controller;

import com.rzdata.framework.annotation.Log;
import com.rzdata.framework.annotation.RepeatSubmit;
import com.rzdata.framework.core.controller.BaseController;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import com.rzdata.framework.core.validate.QueryGroup;
import com.rzdata.framework.enums.BusinessType;
import com.rzdata.framework.utils.poi.ExcelUtil;
import com.rzdata.setting.domain.bo.DistributeGroupDetailBo;
import com.rzdata.setting.domain.vo.DistributeGroupDetailVo;
import com.rzdata.setting.service.IDistributeGroupDetailService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * 预设分组人员详情Controller
 *
 * <AUTHOR>
 * @date 2024-11-26
 */
@Validated
@Api(value = "预设分组人员详情控制器", tags = {"预设分组人员详情管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/setting/distributeGroupDetail")
public class DistributeGroupDetailController extends BaseController {

    private final IDistributeGroupDetailService iDistributeGroupDetailService;

    /**
     * 查询预设分组人员详情列表
     */
    @ApiOperation("查询预设分组人员详情列表")
    @GetMapping("/list")
    public TableDataInfo<DistributeGroupDetailVo> list(@Validated(QueryGroup.class) DistributeGroupDetailBo bo) {
        return iDistributeGroupDetailService.queryPageList(bo);
    }

    /**
     * 导出预设分组人员详情列表
     */
    @ApiOperation("导出预设分组人员详情列表")
    @Log(title = "预设分组人员详情", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public void export(@Validated DistributeGroupDetailBo bo, HttpServletResponse response) {
        List<DistributeGroupDetailVo> list = iDistributeGroupDetailService.queryList(bo);
        ExcelUtil.exportExcel(list, "预设分组人员详情", DistributeGroupDetailVo.class, response);
    }

    /**
     * 获取预设分组人员详情详细信息
     */
    @ApiOperation("获取预设分组人员详情详细信息")
    @GetMapping("/{id}")
    public AjaxResult<DistributeGroupDetailVo> getInfo(@ApiParam("主键")
                                                  @NotNull(message = "主键不能为空")
                                                  @PathVariable("id") String id) {
        return AjaxResult.success(iDistributeGroupDetailService.queryById(id));
    }

    /**
     * 新增预设分组人员详情
     */
    @ApiOperation("新增预设分组人员详情")
    @Log(title = "预设分组人员详情", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public AjaxResult<Void> add(@Validated(AddGroup.class) @RequestBody DistributeGroupDetailBo bo) {
        return toAjax(iDistributeGroupDetailService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改预设分组人员详情
     */
    @ApiOperation("修改预设分组人员详情")
    @Log(title = "预设分组人员详情", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public AjaxResult<Void> edit(@Validated(EditGroup.class) @RequestBody DistributeGroupDetailBo bo) {
        return toAjax(iDistributeGroupDetailService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除预设分组人员详情
     */
    @ApiOperation("删除预设分组人员详情")
    @Log(title = "预设分组人员详情" , businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult<Void> remove(@ApiParam("主键串")
                                       @NotEmpty(message = "主键不能为空")
                                       @PathVariable String[] ids) {
        return toAjax(iDistributeGroupDetailService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}

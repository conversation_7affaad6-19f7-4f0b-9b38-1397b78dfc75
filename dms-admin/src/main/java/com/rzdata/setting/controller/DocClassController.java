package com.rzdata.setting.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.tree.Tree;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.rzdata.framework.annotation.Log;
import com.rzdata.framework.annotation.RepeatSubmit;
import com.rzdata.framework.constant.CommonI18nConstant;
import com.rzdata.framework.core.controller.BaseController;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.core.domain.entity.SysUser;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import com.rzdata.framework.core.validate.QueryGroup;
import com.rzdata.framework.enums.BusinessType;
import com.rzdata.framework.utils.I18nUtils;
import com.rzdata.framework.utils.poi.ExcelUtil;
import com.rzdata.process.domain.vo.ApplyRelationVo;
import com.rzdata.setting.domain.DocClass;
import com.rzdata.setting.domain.bo.DocClassBo;
import com.rzdata.setting.domain.vo.DocClassVo;
import com.rzdata.setting.service.IDocClassService;
import com.rzdata.system.domain.vo.SysUserImportVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.rmi.ServerException;
import java.util.Arrays;
import java.util.List;

/**
 * 文件类型Controller
 *
 * <AUTHOR>
 * @date 2021-12-27
 */
@Validated
@Api(value = "文件类型控制器", tags = {"文件类型管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/setting/docClass")
public class DocClassController extends BaseController {

    private final IDocClassService iDocClassService;

    /**
     * 查询文件类型列表
     */
    @ApiOperation("查询文件类型列表")
    @GetMapping("/list")
    public TableDataInfo<DocClassVo> list(@Validated(QueryGroup.class) DocClassBo bo) {
        return iDocClassService.queryPageList(bo);
    }

    @ApiOperation("更新排序")
    @GetMapping("/sort/{ids}")
    public AjaxResult<Void> settingDocClassSort(@PathVariable String[] ids){
        for (int i=0;i<ids.length;i++) {
           iDocClassService.update(new LambdaUpdateWrapper<DocClass>().eq(DocClass::getId,ids[i]).set(DocClass::getSort,i+1));
        }
        return AjaxResult.success();
    }

    /**
     * 获取部门下拉树列表
     */
    @ApiOperation("查询文件类型下拉树列表")
    @PostMapping("/treeseList")
    public AjaxResult<List<Tree<String>>> treeSelect(DocClassBo bo) {
        List<DocClassVo> docClassVo = iDocClassService.queryList(bo);
        return AjaxResult.success(iDocClassService.buildTreeSelect(docClassVo));
    }

    @ApiOperation("判断分类代码是否已经存在 大于则存在 小于等于0 则不存在")
    @GetMapping("/isExistByCode/{code}")
    public AjaxResult<Long> isExistByCode(@PathVariable("code") String code) {
        QueryWrapper<DocClass> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(DocClass:: getId, code);
        return AjaxResult.success(iDocClassService.count(queryWrapper));
    }

    @ApiOperation("判断分类名称是否已经存在 大于则存在 小于等于0 则不存在")
    @GetMapping("/isExistByName/{name}")
    public AjaxResult<Long> isExistByName(@PathVariable("name") String name) {
        QueryWrapper<DocClass> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(DocClass:: getClassName, name);
        return AjaxResult.success(iDocClassService.count(queryWrapper));
    }

    /**
     * 导出文件类型列表
     */
    @ApiOperation("导出文件类型列表")
    @Log(title = "文件类型", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(@Validated DocClassBo bo, HttpServletResponse response) {
        List<DocClassVo> list = iDocClassService.queryList(bo);
        ExcelUtil.exportExcel(list, "文件类型", DocClassVo.class, response);
    }
    @Log(title = "文件类型导入", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public AjaxResult<Void> importData(@RequestPart("file") MultipartFile file, boolean updateSupport,String dataType) throws Exception {
        List<DocClassVo> userListVo = ExcelUtil.importExcel(file.getInputStream(), DocClassVo.class);
        List<DocClass> docList = BeanUtil.copyToList(userListVo, DocClass.class);
        String message = iDocClassService.importData(docList, updateSupport, dataType);
        return AjaxResult.success(message);
    }
    /**
     * 获取文件类型详细信息
     */
    @ApiOperation("获取文件类型详细信息")
    @GetMapping("/{id}")
    public AjaxResult<DocClassVo> getInfo(@ApiParam("主键")
                                                  @NotNull(message = "主键不能为空")
                                                  @PathVariable("id") String id) {
        return AjaxResult.success(iDocClassService.queryById(id));
    }

    /**
     * 新增文件类型
     */
    @ApiOperation("新增文件类型")
    @Log(title = "文件类型", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public AjaxResult<String> add(@Validated(AddGroup.class) @RequestBody DocClassBo bo) throws ServerException {
        return AjaxResult.success(I18nUtils.getTitle(CommonI18nConstant.FILE_BASE_SUCCESS), iDocClassService.insertByBo(bo));
    }


    /**
     * 修改文件类型(设置)
     */
    @ApiOperation("修改文件类型(设置)")
    @Log(title = "文件类型", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public AjaxResult<Void> edit(@Validated(EditGroup.class) @RequestBody DocClassBo bo) {
        return toAjax(iDocClassService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除文件类型
     */
    @ApiOperation("删除文件类型")
    @Log(title = "文件类型" , businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult<Void> remove(@ApiParam("主键串")
                                       @NotEmpty(message = "主键不能为空")
                                       @PathVariable String[] ids) {
        return toAjax(iDocClassService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }

    @ApiOperation("启/禁用 1：启用，0：禁用")
    @GetMapping("/revDisable")
    public AjaxResult<Void> revDisable(@RequestParam("id")String id, @RequestParam("status")String status) {
        iDocClassService.revDisable(id, status);
        return AjaxResult.success();
    }

    /**
     * 初始化祖级列表i
     */
    @ApiOperation("初始化祖级列表i")
    @GetMapping("/init/ancestors")
    public  AjaxResult<Void> initAncestors() {
        iDocClassService.initAncestors();
        return AjaxResult.success();
    }

    /**
     * 查询dms_zonci_sh库中有效的文件分类
     */
    @ApiOperation("查询dms_zonci_sh库中有效的文件分类")
    @GetMapping("/zonci/active")
    public AjaxResult<List<DocClassVo>> getZonciActiveDocClass(@RequestParam("dbName")String dbName) {
        List<DocClassVo> result = iDocClassService.getZonciActiveDocClass(dbName);
        return AjaxResult.success(result);
    }

}

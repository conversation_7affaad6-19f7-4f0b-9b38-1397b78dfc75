package com.rzdata.setting.controller;

import java.util.List;
import java.util.Arrays;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.rzdata.framework.core.domain.entity.SysDictData;
import com.rzdata.framework.utils.StringUtils;
import com.rzdata.setting.domain.DocClassFlow;
import com.rzdata.system.service.WorkflowService;
import lombok.RequiredArgsConstructor;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;

import org.apache.commons.lang3.ObjectUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.rzdata.framework.annotation.RepeatSubmit;
import com.rzdata.framework.annotation.Log;
import com.rzdata.framework.core.controller.BaseController;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import com.rzdata.framework.core.validate.QueryGroup;
import com.rzdata.framework.enums.BusinessType;
import com.rzdata.framework.utils.poi.ExcelUtil;
import com.rzdata.setting.domain.vo.DocClassFlowVo;
import com.rzdata.setting.domain.bo.DocClassFlowBo;
import com.rzdata.setting.service.IDocClassFlowService;
import com.rzdata.framework.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiOperation;

/**
 * 文件分类设置-流程设置Controller
 *
 * <AUTHOR>
 * @date 2023-01-10
 */
@Validated
@Api(value = "文件分类设置-流程设置控制器", tags = {"文件分类设置-流程设置管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/setting/docClassFlow")
public class DocClassFlowController extends BaseController {

    private final IDocClassFlowService iDocClassFlowService;

    private final WorkflowService workflowService;

    /**
     * 获取业务类型流程模板(前端文件类型设置-文件流程设置-可选流程模板清单)
     *
     * @param bo  DocClassFlowBo
     * @return
     */
    @ApiOperation("获取业务类型流程模板")
    @GetMapping("/queryFlowList")
    public AjaxResult<List<SysDictData>> queryFlowTemplateList(DocClassFlowBo bo) {
        List<SysDictData> result = workflowService.queryFlowList(bo.getFlowKey());
        return AjaxResult.success(result);
    }


    /**
     * 根据文件分类和业务类型，获取文件分类流程配置（向上级联）
     * @param bo
     * @return
     */
    @ApiOperation("根据文件分类和业务类型，向上级联获取文件分类流程配置")
    @GetMapping("/getByUpDocClassAndBizType")
    public AjaxResult<DocClassFlowVo> getByUpDocClassAndBizType(DocClassFlowBo bo) {
        DocClassFlowVo result = iDocClassFlowService.getByUpDocClassAndBizType(bo.getDocClass(),bo.getBizType(),"");
        return AjaxResult.success(result);
    }


    /**
     * 根据文件分类获取分类的所有流程设置信息
     * @param docClass
     * @return
     */
    @ApiOperation("根据文件分类获取分类的所有流程设置信息")
    @GetMapping("/docClass/{docClass}")
    public AjaxResult<List<DocClassFlowVo>> getByDocClass(@ApiParam("文件分类")
                                              @NotNull(message = "文件分类不能为空")
                                              @PathVariable("docClass") String docClass
                                           ) {
        List<DocClassFlowVo> result = iDocClassFlowService.listVo(new QueryWrapper<>(new DocClassFlow().setDocClass(docClass)));
        for(DocClassFlowVo vo : result) {
            this.iDocClassFlowService.buildDeepClassFlowData(vo);
        }
        return AjaxResult.success(result);
    }

    /**
     * 新增保存
     * @param list
     * @return
     */
    @ApiOperation("新增保存")
    @PostMapping("/save")
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult saveOrUpdate(@RequestBody List<DocClassFlowBo> list) {
        if(list != null) {
            // 先根据文件分类进行物理删除相关表数据
            this.iDocClassFlowService.removeClassFlowCascade(list);
            // 保存相关表数据
            this.iDocClassFlowService.insertClassFlowCascade(list);
        }
        return AjaxResult.success();
    }


    /**
     * 查询文件分类设置-流程设置列表
     */
    @ApiOperation("查询文件分类设置-流程设置列表")
    @PreAuthorize("@ss.hasPermi('setting:docClassFlow:list')")
    @GetMapping("/list")
    public TableDataInfo<DocClassFlowVo> list(@Validated(QueryGroup.class) DocClassFlowBo bo) {
        return iDocClassFlowService.queryPageList(bo);
    }

    /**
     * 导出文件分类设置-流程设置列表
     */
    @ApiOperation("导出文件分类设置-流程设置列表")
    @PreAuthorize("@ss.hasPermi('setting:docClassFlow:export')")
    @Log(title = "文件分类设置-流程设置", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public void export(@Validated DocClassFlowBo bo, HttpServletResponse response) {
        List<DocClassFlowVo> list = iDocClassFlowService.queryList(bo);
        ExcelUtil.exportExcel(list, "文件分类设置-流程设置", DocClassFlowVo.class, response);
    }


    /**
     * 获取文件分类设置-流程设置详细信息
     */
    @ApiOperation("获取文件分类设置-流程设置详细信息")
    @PreAuthorize("@ss.hasPermi('setting:docClassFlow:query')")
    @GetMapping("/{id}")
    public AjaxResult<DocClassFlowVo> getInfo(@ApiParam("主键")
                                              @NotNull(message = "主键不能为空")
                                              @PathVariable("id") String id) {
        return AjaxResult.success(iDocClassFlowService.queryById(id));
    }


    /**
     * 新增文件分类设置-流程设置
     */
    @ApiOperation("新增文件分类设置-流程设置")
    @PreAuthorize("@ss.hasPermi('setting:docClassFlow:add')")
    @Log(title = "文件分类设置-流程设置", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public AjaxResult<Void> add(@Validated(AddGroup.class) @RequestBody DocClassFlowBo bo) {
        return toAjax(iDocClassFlowService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改文件分类设置-流程设置
     */
    @ApiOperation("修改文件分类设置-流程设置")
    @PreAuthorize("@ss.hasPermi('setting:docClassFlow:edit')")
    @Log(title = "文件分类设置-流程设置", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public AjaxResult<Void> edit(@Validated(EditGroup.class) @RequestBody DocClassFlowBo bo) {
        return toAjax(iDocClassFlowService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除文件分类设置-流程设置
     */
    @ApiOperation("删除文件分类设置-流程设置")
    @PreAuthorize("@ss.hasPermi('setting:docClassFlow:remove')")
    @Log(title = "文件分类设置-流程设置" , businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult<Void> remove(@ApiParam("主键串")
                                       @NotEmpty(message = "主键不能为空")
                                       @PathVariable String[] ids) {
        return toAjax(iDocClassFlowService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}

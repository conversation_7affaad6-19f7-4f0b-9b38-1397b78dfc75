package com.rzdata.setting.controller;

import java.util.List;
import java.util.Arrays;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.rzdata.process.controller.WorkflowController;
import com.rzdata.setting.domain.DocClassFlow;
import com.rzdata.setting.domain.bo.DocClassFlowNodeDetailBo;
import com.rzdata.setting.domain.vo.DocClassFlowVo;
import com.rzdata.setting.service.IDocClassFlowNodeDetailService;
import com.rzdata.setting.service.IDocClassFlowService;
import lombok.RequiredArgsConstructor;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.rzdata.framework.annotation.RepeatSubmit;
import com.rzdata.framework.annotation.Log;
import com.rzdata.framework.core.controller.BaseController;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import com.rzdata.framework.core.validate.QueryGroup;
import com.rzdata.framework.enums.BusinessType;
import com.rzdata.framework.utils.poi.ExcelUtil;
import com.rzdata.setting.domain.vo.DocClassFlowNodeVo;
import com.rzdata.setting.domain.bo.DocClassFlowNodeBo;
import com.rzdata.setting.service.IDocClassFlowNodeService;
import com.rzdata.framework.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiOperation;

/**
 * 文件分类设置-流程节点设置Controller
 *
 * <AUTHOR>
 * @date 2023-01-10
 */
@Validated
@Api(value = "文件分类设置-流程节点设置控制器", tags = {"文件分类设置-流程节点设置管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/setting/docClassFlowNode")
public class DocClassFlowNodeController extends BaseController {

    private final IDocClassFlowNodeService iDocClassFlowNodeService;

    private final IDocClassFlowService iDocClassFlowService;

    private final IDocClassFlowNodeDetailService iDocClassFlowNodeDetailService;

    com.rzdata.process.controller.WorkflowController workflowController;

    /**
     * 与流程平台比较流程环节数据
     */
    @ApiOperation("与流程平台比较流程环节数据")
    @GetMapping("/compareFlowPlatNodeList/{flowKey}/{id}")
    public AjaxResult compareFlowPlatNodeList(
            @ApiParam("流程KEY")
            @NotNull(message = "流程KEY不能为空")
            @PathVariable("flowKey") String flowKey,
            @ApiParam("id")
            @PathVariable("id") String id) {
        return AjaxResult.success(this.iDocClassFlowNodeService.compareFlowPlatNodeList(flowKey,"null".equals(id) ? "" : id));
    }

    /**
     * 与流程平台同步流程环节数据
     */
    @ApiOperation("与流程平台同步流程环节数据")
    @GetMapping("/syncFlowPlatNodeList/{docClass}/{bizType}/{flowKey}/{id}")
    public AjaxResult<List<DocClassFlowNodeVo>> syncFlowPlatNodeList(
            @ApiParam("文件分类")
            @NotNull(message = "文件分类不能为空")
            @PathVariable("docClass") String docClass,
            @ApiParam("业务类型")
            @NotNull(message = "业务类型不能为空")
            @PathVariable("bizType") String bizType,
            @ApiParam("流程KEY")
            @NotNull(message = "流程KEY不能为空")
            @PathVariable("flowKey") String flowKey,
            @ApiParam("id")
            @PathVariable("id") String id) {
        return this.iDocClassFlowNodeService.syncFlowPlatNodeList(docClass,bizType,flowKey,"null".equals(id) ? "" : id);
    }


    /**
     * 根据文件分类、业务类型、流程KEY获取流程节点清单
     */
    @ApiOperation("根据文件分类、业务类型、流程KEY获取流程节点清单")
    @GetMapping("/getByFlowKey/{docClass}/{bizType}/{flowKey}")
    public AjaxResult<List<DocClassFlowNodeVo>> getByFlowKey(
            @ApiParam("文件分类")
            @NotNull(message = "文件分类不能为空")
            @PathVariable("docClass") String docClass,
            @ApiParam("业务类型")
            @NotNull(message = "业务类型不能为空")
            @PathVariable("bizType") String bizType,
            @ApiParam("流程KEY")
                                                  @NotNull(message = "流程KEY不能为空")
                                                  @PathVariable("flowKey") String flowKey) {
        DocClassFlowVo docClassFlowVo = iDocClassFlowService.getVoOne(new QueryWrapper<DocClassFlow>(new DocClassFlow().setDocClass(docClass).setBizType(bizType).setFlowKey(flowKey)));
        if(docClassFlowVo != null) {
            DocClassFlowNodeBo bo = new DocClassFlowNodeBo();
            bo.setFlowId(docClassFlowVo.getId());
            List<DocClassFlowNodeVo> list = iDocClassFlowNodeService.queryList(bo);
            for(DocClassFlowNodeVo vo : list) {
                // 设置环节权限清单
                DocClassFlowNodeDetailBo detailBo = new DocClassFlowNodeDetailBo();
                detailBo.setNodeId(vo.getId());
                vo.setNodeDetailList(iDocClassFlowNodeDetailService.queryList(detailBo));
            }
            return AjaxResult.success(list);
        } else {
            return AjaxResult.success("",null);
        }
    }


    /**
     * 查询文件分类设置-流程节点设置列表
     */
    @ApiOperation("查询文件分类设置-流程节点设置列表")
    @PreAuthorize("@ss.hasPermi('setting:docClassFlowNode:list')")
    @GetMapping("/list")
    public TableDataInfo<DocClassFlowNodeVo> list(@Validated(QueryGroup.class) DocClassFlowNodeBo bo) {
        return iDocClassFlowNodeService.queryPageList(bo);
    }

    /**
     * 导出文件分类设置-流程节点设置列表
     */
    @ApiOperation("导出文件分类设置-流程节点设置列表")
    @PreAuthorize("@ss.hasPermi('setting:docClassFlowNode:export')")
    @Log(title = "文件分类设置-流程节点设置", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public void export(@Validated DocClassFlowNodeBo bo, HttpServletResponse response) {
        List<DocClassFlowNodeVo> list = iDocClassFlowNodeService.queryList(bo);
        ExcelUtil.exportExcel(list, "文件分类设置-流程节点设置", DocClassFlowNodeVo.class, response);
    }

    /**
     * 获取文件分类设置-流程节点设置详细信息
     */
    @ApiOperation("获取文件分类设置-流程节点设置详细信息")
    @PreAuthorize("@ss.hasPermi('setting:docClassFlowNode:query')")
    @GetMapping("/{id}")
    public AjaxResult<DocClassFlowNodeVo> getInfo(@ApiParam("主键")
                                                  @NotNull(message = "主键不能为空")
                                                  @PathVariable("id") String id) {
        return AjaxResult.success(iDocClassFlowNodeService.queryById(id));
    }

    /**
     * 新增文件分类设置-流程节点设置
     */
    @ApiOperation("新增文件分类设置-流程节点设置")
    @PreAuthorize("@ss.hasPermi('setting:docClassFlowNode:add')")
    @Log(title = "文件分类设置-流程节点设置", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public AjaxResult<Void> add(@Validated(AddGroup.class) @RequestBody DocClassFlowNodeBo bo) {
        return toAjax(iDocClassFlowNodeService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改文件分类设置-流程节点设置
     */
    @ApiOperation("修改文件分类设置-流程节点设置")
    @PreAuthorize("@ss.hasPermi('setting:docClassFlowNode:edit')")
    @Log(title = "文件分类设置-流程节点设置", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public AjaxResult<Void> edit(@Validated(EditGroup.class) @RequestBody DocClassFlowNodeBo bo) {
        return toAjax(iDocClassFlowNodeService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除文件分类设置-流程节点设置
     */
    @ApiOperation("删除文件分类设置-流程节点设置")
    @PreAuthorize("@ss.hasPermi('setting:docClassFlowNode:remove')")
    @Log(title = "文件分类设置-流程节点设置" , businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult<Void> remove(@ApiParam("主键串")
                                       @NotEmpty(message = "主键不能为空")
                                       @PathVariable String[] ids) {
        return toAjax(iDocClassFlowNodeService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }

    /**
     * 根据文件分类、业务类型和环节编码，获取环节授权清单
     *
     * @param docClass
     * @param bizType
     * @param code
     * @return
     */
    @ApiOperation("根据文件分类、业务类型和环节授权编码，获取流程节点清单")
    @GetMapping("/getNodeList")
    public AjaxResult<List<DocClassFlowNodeVo>> getNodeList(@RequestParam(value = "docClass", required = false) String docClass,
                                                            @RequestParam("bizType") String bizType,
                                                            @RequestParam("code") String code) {
        return AjaxResult.success(iDocClassFlowNodeService.getNodeList(docClass,bizType,code));
    }
}

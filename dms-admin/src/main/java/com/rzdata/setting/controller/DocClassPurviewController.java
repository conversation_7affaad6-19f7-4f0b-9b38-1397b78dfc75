package com.rzdata.setting.controller;

import java.util.List;
import java.util.Arrays;

import lombok.RequiredArgsConstructor;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.rzdata.framework.annotation.RepeatSubmit;
import com.rzdata.framework.annotation.Log;
import com.rzdata.framework.core.controller.BaseController;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import com.rzdata.framework.core.validate.QueryGroup;
import com.rzdata.framework.enums.BusinessType;
import com.rzdata.framework.utils.poi.ExcelUtil;
import com.rzdata.setting.domain.vo.DocClassPurviewVo;
import com.rzdata.setting.domain.bo.DocClassPurviewBo;
import com.rzdata.setting.service.IDocClassPurviewService;
import com.rzdata.framework.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiOperation;

/**
 * 文件类型权限Controller
 *
 * <AUTHOR>
 * @date 2023-12-01
 */
@Validated
@Api(value = "文件类型权限控制器", tags = {"文件类型权限管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/setting/docClassPurview")
public class DocClassPurviewController extends BaseController {

    private final IDocClassPurviewService iDocClassPurviewService;

    /**
     * 查询文件类型权限列表
     */
    @ApiOperation("查询文件类型权限列表")
    @GetMapping("/list")
    public TableDataInfo<DocClassPurviewVo> list(@Validated(QueryGroup.class) DocClassPurviewBo bo) {
        return iDocClassPurviewService.queryPageList(bo);
    }

    /**
     * 导出文件类型权限列表
     */
    @ApiOperation("导出文件类型权限列表")
    @Log(title = "文件类型权限", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public void export(@Validated DocClassPurviewBo bo, HttpServletResponse response) {
        List<DocClassPurviewVo> list = iDocClassPurviewService.queryList(bo);
        ExcelUtil.exportExcel(list, "文件类型权限", DocClassPurviewVo.class, response);
    }

    /**
     * 获取文件类型权限详细信息
     */
    @ApiOperation("获取文件类型权限详细信息")
    @GetMapping("/{id}")
    public AjaxResult<DocClassPurviewVo> getInfo(@ApiParam("主键")
                                                  @NotNull(message = "主键不能为空")
                                                  @PathVariable("id") String id) {
        return AjaxResult.success(iDocClassPurviewService.queryById(id));
    }

    /**
     * 新增文件类型权限
     */
    @ApiOperation("新增文件类型权限")
    @Log(title = "文件类型权限", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public AjaxResult<Void> add(@Validated(AddGroup.class) @RequestBody DocClassPurviewBo bo) {
        return toAjax(iDocClassPurviewService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改文件类型权限
     */
    @ApiOperation("修改文件类型权限")
    @Log(title = "文件类型权限", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public AjaxResult<Void> edit(@Validated(EditGroup.class) @RequestBody DocClassPurviewBo bo) {
        return toAjax(iDocClassPurviewService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除文件类型权限
     */
    @ApiOperation("删除文件类型权限")
    @Log(title = "文件类型权限" , businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult<Void> remove(@ApiParam("主键串")
                                       @NotEmpty(message = "主键不能为空")
                                       @PathVariable String[] ids) {
        return toAjax(iDocClassPurviewService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}

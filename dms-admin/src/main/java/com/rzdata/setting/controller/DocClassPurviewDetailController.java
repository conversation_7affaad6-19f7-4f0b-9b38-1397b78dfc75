package com.rzdata.setting.controller;

import java.util.List;
import java.util.Arrays;

import lombok.RequiredArgsConstructor;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.rzdata.framework.annotation.RepeatSubmit;
import com.rzdata.framework.annotation.Log;
import com.rzdata.framework.core.controller.BaseController;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import com.rzdata.framework.core.validate.QueryGroup;
import com.rzdata.framework.enums.BusinessType;
import com.rzdata.framework.utils.poi.ExcelUtil;
import com.rzdata.setting.domain.vo.DocClassPurviewDetailVo;
import com.rzdata.setting.domain.bo.DocClassPurviewDetailBo;
import com.rzdata.setting.service.IDocClassPurviewDetailService;
import com.rzdata.framework.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiOperation;

/**
 * 文件类型权限详情Controller
 *
 * <AUTHOR>
 * @date 2023-12-01
 */
@Validated
@Api(value = "文件类型权限详情控制器", tags = {"文件类型权限详情管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/setting/docClassPurviewDetail")
public class DocClassPurviewDetailController extends BaseController {

    private final IDocClassPurviewDetailService iDocClassPurviewDetailService;

    /**
     * 查询文件类型权限详情列表
     */
    @ApiOperation("查询文件类型权限详情列表")
    @GetMapping("/list")
    public TableDataInfo<DocClassPurviewDetailVo> list(@Validated(QueryGroup.class) DocClassPurviewDetailBo bo) {
        return iDocClassPurviewDetailService.queryPageList(bo);
    }

    /**
     * 导出文件类型权限详情列表
     */
    @ApiOperation("导出文件类型权限详情列表")
    @Log(title = "文件类型权限详情", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public void export(@Validated DocClassPurviewDetailBo bo, HttpServletResponse response) {
        List<DocClassPurviewDetailVo> list = iDocClassPurviewDetailService.queryList(bo);
        ExcelUtil.exportExcel(list, "文件类型权限详情", DocClassPurviewDetailVo.class, response);
    }

    /**
     * 获取文件类型权限详情详细信息
     */
    @ApiOperation("获取文件类型权限详情详细信息")
    @GetMapping("/{id}")
    public AjaxResult<DocClassPurviewDetailVo> getInfo(@ApiParam("主键")
                                                  @NotNull(message = "主键不能为空")
                                                  @PathVariable("id") String id) {
        return AjaxResult.success(iDocClassPurviewDetailService.queryById(id));
    }

    /**
     * 新增文件类型权限详情
     */
    @ApiOperation("新增文件类型权限详情")
    @Log(title = "文件类型权限详情", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public AjaxResult<Void> add(@Validated(AddGroup.class) @RequestBody DocClassPurviewDetailBo bo) {
        return toAjax(iDocClassPurviewDetailService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改文件类型权限详情
     */
    @ApiOperation("修改文件类型权限详情")
    @Log(title = "文件类型权限详情", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public AjaxResult<Void> edit(@Validated(EditGroup.class) @RequestBody DocClassPurviewDetailBo bo) {
        return toAjax(iDocClassPurviewDetailService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除文件类型权限详情
     */
    @ApiOperation("删除文件类型权限详情")
    @Log(title = "文件类型权限详情" , businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult<Void> remove(@ApiParam("主键串")
                                       @NotEmpty(message = "主键不能为空")
                                       @PathVariable String[] ids) {
        return toAjax(iDocClassPurviewDetailService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}

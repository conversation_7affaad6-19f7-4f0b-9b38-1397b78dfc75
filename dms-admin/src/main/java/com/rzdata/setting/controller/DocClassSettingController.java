package com.rzdata.setting.controller;

import java.util.List;
import java.util.Arrays;

import lombok.RequiredArgsConstructor;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.rzdata.framework.annotation.RepeatSubmit;
import com.rzdata.framework.annotation.Log;
import com.rzdata.framework.core.controller.BaseController;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import com.rzdata.framework.core.validate.QueryGroup;
import com.rzdata.framework.enums.BusinessType;
import com.rzdata.framework.utils.poi.ExcelUtil;
import com.rzdata.setting.domain.vo.DocClassSettingVo;
import com.rzdata.setting.domain.bo.DocClassSettingBo;
import com.rzdata.setting.service.IDocClassSettingService;
import com.rzdata.framework.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiOperation;

/**
 * 文件类型设置Controller
 *
 * <AUTHOR>
 * @date 2023-12-28
 */
@Validated
@Api(value = "文件类型设置控制器", tags = {"文件类型设置管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/setting/docClassSetting")
public class DocClassSettingController extends BaseController {

    private final IDocClassSettingService iDocClassSettingService;

    /**
     * 查询文件类型设置列表
     */
    @ApiOperation("查询文件类型设置列表")
    @GetMapping("/list")
    public TableDataInfo<DocClassSettingVo> list(@Validated(QueryGroup.class) DocClassSettingBo bo) {
        return iDocClassSettingService.queryPageList(bo);
    }

    @ApiOperation("查询文件类型设置列表")
    @GetMapping("/info")
    public AjaxResult<DocClassSettingVo> getInfoBy(@Validated(QueryGroup.class) DocClassSettingBo bo) {
        return AjaxResult.success(iDocClassSettingService.queryInfo(bo));
    }

    /**
     * 导出文件类型设置列表
     */
    @ApiOperation("导出文件类型设置列表")
    @Log(title = "文件类型设置", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public void export(@Validated DocClassSettingBo bo, HttpServletResponse response) {
        List<DocClassSettingVo> list = iDocClassSettingService.queryList(bo);
        ExcelUtil.exportExcel(list, "文件类型设置", DocClassSettingVo.class, response);
    }

    /**
     * 获取文件类型设置详细信息
     */
    @ApiOperation("获取文件类型设置详细信息")
    @GetMapping("/{id}")
    public AjaxResult<DocClassSettingVo> getInfo(@ApiParam("主键")
                                                  @NotNull(message = "主键不能为空")
                                                  @PathVariable("id") String id) {
        return AjaxResult.success(iDocClassSettingService.queryById(id));
    }

    /**
     * 新增文件类型设置
     */
    @ApiOperation("新增文件类型设置")
    @Log(title = "文件类型设置", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public AjaxResult<Void> add(@Validated(AddGroup.class) @RequestBody DocClassSettingBo bo) {
        return toAjax(iDocClassSettingService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改文件类型设置
     */
    @ApiOperation("修改文件类型设置")
    @Log(title = "文件类型设置", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public AjaxResult<String> edit(@Validated(EditGroup.class) @RequestBody DocClassSettingBo bo) {
        return AjaxResult.success("操作成功",iDocClassSettingService.updateByBo(bo));
    }

    /**
     * 删除文件类型设置
     */
    @ApiOperation("删除文件类型设置")
    @Log(title = "文件类型设置" , businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult<Void> remove(@ApiParam("主键串")
                                       @NotEmpty(message = "主键不能为空")
                                       @PathVariable String[] ids) {
        return toAjax(iDocClassSettingService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}

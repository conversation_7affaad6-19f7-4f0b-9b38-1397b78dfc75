package com.rzdata.setting.controller;

import com.rzdata.framework.core.controller.BaseController;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.setting.service.IDocClassMergeService;
import com.rzdata.setting.service.IDocClassSignatureService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.constraints.NotEmpty;

/**
 * 文件类型Controller
 *
 * <AUTHOR>
 * @date 2021-12-27
 */
@Validated
@Api(value = "文件类型-签章管理控制器", tags = {"文件类型管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/setting/docClassSignature")
public class DocClassSignatureController extends BaseController {

    @Resource
    IDocClassSignatureService docClassSignatureService;
    /**
     * 删除文件类型
     */
    @ApiOperation("删除签章设置")
    @GetMapping("/delete/{id}")
    public AjaxResult<Void> remove(@ApiParam("主键串")
                                       @NotEmpty(message = "主键不能为空")
                                       @PathVariable String id) {
        return toAjax(docClassSignatureService.removeById(id) ? 1 : 0);
    }
}

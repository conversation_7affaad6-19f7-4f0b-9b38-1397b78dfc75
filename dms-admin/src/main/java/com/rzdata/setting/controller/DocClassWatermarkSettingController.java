package com.rzdata.setting.controller;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.rzdata.framework.core.controller.BaseController;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.setting.domain.DocClassFlow;
import com.rzdata.setting.domain.DocClassWatermarkSetting;
import com.rzdata.setting.domain.bo.DocClassFlowBo;
import com.rzdata.setting.domain.bo.DocClassWatermarkSettingBo;
import com.rzdata.setting.domain.vo.DocClassFlowVo;
import com.rzdata.setting.domain.vo.DocClassWatermarkSettingVo;
import com.rzdata.setting.service.IDocClassWatermarkSettingDetailService;
import com.rzdata.setting.service.IDocClassWatermarkSettingService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 文件水印设置Controller
 *
 * <AUTHOR>
 * @date 2024-04-29
 */
@Validated
@Api(value = "文件水印设置控制器", tags = {"文件水印设置管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/setting/classWatermarkSetting")
public class DocClassWatermarkSettingController extends BaseController {

    @Autowired
    IDocClassWatermarkSettingService iDocClassWatermarkSettingService;

    /**
     * 获取列表数据
     * @param docClass
     * @return
     */
    @ApiOperation("获取列表数据")
    @GetMapping("/getWatermarkSettingList/{docClass}")
    public AjaxResult<List<DocClassWatermarkSettingVo>> getByDocClass(@ApiParam("文件分类") @NotNull(message = "文件分类不能为空") @PathVariable("docClass") String docClass) {
        List<DocClassWatermarkSettingVo> voList = iDocClassWatermarkSettingService.listVo(new QueryWrapper<>(new DocClassWatermarkSetting().setDocClass(docClass)));
        if(!voList.isEmpty()){
            voList = iDocClassWatermarkSettingService.assemblyDetailData(voList);
        }
        return AjaxResult.success(voList);
    }


    /**
     * 新增保存
     * @param list
     * @return
     */
    @ApiOperation("新增保存")
    @PostMapping("/savaWatermarkSetting")
    public AjaxResult savaWatermarkSetting(@RequestBody List<DocClassWatermarkSettingBo> list) {
        System.out.println(JSON.toJSONString(list));
        if(!list.isEmpty()){
            iDocClassWatermarkSettingService.savaWatermarkSetting(list);
        }
        return AjaxResult.success();
    }
}

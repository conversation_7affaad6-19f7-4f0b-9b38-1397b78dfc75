package com.rzdata.setting.controller;

import com.rzdata.framework.core.controller.BaseController;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 文件水印设置明细Controller
 *
 * <AUTHOR>
 * @date 2024-04-29
 */
@Validated
@Api(value = "文件水印设置明细控制器", tags = {"文件水印设置明细管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/system/classWatermarkSettingDetail")
public class DocClassWatermarkSettingDetailController extends BaseController {

}

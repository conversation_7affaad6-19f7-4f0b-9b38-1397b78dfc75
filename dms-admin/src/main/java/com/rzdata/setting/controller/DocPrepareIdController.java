package com.rzdata.setting.controller;

import com.rzdata.framework.annotation.Log;
import com.rzdata.framework.annotation.RepeatSubmit;
import com.rzdata.framework.core.controller.BaseController;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.core.validate.EditGroup;
import com.rzdata.framework.core.validate.QueryGroup;
import com.rzdata.framework.enums.BusinessType;
import com.rzdata.framework.utils.poi.ExcelUtil;
import com.rzdata.setting.domain.bo.DocPrepareIdBo;
import com.rzdata.setting.domain.vo.DocPrepareIdVo;
import com.rzdata.setting.service.IDocPrepareIdService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * 预制编号Controller
 *
 * <AUTHOR>
 * @date 2023-10-07
 */
@Validated
@Api(value = "预制编号控制器", tags = {"预制编号管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/setting/prepareId")
public class DocPrepareIdController extends BaseController {

    private final IDocPrepareIdService iDocPrepareIdService;

    /**
     * 查询预制编号列表
     */
    @ApiOperation("查询预制编号列表")
    @GetMapping("/list")
    public TableDataInfo<DocPrepareIdVo> list(@Validated(QueryGroup.class) DocPrepareIdBo bo) {
        return iDocPrepareIdService.queryPageList(bo);
    }

    /**
     * 导出预制编号列表
     */
    @ApiOperation("导出预制编号列表")
    @PreAuthorize("@ss.hasPermi('setting:prepareId:export')")
    @Log(title = "预制编号", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public void export(@Validated DocPrepareIdBo bo, HttpServletResponse response) {
        List<DocPrepareIdVo> list = iDocPrepareIdService.queryList(bo);
        ExcelUtil.exportExcel(list, "预制编号", DocPrepareIdVo.class, response);
    }

    /**
     * 获取预制编号详细信息
     */
    @ApiOperation("获取预制编号详细信息")
    @GetMapping("/{docId}")
    public AjaxResult<DocPrepareIdVo> getInfo(@ApiParam("主键")
                                                  @NotNull(message = "主键不能为空")
                                                  @PathVariable("docId") String docId) {
        return AjaxResult.success(iDocPrepareIdService.queryById(docId));
    }

    /**
     * 新增预制编号
     */
    @ApiOperation("新增预制编号")
    @PreAuthorize("@ss.hasPermi('setting:prepareId:add')")
    @Log(title = "预制编号", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public AjaxResult<Void> add(@RequestBody DocPrepareIdBo bo) throws Exception {
        return toAjax(iDocPrepareIdService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改预制编号
     */
    @ApiOperation("修改预制编号")
    @PreAuthorize("@ss.hasPermi('setting:prepareId:edit')")
    @Log(title = "预制编号", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public AjaxResult<Void> edit(@Validated(EditGroup.class) @RequestBody DocPrepareIdBo bo) {
        return toAjax(iDocPrepareIdService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除预制编号
     */
    @ApiOperation("删除预制编号")
    @PreAuthorize("@ss.hasPermi('setting:prepareId:remove')")
    @Log(title = "预制编号" , businessType = BusinessType.DELETE)
    @DeleteMapping("/{docIds}")
    public AjaxResult<Void> remove(@ApiParam("主键串")
                                       @NotEmpty(message = "主键不能为空")
                                       @PathVariable String[] docIds) {
        return toAjax(iDocPrepareIdService.deleteWithValidByIds(Arrays.asList(docIds), true) ? 1 : 0);
    }


    /**
     * 验证预制编号
     */
    @ApiOperation("验证预制编号是否使用")
    @PreAuthorize("@ss.hasPermi('setting:prepareId:add')")
    @Log(title = "预制编号", businessType = BusinessType.OTHER)
    @RepeatSubmit()
    @PostMapping("validatePrepareId")
    public AjaxResult<Boolean> validatePrepareId(@RequestBody DocPrepareIdBo bo) throws Exception {
        return AjaxResult.success(iDocPrepareIdService.validatePrepareId(bo));
    }
}

package com.rzdata.setting.controller;

import com.rzdata.framework.annotation.Log;
import com.rzdata.framework.annotation.RepeatSubmit;
import com.rzdata.framework.core.controller.BaseController;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import com.rzdata.framework.core.validate.QueryGroup;
import com.rzdata.framework.enums.BusinessType;
import com.rzdata.framework.utils.poi.ExcelUtil;
import com.rzdata.setting.domain.bo.DocPresetUserBo;
import com.rzdata.setting.domain.vo.DocPresetUserVo;
import com.rzdata.setting.service.IDocPresetUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * 预选环节用户Controller
 *
 * <AUTHOR>
 * @date 2023-11-01
 */
@Validated
@Api(value = "预选环节用户控制器", tags = {"预选环节用户管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/setting/presetUser")
public class DocPresetUserController extends BaseController {

    private final IDocPresetUserService iDocPresetUserService;

    /**
     * 查询预选环节用户列表
     */
    @ApiOperation("查询预选环节用户列表")
    @GetMapping("/page")
    public TableDataInfo<DocPresetUserVo> page(@Validated(QueryGroup.class) DocPresetUserBo bo) {
        return iDocPresetUserService.queryPageList(bo);
    }

    /**
     * 导出预选环节用户列表
     */
    @ApiOperation("导出预选环节用户列表")
    @PreAuthorize("@ss.hasPermi('setting:presetUser:export')")
    @Log(title = "预选环节用户", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public void export(@Validated DocPresetUserBo bo, HttpServletResponse response) {
        List<DocPresetUserVo> list = iDocPresetUserService.queryList(bo);
        ExcelUtil.exportExcel(list, "预选环节用户", DocPresetUserVo.class, response);
    }

    /**
     * 获取预选环节用户详细信息
     */
    @ApiOperation("获取预选环节用户详细信息")
    @PreAuthorize("@ss.hasPermi('setting:presetUser:query')")
    @GetMapping("/{id}")
    public AjaxResult<DocPresetUserVo> getInfo(@ApiParam("主键")
                                                  @NotNull(message = "主键不能为空")
                                                  @PathVariable("id") String id) {
        return AjaxResult.success(iDocPresetUserService.queryById(id));
    }

    /**
     * 新增预选环节用户
     */
    @ApiOperation("新增预选环节用户")
    @PreAuthorize("@ss.hasPermi('setting:presetUser:add')")
    @Log(title = "预选环节用户", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public AjaxResult<Void> add(@Validated(AddGroup.class) @RequestBody DocPresetUserBo bo) {
        return toAjax(iDocPresetUserService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改预选环节用户
     */
    @ApiOperation("修改预选环节用户")
    @PreAuthorize("@ss.hasPermi('setting:presetUser:edit')")
    @Log(title = "预选环节用户", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public AjaxResult<Void> edit(@Validated(EditGroup.class) @RequestBody DocPresetUserBo bo) {
        return toAjax(iDocPresetUserService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除预选环节用户
     */
    @ApiOperation("删除预选环节用户")
    @PreAuthorize("@ss.hasPermi('setting:presetUser:remove')")
    @Log(title = "预选环节用户" , businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult<Void> remove(@ApiParam("主键串")
                                       @NotEmpty(message = "主键不能为空")
                                       @PathVariable String[] ids) {
        return toAjax(iDocPresetUserService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }


    @ApiOperation("查询预选环节用户列表")
    @GetMapping("/list")
    public AjaxResult<List<DocPresetUserVo>> list(@Validated(QueryGroup.class) DocPresetUserBo bo) {
        return AjaxResult.success(iDocPresetUserService.queryList(bo));
    }
}

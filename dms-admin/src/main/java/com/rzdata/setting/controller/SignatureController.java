package com.rzdata.setting.controller;

import com.rzdata.framework.annotation.Log;
import com.rzdata.framework.annotation.RepeatSubmit;
import com.rzdata.framework.core.controller.BaseController;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import com.rzdata.framework.core.validate.QueryGroup;
import com.rzdata.framework.enums.BusinessType;
import com.rzdata.framework.utils.poi.ExcelUtil;
import com.rzdata.setting.domain.bo.SignatureBo;
import com.rzdata.setting.domain.vo.SignatureVo;
import com.rzdata.setting.service.ISignatureService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * 签章管理Controller
 *
 * <AUTHOR>
 * @date 2022-03-04
 */
@Validated
@Api(value = "签章管理控制器", tags = {"签章管理管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/setting/signature")
public class SignatureController extends BaseController {

    private final ISignatureService iSignatureService;

    /**
     * 查询签章管理列表
     */
    @ApiOperation("查询签章管理列表")
    @GetMapping("/list")
    public TableDataInfo<SignatureVo> list(@Validated(QueryGroup.class) SignatureBo bo) {
        return iSignatureService.queryPageList(bo);
    }

    /**
     * 导出签章管理列表
     */
    @ApiOperation("导出签章管理列表")
    @Log(title = "签章管理", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public void export(@Validated SignatureBo bo, HttpServletResponse response) {
        List<SignatureVo> list = iSignatureService.queryList(bo);
        ExcelUtil.exportExcel(list, "签章管理", SignatureVo.class, response);
    }

    /**
     * 获取签章管理详细信息
     */
    @ApiOperation("获取签章管理详细信息")
    @GetMapping("/{id}")
    public AjaxResult<SignatureVo> getInfo(@ApiParam("主键")
                                                  @NotNull(message = "主键不能为空")
                                                  @PathVariable("id") String id) {
        return AjaxResult.success(iSignatureService.queryById(id));
    }

    /**
     * 新增签章管理
     */
    @ApiOperation("新增签章管理")
    @Log(title = "签章管理", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public AjaxResult<Void> add(@Validated(AddGroup.class) @RequestBody SignatureBo bo) {
        return toAjax(iSignatureService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改签章管理
     */
    @ApiOperation("修改签章管理")
    @Log(title = "签章管理", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public AjaxResult<Void> edit(@Validated(EditGroup.class) @RequestBody SignatureBo bo) {
        return toAjax(iSignatureService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除签章管理
     */
    @ApiOperation("删除签章管理")
    @Log(title = "签章管理" , businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult<Void> remove(@ApiParam("主键串")
                                       @NotEmpty(message = "主键不能为空")
                                       @PathVariable String[] ids) {
        return toAjax(iSignatureService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}

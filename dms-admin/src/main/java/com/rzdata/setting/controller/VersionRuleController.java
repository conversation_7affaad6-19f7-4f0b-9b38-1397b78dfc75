package com.rzdata.setting.controller;

import java.util.List;
import java.util.Arrays;

import lombok.RequiredArgsConstructor;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.rzdata.framework.annotation.RepeatSubmit;
import com.rzdata.framework.annotation.Log;
import com.rzdata.framework.core.controller.BaseController;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import com.rzdata.framework.core.validate.QueryGroup;
import com.rzdata.framework.enums.BusinessType;
import com.rzdata.framework.utils.poi.ExcelUtil;
import com.rzdata.setting.domain.vo.VersionRuleVo;
import com.rzdata.setting.domain.bo.VersionRuleBo;
import com.rzdata.setting.service.IVersionRuleService;
import com.rzdata.framework.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiOperation;

/**
 * 版本规则Controller
 *
 * <AUTHOR>
 * @date 2023-12-27
 */
@Validated
@Api(value = "版本规则控制器", tags = {"版本规则管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/setting/versionRule")
public class VersionRuleController extends BaseController {

    private final IVersionRuleService iVersionRuleService;

    /**
     * 查询版本规则列表
     */
    @ApiOperation("查询版本规则列表")
    @GetMapping("/list")
    public TableDataInfo<VersionRuleVo> list(@Validated(QueryGroup.class) VersionRuleBo bo) {
        return iVersionRuleService.queryPageList(bo);
    }

    /**
     * 导出版本规则列表
     */
    @ApiOperation("导出版本规则列表")
    @Log(title = "版本规则", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public void export(@Validated VersionRuleBo bo, HttpServletResponse response) {
        List<VersionRuleVo> list = iVersionRuleService.queryList(bo);
        ExcelUtil.exportExcel(list, "版本规则", VersionRuleVo.class, response);
    }

    /**
     * 获取版本规则详细信息
     */
    @ApiOperation("获取版本规则详细信息")
    @GetMapping("/{id}")
    public AjaxResult<VersionRuleVo> getInfo(@ApiParam("主键")
                                                  @NotNull(message = "主键不能为空")
                                                  @PathVariable("id") String id) {
        return AjaxResult.success(iVersionRuleService.queryById(id));
    }

    /**
     * 新增版本规则
     */
    @ApiOperation("新增版本规则")
    @Log(title = "版本规则", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public AjaxResult<Void> add(@Validated(AddGroup.class) @RequestBody VersionRuleBo bo) {
        return toAjax(iVersionRuleService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改版本规则
     */
    @ApiOperation("修改版本规则")
    @Log(title = "版本规则", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public AjaxResult<Void> edit(@Validated(EditGroup.class) @RequestBody VersionRuleBo bo) {
        return toAjax(iVersionRuleService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除版本规则
     */
    @ApiOperation("删除版本规则")
    @Log(title = "版本规则" , businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult<Void> remove(@ApiParam("主键串")
                                       @NotEmpty(message = "主键不能为空")
                                       @PathVariable String[] ids) {
        return toAjax(iVersionRuleService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}

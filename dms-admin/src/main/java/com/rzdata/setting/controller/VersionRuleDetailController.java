package com.rzdata.setting.controller;

import java.util.List;
import java.util.Arrays;

import com.rzdata.framework.constant.CommonI18nConstant;
import com.rzdata.framework.utils.I18nUtils;
import lombok.RequiredArgsConstructor;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.rzdata.framework.annotation.RepeatSubmit;
import com.rzdata.framework.annotation.Log;
import com.rzdata.framework.core.controller.BaseController;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import com.rzdata.framework.core.validate.QueryGroup;
import com.rzdata.framework.enums.BusinessType;
import com.rzdata.framework.utils.poi.ExcelUtil;
import com.rzdata.setting.domain.vo.VersionRuleDetailVo;
import com.rzdata.setting.domain.bo.VersionRuleDetailBo;
import com.rzdata.setting.service.IVersionRuleDetailService;
import com.rzdata.framework.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiOperation;

/**
 * 版本规则明细Controller
 *
 * <AUTHOR>
 * @date 2023-12-27
 */
@Validated
@Api(value = "版本规则明细控制器", tags = {"版本规则明细管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/setting/versionRuleDetail")
public class VersionRuleDetailController extends BaseController {

    private final IVersionRuleDetailService iVersionRuleDetailService;

    /**
     * 查询版本规则明细列表
     */
    @ApiOperation("查询版本规则明细列表")
    @GetMapping("/list")
    public TableDataInfo<VersionRuleDetailVo> list(@Validated(QueryGroup.class) VersionRuleDetailBo bo) {
        return iVersionRuleDetailService.queryPageList(bo);
    }

    /**
     * 导出版本规则明细列表
     */
    @ApiOperation("导出版本规则明细列表")
    @Log(title = "版本规则明细", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public void export(@Validated VersionRuleDetailBo bo, HttpServletResponse response) {
        List<VersionRuleDetailVo> list = iVersionRuleDetailService.queryList(bo);
        ExcelUtil.exportExcel(list, "版本规则明细", VersionRuleDetailVo.class, response);
    }

    /**
     * 获取版本规则明细详细信息
     */
    @ApiOperation("获取版本规则明细详细信息")
    @GetMapping("/{id}")
    public AjaxResult<VersionRuleDetailVo> getInfo(@ApiParam("主键")
                                                  @NotNull(message = "主键不能为空")
                                                  @PathVariable("id") String id) {
        return AjaxResult.success(iVersionRuleDetailService.queryById(id));
    }

    /**
     * 新增版本规则明细
     */
    @ApiOperation("新增版本规则明细")
    @Log(title = "版本规则明细", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public AjaxResult<Void> add(@Validated(AddGroup.class) @RequestBody VersionRuleDetailBo bo) {
        return toAjax(iVersionRuleDetailService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改版本规则明细
     */
    @ApiOperation("修改版本规则明细")
    @Log(title = "版本规则明细", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public AjaxResult<Void> edit(@Validated(EditGroup.class) @RequestBody VersionRuleDetailBo bo) {
        return toAjax(iVersionRuleDetailService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除版本规则明细
     */
    @ApiOperation("删除版本规则明细")
    @Log(title = "版本规则明细" , businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult<Void> remove(@ApiParam("主键串")
                                       @NotEmpty(message = "主键不能为空")
                                       @PathVariable String[] ids) {
        return toAjax(iVersionRuleDetailService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }

    /**
     * 根据当前版本计算下个版本号
     */
    @ApiOperation("根据当前版本计算下个版本号")
    @Log(title = "版本规则明细", businessType = BusinessType.INSERT)
    @PostMapping("/next/version")
    public AjaxResult<String> getNextVersion(@RequestParam String version,@RequestParam String docClass) {
        return AjaxResult.success(I18nUtils.getTitle(CommonI18nConstant.VERSION_RULE_QUERY_SUCCESS),iVersionRuleDetailService.getNextVersion(version,docClass));
    }
}

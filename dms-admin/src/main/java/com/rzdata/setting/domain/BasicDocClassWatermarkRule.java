package com.rzdata.setting.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rzdata.framework.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 文档水印规则对象 basic_doc_class_watermark_rule
 *
 * <AUTHOR>
 * @date 2024-01-22
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("basic_doc_class_watermark_rule")
public class BasicDocClassWatermarkRule extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /** 主键id */
    @TableId(value = "id",type = IdType.ASSIGN_ID)
    private String id;

    /** 水印规则名称 */
    private String ruleName;

    /** 规则内容 */
    private String ruleDetails;

    /** 序号 */
    private Integer sort;

    /** 备注 */
    private String remark;
} 
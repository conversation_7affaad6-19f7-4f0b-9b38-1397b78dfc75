package com.rzdata.setting.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;
import com.rzdata.framework.core.domain.BaseEntity;


/**
 * 单规则对象 basic_form_rule
 *
 * <AUTHOR>
 * @date 2024-09-20
 */
@Data
@Accessors(chain = true)
@TableName("basic_form_rule")
public class BasicFormRule extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 主键
     */
    @TableId(value = "id",type = IdType.ASSIGN_ID)
    private String id;
    /**
     * 规则名称
     */
    private String ruleName;
    /**
     * 规则详情
     */
    private String ruleDetails;
    /**
     * 租户id(basic_tenant主键id)
     */
    private String tenantId;

}

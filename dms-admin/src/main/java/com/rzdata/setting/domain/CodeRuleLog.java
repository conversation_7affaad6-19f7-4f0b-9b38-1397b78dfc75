package com.rzdata.setting.domain;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;
import com.rzdata.framework.core.domain.BaseEntity;

import java.util.Date;


/**
 * 编号规则日志对象 basic_code_rule_log
 *
 * <AUTHOR>
 * @date 2021-12-27
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Getter
@TableName("basic_code_rule_log")
public class CodeRuleLog {

    private static final long serialVersionUID=1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;
    /**
     * 规则主键
     */
    private String ruleId;
    /**
     * 流水值
     */
    private Long numberValue;
    /**
     * 规则生成值
     */
    private String ruleValue;
    /**
     * 规则使用业务主键
     */
    private String businessId;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 创建者
     */
    @ApiModelProperty(value = "创建者")
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    private String deptCode;

    private String fileType;

}

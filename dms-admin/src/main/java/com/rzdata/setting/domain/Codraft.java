package com.rzdata.setting.domain;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;


/**
 * 合稿管理对象 basic_codraft
 *
 * <AUTHOR>
 * @date 2022-03-02
 */
@Data
@Accessors(chain = true)
@TableName("basic_codraft")
public class Codraft {

    private static final long serialVersionUID=1L;

    /**
     * 合稿管理ID
     */
    @TableId(value = "id",type = IdType.ASSIGN_ID)
    private String id;
    /**
     * 合稿要素名称
     */
    @TableField(value = "codraft_name")
    private String codraftName;
    /**
     * 合稿模板
     */    private String fileId;
    /**
     * 状态
     */
    private String codraftStatus;

    /**
     * 模板名称
     */
    @ApiModelProperty(value = "模板名称")
    private String templateName;

    @TableField
    private Date createTime;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 逻辑删除标志 0=正常 2=已删除
     */
    @TableLogic
    private Integer deleteFlag;

    /**
     * 位置
     */
    private String position;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 分类Id
     */
    private String classId;

    /**
     * 业务id
     */
    private String bizId;
}

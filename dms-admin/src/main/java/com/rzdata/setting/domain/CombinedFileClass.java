package com.rzdata.setting.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;
import com.rzdata.framework.core.domain.BaseEntity;


/**
 * 合稿文件分类对象 combined_file_class
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Data
@Accessors(chain = true)
@TableName("combined_file_class")
public class CombinedFileClass extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 主键
     */
    @TableId(value = "id",type = IdType.ASSIGN_ID)
    private String id;
    /**
     * 合稿ID;关联合稿管理表
     */
    private String combinedMgrId;
    /**
     * 文件分类ID;关联文件分类表
     */
    private String fileClassId;

}

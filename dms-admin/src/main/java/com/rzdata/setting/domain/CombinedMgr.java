package com.rzdata.setting.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;
import com.rzdata.framework.core.domain.BaseEntity;


/**
 * 合稿管理对象 combined_mgr
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Data
@Accessors(chain = true)
@TableName("combined_mgr")
public class CombinedMgr extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 主键
     */
    @TableId(value = "id",type = IdType.ASSIGN_ID)
    private String id;
    /**
     * 名称
     */
    private String name;
    /**
     * 备注
     */
    private String remark;
    /**
     * 逻辑删除标志 0=正常 2=已删除
     */
    @TableLogic
    private Integer isDeleted;

}

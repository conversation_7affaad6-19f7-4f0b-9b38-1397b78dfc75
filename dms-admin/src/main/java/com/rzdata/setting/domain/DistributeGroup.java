package com.rzdata.setting.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;
import com.rzdata.framework.core.domain.BaseEntity;


/**
 * 预设分组人员对象 basic_distribute_group
 *
 * <AUTHOR>
 * @date 2024-11-26
 */
@Data
@Accessors(chain = true)
@TableName("basic_distribute_group")
public class DistributeGroup extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;
    /**
     * 规则名称
     */
    private String groupName;
    /**
     * 类型
     */
    private String type;
    /**
     * 人员范围类型
     */
    private String scopeType;
    /**
     * 租户id(basic_tenant主键id)
     */
    private String tenantId;

}

package com.rzdata.setting.domain;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import com.rzdata.framework.core.domain.BaseEntity;


/**
 * 预设分组人员详情对象 basic_distribute_group_detail
 *
 * <AUTHOR>
 * @date 2024-11-26
 */
@Data
@Accessors(chain = true)
@TableName("basic_distribute_group_detail")
public class DistributeGroupDetail {

    private static final long serialVersionUID=1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;
    /**
     * 分组id
     */
    private String groupId;
    /**
     * 租户id
     */
    private String tenantId;
    /**
     * 接收人
     */
    private String receiveUserName;
    /**
     * 接收人昵称
     */
    private String receiveNickName;
    /**
     * 接收人部门id
     */
    private String receiveUserDeptId;
    /**
     * 接收人部门名称
     */
    private String receiveUserDept;
    /**
     * 打印纸张类型
     */
    private String printPaperType;
    /**
     * 数量
     */
    private Integer nums;
    /**
     * 类型 部门 dept、个人 person
     */
    private String type;
    /**
     * 类别 分发 print、培训 train
顺络电子分发是打印权限 培训是查看权限
     */
    private String category;

}

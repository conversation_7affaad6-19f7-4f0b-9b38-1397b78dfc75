package com.rzdata.setting.domain;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;


/**
 * 文件类型对象 basic_doc_class
 *
 * <AUTHOR>
 * @date 2021-12-27
 */
@Data
@Accessors(chain = true)
@TableName("basic_doc_class")
public class DocClass {

    private static final long serialVersionUID=1L;

    /**
     * 分类代码
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 分类编码
     */
    private String classCode;

    /**
     * 数据类型
     */
    private String dataType;

    /**
     * 分类名称
     */
    private String className;

    /**
     * 上级分类
     */
    private String parentClassId;

    /**
     * 祖级列表
     */
    private String ancestors;
    /**
     * 分类状态
     */
    private String classStatus;
    /**
     * 分类层级
     */
    private String classLevel;
    /**
     * 文件有效期
     */
    private Long expiration;
    /**
     * 文件复审周期
     */
    private Long reviewCycle;
    /**
     * 签收类型，个人，部门
     */
    private String checkType;
    /**
     * 签章类型，个人、部门、公司
     */
    private String encryptType;
    /**
     * 是否纸质打印
     */
    private String yNPrint;
    /**
     * 是否纸质回收
     */
    private String yNRecyle;
    /**
     * 合稿设置
     */
    private String mergeDocConfig;
    /**
     * 是否启用
     */
    private String yNOpenCode;
    /**
     * 是否应用到子文件类型
     */
    private String yNApplyCode;
    /**
     * 编号设置
     */
    private String codeId;
    /**
     * 分发号
     */
    private String distributeId;
    /**
     * 是否启用
     */
    private String openDistribute;
    /**
     * 是否应用到子应用
     */
    private String applyDistribute;
    /**
     * 文件时效设置是否应用到子类型
     */
    private String applyPrescription;

    private String openReview;
    private String applyReview;


    private String openMerge;
    private String applyMerge;

    private String signatureType;
    private String openSignature;
    private String applySignature;
    /**
     * 是否启用文件时效设置
     */
    private String openPrescription;
    /**
     * 文件模板
     */
    private String fileId;

    private Integer sort;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 分类所属类型
     * DOC文件台账、RECORD记录台账、FOREIGN外来文件
     */
    @ApiModelProperty("分类所属类型")
    private String classType;

    /**
     * 是否为临时文件 Y N
     */
    private String isFileType;

    private String permission;

    private String combinedMgrId;

    /**
     * 下推公司
     */
    @ApiModelProperty("下推公司")
    private String subsidiaryCompany;

    /**
     * 下推文件分类ID
     */
    @ApiModelProperty("下推文件分类ID")
    private String subsidiaryDocClassId;

    /**
     * 下推文件分类名称
     */
    @ApiModelProperty("下推文件分类名称")
    private String subsidiaryDocClassName;

    /**
     * 接收人
     */
    @ApiModelProperty("接收人")
    private String connector;

    /**
     * 修改人
     */
    @ApiModelProperty("修改人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;
}

package com.rzdata.setting.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;
import com.rzdata.framework.core.domain.BaseEntity;


/**
 * 文件分类设置-节点明细设置对象 basic_doc_class_flow_node_detail
 *
 * <AUTHOR>
 * @date 2023-01-10
 */
@Data
@Accessors(chain = true)
@TableName("basic_doc_class_flow_node_detail")
public class DocClassFlowNodeDetail extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;
    /**
     * 租户id
     */
    private String tenantId;
    /**
     * 流程节点主键;basic_doc_flow_node主键id
     */
    private String nodeId;
    /**
     * 权限类型;btn按钮、oper操作
     */
    private String type;
    /**
     * 权限编码
     */
    private String code;
    /**
     * 权限名称
     */
    private String name;
    /**
     * 调用方法名称
     */
    private String funName;
    /**
     * 调用方法条件;调用方法前置条件
     */
    private String funCondition;
    /**
     * 排序
     */
    private Long sort;
    /**
     * 备注
     */
    private String remark;

}

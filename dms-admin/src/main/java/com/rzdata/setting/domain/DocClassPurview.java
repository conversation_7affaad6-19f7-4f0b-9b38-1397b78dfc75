package com.rzdata.setting.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;
import com.rzdata.framework.core.domain.BaseEntity;


/**
 * 文件类型权限对象 basic_doc_class_purview
 *
 * <AUTHOR>
 * @date 2023-12-01
 */
@Data
@Accessors(chain = true)
@TableName("basic_doc_class_purview")
public class DocClassPurview extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 主键 basic_doc_class表主键ID
     */
    @TableId(value = "id")
    private String id;
    /**
     * 是否应用子分类;Y应用 N不应用
     */
    private String applyFlag;
    /**
     * 是否生效;Y生效 N不生效
     */
    private String openFlag;

}

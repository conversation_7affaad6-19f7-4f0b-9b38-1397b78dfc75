package com.rzdata.setting.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;
import com.rzdata.framework.core.domain.BaseEntity;


/**
 * 文件类型权限详情对象 basic_doc_class_purview_detail
 *
 * <AUTHOR>
 * @date 2023-12-01
 */
@Data
@Accessors(chain = true)
@TableName("basic_doc_class_purview_detail")
public class DocClassPurviewDetail extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 主键
     */
    @TableId(value = "id",type = IdType.ASSIGN_ID)
    private String id;
    /**
     * basic_doc_class_purview表主键ID
     */
    private String purviewId;
    /**
     * 类型: 公司company 部门dept 角色role 个人person
     */
    private String type;
    /**
     * 值：公司主键 部门主键 角色编码 个人账号
     */
    private String value;

    /**
     * 标签
     */
    private String label;
}

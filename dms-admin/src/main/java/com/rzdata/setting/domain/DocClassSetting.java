package com.rzdata.setting.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;
import com.rzdata.framework.core.domain.BaseEntity;


/**
 * 文件类型设置对象 basic_doc_class_setting
 *
 * <AUTHOR>
 * @date 2023-12-28
 */
@Data
@Accessors(chain = true)
@TableName("basic_doc_class_setting")
public class DocClassSetting extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;
    /**
     * 文件类型;basic_doc_class表主键ID
     */
    private String docClass;
    /**
     * 设置类型  version 版本号
     */
    private String type;
    /**
     * 规则id
     */
    private String ruleId;
    /**
     * 设置id
     */
    private String settingId;
    /**
     * 是否应用子分类;Y应用 N不应用
     */
    private String applyFlag;
    /**
     * 是否生效;Y生效 N不生效
     */
    private String openFlag;

}

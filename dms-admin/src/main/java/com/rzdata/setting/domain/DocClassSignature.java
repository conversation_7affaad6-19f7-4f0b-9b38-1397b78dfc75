package com.rzdata.setting.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2022/3/14 15:47
 * @Version 1.0
 * @Description
 */
@Data
@Accessors(chain = true)
@TableName("basic_doc_class_signature")
public class DocClassSignature {

    /**
     * 主键id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 排序字段
     */
    private int sort;

    /**
     * 文件类型
     */
    private String docClass;

    /**
     * 签章要素名称
     */
    private String signatureFactorName;

    /**
     * 是否选择 0=未选择 1=已选择
     */
    private Integer signatureFactorUse;

    /**
     * 签证文件主键id
     */
    private String signatureId;

    /**
     * 应用范围 1=除首页 2=除末页 3=全文应用 4=仅首页
     */
    private Integer rangeValue;

    /**
     * 是否是默认值 是默认值则不可以删除 0=否 1=是
     */
    private Integer defaultFlag;

    @ApiModelProperty(value = "生成时间")
    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    private Date updateTime;
}

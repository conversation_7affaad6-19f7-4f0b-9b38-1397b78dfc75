package com.rzdata.setting.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;
import com.rzdata.framework.core.domain.BaseEntity;

import java.util.Date;


/**
 * 文件水印设置对象 doc_class_watermark_setting
 *
 * <AUTHOR>
 * @date 2024-04-29
 */
@Data
@Accessors(chain = true)
@TableName("basic_doc_class_watermark_setting")
public class DocClassWatermarkSetting {

    private static final long serialVersionUID=1L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;
    /**
     * 文件类型;basic_doc_class表主键ID
     */
    private String docClass;
    /**
     * 业务类型;文件新增add_doc、文件修订update_doc、文件作废disuse_doc
     */
    private String bizType;
    /**
     * 是否生效;Y生效 N不生效
     */
    private String openFlag;
    /**
     * 是否应用子分类;Y应用 N不应用
     */
    private String applyFlag;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 创建人
     */
    private String createBy;
    /**
     * 更新人
     */
    private String updateBy;

}

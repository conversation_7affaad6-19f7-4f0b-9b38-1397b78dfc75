package com.rzdata.setting.domain;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import com.rzdata.framework.core.domain.BaseEntity;

import java.util.Date;


/**
 * 文件水印设置明细对象 doc_class_watermark_setting_detail
 *
 * <AUTHOR>
 * @date 2024-04-29
 */
@Data
@Accessors(chain = true)
@TableName("basic_doc_class_watermark_setting_detail")
public class DocClassWatermarkSettingDetail {

    private static final long serialVersionUID=1L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;
    /**
     * 关联ID，basic_doc_class_watermark_setting表主键
     */
    private String bizId;
    /**
     * 水印类型（text：文本，image：图片）
     */
    private String watermarkSettingType;
    /**
     * 水印名称
     */
    private String watermarkSettingName;
    /**
     * 水印编码
     */
    private String watermarkSettingCode;
    /**
     *  2=未选择 1=已选择
     */
    private Integer watermarkSettingFactorUse;
    /**
     * 字号（水印类型为text时）
     */
    private Integer typeSize;
    /**
     * 规模（水印类型为image时使用此字段，长宽暂时不使用）
     */
    private Double scale;
    /**
     * 长
     */
    private Integer length;
    /**
     * 宽
     */
    private Integer width;
    /**
     * 位置X轴
     */
    private Integer xPosition;
    /**
     * 位置Y轴
     */
    private Integer yPosition;

    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 序号
     */
    private Integer sort;

    /**
     * 应用范围：（主文件：main，版本附件：attach，都支持：all）
     */
    private String appliedRange;

    /**
     * 页面范围
     */
    private String pageOption;

}

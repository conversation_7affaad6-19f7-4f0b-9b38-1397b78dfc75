package com.rzdata.setting.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rzdata.framework.core.domain.BaseEntity;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 预制编号对象 doc_prepare_id
 *
 * <AUTHOR>
 * @date 2023-10-07
 */
@Data
@Accessors(chain = true)
@TableName("doc_prepare_id")
public class DocPrepareId extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 文件编号
     */
    @TableId(value = "doc_id")
    private String docId;
    /**
     * 编号类型 DOC主文件  RECORD关联记录
     */
    private String codeType;
    /**
     * 文件类型
     */
    private String docClass;
    /**
     * 备注说明
     */
    private String remark;
    /**
     * 记录文件关联主文件编号
     */
    private String parentDocId;
    /**
     * 申请人
     */
    private String applyBy;
    /**
     * 使用状态 0未使用 1已使用
     */
    private String useStatus;
    /**
     * 使用时间
     */
    private Date useTime;
    /**
     * 分类
     */
    private String dataType;
    /**
     * 项目
     */
    private String projectId;
}

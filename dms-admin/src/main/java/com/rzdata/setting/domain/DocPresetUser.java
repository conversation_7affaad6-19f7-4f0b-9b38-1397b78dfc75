package com.rzdata.setting.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rzdata.framework.core.domain.BaseEntity;
import lombok.Data;
import lombok.experimental.Accessors;


/**
 * 预选环节用户对象 doc_preset_user
 *
 * <AUTHOR>
 * @date 2023-11-01
 */
@Data
@Accessors(chain = true)
@TableName("doc_preset_user")
public class DocPresetUser extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;
    /**
     * 业务id
     */
    private String bizId;
    /**
     * 节点编码
     */
    private String nodeCode;
    /**
     * 所选用户
     */
    private String users;

}

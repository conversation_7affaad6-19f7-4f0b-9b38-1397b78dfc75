package com.rzdata.setting.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;
import com.rzdata.framework.core.domain.BaseEntity;


/**
 * 版本规则对象 basic_version_rule
 *
 * <AUTHOR>
 * @date 2023-12-27
 */
@Data
@Accessors(chain = true)
@TableName("basic_version_rule")
public class VersionRule extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;
    /**
     * 规则名称
     */
    private String ruleName;

    /**
     * 后续规则
     */
    private String nextId;

    /**
     * 起始值
     */
    private String startValue;
    /**
     * 租户id(basic_tenant主键id)
     */
    private String tenantId;

}

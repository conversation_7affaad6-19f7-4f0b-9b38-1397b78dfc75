package com.rzdata.setting.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;



/**
 * 版本规则明细对象 basic_version_rule_detail
 *
 * <AUTHOR>
 * @date 2023-12-27
 */
@Data
@Accessors(chain = true)
@TableName("basic_version_rule_detail")
public class VersionRuleDetail {

    private static final long serialVersionUID=1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;
    /**
     * 规则主键
     */
    private String ruleId;
    /**
     * 规则类型，固定字符：STR、字母：ALP、数字：NUM
     */
    private String ruleType;
    /**
     * 起始值
     */
    private String startValue;
    /**
     * 数位
     */
    private Integer digit;
    /**
     * 满足条件
     */
    private String endValue;
    /**
     * 是否同步
     */
    private Boolean sync;
    /**
     * 排序号
     */
    private Long orderBy;
    /**
     * 租户id(basic_tenant主键id)
     */
    private String tenantId;

}

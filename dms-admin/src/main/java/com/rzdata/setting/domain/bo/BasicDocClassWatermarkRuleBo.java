package com.rzdata.setting.domain.bo;

import com.rzdata.framework.core.domain.BaseEntity;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;

/**
 * 文档水印规则业务对象
 *
 * <AUTHOR>
 * @date 2024-01-22
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("文档水印规则业务对象")
public class BasicDocClassWatermarkRuleBo extends BaseEntity {

    /** 主键 */
    @ApiModelProperty(value = "主键")
    private String id;

    /** 水印规则名称 */
    @ApiModelProperty(value = "水印规则名称", required = true)
    @NotBlank(message = "水印规则名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String ruleName;

    /** 规则内容 */
    @ApiModelProperty(value = "规则内容", required = true)
    @NotBlank(message = "规则内容不能为空", groups = { AddGroup.class, EditGroup.class })
    private String ruleDetails;

    /** 序号 */
    @ApiModelProperty(value = "序号")
    private Integer sort;

    /** 备注 */
    @ApiModelProperty(value = "备注")
    private String remark;

    /** 分页大小 */
    @ApiModelProperty("分页大小")
    private Integer pageSize;

    /** 当前页数 */
    @ApiModelProperty("当前页数")
    private Integer pageNum;

    /** 排序列 */
    @ApiModelProperty("排序列")
    private String orderByColumn;

    /** 排序的方向desc或者asc */
    @ApiModelProperty(value = "排序的方向", example = "asc,desc")
    private String isAsc;

    /** 是否更新所有使用该规则的配置 */
    @ApiModelProperty(value = "是否更新所有使用该规则的配置")
    private Boolean updateAll;
} 
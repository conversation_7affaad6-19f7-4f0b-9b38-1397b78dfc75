package com.rzdata.setting.domain.bo;

import com.rzdata.framework.core.domain.BaseEntity;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;


/**
 * 单规则业务对象 basic_form_rule
 *
 * <AUTHOR>
 * @date 2024-09-20
 */

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("单规则业务对象")
public class BasicFormRuleBo extends BaseEntity {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 规则名称
     */
    @ApiModelProperty(value = "规则名称", required = true)
    @NotBlank(message = "规则名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String ruleName;

    /**
     * 规则详情
     */
    @ApiModelProperty(value = "规则详情", required = true)
    @NotBlank(message = "规则详情不能为空", groups = { AddGroup.class, EditGroup.class })
    private String ruleDetails;


    /**
     * 分页大小
     */
    @ApiModelProperty("分页大小")
    private Integer pageSize;

    /**
     * 当前页数
     */
    @ApiModelProperty("当前页数")
    private Integer pageNum;

    /**
     * 排序列
     */
    @ApiModelProperty("排序列")
    private String orderByColumn;

    /**
     * 排序的方向desc或者asc
     */
    @ApiModelProperty(value = "排序的方向", example = "asc,desc")
    private String isAsc;

}

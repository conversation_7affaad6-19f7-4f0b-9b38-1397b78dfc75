package com.rzdata.setting.domain.bo;

import com.rzdata.framework.core.domain.BaseEntity;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;
import java.util.List;


/**
 * 合稿文件分类业务对象 combined_file_class
 *
 * <AUTHOR>
 * @date 2025-01-15
 */

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("合稿文件分类业务对象")
public class CombinedFileClassBo extends BaseEntity {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键", required = true)
    @NotBlank(message = "主键不能为空", groups = { EditGroup.class })
    private String id;

    /**
     * 合稿ID;关联合稿管理表
     */
    @ApiModelProperty(value = "合稿ID;关联合稿管理表", required = true)
    @NotBlank(message = "合稿ID;关联合稿管理表不能为空", groups = { AddGroup.class, EditGroup.class })
    private String combinedMgrId;

    /**
     * 文件分类ID;关联文件分类表
     */
    @ApiModelProperty(value = "文件分类ID;关联文件分类表")
    private String fileClassId;


    /**
     * 文件分类ID;关联文件分类表
     */
    @ApiModelProperty(value = "文件分类ID;关联文件分类表")
    private List<String> combinedMgrIdList;


    /**
     * 分页大小
     */
    @ApiModelProperty("分页大小")
    private Integer pageSize;

    /**
     * 当前页数
     */
    @ApiModelProperty("当前页数")
    private Integer pageNum;

    /**
     * 排序列
     */
    @ApiModelProperty("排序列")
    private String orderByColumn;

    /**
     * 排序的方向desc或者asc
     */
    @ApiModelProperty(value = "排序的方向", example = "asc,desc")
    private String isAsc;

}

package com.rzdata.setting.domain.bo;

import com.rzdata.framework.core.domain.BaseEntity;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import com.rzdata.setting.domain.DistributeGroupDetail;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import java.util.List;


/**
 * 预设分组人员业务对象 basic_distribute_group
 *
 * <AUTHOR>
 * @date 2024-11-26
 */

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("预设分组人员业务对象")
public class DistributeGroupBo extends BaseEntity {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键", required = true)
    @NotBlank(message = "主键不能为空", groups = { EditGroup.class })
    private String id;

    /**
     * 规则名称
     */
    @ApiModelProperty(value = "规则名称", required = true)
    @NotBlank(message = "规则名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String groupName;

    /**
     * 类型
     */
    @ApiModelProperty(value = "类型", required = true)
    @NotBlank(message = "类型", groups = { AddGroup.class, EditGroup.class })
    private String type;

    /**
     * 人员范围类型
     */
    @ApiModelProperty(value = "人员范围类型", required = true)
    private String scopeType;

    /**
     * 租户id(basic_tenant主键id)
     */
    @ApiModelProperty(value = "租户id(basic_tenant主键id)", required = true)
    private String tenantId;


    /**
     * 分页大小
     */
    @ApiModelProperty("分页大小")
    private Integer pageSize;

    /**
     * 当前页数
     */
    @ApiModelProperty("当前页数")
    private Integer pageNum;

    /**
     * 排序列
     */
    @ApiModelProperty("排序列")
    private String orderByColumn;

    /**
     * 排序的方向desc或者asc
     */
    @ApiModelProperty(value = "排序的方向", example = "asc,desc")
    private String isAsc;

    private List<DistributeGroupDetail> itemList;
}

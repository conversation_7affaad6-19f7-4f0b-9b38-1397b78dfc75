package com.rzdata.setting.domain.bo;

import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;


/**
 * 预设分组人员详情业务对象 basic_distribute_group_detail
 *
 * <AUTHOR>
 * @date 2024-11-26
 */

@Data
@ApiModel("预设分组人员详情业务对象")
public class DistributeGroupDetailBo {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键", required = true)
    @NotBlank(message = "主键不能为空", groups = { EditGroup.class })
    private String id;

    /**
     * 分组id
     */
    @ApiModelProperty(value = "分组id", required = true)
    @NotBlank(message = "分组id不能为空", groups = { AddGroup.class, EditGroup.class })
    private String groupId;

    /**
     * 租户id
     */
    @ApiModelProperty(value = "租户id", required = true)
    @NotBlank(message = "租户id不能为空", groups = { AddGroup.class, EditGroup.class })
    private String tenantId;

    /**
     * 接收人
     */
    @ApiModelProperty(value = "接收人", required = true)
    @NotBlank(message = "接收人不能为空", groups = { AddGroup.class, EditGroup.class })
    private String receiveUserName;

    /**
     * 接收人昵称
     */
    @ApiModelProperty(value = "接收人昵称", required = true)
    @NotBlank(message = "接收人昵称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String receiveNickName;

    /**
     * 接收人部门id
     */
    @ApiModelProperty(value = "接收人部门id", required = true)
    @NotBlank(message = "接收人部门id不能为空", groups = { AddGroup.class, EditGroup.class })
    private String receiveUserDeptId;

    /**
     * 接收人部门名称
     */
    @ApiModelProperty(value = "接收人部门名称", required = true)
    @NotBlank(message = "接收人部门名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String receiveUserDept;

    /**
     * 打印纸张类型
     */
    private String printPaperType;

    /**
     * 数量
     */
    private Integer nums;
    /**
     * 类型 部门 dept、个人 person
     */
    @ApiModelProperty(value = "类型 部门 dept、个人 person", required = true)
    @NotBlank(message = "类型 部门 dept、个人 person不能为空", groups = { AddGroup.class, EditGroup.class })
    private String type;

    /**
     * 类别 分发 print、培训 train 顺络电子分发是打印权限 培训是查看权限
     */
    @ApiModelProperty(value = "类别 分发 print、培训 train", required = true)
    @NotBlank(message = "类别 分发 print、培训 train", groups = { AddGroup.class, EditGroup.class })
    private String category;


    /**
     * 分页大小
     */
    @ApiModelProperty("分页大小")
    private Integer pageSize;

    /**
     * 当前页数
     */
    @ApiModelProperty("当前页数")
    private Integer pageNum;

    /**
     * 排序列
     */
    @ApiModelProperty("排序列")
    private String orderByColumn;

    /**
     * 排序的方向desc或者asc
     */
    @ApiModelProperty(value = "排序的方向", example = "asc,desc")
    private String isAsc;

}

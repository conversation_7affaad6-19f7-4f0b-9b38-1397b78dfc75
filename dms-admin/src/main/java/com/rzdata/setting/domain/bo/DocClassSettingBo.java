package com.rzdata.setting.domain.bo;

import com.rzdata.framework.core.domain.BaseEntity;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;


/**
 * 文件类型设置业务对象 basic_doc_class_setting
 *
 * <AUTHOR>
 * @date 2023-12-28
 */

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("文件类型设置业务对象")
public class DocClassSettingBo extends BaseEntity {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键", required = true)
    private String id;

    /**
     * 文件类型;basic_doc_class表主键ID
     */
    @ApiModelProperty(value = "文件类型;basic_doc_class表主键ID", required = true)
    @NotBlank(message = "文件类型;basic_doc_class表主键ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private String docClass;

    /**
     * 设置类型  version 版本号
     */
    @ApiModelProperty(value = "设置类型  version 版本号", required = true)
    @NotBlank(message = "设置类型  version 版本号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String type;

    /**
     * 规则id
     */
    private String ruleId;


    /**
     * 设置id
     */
    private String settingId;


    /**
     * 是否应用子分类;Y应用 N不应用
     */
    @ApiModelProperty(value = "是否应用子分类;Y应用 N不应用", required = true)
    @NotBlank(message = "是否应用子分类;Y应用 N不应用不能为空", groups = { AddGroup.class, EditGroup.class })
    private String applyFlag;

    /**
     * 是否生效;Y生效 N不生效
     */
    @ApiModelProperty(value = "是否生效;Y生效 N不生效", required = true)
    @NotBlank(message = "是否生效;Y生效 N不生效不能为空", groups = { AddGroup.class, EditGroup.class })
    private String openFlag;


    /**
     * 分页大小
     */
    @ApiModelProperty("分页大小")
    private Integer pageSize;

    /**
     * 当前页数
     */
    @ApiModelProperty("当前页数")
    private Integer pageNum;

    /**
     * 排序列
     */
    @ApiModelProperty("排序列")
    private String orderByColumn;

    /**
     * 排序的方向desc或者asc
     */
    @ApiModelProperty(value = "排序的方向", example = "asc,desc")
    private String isAsc;

}

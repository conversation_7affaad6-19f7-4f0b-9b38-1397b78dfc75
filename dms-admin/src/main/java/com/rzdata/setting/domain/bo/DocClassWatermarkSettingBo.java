package com.rzdata.setting.domain.bo;

import com.rzdata.framework.core.domain.BaseEntity;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import java.util.List;


/**
 * 文件水印设置业务对象 doc_class_watermark_setting
 *
 * <AUTHOR>
 * @date 2024-04-29
 */

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("文件水印设置业务对象")
public class DocClassWatermarkSettingBo extends BaseEntity {

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id", required = true)
    @NotBlank(message = "主键id不能为空", groups = { EditGroup.class })
    private String id;

    /**
     * 文件类型;basic_doc_class表主键ID
     */
    @ApiModelProperty(value = "文件类型;basic_doc_class表主键ID", required = true)
    @NotBlank(message = "文件类型;basic_doc_class表主键ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private String docClass;

    /**
     * 业务类型;文件新增add_doc、文件修订update_doc、文件作废disuse_doc
     */
    @ApiModelProperty(value = "业务类型;文件新增add_doc、文件修订update_doc、文件作废disuse_doc", required = true)
    @NotBlank(message = "业务类型;文件新增add_doc、文件修订update_doc、文件作废disuse_doc不能为空", groups = { AddGroup.class, EditGroup.class })
    private String bizType;

    /**
     * 是否生效;Y生效 N不生效
     */
    @ApiModelProperty(value = "是否生效;Y生效 N不生效", required = true)
    @NotBlank(message = "是否生效;Y生效 N不生效不能为空", groups = { AddGroup.class, EditGroup.class })
    private String openFlag;

    /**
     * 是否应用子分类;Y应用 N不应用
     */
    @ApiModelProperty(value = "是否应用子分类;Y应用 N不应用", required = true)
    @NotBlank(message = "是否应用子分类;Y应用 N不应用不能为空", groups = { AddGroup.class, EditGroup.class })
    private String applyFlag;


    /**
     * 分页大小
     */
    @ApiModelProperty("分页大小")
    private Integer pageSize;

    /**
     * 当前页数
     */
    @ApiModelProperty("当前页数")
    private Integer pageNum;

    /**
     * 排序列
     */
    @ApiModelProperty("排序列")
    private String orderByColumn;

    /**
     * 排序的方向desc或者asc
     */
    @ApiModelProperty(value = "排序的方向", example = "asc,desc")
    private String isAsc;

    @ApiModelProperty("子项清单")
    private List<DocClassWatermarkSettingDetailBo> nodeList;

    /**
     * 水印规则id
     */
    @ApiModelProperty("水印规则id")
    private String ruleId;

}

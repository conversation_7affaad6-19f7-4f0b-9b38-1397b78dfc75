package com.rzdata.setting.domain.bo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.rzdata.framework.core.domain.BaseEntity;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;


/**
 * 文件水印设置明细业务对象 doc_class_watermark_setting_detail
 *
 * <AUTHOR>
 * @date 2024-04-29
 */

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("文件水印设置明细业务对象")
public class DocClassWatermarkSettingDetailBo extends BaseEntity {

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id", required = true)
    @NotBlank(message = "主键id不能为空", groups = { EditGroup.class })
    private String id;

    /**
     * 关联ID，basic_doc_class_watermark_setting表主键
     */
    @ApiModelProperty(value = "关联ID，basic_doc_class_watermark_setting表主键", required = true)
    @NotBlank(message = "关联ID，basic_doc_class_watermark_setting表主键不能为空", groups = { AddGroup.class, EditGroup.class })
    private String bizId;

    /**
     * 水印类型（text：文本，image：图片）
     */
    @ApiModelProperty(value = "水印类型（text：文本，image：图片）", required = true)
    @NotBlank(message = "水印类型（text：文本，image：图片）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String watermarkSettingType;

    /**
     * 水印名称
     */
    @ApiModelProperty(value = "水印名称", required = true)
    @NotBlank(message = "水印名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String watermarkSettingName;

    /**
     * 水印编码
     */
    @ApiModelProperty(value = "水印编码", required = true)
    @NotBlank(message = "水印编码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String watermarkSettingCode;

    /**
     *  0=未选择 1=已选择
     */
    @ApiModelProperty(value = " 0=未选择 1=已选择", required = true)
    @NotNull(message = " 0=未选择 1=已选择不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer watermarkSettingFactorUse;

    /**
     * 字号（水印类型为text时）
     */
    @ApiModelProperty("字号（水印类型为text时）")
    @NotNull(message = "位置X轴不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer typeSize;

    /**
     * 规模（水印类型为image时使用此字段，长宽暂时不使用）
     */
    @ApiModelProperty("规模（水印类型为image时使用此字段，长宽暂时不使用）")
    private Double scale;

    /**
     * 长
     */
    @ApiModelProperty("长")
    @NotNull(message = "长不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer length;

    /**
     * 宽
     */
    @ApiModelProperty("宽")
    @NotNull(message = "宽不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer width;

    /**
     * 位置X轴
     */
    @ApiModelProperty(value = "位置X轴", required = true)
    @NotNull(message = "位置X轴不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer xPosition;

    /**
     * 位置Y轴
     */
    @ApiModelProperty(value = "位置Y轴", required = true)
    @NotNull(message = "位置Y轴不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer yPosition;


    /**
     * 分页大小
     */
    @ApiModelProperty("分页大小")
    private Integer pageSize;

    /**
     * 当前页数
     */
    @ApiModelProperty("当前页数")
    private Integer pageNum;

    /**
     * 排序列
     */
    @ApiModelProperty("排序列")
    private String orderByColumn;

    /**
     * 排序的方向desc或者asc
     */
    @ApiModelProperty(value = "排序的方向", example = "asc,desc")
    private String isAsc;

    private Integer sort;

    /**
     * 应用范围：（主文件：main，版本附件：attach，都支持：all）
     */
    @ApiModelProperty("应用范围")
    private String appliedRange;

    /**
     * 页面范围
     */
    private String pageOption;
}

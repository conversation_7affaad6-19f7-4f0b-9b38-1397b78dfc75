package com.rzdata.setting.domain.bo;

import com.rzdata.process.domain.BasicFile;
import com.rzdata.setting.domain.DocClassWatermarkSettingDetail;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Data
@ApiModel("水印对象")
public class WatermarkParamBo {

    @ApiModelProperty("生效日期")
    private String startDate;

    @ApiModelProperty("发布日期")
    private String releaseTime;

    @ApiModelProperty("分发日期")
    private String dispenseDate;

    @ApiModelProperty("作废日期")
    private String cancelData;

    @ApiModelProperty("留用截止日期")
    private String retainData;

    @ApiModelProperty("水印List")
    private List<DocClassWatermarkSettingDetail> docClassWatermarkSettingDetailList;

    @ApiModelProperty("版本号")
    private String versionValue;

    @ApiModelProperty("文件编号")
    private String docId;

    @ApiModelProperty("分发号")
    private String distributeChar;


    @ApiModelProperty("编制人")
    private BasicFile organizerSignatureFile;

    @ApiModelProperty("编制人处理时间")
    private String organizerHandleTime;

    @ApiModelProperty("审核人")
    private BasicFile auditorSignatureFile;

    @ApiModelProperty("审核人处理时间")
    private String auditorHandleTime;

    @ApiModelProperty("批准人")
    private BasicFile approverSignatureFile;

    @ApiModelProperty("批准人处理时间")
    private String approverHandleTime;

    @ApiModelProperty("内部文件编号")
    private String internalFileNumber;

    @ApiModelProperty("文件生效章")
    private BasicFile ctrlMarkFilePath;

    @ApiModelProperty("文件失效章")
    private BasicFile cancelMarkFilePath;

    @ApiModelProperty("文件分发章")
    private BasicFile distributeMarkFilePath;

    @ApiModelProperty("外来文件章")
    private BasicFile foreignFilePath;

    @ApiModelProperty("临时文件章")
    private BasicFile temporaryFilePath;

    @ApiModelProperty("文件留用章")
    private BasicFile retainMarkFilePath;

    @ApiModelProperty("委托文件原编号")
    private String ext2;

    @ApiModelProperty("委托文件原名称")
    private String ext8;

    @ApiModelProperty("委托文件原版本")
    private String ext9;

    private String qrCode;
}

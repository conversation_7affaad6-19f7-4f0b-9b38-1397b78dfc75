package com.rzdata.setting.domain.dto;

import lombok.Data;

import java.util.List;


@Data
public class FunConditionDTO {

    /**
     * 绑定目标环节节点
     */
    private List<String> nodeCode;
    /**
     * 是否需要验证
     */
    private Boolean validate;
    /**
     * 反向绑定目标环节节点
     */
    private List<String> neNodeCode;
    /**
     * 限定值
     */
    private String limitValue;
    /**
     * 预选分组Id
     */
    private String groupId;
}

package com.rzdata.setting.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.rzdata.framework.annotation.ExcelDictFormat;
import com.rzdata.framework.convert.ExcelDictConvert;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 文档水印规则视图对象 basic_doc_class_watermark_rule
 *
 * <AUTHOR>
 * @date 2024-01-22
 */
@Data
@ApiModel("文档水印规则视图对象")
@ExcelIgnoreUnannotated
public class BasicDocClassWatermarkRuleVo {
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @ExcelProperty(value = "主键")
    @ApiModelProperty("主键")
    private String id;

    /** 水印规则名称 */
    @ExcelProperty(value = "水印规则名称")
    @ApiModelProperty("水印规则名称")
    private String ruleName;

    /** 规则内容 */
    @ExcelProperty(value = "规则内容")
    @ApiModelProperty("规则内容")
    private String ruleDetails;

    /** 序号 */
    @ExcelProperty(value = "序号")
    @ApiModelProperty("序号")
    private Integer sort;

    /** 备注 */
    @ExcelProperty(value = "备注")
    @ApiModelProperty("备注")
    private String remark;

    /** 创建时间 */
    @ExcelProperty(value = "创建时间")
    @ApiModelProperty("创建时间")
    private Date createTime;

    /** 更新时间 */
    @ExcelProperty(value = "更新时间")
    @ApiModelProperty("更新时间")
    private Date updateTime;

    /** 创建人昵称 */
    private String createByName;
} 
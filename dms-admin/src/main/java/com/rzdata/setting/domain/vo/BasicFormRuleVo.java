package com.rzdata.setting.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.rzdata.framework.annotation.ExcelDictFormat;
import com.rzdata.framework.convert.ExcelDictConvert;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 单规则视图对象 basic_form_rule
 *
 * <AUTHOR>
 * @date 2024-09-20
 */
@Data
@ApiModel("单规则视图对象")
@ExcelIgnoreUnannotated
public class BasicFormRuleVo {

	private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
	@ExcelProperty(value = "主键")
	@ApiModelProperty("主键")
	private String id;

    /**
     * 规则名称
     */
	@ExcelProperty(value = "规则名称")
	@ApiModelProperty("规则名称")
	private String ruleName;

    /**
     * 规则详情
     */
	@ExcelProperty(value = "规则详情")
	@ApiModelProperty("规则详情")
	private String ruleDetails;


}

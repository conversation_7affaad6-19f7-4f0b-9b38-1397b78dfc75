package com.rzdata.setting.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.rzdata.framework.annotation.ExcelDictFormat;
import com.rzdata.framework.convert.ExcelDictConvert;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 编号规则日志视图对象 basic_code_rule_log
 *
 * <AUTHOR>
 * @date 2021-12-27
 */
@Data
@ApiModel("编号规则日志视图对象")
@ExcelIgnoreUnannotated
public class CodeRuleLogVo {

	private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
	@ExcelProperty(value = "主键")
	@ApiModelProperty("主键")
	private String id;

    /**
     * 规则主键
     */
	@ExcelProperty(value = "规则主键")
	@ApiModelProperty("规则主键")
	private String ruleId;

    /**
     * 流水值
     */
	@ExcelProperty(value = "流水值")
	@ApiModelProperty("流水值")
	private Long numberValue;

    /**
     * 规则生成值
     */
	@ExcelProperty(value = "规则生成值")
	@ApiModelProperty("规则生成值")
	private String ruleValue;

    /**
     * 规则使用业务主键
     */
	@ExcelProperty(value = "规则使用业务主键")
	@ApiModelProperty("规则使用业务主键")
	private String businessId;


}

package com.rzdata.setting.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.rzdata.framework.annotation.ExcelDictFormat;
import com.rzdata.framework.convert.ExcelDictConvert;
import com.rzdata.setting.domain.CodeRuleDetail;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;


/**
 * 编号规则视图对象 basic_code_rule
 *
 * <AUTHOR>
 * @date 2021-12-27
 */
@Data
@ApiModel("编号规则视图对象")
@ExcelIgnoreUnannotated
public class CodeRuleVo {

	private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
	@ExcelProperty(value = "主键")
	@ApiModelProperty("主键")
	private String id;

    /**
     * 规则名称
     */
	@ExcelProperty(value = "规则名称")
	@ApiModelProperty("规则名称")
	private String ruleName;

    /**
     * 流水号重置周期，不重置：NO、年：Y、月：M、日：D
     */
	@ExcelProperty(value = "流水号重置周期，不重置：NO、年：Y、月：M、日：D")
	@ApiModelProperty("流水号重置周期，不重置：NO、年：Y、月：M、日：D")
	private String resetCycle;

    /**
     * 流水号初始值
     */
	@ExcelProperty(value = "流水号初始值")
	@ApiModelProperty("流水号初始值")
	private Long numberInitValue;

    /**
     * 流水号位数
     */
	@ExcelProperty(value = "流水号位数")
	@ApiModelProperty("流水号位数")
	private Integer numberDigit;

	@ApiModelProperty("详情")
	private List<CodeRuleDetail> ruleDetailList;

	/**
	 * 创建时间
	 */
	@ApiModelProperty(value = "创建时间")
	private Date createTime;

	/**
	 * 更新时间
	 */
	@ApiModelProperty(value = "更新时间")
	private Date updateTime;

}

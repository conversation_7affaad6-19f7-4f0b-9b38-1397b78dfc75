package com.rzdata.setting.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.rzdata.framework.annotation.ExcelDictFormat;
import com.rzdata.framework.convert.ExcelDictConvert;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 合稿管理视图对象 basic_codraft
 *
 * <AUTHOR>
 * @date 2022-03-02
 */
@Data
@ApiModel("合稿管理视图对象")
@ExcelIgnoreUnannotated
public class CodraftVo {

	private static final long serialVersionUID = 1L;

    /**
     * 合稿管理ID
     */
	@ExcelProperty(value = "合稿管理ID")
	@ApiModelProperty("合稿管理ID")
	private String id;

    /**
     * 合稿要素名称
     */
	@ExcelProperty(value = "合稿要素名称")
	@ApiModelProperty("合稿要素名称")
	private String codraftName;

    /**
     * 合稿模板
     */
	@ExcelProperty(value = "合稿模板")
	@ApiModelProperty("合稿模板")
	private String fileId;

	/**
	 * 模板名称
	 */
	@ExcelProperty(value = "模板名称")
	@ApiModelProperty("模板名称")
	private String templateName;

	/**
	 * 位置
	 */
	@ExcelProperty(value = "位置")
	@ApiModelProperty(value = "位置")
	private String position;

	/**
	 * 排序
	 */
	@ExcelProperty(value = "排序")
	@ApiModelProperty(value = "排序")
	private Integer sort;

	/**
	 * 分类Id
	 */
	@ExcelProperty(value = "分类Id")
	@ApiModelProperty(value = "分类Id")
	private String classId;


    /**
     * 状态
     */
    @ExcelProperty(value = "状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "yes_no")
	@ApiModelProperty("状态")
	private String codraftStatus;

	/**
	 * 业务ID
	 */
	private String bizId;
}

package com.rzdata.setting.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 合稿文件分类视图对象 combined_file_class
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Data
@ApiModel("合稿文件分类视图对象")
@ExcelIgnoreUnannotated
public class CombinedFileClassVo {

	private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
	@ExcelProperty(value = "主键")
	@ApiModelProperty("主键")
	private String id;

    /**
     * 合稿ID;关联合稿管理表
     */
	@ExcelProperty(value = "合稿ID;关联合稿管理表")
	@ApiModelProperty("合稿ID;关联合稿管理表")
	private String combinedMgrId;

    /**
     * 文件分类ID;关联文件分类表
     */
	@ExcelProperty(value = "文件分类ID;关联文件分类表")
	@ApiModelProperty("文件分类ID;关联文件分类表")
	private String fileClassId;


}

package com.rzdata.setting.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.rzdata.setting.domain.bo.CodraftBo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;


/**
 * 合稿管理视图对象 combined_mgr
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Data
@ApiModel("合稿管理视图对象")
@ExcelIgnoreUnannotated
public class CombinedMgrVo {

	private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
	@ExcelProperty(value = "主键")
	@ApiModelProperty("主键")
	private String id;

    /**
     * 名称
     */
	@ExcelProperty(value = "名称")
	@ApiModelProperty("名称")
	private String name;

    /**
     * 备注
     */
	@ExcelProperty(value = "备注")
	@ApiModelProperty("备注")
	private String remark;

    /**
     * 是否删除: 0-正常, 1-删除
     */
	@ExcelProperty(value = "是否删除: 0-正常, 1-删除")
	private Integer isDeleted;

	private String createBy;

	@ExcelProperty(value = "创建时间")
	@ApiModelProperty("创建时间")
	private Date createTime;

	@ApiModelProperty("合稿管理对象")
	private List<CodraftVo> codraftVoList;
}

package com.rzdata.setting.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 预设分组人员详情视图对象 basic_distribute_group_detail
 *
 * <AUTHOR>
 * @date 2024-11-26
 */
@Data
@ApiModel("预设分组人员详情视图对象")
@ExcelIgnoreUnannotated
public class DistributeGroupDetailVo {

	private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
	@ExcelProperty(value = "主键")
	@ApiModelProperty("主键")
	private String id;

    /**
     * 分组id
     */
	@ExcelProperty(value = "分组id")
	@ApiModelProperty("分组id")
	private String groupId;

    /**
     * 租户id
     */
	@ExcelProperty(value = "租户id")
	@ApiModelProperty("租户id")
	private String tenantId;

    /**
     * 接收人
     */
	@ExcelProperty(value = "接收人")
	@ApiModelProperty("接收人")
	private String receiveUserName;

    /**
     * 接收人昵称
     */
	@ExcelProperty(value = "接收人昵称")
	@ApiModelProperty("接收人昵称")
	private String receiveNickName;

    /**
     * 接收人部门id
     */
	@ExcelProperty(value = "接收人部门id")
	@ApiModelProperty("接收人部门id")
	private String receiveUserDeptId;

    /**
     * 接收人部门名称
     */
	@ExcelProperty(value = "接收人部门名称")
	@ApiModelProperty("接收人部门名称")
	private String receiveUserDept;

	/**
	 * 打印纸张类型
	 */
	private String printPaperType;

	/**
	 * 数量
	 */
	private Integer nums;

    /**
     * 类型 部门 dept、个人 person
     */
	@ExcelProperty(value = "类型 部门 dept、个人 person")
	@ApiModelProperty("类型 部门 dept、个人 person")
	private String type;

    /**
     * 类别 分发 print、培训 train顺络电子分发是打印权限 培训是查看权限
     */
	@ExcelProperty(value = "类别 分发 print、培训 train")
	@ApiModelProperty("类别 分发 print、培训 train")
	private String category;


}

package com.rzdata.setting.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.rzdata.setting.domain.DistributeGroupDetail;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;


/**
 * 预设分组人员视图对象 basic_distribute_group
 *
 * <AUTHOR>
 * @date 2024-11-26
 */
@Data
@ApiModel("预设分组人员视图对象")
@ExcelIgnoreUnannotated
public class DistributeGroupVo {

	private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
	@ExcelProperty(value = "主键")
	@ApiModelProperty("主键")
	private String id;

    /**
     * 规则名称
     */
	@ExcelProperty(value = "规则名称")
	@ApiModelProperty("规则名称")
	private String groupName;

    /**
     * 类型
     */
	@ExcelProperty(value = "类型")
	@ApiModelProperty("类型")
	private String type;

	/**
	 * 人员范围类型
	 */
	@ExcelProperty(value = "人员范围类型")
	@ApiModelProperty("人员范围类型")
	private String scopeType;

    /**
     * 租户id(basic_tenant主键id)
     */
	@ExcelProperty(value = "租户id(basic_tenant主键id)")
	@ApiModelProperty("租户id(basic_tenant主键id)")
	private String tenantId;

	private Date createTime;

	private List<DistributeGroupDetail> itemList;
}

package com.rzdata.setting.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.rzdata.framework.annotation.ExcelDictFormat;
import com.rzdata.framework.convert.ExcelDictConvert;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;


/**
 * 文件分类设置-流程设置视图对象 basic_doc_class_flow
 *
 * <AUTHOR>
 * @date 2023-01-10
 */
@Data
@ApiModel("文件分类设置-流程设置视图对象")
@ExcelIgnoreUnannotated
public class DocClassFlowVo {

	private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
	@ExcelProperty(value = "主键id")
	@ApiModelProperty("主键id")
	private String id;

    /**
     * 租户id
     */
	@ExcelProperty(value = "租户id")
	@ApiModelProperty("租户id")
	private String tenantId;

    /**
     * 文件类型;basic_doc_class表主键ID
     */
	@ExcelProperty(value = "文件类型;basic_doc_class表主键ID")
	@ApiModelProperty("文件类型;basic_doc_class表主键ID")
	private String docClass;

    /**
     * 业务类型;文件新增add_doc、文件修订update_doc、文件作废disuse_doc
     */
	@ExcelProperty(value = "业务类型;文件新增add_doc、文件修订update_doc、文件作废disuse_doc")
	@ApiModelProperty("业务类型;文件新增add_doc、文件修订update_doc、文件作废disuse_doc")
	private String bizType;

    /**
     * 业务名称
     */
	@ExcelProperty(value = "业务名称")
	@ApiModelProperty("业务名称")
	private String bizName;

    /**
     * 流程KEY;对应流程KEY
     */
	@ExcelProperty(value = "流程KEY;对应流程KEY")
	@ApiModelProperty("流程KEY;对应流程KEY")
	private String flowKey;

    /**
     * 流程名称;对应流程名称
     */
	@ExcelProperty(value = "流程名称;对应流程名称")
	@ApiModelProperty("流程名称;对应流程名称")
	private String flowName;

    /**
     * 是否生效;Y生效 N不生效
     */
	@ExcelProperty(value = "是否生效;Y生效 N不生效")
	@ApiModelProperty("是否生效;Y生效 N不生效")
	private String openFlag;

    /**
     * 是否应用子分类;Y应用 N不应用
     */
	@ExcelProperty(value = "是否应用子分类;Y应用 N不应用")
	@ApiModelProperty("是否应用子分类;Y应用 N不应用")
	private String applyFlag;

    /**
     * 审批时效性
     */
	@ExcelProperty(value = "审批时效性")
	@ApiModelProperty("审批时效性")
	private Long dealTime;

    /**
     * 审批时效性单位
     */
	@ExcelProperty(value = "审批时效性单位")
	@ApiModelProperty("审批时效性单位")
	private String dealTimeUnit;

	@ApiModelProperty("流程节点清单")
	private List<DocClassFlowNodeVo> nodeList;

}

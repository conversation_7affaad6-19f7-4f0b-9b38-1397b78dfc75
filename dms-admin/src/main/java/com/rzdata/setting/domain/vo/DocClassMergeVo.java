package com.rzdata.setting.domain.vo;

import com.rzdata.process.enums.MergeTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2022/3/14 15:47
 * @Version 1.0
 * @Description
 */
@Data
public class DocClassMergeVo {

    /**
     * 主键id
     */
    @ApiModelProperty("主键id")
    private String id;
    /**
     * 文件类型
     */
    @ApiModelProperty("文件类型")
    private String docClass;

    /**
     * 合稿要素名称
     */
    @ApiModelProperty("合稿要素名称")
    private String mergeFactorName;

    /**
     * 合稿要素-是否选择 0=未选择 1=已选择
     */
    @ApiModelProperty("合稿要素-是否选择 0=未选择 1=已选择")
    private Integer mergeFactorUse;

    /**
     * 样式名称
     */
    //@ApiModelProperty("样式名称")
    //private String codraftName;

    /**
     * 样式id(basic_codraft主键id)
     */
    @ApiModelProperty("样式id(basic_codraft主键id)")
    private String codraftId;

    /**
     * 是否另起一页 0=否 1=是
     */
    @ApiModelProperty("是否另起一页 0=否 1=是")
    private Integer anotherPage;
    /**
     * 用于合稿要素二
     * 应用范围 1=除首页 2=除末页 3=全文应用 4=仅首页
     */
    @ApiModelProperty(value = "1=除首页 2=除末页 3=全文应用 4=仅首页",required = true)
    private Integer rangeValue;

    /**
     * 类型:生效文件(VALID)/失效文件(INVALID)
     */
    @ApiModelProperty("类型:生效文件(VALID)/失效文件(INVALID)")
    private MergeTypeEnum type;

    /**
     * 是否是默认值 是默认值则不可以删除 0=否 1=是
     */
    @ApiModelProperty(value = "是否是默认值 是默认值则不可以删除 0=否 1=是",required = false)
    private Integer defaultFlag;
    /**
     * 1=合稿要素一 2=合稿要素二 3=合稿要素三.....
     */
    @ApiModelProperty("1=合稿要素一 2=合稿要素二 3=合稿要素三.....")
    private Integer mergeFactor;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    private Date updateTime;
}

package com.rzdata.setting.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.rzdata.framework.annotation.ExcelDictFormat;
import com.rzdata.framework.convert.ExcelDictConvert;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 文件类型设置视图对象 basic_doc_class_setting
 *
 * <AUTHOR>
 * @date 2023-12-28
 */
@Data
@ApiModel("文件类型设置视图对象")
@ExcelIgnoreUnannotated
public class DocClassSettingVo {

	private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
	@ExcelProperty(value = "主键")
	@ApiModelProperty("主键")
	private String id;

    /**
     * 文件类型;basic_doc_class表主键ID
     */
	@ExcelProperty(value = "文件类型;basic_doc_class表主键ID")
	@ApiModelProperty("文件类型;basic_doc_class表主键ID")
	private String docClass;

    /**
     * 设置类型  version 版本号
     */
	@ExcelProperty(value = "设置类型  version 版本号")
	@ApiModelProperty("设置类型  version 版本号")
	private String type;

	/**
	 * 规则id
	 */
	private String ruleId;

    /**
     * 是否应用子分类;Y应用 N不应用
     */
	@ExcelProperty(value = "是否应用子分类;Y应用 N不应用")
	@ApiModelProperty("是否应用子分类;Y应用 N不应用")
	private String applyFlag;

    /**
     * 是否生效;Y生效 N不生效
     */
	@ExcelProperty(value = "是否生效;Y生效 N不生效")
	@ApiModelProperty("是否生效;Y生效 N不生效")
	private String openFlag;


	/**
	 * 设置id
	 */
	private String settingId;
}

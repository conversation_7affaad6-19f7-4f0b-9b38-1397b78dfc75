package com.rzdata.setting.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.rzdata.framework.annotation.ExcelDictFormat;
import com.rzdata.framework.convert.ExcelDictConvert;
import com.rzdata.process.domain.BasicFile;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;


/**
 * 文件类型视图对象 basic_doc_class
 *
 * <AUTHOR>
 * @date 2021-12-27
 */
@Data
@ApiModel("文件类型视图对象")
@ExcelIgnoreUnannotated
public class DocClassVo {

	private static final long serialVersionUID = 1L;

    /**
     * 分类代码
     */
	@ExcelProperty(value = "分类代码")
	@ApiModelProperty("分类代码")
	private String id;


    /**
     * 分类名称
     */
	@ExcelProperty(value = "分类名称")
	@ApiModelProperty("分类名称")
	private String className;

	/**
	 * 分类编码
	 */
	@ExcelProperty(value = "分类编码")
	private String classCode;

	@ExcelProperty(value = "权限字符")
	private String permission;

    /**
     * 上级分类
     */
	@ExcelProperty(value = "上级分类")
	@ApiModelProperty("上级分类")
	private String parentClassId;

	/**
	 * 祖级列表
	 */
	private String ancestors;

    /**
     * 分类层级
     */
	//@ExcelProperty(value = "分类层级")
	@ApiModelProperty("分类层级")
	private String classLevel;

    /**
     * 文件有效期
     */
	//@ExcelProperty(value = "文件有效期")
	@ApiModelProperty("文件有效期")
	private Long expiration;

    /**
     * 文件复审周期
     */
	//@ExcelProperty(value = "文件复审周期")
	@ApiModelProperty("文件复审周期")
	private Long reviewCycle;

    /**
     * 签收类型，个人，部门
     */
	//@ExcelProperty(value = "签收类型，个人，部门")
	@ApiModelProperty("签收类型，个人，部门")
	private String checkType;

    /**
     * 签章类型，个人、部门、公司
     */
	//@ExcelProperty(value = "签章类型，个人、部门、公司")
	@ApiModelProperty("签章类型，个人、部门、公司")
	private String encryptType;

    /**
     * 是否纸质打印
     */
	//@ExcelProperty(value = "是否纸质打印")
	@ApiModelProperty("是否纸质打印")
	private String yNPrint;

    /**
     * 是否纸质回收
     */
	//@ExcelProperty(value = "是否纸质回收")
	@ApiModelProperty("是否纸质回收")
	private String yNRecyle;

    /**
     * 合稿设置
     */
	//@ExcelProperty(value = "合稿设置")
	@ApiModelProperty("合稿设置")
	private String mergeDocConfig;

    /**
     * 编号设置
     */
	//@ExcelProperty(value = "编号设置")
	@ApiModelProperty("编号设置")
	private String codeId;

	/**
	 * 文件分发号规则
	 */
	//@ExcelProperty(value = "文件分发号规则")
	@ApiModelProperty("文件分发号规则")
	private String distributeId;

	/**
	 * 是否启用文件分发号设置
	 */
	//@ExcelProperty(value = "是否启用文件分发号设置")
	@ApiModelProperty("是否启用文件分发号设置")
	private String openDistribute;

	/**
	 * 文件分发号设置是否应用到子类型
	 */
	//@ExcelProperty(value = "文件分发号设置是否应用到子类型")
	@ApiModelProperty("文件分发号设置是否应用到子类型")
	private String applyDistribute;

	/**
	 * 是否启用文件编号
	 */
	//@ExcelProperty(value = "是否启用文件编号")
	@ApiModelProperty("是否启用文件编号")
	private String yNOpenCode;

	/**
	 * 是否启用文件编号
	 */
	//@ExcelProperty(value = "是否应用到子文件类型")
	@ApiModelProperty("是否应用到子文件类型")
	private String yNApplyCode;


	/**
	 * 文件时效设置是否应用到子类型
	 */
	@ApiModelProperty(value = "文件时效设置是否应用到子类型", required = true)

	private String applyPrescription;

	private String openReview;
	private String applyReview;
	private String openMerge;
	private String applyMerge;

	private String signatureType;
	private String openSignature;
	private String applySignature;

	private Boolean purview;

	/**
	 * 是否启用文件时效设置
	 */
	@ApiModelProperty(value = "是否启用文件时效设置", required = true)

	private String openPrescription;

	@ExcelProperty(value = "排序")
	@ApiModelProperty("排序")
	private Integer sort;

	/**
     * 文件模板
     */
	//@ExcelProperty(value = "文件模板")
	@ApiModelProperty("文件模板")
	private String fileId;
	private String dataType;

	/**
	 * 创建时间
	 */
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm",timezone = "GMT+8")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm")
	@ApiModelProperty(value = "创建时间")
	private Date createTime;

	/**
	 * 更新时间
	 */
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm",timezone = "GMT+8")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm")
	@ApiModelProperty(value = "更新时间")
	private Date updateTime;

	/**
	 * 分类所属类型
	 * DOC文件台账、RECORD记录台账、FOREIGN外来文件
	 */
	@ApiModelProperty("分类所属类型")
	@ExcelProperty(value = "分类所属类型", converter = ExcelDictConvert.class)
	@ExcelDictFormat(dictType = "class_type")
	private String classType;

	@ApiModelProperty("文件详情信息")
	private List<BasicFile> fileList;

	@ApiModelProperty("文件合稿设置信息")
	private List<DocClassMergeVo> docClassMergeVoList;

	@ApiModelProperty("文件签章设置信息")
	private List<DocClassSignatureVo> docClassSignatureVoList;

	/**
	 * 分类状态
	 */
	@ApiModelProperty("分类状态")
	private String classStatus;

	/**
	 * 是否为临时文件 Y N
	 */
	@ExcelProperty(value = "是否临时文件", converter = ExcelDictConvert.class)
	@ExcelDictFormat(dictType = "sys_yes_no")
	private String isFileType;

	private String combinedMgrId;
}

package com.rzdata.setting.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.rzdata.framework.annotation.ExcelDictFormat;
import com.rzdata.framework.convert.ExcelDictConvert;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;


/**
 * 文件水印设置视图对象 doc_class_watermark_setting
 *
 * <AUTHOR>
 * @date 2024-04-29
 */
@Data
@ApiModel("文件水印设置视图对象")
@ExcelIgnoreUnannotated
public class DocClassWatermarkSettingVo {

	private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
	@ExcelProperty(value = "主键id")
	@ApiModelProperty("主键id")
	private String id;

    /**
     * 文件类型;basic_doc_class表主键ID
     */
	@ExcelProperty(value = "文件类型;basic_doc_class表主键ID")
	@ApiModelProperty("文件类型;basic_doc_class表主键ID")
	private String docClass;

    /**
     * 业务类型;文件新增add_doc、文件修订update_doc、文件作废disuse_doc
     */
	@ExcelProperty(value = "业务类型;文件新增add_doc、文件修订update_doc、文件作废disuse_doc")
	@ApiModelProperty("业务类型;文件新增add_doc、文件修订update_doc、文件作废disuse_doc")
	private String bizType;

    /**
     * 是否生效;Y生效 N不生效
     */
	@ExcelProperty(value = "是否生效;Y生效 N不生效")
	@ApiModelProperty("是否生效;Y生效 N不生效")
	private String openFlag;

    /**
     * 是否应用子分类;Y应用 N不应用
     */
	@ExcelProperty(value = "是否应用子分类;Y应用 N不应用")
	@ApiModelProperty("是否应用子分类;Y应用 N不应用")
	private String applyFlag;

	@ApiModelProperty("子项清单")
	private List<DocClassWatermarkSettingDetailVo> nodeList;

	/**
	 * 水印规则id
	 */
	private String ruleId;

	/**
	 * 文件类型名称
	 */
	private String className;

}

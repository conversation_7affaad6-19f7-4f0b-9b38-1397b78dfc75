package com.rzdata.setting.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;


/**
 * 预制编号视图对象 doc_prepare_id
 *
 * <AUTHOR>
 * @date 2023-10-07
 */
@Data
@ApiModel("预制编号视图对象")
@ExcelIgnoreUnannotated
public class DocPrepareIdVo {

	private static final long serialVersionUID = 1L;

    /**
     * 文件编号
     */
	@ExcelProperty(value = "文件编号")
	@ApiModelProperty("文件编号")
	private String docId;

    /**
     * 编号类型 DOC主文件  RECORD关联记录
     */
	@ExcelProperty(value = "编号类型 DOC主文件  RECORD关联记录")
	@ApiModelProperty("编号类型 DOC主文件  RECORD关联记录")
	private String codeType;

    /**
     * 文件类型
     */
	@ExcelProperty(value = "文件类型")
	@ApiModelProperty("文件类型")
	private String docClass;

    /**
     * 备注说明
     */
	@ExcelProperty(value = "备注说明")
	@ApiModelProperty("备注说明")
	private String remark;

    /**
     * 记录文件关联主文件编号
     */
	@ExcelProperty(value = "记录文件关联主文件编号")
	@ApiModelProperty("记录文件关联主文件编号")
	private String parentDocId;

    /**
     * 申请人
     */
	@ExcelProperty(value = "申请人")
	@ApiModelProperty("申请人")
	private String applyBy;

    /**
     * 使用状态 0未使用 1已使用
     */
	@ExcelProperty(value = "使用状态 0未使用 1已使用")
	@ApiModelProperty("使用状态 0未使用 1已使用")
	private String useStatus;

    /**
     * 使用时间
     */
	@ExcelProperty(value = "使用时间")
	@ApiModelProperty("使用时间")
	private Date useTime;

	private String dataType;

	private String projectId;

    /**
     * 创建时间
     */
	@ExcelProperty(value = "创建时间")
	@ApiModelProperty("创建时间")
	private Date createTime;

	private String applyNickName;

	private String businessId;
}

package com.rzdata.setting.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.rzdata.framework.annotation.ExcelDictFormat;
import com.rzdata.framework.convert.ExcelDictConvert;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 版本规则明细视图对象 basic_version_rule_detail
 *
 * <AUTHOR>
 * @date 2023-12-27
 */
@Data
@ApiModel("版本规则明细视图对象")
@ExcelIgnoreUnannotated
public class VersionRuleDetailVo {

	private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
	@ExcelProperty(value = "主键")
	@ApiModelProperty("主键")
	private String id;

    /**
     * 规则主键
     */
	@ExcelProperty(value = "规则主键")
	@ApiModelProperty("规则主键")
	private String ruleId;

    /**
     * 规则类型，固定字符：STR、字母：ALP、数字：NUM
     */
	@ExcelProperty(value = "规则类型，固定字符：STR、字母：ALP、数字：NUM")
	@ApiModelProperty("规则类型，固定字符：STR、字母：ALP、数字：NUM")
	private String ruleType;

    /**
     * 起始值
     */
	@ExcelProperty(value = "起始值")
	@ApiModelProperty("起始值")
	private String startValue;

    /**
     * 满足条件
     */
	@ExcelProperty(value = "满足条件")
	@ApiModelProperty("满足条件")
	private String endValue;

	/**
	 * 是否同步
	 */
	private Boolean sync;

	private String value;

	/**
	 * 数位
	 */
	@ExcelProperty(value = "数位")
	@ApiModelProperty("数位")
	private Integer digit;

    /**
     * 排序号
     */
	@ExcelProperty(value = "排序号")
	@ApiModelProperty("排序号")
	private Long orderBy;

    /**
     * 租户id(basic_tenant主键id)
     */
	@ExcelProperty(value = "租户id(basic_tenant主键id)")
	@ApiModelProperty("租户id(basic_tenant主键id)")
	private String tenantId;


}

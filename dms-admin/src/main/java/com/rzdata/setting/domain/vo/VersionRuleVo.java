package com.rzdata.setting.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;


/**
 * 版本规则视图对象 basic_version_rule
 *
 * <AUTHOR>
 * @date 2023-12-27
 */
@Data
@ApiModel("版本规则视图对象")
@ExcelIgnoreUnannotated
public class VersionRuleVo {

	private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
	@ExcelProperty(value = "主键")
	@ApiModelProperty("主键")
	private String id;

    /**
     * 规则名称
     */
	@ExcelProperty(value = "规则名称")
	@ApiModelProperty("规则名称")
	private String ruleName;

	/**
	 * 后续规则
	 */
	private String nextId;

	/**
	 * 起始值
	 */
	private String startValue;

    /**
     * 租户id(basic_tenant主键id)
     */
	@ExcelProperty(value = "租户id(basic_tenant主键id)")
	@ApiModelProperty("租户id(basic_tenant主键id)")
	private String tenantId;

	/**
	 * 创建时间
	 */
	@ApiModelProperty(value = "创建时间")
	private Date createTime;

	/**
	 * 更新时间
	 */
	@ApiModelProperty(value = "更新时间")
	private Date updateTime;
}

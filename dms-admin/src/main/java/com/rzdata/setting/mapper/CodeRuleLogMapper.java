package com.rzdata.setting.mapper;

import com.rzdata.setting.domain.CodeRuleLog;
import com.rzdata.framework.core.mybatisplus.core.BaseMapperPlus;
import org.apache.ibatis.annotations.Param;

/**
 * 编号规则日志Mapper接口
 *
 * <AUTHOR>
 * @date 2021-12-27
 */
public interface CodeRuleLogMapper extends BaseMapperPlus<CodeRuleLog> {

    CodeRuleLog selectLogByValue(@Param("ruleId") String ruleId, @Param("businessId") String businessId, @Param("ruleValue") String ruleValue,@Param("numberValue") Long numberValue);
}

package com.rzdata.setting.mapper;

import com.rzdata.setting.domain.DocClassFlowNode;
import com.rzdata.framework.core.mybatisplus.core.BaseMapperPlus;
import com.rzdata.setting.domain.vo.DocClassFlowNodeVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 文件分类设置-流程节点设置Mapper接口
 *
 * <AUTHOR>
 * @date 2023-01-10
 */
public interface DocClassFlowNodeMapper extends BaseMapperPlus<DocClassFlowNode> {

    List<DocClassFlowNodeVo> getNodeList(@Param("flowId") String flowId, @Param("code") String code);
}

package com.rzdata.setting.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.rzdata.setting.domain.DocClass;
import com.rzdata.framework.core.mybatisplus.core.BaseMapperPlus;
import com.rzdata.setting.domain.vo.DocClassVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 文件类型Mapper接口
 *
 * <AUTHOR>
 * @date 2021-12-27
 */
public interface DocClassMapper extends BaseMapperPlus<DocClass> {

    List<DocClassVo> getClassByPerm(@Param("companyIds") List<String> companyIds, @Param("deptIds")  List<String> deptIds, @Param("roleIds") List<Long> roleIds, @Param("persons") List<String> person, @Param("ew") LambdaQueryWrapper<DocClass> wrapper);

    /**
     * 查询dms_zonci_sh库中有效的文件分类
     * @return 有效的文件分类列表
     */
    List<DocClassVo> selectZonciActiveDocClass(@Param("dbName") String dbName);
}

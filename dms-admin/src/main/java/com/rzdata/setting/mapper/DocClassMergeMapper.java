package com.rzdata.setting.mapper;

import com.rzdata.framework.core.mybatisplus.core.BaseMapperPlus;
import com.rzdata.setting.domain.DocClassMerge;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @Date 2022/3/14 16:05
 * @Version 1.0
 * @Description
 */
public interface DocClassMergeMapper extends BaseMapperPlus<DocClassMerge> {

    /**
     * 通过文件类型拿到对应的合稿要素名称
     * @param docClass 文件类型
     * @param mergeFactorName 合稿要素名称
     * @return
     */
    String getTemplateIdByDocClass(@Param("docClass") String docClass, @Param("mergeFactorName") String mergeFactorName);
}

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rzdata.process.mapper.DocDistributeReadRecordsMapper">

    <resultMap type="com.rzdata.process.domain.DocDistributeReadRecords" id="DocDistributeReadRecordsResult">
        <result property="id" column="id"/>
        <result property="versionId" column="version_id"/>
        <result property="userId" column="user_id"/>
        <result property="userName" column="user_name"/>
        <result property="nickName" column="nick_name"/>
        <result property="deptId" column="dept_id"/>
        <result property="deptName" column="dept_name"/>
        <result property="status" column="status"/>
        <result property="times" column="times"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>


</mapper>

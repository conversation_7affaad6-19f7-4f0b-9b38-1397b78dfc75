package com.rzdata.setting.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.rzdata.setting.domain.BasicDocClassWatermarkRule;
import com.rzdata.setting.domain.vo.BasicDocClassWatermarkRuleVo;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.setting.domain.bo.BasicDocClassWatermarkRuleBo;
import com.rzdata.setting.domain.bo.DocClassWatermarkSettingBo;
import com.rzdata.setting.domain.vo.DocClassWatermarkSettingVo;

import java.util.Collection;
import java.util.List;

/**
 * 文档水印规则Service接口
 *
 * <AUTHOR>
 * @date 2024-01-22
 */
public interface IBasicDocClassWatermarkRuleService extends IService<BasicDocClassWatermarkRule> {

    /**
     * 查询文档水印规则
     *
     * @param id 文档水印规则主键
     * @return 文档水印规则
     */
    BasicDocClassWatermarkRuleVo queryById(String id);

    /**
     * 查询文档水印规则列表
     *
     * @param bo 文档水印规则
     * @return 文档水印规则集合
     */
    TableDataInfo<BasicDocClassWatermarkRuleVo> queryPageList(BasicDocClassWatermarkRuleBo bo);

    /**
     * 查询文档水印规则列表
     *
     * @param bo 文档水印规则
     * @return 文档水印规则集合
     */
    List<BasicDocClassWatermarkRuleVo> queryList(BasicDocClassWatermarkRuleBo bo);

    /**
     * 修改文档水印规则
     *
     * @param bo 文档水印规则
     * @return 结果
     */
    Boolean insertByBo(BasicDocClassWatermarkRuleBo bo);

    /**
     * 修改文档水印规则
     *
     * @param bo 文档水印规则
     * @return 结果
     */
    Boolean updateByBo(BasicDocClassWatermarkRuleBo bo);

    /**
     * 校验并批量删除文档水印规则信息
     *
     * @param ids 需要删除的文档水印规则主键集合
     * @param isValid 是否校验,true-删除前校验,false-不校验
     * @return 结果
     */
    Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);

    /**
     * 检查水印规则是否被使用
     *
     * @param id 水印规则ID
     * @return 使用该规则的文件类型列表
     */
    List<DocClassWatermarkSettingVo> checkRuleUsage(String id);

    int removeUsage(DocClassWatermarkSettingBo bo);

    /**
     * 新增水印规则应用
     *
     * @param bo 水印规则应用业务对象
     * @return 结果
     */
    Boolean addUsage(DocClassWatermarkSettingBo bo);
} 
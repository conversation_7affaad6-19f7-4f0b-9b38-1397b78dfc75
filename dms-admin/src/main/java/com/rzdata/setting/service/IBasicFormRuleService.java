package com.rzdata.setting.service;

import com.rzdata.setting.domain.BasicFormRule;
import com.rzdata.setting.domain.vo.BasicFormRuleVo;
import com.rzdata.setting.domain.bo.BasicFormRuleBo;
import com.rzdata.framework.core.mybatisplus.core.IServicePlus;
import com.rzdata.framework.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 表单规则Service接口
 *
 * <AUTHOR>
 * @date 2024-09-20
 */
public interface IBasicFormRuleService extends IServicePlus<BasicFormRule, BasicFormRuleVo> {
	/**
	 * 查询单个
	 * @return
	 */
	BasicFormRuleVo queryById(Long id);

	/**
	 * 查询列表
	 */
    TableDataInfo<BasicFormRuleVo> queryPageList(BasicFormRuleBo bo);

	/**
	 * 查询列表
	 */
	List<BasicFormRuleVo> queryList(BasicFormRuleBo bo);

	/**
	 * 根据新增业务对象插入表单规则
	 * @param bo 表单规则新增业务对象
	 * @return
	 */
	Boolean insertByBo(BasicFormRuleBo bo);

	/**
	 * 根据编辑业务对象修改表单规则
	 * @param bo 表单规则编辑业务对象
	 * @return
	 */
	Boolean updateByBo(BasicFormRuleBo bo);

	/**
	 * 校验并删除数据
	 * @param ids 主键集合
	 * @param isValid 是否校验,true-删除前校验,false-不校验
	 * @return
	 */
	Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

	/**
	 * 根据文档类型获取表单规则
	 * @param docClass
	 * @return
	 */
    BasicFormRuleVo getFormRuleByRecursive(String docClass);

	List<String> getHiddenColumns(String docClass);
}

package com.rzdata.setting.service;

import com.rzdata.setting.domain.CodeRuleLog;
import com.rzdata.setting.domain.vo.CodeRuleLogVo;
import com.rzdata.setting.domain.bo.CodeRuleLogBo;
import com.rzdata.framework.core.mybatisplus.core.IServicePlus;
import com.rzdata.framework.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 编号规则日志Service接口
 *
 * <AUTHOR>
 * @date 2021-12-27
 */
public interface ICodeRuleLogService extends IServicePlus<CodeRuleLog, CodeRuleLogVo> {
	/**
	 * 查询单个
	 * @return
	 */
	CodeRuleLogVo queryById(String id);

	/**
	 * 查询列表
	 */
    TableDataInfo<CodeRuleLogVo> queryPageList(CodeRuleLogBo bo);

	/**
	 * 查询列表
	 */
	List<CodeRuleLogVo> queryList(CodeRuleLogBo bo);

	/**
	 * 根据新增业务对象插入编号规则日志
	 * @param bo 编号规则日志新增业务对象
	 * @return
	 */
	Boolean insertByBo(CodeRuleLogBo bo);

	/**
	 * 根据编辑业务对象修改编号规则日志
	 * @param bo 编号规则日志编辑业务对象
	 * @return
	 */
	Boolean updateByBo(CodeRuleLogBo bo);

	/**
	 * 校验并删除数据
	 * @param ids 主键集合
	 * @param isValid 是否校验,true-删除前校验,false-不校验
	 * @return
	 */
	Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);

	/**
	 * 根据条件查询日志记录
	 * @param ruleId 规则id
	 * @param ruleValue 规则值
	 * @return
	 */
	CodeRuleLog selectLogByValue(String ruleId, String businessId, String ruleValue, Long numberValue);

	CodeRuleLog docIdExist(String ruleValue);

	void initDocId();
}

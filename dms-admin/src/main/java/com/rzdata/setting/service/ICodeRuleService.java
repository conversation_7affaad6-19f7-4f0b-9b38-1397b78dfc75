package com.rzdata.setting.service;

import com.rzdata.setting.domain.CodeRule;
import com.rzdata.setting.domain.CodeRuleLog;
import com.rzdata.setting.domain.bo.CodeRuleDetailBo;
import com.rzdata.setting.domain.vo.CodeRuleDetailVo;
import com.rzdata.setting.domain.vo.CodeRuleVo;
import com.rzdata.setting.domain.bo.CodeRuleBo;
import com.rzdata.framework.core.mybatisplus.core.IServicePlus;
import com.rzdata.framework.core.page.TableDataInfo;

import java.rmi.ServerException;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 编号规则Service接口
 *
 * <AUTHOR>
 * @date 2021-12-27
 */
public interface ICodeRuleService extends IServicePlus<CodeRule, CodeRuleVo> {
	/**
	 * 查询单个
	 * @return
	 */
	CodeRuleVo queryById(String id);

	List<CodeRuleDetailVo> getInfoByDocClass(CodeRuleDetailBo bo) throws ServerException;

	/**
	 * 查询列表
	 */
    TableDataInfo<CodeRuleVo> queryPageList(CodeRuleBo bo);

	/**
	 * 查询列表
	 */
	List<CodeRuleVo> queryList(CodeRuleBo bo);

	/**
	 * 根据新增业务对象插入编号规则
	 * @param bo 编号规则新增业务对象
	 * @return
	 */
	String insertByBo(CodeRuleBo bo);

	/**
	 * 根据编辑业务对象修改编号规则
	 * @param bo 编号规则编辑业务对象
	 * @return
	 */
	Boolean updateByBo(CodeRuleBo bo);

	/**
	 * 校验并删除数据
	 * @param ids 主键集合
	 * @param isValid 是否校验,true-删除前校验,false-不校验
	 * @return
	 */
	Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);



	/**
	 * 判断编号是否存在
	 * @param busId 业务主键id
	 * @param newNo 编号
	 * @return
	 */
	Boolean checkNoIsExist(String busId,String newNo);

	/**
	 * 如果生成了编号，但没有使用的时候 删除日志记录
	 * @param
	 */
	void removeByRule(String busId, String newNo);



	/**
	 * 生产编号
	 * @param codeId 编号规则id
	 * @param bizId 业务主键
	 * @param bizMap 业务参数信息
	 * @return
	 */
	CodeRuleLog generatorEncodingRule(String codeId, String bizId, Map<String, Object> bizMap);

	void logSetFileType( CodeRule codeRule,CodeRuleLog codeRuleLog,Map<String, Object> bizMap);

	void getMaxSerialNumber(CodeRule codeRule,CodeRuleLog codeRuleLog);

	String getMapValue(Map<String, Object> map,String key);
}

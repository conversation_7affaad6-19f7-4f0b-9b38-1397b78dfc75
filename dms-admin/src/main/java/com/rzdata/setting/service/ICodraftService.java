package com.rzdata.setting.service;

import com.rzdata.setting.domain.Codraft;
import com.rzdata.setting.domain.vo.CodraftVo;
import com.rzdata.setting.domain.bo.CodraftBo;
import com.rzdata.framework.core.mybatisplus.core.IServicePlus;
import com.rzdata.framework.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 合稿管理Service接口
 *
 * <AUTHOR>
 * @date 2022-03-02
 */
public interface ICodraftService extends IServicePlus<Codraft, CodraftVo> {
	/**
	 * 查询单个
	 * @return
	 */
	CodraftVo queryById(Long id);

	/**
	 * 查询列表
	 */
    TableDataInfo<CodraftVo> queryPageList(CodraftBo bo);

	/**
	 * 查询列表
	 */
	List<CodraftVo> queryList(CodraftBo bo);

	/**
	 * 根据新增业务对象插入合稿管理
	 * @param bo 合稿管理新增业务对象
	 * @return
	 */
	Boolean insertByBo(CodraftBo bo);

	/**
	 * 根据编辑业务对象修改合稿管理
	 * @param bo 合稿管理编辑业务对象
	 * @return
	 */
	Boolean updateByBo(CodraftBo bo);

	/**
	 * 校验并删除数据
	 * @param ids 主键集合
	 * @param isValid 是否校验,true-删除前校验,false-不校验
	 * @return
	 */
	Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}

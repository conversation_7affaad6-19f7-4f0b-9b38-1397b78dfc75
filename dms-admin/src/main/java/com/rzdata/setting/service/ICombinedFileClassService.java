package com.rzdata.setting.service;

import com.rzdata.framework.core.mybatisplus.core.IServicePlus;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.setting.domain.CombinedFileClass;
import com.rzdata.setting.domain.bo.CombinedFileClassBo;
import com.rzdata.setting.domain.vo.CombinedFileClassVo;

import java.util.Collection;
import java.util.List;

/**
 * 合稿文件分类Service接口
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
public interface ICombinedFileClassService extends IServicePlus<CombinedFileClass, CombinedFileClassVo> {
	/**
	 * 查询单个
	 * @return
	 */
	CombinedFileClassVo queryById(String id);

	/**
	 * 查询列表
	 */
    TableDataInfo<CombinedFileClassVo> queryPageList(CombinedFileClassBo bo);

	/**
	 * 查询列表
	 */
	List<CombinedFileClassVo> queryList(CombinedFileClassBo bo);

	/**
	 * 根据新增业务对象插入合稿文件分类
	 * @param bo 合稿文件分类新增业务对象
	 * @return
	 */
	Boolean saveCombinedFileClass(CombinedFileClassBo bo);

	/**
	 * 根据编辑业务对象修改合稿文件分类
	 * @param bo 合稿文件分类编辑业务对象
	 * @return
	 */
	Boolean updateByBo(CombinedFileClassBo bo);

	/**
	 * 校验并删除数据
	 * @param ids 主键集合
	 * @param isValid 是否校验,true-删除前校验,false-不校验
	 * @return
	 */
	Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);

}

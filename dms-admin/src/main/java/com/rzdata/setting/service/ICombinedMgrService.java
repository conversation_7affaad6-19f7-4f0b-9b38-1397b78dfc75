package com.rzdata.setting.service;


import com.rzdata.framework.core.mybatisplus.core.IServicePlus;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.setting.domain.CombinedMgr;
import com.rzdata.setting.domain.bo.CombinedMgrBo;
import com.rzdata.setting.domain.vo.CombinedMgrVo;

import java.util.Collection;
import java.util.List;

/**
 * 合稿管理Service接口
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
public interface ICombinedMgrService extends IServicePlus<CombinedMgr, CombinedMgrVo> {
	/**
	 * 查询单个
	 * @return
	 */
	CombinedMgrVo queryById(String id);

	/**
	 * 查询列表
	 */
    TableDataInfo<CombinedMgrVo> queryPageList(CombinedMgrBo bo);

	/**
	 * 查询列表
	 */
	List<CombinedMgrVo> queryList(CombinedMgrBo bo);

	/**
	 * 根据新增业务对象插入合稿管理
	 * @param bo 合稿管理新增业务对象
	 * @return
	 */
	Boolean insertByBo(CombinedMgrBo bo);

	/**
	 * 根据编辑业务对象修改合稿管理
	 * @param bo 合稿管理编辑业务对象
	 * @return
	 */
	Boolean updateByBo(CombinedMgrBo bo);

	/**
	 * 校验并删除数据
	 * @param ids 主键集合
	 * @param isValid 是否校验,true-删除前校验,false-不校验
	 * @return
	 */
	Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);
}

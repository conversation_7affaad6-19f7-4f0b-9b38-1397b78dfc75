package com.rzdata.setting.service;

import com.rzdata.setting.domain.DistributeGroupDetail;
import com.rzdata.setting.domain.vo.DistributeGroupDetailVo;
import com.rzdata.setting.domain.bo.DistributeGroupDetailBo;
import com.rzdata.framework.core.mybatisplus.core.IServicePlus;
import com.rzdata.framework.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 预设分组人员详情Service接口
 *
 * <AUTHOR>
 * @date 2024-11-26
 */
public interface IDistributeGroupDetailService extends IServicePlus<DistributeGroupDetail, DistributeGroupDetailVo> {
	/**
	 * 查询单个
	 * @return
	 */
	DistributeGroupDetailVo queryById(String id);

	/**
	 * 查询列表
	 */
    TableDataInfo<DistributeGroupDetailVo> queryPageList(DistributeGroupDetailBo bo);

	/**
	 * 查询列表
	 */
	List<DistributeGroupDetailVo> queryList(DistributeGroupDetailBo bo);

	/**
	 * 根据新增业务对象插入预设分组人员详情
	 * @param bo 预设分组人员详情新增业务对象
	 * @return
	 */
	Boolean insertByBo(DistributeGroupDetailBo bo);

	/**
	 * 根据编辑业务对象修改预设分组人员详情
	 * @param bo 预设分组人员详情编辑业务对象
	 * @return
	 */
	Boolean updateByBo(DistributeGroupDetailBo bo);

	/**
	 * 校验并删除数据
	 * @param ids 主键集合
	 * @param isValid 是否校验,true-删除前校验,false-不校验
	 * @return
	 */
	Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);
}

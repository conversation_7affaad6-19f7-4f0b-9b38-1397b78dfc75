package com.rzdata.setting.service;

import com.rzdata.framework.core.mybatisplus.core.IServicePlus;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.setting.domain.DistributeGroup;
import com.rzdata.setting.domain.bo.DistributeGroupBo;
import com.rzdata.setting.domain.vo.DistributeGroupVo;

import java.util.Collection;
import java.util.List;

/**
 * 预设分组人员Service接口
 *
 * <AUTHOR>
 * @date 2024-11-26
 */
public interface IDistributeGroupService extends IServicePlus<DistributeGroup, DistributeGroupVo> {
	/**
	 * 查询单个
	 *
	 * @return
	 */
	DistributeGroupVo queryById(String id);

	/**
	 * 查询列表
	 */
	TableDataInfo<DistributeGroupVo> queryPageList(DistributeGroupBo bo);

	/**
	 * 查询列表
	 */
	List<DistributeGroupVo> queryList(DistributeGroupBo bo);

	/**
	 * 根据新增业务对象插入预设分组人员
	 *
	 * @param bo 预设分组人员新增业务对象
	 * @return
	 */
	Boolean insertByBo(DistributeGroupBo bo);

	/**
	 * 根据编辑业务对象修改预设分组人员
	 *
	 * @param bo 预设分组人员编辑业务对象
	 * @return
	 */
	Boolean updateByBo(DistributeGroupBo bo);

	/**
	 * 校验并删除数据
	 *
	 * @param ids     主键集合
	 * @param isValid 是否校验,true-删除前校验,false-不校验
	 * @return
	 */
	Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);

	List<DistributeGroupVo> listByDocClass(String docClass);

}

package com.rzdata.setting.service;

import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.setting.domain.DocClassFlowNode;
import com.rzdata.setting.domain.bo.DocClassFlowBo;
import com.rzdata.setting.domain.vo.DocClassFlowNodeVo;
import com.rzdata.setting.domain.bo.DocClassFlowNodeBo;
import com.rzdata.framework.core.mybatisplus.core.IServicePlus;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.setting.domain.vo.DocClassFlowVo;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 文件分类设置-流程节点设置Service接口
 *
 * <AUTHOR>
 * @date 2023-01-10
 */
public interface IDocClassFlowNodeService extends IServicePlus<DocClassFlowNode, DocClassFlowNodeVo> {
	/**
	 * 查询单个
	 * @return
	 */
	DocClassFlowNodeVo queryById(String id);

	/**
	 * 查询列表
	 */
    TableDataInfo<DocClassFlowNodeVo> queryPageList(DocClassFlowNodeBo bo);

	/**
	 * 查询列表
	 */
	List<DocClassFlowNodeVo> queryList(DocClassFlowNodeBo bo);

	/**
	 * 根据新增业务对象插入文件分类设置-流程节点设置
	 * @param bo 文件分类设置-流程节点设置新增业务对象
	 * @return
	 */
	Boolean insertByBo(DocClassFlowNodeBo bo);

	/**
	 * 根据编辑业务对象修改文件分类设置-流程节点设置
	 * @param bo 文件分类设置-流程节点设置编辑业务对象
	 * @return
	 */
	Boolean updateByBo(DocClassFlowNodeBo bo);

	/**
	 * 校验并删除数据
	 * @param ids 主键集合
	 * @param isValid 是否校验,true-删除前校验,false-不校验
	 * @return
	 */
	Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);

	void removeFlowNodeCascade(String flowId);
}

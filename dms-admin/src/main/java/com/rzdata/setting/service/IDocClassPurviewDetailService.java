package com.rzdata.setting.service;

import com.rzdata.setting.domain.DocClassPurviewDetail;
import com.rzdata.setting.domain.vo.DocClassPurviewDetailVo;
import com.rzdata.setting.domain.bo.DocClassPurviewDetailBo;
import com.rzdata.framework.core.mybatisplus.core.IServicePlus;
import com.rzdata.framework.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 文件类型权限详情Service接口
 *
 * <AUTHOR>
 * @date 2023-12-01
 */
public interface IDocClassPurviewDetailService extends IServicePlus<DocClassPurviewDetail, DocClassPurviewDetailVo> {
	/**
	 * 查询单个
	 * @return
	 */
	DocClassPurviewDetailVo queryById(String id);

	/**
	 * 查询列表
	 */
    TableDataInfo<DocClassPurviewDetailVo> queryPageList(DocClassPurviewDetailBo bo);

	/**
	 * 查询列表
	 */
	List<DocClassPurviewDetailVo> queryList(DocClassPurviewDetailBo bo);

	/**
	 * 根据新增业务对象插入文件类型权限详情
	 * @param bo 文件类型权限详情新增业务对象
	 * @return
	 */
	Boolean insertByBo(DocClassPurviewDetailBo bo);

	/**
	 * 根据编辑业务对象修改文件类型权限详情
	 * @param bo 文件类型权限详情编辑业务对象
	 * @return
	 */
	Boolean updateByBo(DocClassPurviewDetailBo bo);

	/**
	 * 校验并删除数据
	 * @param ids 主键集合
	 * @param isValid 是否校验,true-删除前校验,false-不校验
	 * @return
	 */
	Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);
}

package com.rzdata.setting.service;

import com.rzdata.framework.core.domain.entity.SysUser;
import com.rzdata.setting.domain.DocClassPurview;
import com.rzdata.setting.domain.vo.DocClassPurviewVo;
import com.rzdata.setting.domain.bo.DocClassPurviewBo;
import com.rzdata.framework.core.mybatisplus.core.IServicePlus;
import com.rzdata.framework.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 文件类型权限Service接口
 *
 * <AUTHOR>
 * @date 2023-12-01
 */
public interface IDocClassPurviewService extends IServicePlus<DocClassPurview, DocClassPurviewVo> {
	/**
	 * 查询单个
	 * @return
	 */
	DocClassPurviewVo queryById(String id);

	/**
	 * 查询列表
	 */
    TableDataInfo<DocClassPurviewVo> queryPageList(DocClassPurviewBo bo);

	/**
	 * 查询列表
	 */
	List<DocClassPurviewVo> queryList(DocClassPurviewBo bo);

	/**
	 * 根据新增业务对象插入文件类型权限
	 * @param bo 文件类型权限新增业务对象
	 * @return
	 */
	Boolean insertByBo(DocClassPurviewBo bo);

	/**
	 * 根据编辑业务对象修改文件类型权限
	 * @param bo 文件类型权限编辑业务对象
	 * @return
	 */
	Boolean updateByBo(DocClassPurviewBo bo);

	/**
	 * 校验并删除数据
	 * @param ids 主键集合
	 * @param isValid 是否校验,true-删除前校验,false-不校验
	 * @return
	 */
	Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);

	/**
	 * 获取文件类型的使用状态
	 * @param id 主键
	 * @return
	 */
	DocClassPurview getPurviewStatus(String id,String parentClassId);

	boolean getPurviewDetailStatus(String purviewId, SysUser user);
}

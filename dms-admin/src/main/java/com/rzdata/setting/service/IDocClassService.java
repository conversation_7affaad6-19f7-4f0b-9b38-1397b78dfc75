package com.rzdata.setting.service;

import cn.hutool.core.lang.tree.Tree;
import com.rzdata.process.domain.bo.VersionBo;
import com.rzdata.process.domain.vo.VersionExportVo;
import com.rzdata.setting.domain.DocClass;
import com.rzdata.setting.domain.vo.DocClassVo;
import com.rzdata.setting.domain.bo.DocClassBo;
import com.rzdata.framework.core.mybatisplus.core.IServicePlus;
import com.rzdata.framework.core.page.TableDataInfo;

import java.rmi.ServerException;
import java.util.Collection;
import java.util.List;

/**
 * 文件类型Service接口
 *
 * <AUTHOR>
 * @date 2021-12-27
 */
public interface IDocClassService extends IServicePlus<DocClass, DocClassVo> {
	/**
	 * 查询单个
	 * @return
	 */
	DocClassVo queryById(String id);

	/**
	 * 查询列表
	 */
    TableDataInfo<DocClassVo> queryPageList(DocClassBo bo);

	List<DocClassVo> queryChildList(String docClass);

	/**
	 * 构建前端所需要下拉树结构
	 */
	List<Tree<String>> buildTreeSelect(List<DocClassVo> docClassList);

	/**
	 * 查询列表
	 */
	List<DocClassVo> queryList(DocClassBo bo);

	/**
	 * 根据新增业务对象插入文件类型
	 * @param bo 文件类型新增业务对象
	 * @return
	 */
	String insertByBo(DocClassBo bo) throws ServerException;

	/**
	 * 根据编辑业务对象修改文件类型
	 * @param bo 文件类型编辑业务对象
	 * @return
	 */
	Boolean updateByBo(DocClassBo bo);

	/**
	 * 校验并删除数据
	 * @param ids 主键集合
	 * @param isValid 是否校验,true-删除前校验,false-不校验
	 * @return
	 */
	Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);

	/**
	 * 启/禁用 1：启用，0：禁用
	 * @param id
	 * @param status
	 */
	void revDisable(String id, String status);

	Integer validateZf(String versionId,String docId);

	/**
	 * 获取分类的全路径（正向的）
	 *
	 * @param classId
	 * @return
	 */
	String getClassFullPath(String classId);


	/**
	 * 获取该分类的文件类型（对应的顶级分类对象）
	 *
	 * @param classId
	 * @return
	 */
	DocClass getClassType(String classId);

	String getPDocClass(String docClass, String lv);

	List<String> getClassByParentId(String parentId);

	void initAncestors();

	/**
	 * 根据ID查找所有子级（基于ancestors字段）
	 * @param id 父级ID
	 * @return 所有子级的DocClass列表
	 */
	List<DocClassVo> findAllChildrenById(String id);

	/**
	 * 根据ID查找所有子级ID（基于ancestors字段）
	 * @param id 父级ID
	 * @return 所有子级的ID列表
	 */
	List<String> findAllChildrenIdsById(String id);
}

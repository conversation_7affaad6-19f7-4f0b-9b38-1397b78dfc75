package com.rzdata.setting.service;

import com.rzdata.setting.domain.DocClassSetting;
import com.rzdata.setting.domain.vo.DocClassSettingVo;
import com.rzdata.setting.domain.bo.DocClassSettingBo;
import com.rzdata.framework.core.mybatisplus.core.IServicePlus;
import com.rzdata.framework.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 文件类型设置Service接口
 *
 * <AUTHOR>
 * @date 2023-12-28
 */
public interface IDocClassSettingService extends IServicePlus<DocClassSetting, DocClassSettingVo> {
	/**
	 * 查询单个
	 * @return
	 */
	DocClassSettingVo queryById(String id);

	/**
	 * 查询列表
	 */
    TableDataInfo<DocClassSettingVo> queryPageList(DocClassSettingBo bo);

	DocClassSettingVo queryInfo(DocClassSettingBo bo);

	/**
	 * 查询列表
	 */
	List<DocClassSettingVo> queryList(DocClassSettingBo bo);

	/**
	 * 根据新增业务对象插入文件类型设置
	 * @param bo 文件类型设置新增业务对象
	 * @return
	 */
	Boolean insertByBo(DocClassSettingBo bo);

	/**
	 * 根据编辑业务对象修改文件类型设置
	 * @param bo 文件类型设置编辑业务对象
	 * @return
	 */
	String updateByBo(DocClassSettingBo bo);

	/**
	 * 校验并删除数据
	 * @param ids 主键集合
	 * @param isValid 是否校验,true-删除前校验,false-不校验
	 * @return
	 */
	Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);

	DocClassSetting getSettingStatus(String docClass, String type,String applyFlag);
}

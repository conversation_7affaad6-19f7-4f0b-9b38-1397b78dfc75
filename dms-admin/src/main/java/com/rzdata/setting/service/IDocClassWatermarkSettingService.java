package com.rzdata.setting.service;

import com.rzdata.framework.core.mybatisplus.core.IServicePlus;
import com.rzdata.setting.domain.DocClassWatermarkSetting;
import com.rzdata.setting.domain.DocClassWatermarkSettingDetail;
import com.rzdata.setting.domain.bo.DocClassWatermarkSettingBo;
import com.rzdata.setting.domain.vo.DocClassWatermarkSettingVo;

import java.rmi.ServerException;
import java.util.List;

/**
 * 文件水印设置Service接口
 *
 * <AUTHOR>
 * @date 2024-04-29
 */
public interface IDocClassWatermarkSettingService extends IServicePlus<DocClassWatermarkSetting, DocClassWatermarkSettingVo> {

    void initWatermarkSetting(String id) throws ServerException;

    List<DocClassWatermarkSettingVo> assemblyDetailData(List<DocClassWatermarkSettingVo> voList);

    void savaWatermarkSetting(List<DocClassWatermarkSettingBo> list);

    /**
     * 根据文件分类ID，类型，查询文件水印设置功能，如果没有则向上查找
     * @param docClassId
     * @param bizType
     * @return
     */
    DocClassWatermarkSetting getWatermarkSettingByDocClassAndType(String docClassId,String bizType, Boolean isFirst);

    List<DocClassWatermarkSettingDetail> getDocClassWatermarkSettingDetailList(String docClassId, String bizType, Boolean isFirst, String appliedRange);
}

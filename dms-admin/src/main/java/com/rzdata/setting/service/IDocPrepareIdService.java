package com.rzdata.setting.service;

import com.rzdata.framework.core.mybatisplus.core.IServicePlus;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.setting.domain.DocPrepareId;
import com.rzdata.setting.domain.bo.DocPrepareIdBo;
import com.rzdata.setting.domain.vo.DocPrepareIdVo;

import java.util.Collection;
import java.util.List;

/**
 * 预制编号Service接口
 *
 * <AUTHOR>
 * @date 2023-10-07
 */
public interface IDocPrepareIdService extends IServicePlus<DocPrepareId, DocPrepareIdVo> {
	/**
	 * 查询单个
	 * @return
	 */
	DocPrepareIdVo queryById(String docId);

	/**
	 * 查询列表
	 */
    TableDataInfo<DocPrepareIdVo> queryPageList(DocPrepareIdBo bo);

	/**
	 * 查询列表
	 */
	List<DocPrepareIdVo> queryList(DocPrepareIdBo bo);

	/**
	 * 根据新增业务对象插入预制编号
	 * @param bo 预制编号新增业务对象
	 * @return
	 */
	Boolean insertByBo(DocPrepareIdBo bo) throws Exception;

	/**
	 * 根据编辑业务对象修改预制编号
	 * @param bo 预制编号编辑业务对象
	 * @return
	 */
	Boolean updateByBo(DocPrepareIdBo bo);

	/**
	 * 校验并删除数据
	 * @param ids 主键集合
	 * @param isValid 是否校验,true-删除前校验,false-不校验
	 * @return
	 */
	Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);

	Boolean validatePrepareId(DocPrepareIdBo bo);
}

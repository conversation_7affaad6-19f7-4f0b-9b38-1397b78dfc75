package com.rzdata.setting.service;

import com.rzdata.framework.core.mybatisplus.core.IServicePlus;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.setting.domain.DocPresetUser;
import com.rzdata.setting.domain.bo.DocPresetUserBo;
import com.rzdata.setting.domain.vo.DocPresetUserVo;

import java.util.Collection;
import java.util.List;

/**
 * 预选环节用户Service接口
 *
 * <AUTHOR>
 * @date 2023-11-01
 */
public interface IDocPresetUserService extends IServicePlus<DocPresetUser, DocPresetUserVo> {
	/**
	 * 查询单个
	 * @return
	 */
	DocPresetUserVo queryById(String id);

	/**
	 * 查询列表
	 */
    TableDataInfo<DocPresetUserVo> queryPageList(DocPresetUserBo bo);

	/**
	 * 查询列表
	 */
	List<DocPresetUserVo> queryList(DocPresetUserBo bo);

	/**
	 * 根据新增业务对象插入预选环节用户
	 * @param bo 预选环节用户新增业务对象
	 * @return
	 */
	Boolean insertByBo(DocPresetUserBo bo);

	/**
	 * 根据编辑业务对象修改预选环节用户
	 * @param bo 预选环节用户编辑业务对象
	 * @return
	 */
	Boolean updateByBo(DocPresetUserBo bo);

	/**
	 * 校验并删除数据
	 * @param ids 主键集合
	 * @param isValid 是否校验,true-删除前校验,false-不校验
	 * @return
	 */
	Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);

	Boolean updatePresetUser(List<DocPresetUser> list,String bizId);
}

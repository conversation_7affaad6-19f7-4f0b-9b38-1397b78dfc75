package com.rzdata.setting.service;

import com.rzdata.setting.domain.Signature;
import com.rzdata.setting.domain.vo.SignatureVo;
import com.rzdata.setting.domain.bo.SignatureBo;
import com.rzdata.framework.core.mybatisplus.core.IServicePlus;
import com.rzdata.framework.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 签章管理Service接口
 *
 * <AUTHOR>
 * @date 2022-03-04
 */
public interface ISignatureService extends IServicePlus<Signature, SignatureVo> {
	/**
	 * 查询单个
	 * @return
	 */
	SignatureVo queryById(String id);

	/**
	 * 查询列表
	 */
    TableDataInfo<SignatureVo> queryPageList(SignatureBo bo);

	/**
	 * 查询列表
	 */
	List<SignatureVo> queryList(SignatureBo bo);

	/**
	 * 根据新增业务对象插入签章管理
	 * @param bo 签章管理新增业务对象
	 * @return
	 */
	Boolean insertByBo(SignatureBo bo);

	/**
	 * 根据编辑业务对象修改签章管理
	 * @param bo 签章管理编辑业务对象
	 * @return
	 */
	Boolean updateByBo(SignatureBo bo);

	/**
	 * 校验并删除数据
	 * @param ids 主键集合
	 * @param isValid 是否校验,true-删除前校验,false-不校验
	 * @return
	 */
	Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);
}

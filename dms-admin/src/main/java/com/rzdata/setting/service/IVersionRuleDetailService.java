package com.rzdata.setting.service;

import com.rzdata.setting.domain.VersionRuleDetail;
import com.rzdata.setting.domain.vo.VersionRuleDetailVo;
import com.rzdata.setting.domain.bo.VersionRuleDetailBo;
import com.rzdata.framework.core.mybatisplus.core.IServicePlus;
import com.rzdata.framework.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 版本规则明细Service接口
 *
 * <AUTHOR>
 * @date 2023-12-27
 */
public interface IVersionRuleDetailService extends IServicePlus<VersionRuleDetail, VersionRuleDetailVo> {
	/**
	 * 查询单个
	 * @return
	 */
	VersionRuleDetailVo queryById(String id);

	/**
	 * 查询列表
	 */
    TableDataInfo<VersionRuleDetailVo> queryPageList(VersionRuleDetailBo bo);

	/**
	 * 查询列表
	 */
	List<VersionRuleDetailVo> queryList(VersionRuleDetailBo bo);

	/**
	 * 根据新增业务对象插入版本规则明细
	 * @param bo 版本规则明细新增业务对象
	 * @return
	 */
	Boolean insertByBo(VersionRuleDetailBo bo);

	/**
	 * 根据编辑业务对象修改版本规则明细
	 * @param bo 版本规则明细编辑业务对象
	 * @return
	 */
	Boolean updateByBo(VersionRuleDetailBo bo);

	/**
	 * 校验并删除数据
	 * @param ids 主键集合
	 * @param isValid 是否校验,true-删除前校验,false-不校验
	 * @return
	 */
	Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);

	String getNextVersion(String version,String docClass);
}

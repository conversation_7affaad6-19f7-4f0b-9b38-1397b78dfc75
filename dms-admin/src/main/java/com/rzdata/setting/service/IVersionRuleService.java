package com.rzdata.setting.service;

import com.rzdata.setting.domain.VersionRule;
import com.rzdata.setting.domain.vo.VersionRuleVo;
import com.rzdata.setting.domain.bo.VersionRuleBo;
import com.rzdata.framework.core.mybatisplus.core.IServicePlus;
import com.rzdata.framework.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 版本规则Service接口
 *
 * <AUTHOR>
 * @date 2023-12-27
 */
public interface IVersionRuleService extends IServicePlus<VersionRule, VersionRuleVo> {
	/**
	 * 查询单个
	 * @return
	 */
	VersionRuleVo queryById(String id);

	/**
	 * 查询列表
	 */
    TableDataInfo<VersionRuleVo> queryPageList(VersionRuleBo bo);

	/**
	 * 查询列表
	 */
	List<VersionRuleVo> queryList(VersionRuleBo bo);

	/**
	 * 根据新增业务对象插入版本规则
	 * @param bo 版本规则新增业务对象
	 * @return
	 */
	Boolean insertByBo(VersionRuleBo bo);

	/**
	 * 根据编辑业务对象修改版本规则
	 * @param bo 版本规则编辑业务对象
	 * @return
	 */
	Boolean updateByBo(VersionRuleBo bo);

	/**
	 * 校验并删除数据
	 * @param ids 主键集合
	 * @param isValid 是否校验,true-删除前校验,false-不校验
	 * @return
	 */
	Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);
}

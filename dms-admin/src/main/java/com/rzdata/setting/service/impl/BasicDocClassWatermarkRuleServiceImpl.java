package com.rzdata.setting.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.rzdata.framework.exception.ServiceException;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.utils.SecurityUtils;
import com.rzdata.framework.utils.StringUtils;
import com.rzdata.setting.domain.BasicDocClassWatermarkRule;
import com.rzdata.setting.domain.DocClass;
import com.rzdata.setting.domain.DocClassWatermarkSetting;
import com.rzdata.setting.domain.DocClassWatermarkSettingDetail;
import com.rzdata.setting.domain.bo.BasicDocClassWatermarkRuleBo;
import com.rzdata.setting.domain.bo.DocClassWatermarkSettingBo;
import com.rzdata.setting.domain.vo.BasicDocClassWatermarkRuleVo;
import com.rzdata.setting.domain.vo.DocClassWatermarkSettingVo;
import com.rzdata.setting.mapper.BasicDocClassWatermarkRuleMapper;
import com.rzdata.setting.mapper.DocClassWatermarkSettingMapper;
import com.rzdata.setting.service.IBasicDocClassWatermarkRuleService;
import com.rzdata.setting.service.IDocClassService;
import com.rzdata.setting.service.IDocClassWatermarkSettingService;
import com.rzdata.system.service.ISysUserService;
import com.rzdata.setting.service.IDocClassWatermarkSettingDetailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONException;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 文档水印规则Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-01-22
 */
@Service
public class BasicDocClassWatermarkRuleServiceImpl
        extends ServiceImpl<BasicDocClassWatermarkRuleMapper, BasicDocClassWatermarkRule>
        implements IBasicDocClassWatermarkRuleService {

    @Autowired
    private IDocClassWatermarkSettingService docClassWatermarkSettingService;

    @Autowired
    private IDocClassWatermarkSettingDetailService docClassWatermarkSettingDetailService;

    @Autowired
    private IDocClassService iDocClassService;

    @Autowired
    private DocClassWatermarkSettingMapper docClassWatermarkSettingMapper;

    @Autowired
    private ISysUserService userService;

    @Override
    public List<DocClassWatermarkSettingVo> checkRuleUsage(String id) {
        // 查询使用该规则的所有文件类型设置
        LambdaQueryWrapper<DocClassWatermarkSetting> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DocClassWatermarkSetting::getRuleId, id);
        List<DocClassWatermarkSettingVo> settings = docClassWatermarkSettingService.listVo(wrapper);
        // 查询文件类型列表并转换成以id为key的map
        List<DocClass> docClassList = iDocClassService.list();
        Map<String, DocClass> docClassMap = docClassList.stream()
                .collect(Collectors.toMap(DocClass::getId, Function.identity()));

        // 转换为VO对象
        return settings.stream()
                .map(setting -> {
                    DocClass docClass = docClassMap.get(setting.getDocClass());
                    if(docClass!=null){
                        setting.setClassName(docClass.getClassName());
                    }
                    return setting;
                })
                .collect(Collectors.toList());
    }

    @Override
    public BasicDocClassWatermarkRuleVo queryById(String id) {
        BasicDocClassWatermarkRule entity = getById(id);
        return entity != null ? BeanUtil.toBean(entity, BasicDocClassWatermarkRuleVo.class) : null;
    }

    @Override
    public TableDataInfo<BasicDocClassWatermarkRuleVo> queryPageList(BasicDocClassWatermarkRuleBo bo) {
        Page<BasicDocClassWatermarkRule> page = new Page<>(bo.getPageNum(), bo.getPageSize());
        LambdaQueryWrapper<BasicDocClassWatermarkRule> lqw = buildQueryWrapper(bo);
        Page<BasicDocClassWatermarkRule> result = page(page, lqw);
        
        // 获取所有创建人ID
        List<String> createByIds = result.getRecords().stream()
            .map(BasicDocClassWatermarkRule::getCreateBy)
            .filter(StringUtils::isNotEmpty)  // 添加非空过滤
            .collect(Collectors.toList());
        
        // 查询用户信息
        Map<String, String> userMap = userService.selectUserMapByUserNames(createByIds);
        
        // 转换并设置创建人昵称
        List<BasicDocClassWatermarkRuleVo> voList = result.getRecords().stream()
            .map(entity -> {
                if (entity == null) {
                    return null;
                }
                BasicDocClassWatermarkRuleVo vo = new BasicDocClassWatermarkRuleVo();  // 手动创建对象
                BeanUtil.copyProperties(entity, vo);  // 复制属性
                vo.setCreateByName(userMap.getOrDefault(entity.getCreateBy(), entity.getCreateBy()));
                return vo;
            })
            .filter(vo -> vo != null)  // 过滤掉null值
            .collect(Collectors.toList());

        return new TableDataInfo<>(voList, result.getTotal());
    }

    @Override
    public List<BasicDocClassWatermarkRuleVo> queryList(BasicDocClassWatermarkRuleBo bo) {
        LambdaQueryWrapper<BasicDocClassWatermarkRule> lqw = buildQueryWrapper(bo);
        return list(lqw).stream()
                .map(entity -> BeanUtil.toBean(entity, BasicDocClassWatermarkRuleVo.class))
                .collect(Collectors.toList());
    }

    private LambdaQueryWrapper<BasicDocClassWatermarkRule> buildQueryWrapper(BasicDocClassWatermarkRuleBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<BasicDocClassWatermarkRule> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getRuleName()), BasicDocClassWatermarkRule::getRuleName, bo.getRuleName());
        // 按序号排序
        lqw.orderByAsc(BasicDocClassWatermarkRule::getSort);
        return lqw;
    }

    @Override
    public Boolean insertByBo(BasicDocClassWatermarkRuleBo bo) {
        BasicDocClassWatermarkRule add = BeanUtil.toBean(bo, BasicDocClassWatermarkRule.class);
        // 生成32位UUID作为ID
        add.setId(IdUtil.simpleUUID());
        validEntityBeforeSave(add);
        return save(add);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateByBo(BasicDocClassWatermarkRuleBo bo) {
        BasicDocClassWatermarkRule update = BeanUtil.toBean(bo, BasicDocClassWatermarkRule.class);
        validEntityBeforeSave(update);
        boolean result = updateById(update);

        // 如果需要更新所有使用该规则的配置
        if (bo.getUpdateAll() != null && bo.getUpdateAll() && StringUtils.isNotEmpty(update.getRuleDetails())) {
            try {
                // 查询使用该规则的所有文件类型设置
                LambdaQueryWrapper<DocClassWatermarkSetting> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(DocClassWatermarkSetting::getRuleId, update.getId());
                List<DocClassWatermarkSetting> settings = docClassWatermarkSettingService.list(wrapper);

                if (!settings.isEmpty()) {
                    // 解析规则详情
                    List<DocClassWatermarkSettingDetail> templateDetails;
                    try {
                        templateDetails = JSON.parseArray(update.getRuleDetails(),
                                DocClassWatermarkSettingDetail.class);
                    } catch (JSONException e) {
                        log.error("解析水印规则详情失败", e);
                        return false;
                    }

                    if (templateDetails != null && !templateDetails.isEmpty()) {
                        // 批量删除原有的规则详情
                        List<String> settingIds = settings.stream()
                                .map(DocClassWatermarkSetting::getId)
                                .collect(Collectors.toList());
                        LambdaQueryWrapper<DocClassWatermarkSettingDetail> detailWrapper = new LambdaQueryWrapper<>();
                        detailWrapper.in(DocClassWatermarkSettingDetail::getBizId, settingIds);
                        docClassWatermarkSettingDetailService.remove(detailWrapper);

                        // 准备批量插入的新规则详情
                        List<DocClassWatermarkSettingDetail> newDetails = new ArrayList<>();
                        for (DocClassWatermarkSetting setting : settings) {
                            for (DocClassWatermarkSettingDetail template : templateDetails) {
                                DocClassWatermarkSettingDetail detail = new DocClassWatermarkSettingDetail();
                                BeanUtil.copyProperties(template, detail);
                                detail.setId(IdUtil.simpleUUID());
                                detail.setBizId(setting.getId());
                                newDetails.add(detail);
                            }
                        }

                        // 批量保存新的规则详情
                        docClassWatermarkSettingDetailService.saveBatch(newDetails);
                    }
                }
            } catch (Exception e) {
                log.error("更新水印规则详情失败", e);
                throw e;
            }
        }

        return result;
    }

    /**
     * 保存前的数据校验
     *
     * @param entity 实体类数据
     */
    private void validEntityBeforeSave(BasicDocClassWatermarkRule entity) {
        // TODO 做一些数据校验,如唯一约束
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        if (isValid) {
            // TODO 做一些业务上的校验,判断是否允许删除
        }
        return removeByIds(ids);
    }

    /**
     * 移除水印规则应用
     * 
     * @param bo 文件水印设置业务对象
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int removeUsage(DocClassWatermarkSettingBo bo) {
        // 删除明细
        LambdaQueryWrapper<DocClassWatermarkSettingDetail> detailWrapper = new LambdaQueryWrapper<>();
                        detailWrapper.eq(DocClassWatermarkSettingDetail::getBizId, bo.getId());
        docClassWatermarkSettingDetailService.remove(detailWrapper);


        // 3. 删除水印规则应用记录
        return docClassWatermarkSettingMapper.deleteById(bo.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean addUsage(DocClassWatermarkSettingBo bo) {
        // 1. 获取水印规则
        BasicDocClassWatermarkRule rule = getById(bo.getRuleId());
        if (rule == null) {
            throw new ServiceException("水印规则不存在",500);
        }

        // 2. 检查是否已存在相同的应用
        LambdaQueryWrapper<DocClassWatermarkSetting> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DocClassWatermarkSetting::getDocClass, bo.getDocClass())
               .eq(DocClassWatermarkSetting::getBizType, bo.getBizType())
               .eq(DocClassWatermarkSetting::getRuleId, bo.getRuleId());
        if (docClassWatermarkSettingService.count(wrapper) > 0) {
            throw new ServiceException("该文件类型的业务类型已配置水印规则",500);
        }

        // 3. 创建水印设置
        DocClassWatermarkSetting setting = new DocClassWatermarkSetting();
        setting.setDocClass(bo.getDocClass());
        setting.setBizType(bo.getBizType());
        setting.setOpenFlag(bo.getOpenFlag());
        setting.setApplyFlag(bo.getApplyFlag());
        setting.setRuleId(bo.getRuleId());
        setting.setCreateTime(new Date());
        setting.setCreateBy(SecurityUtils.getUsername());
        boolean result = docClassWatermarkSettingService.save(setting);

        if (result) {
            // 4. 复制规则详情
            List<DocClassWatermarkSettingDetail> details = JSON.parseArray(rule.getRuleDetails(), DocClassWatermarkSettingDetail.class);
            if (details != null && !details.isEmpty()) {
                for (DocClassWatermarkSettingDetail detail : details) {
                    detail.setId(IdUtil.simpleUUID());
                    detail.setBizId(setting.getId());
                }
                docClassWatermarkSettingDetailService.saveBatch(details);
            }
        }

        return result;
    }
}
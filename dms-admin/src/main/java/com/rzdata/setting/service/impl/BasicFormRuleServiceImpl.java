package com.rzdata.setting.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.rzdata.framework.constant.Constants;
import com.rzdata.framework.utils.PageUtils;
import com.rzdata.framework.core.page.PagePlus;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.utils.StringUtils;
import com.rzdata.setting.domain.DocClassSetting;
import com.rzdata.setting.service.IDocClassSettingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.rzdata.framework.core.mybatisplus.core.ServicePlusImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.rzdata.setting.domain.bo.BasicFormRuleBo;
import com.rzdata.setting.domain.vo.BasicFormRuleVo;
import com.rzdata.setting.domain.BasicFormRule;
import com.rzdata.setting.mapper.BasicFormRuleMapper;
import com.rzdata.setting.service.IBasicFormRuleService;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 单规则Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-09-20
 */
@Service
public class BasicFormRuleServiceImpl extends ServicePlusImpl<BasicFormRuleMapper, BasicFormRule, BasicFormRuleVo> implements IBasicFormRuleService {

    @Autowired
    private IDocClassSettingService iDocClassSettingService;

    @Override
    public BasicFormRuleVo queryById(Long id){
        return getVoById(id);
    }

    @Override
    public TableDataInfo<BasicFormRuleVo> queryPageList(BasicFormRuleBo bo) {
        PagePlus<BasicFormRule, BasicFormRuleVo> result = pageVo(PageUtils.buildPagePlus(), buildQueryWrapper(bo));
        return PageUtils.buildDataInfo(result);
    }

    @Override
    public List<BasicFormRuleVo> queryList(BasicFormRuleBo bo) {
        return listVo(buildQueryWrapper(bo));
    }

    private LambdaQueryWrapper<BasicFormRule> buildQueryWrapper(BasicFormRuleBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<BasicFormRule> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getId() != null, BasicFormRule::getId, bo.getId());
        lqw.like(StringUtils.isNotBlank(bo.getRuleName()), BasicFormRule::getRuleName, bo.getRuleName());
        lqw.eq(StringUtils.isNotBlank(bo.getRuleDetails()), BasicFormRule::getRuleDetails, bo.getRuleDetails());
        return lqw;
    }

    @Override
    public Boolean insertByBo(BasicFormRuleBo bo) {
        BasicFormRule add = BeanUtil.toBean(bo, BasicFormRule.class);
        validEntityBeforeSave(add);
        boolean flag = save(add);
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    @Override
    public Boolean updateByBo(BasicFormRuleBo bo) {
        BasicFormRule update = BeanUtil.toBean(bo, BasicFormRule.class);
        validEntityBeforeSave(update);
        return updateById(update);
    }

    /**
     * 保存前的数据校验
     *
     * @param entity 实体类数据
     */
    private void validEntityBeforeSave(BasicFormRule entity){
        //TODO 做一些数据校验,如唯一约束
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return removeByIds(ids);
    }

    @Override
    public BasicFormRuleVo getFormRuleByRecursive(String docClass) {
        //根据文件类型获取配置
        DocClassSetting setting= iDocClassSettingService.getSettingStatus(docClass,"formShow",null);
        if(setting==null){
            return null;
        }
        return getVoById(setting.getRuleId());
    }
    @Override
    public List<String> getHiddenColumns(String docClass){
        // 表单规则
        // 读取json 字符串rules ,取show=N 的字段列表
        List<String> excludeFieldList = new ArrayList<>();
        BasicFormRuleVo basicFormRuleVo = this.getFormRuleByRecursive(docClass);
        if(basicFormRuleVo!=null) {
            String rules = basicFormRuleVo.getRuleDetails();

            if (!StrUtil.isEmpty(rules)) {
                JSONArray jsonArray = JSONUtil.parseArray(rules);
                for (Object o : jsonArray) {
                    Map<String, Object> map = (Map<String, Object>) o;
                    if (Constants.VALUE_N.equalsIgnoreCase((String) map.get("show"))) {
                        excludeFieldList.add(map.get("value").toString());
                    }
                }
            }
        }
        return excludeFieldList;
    }
}

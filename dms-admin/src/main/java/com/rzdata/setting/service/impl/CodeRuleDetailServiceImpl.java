package com.rzdata.setting.service.impl;

import cn.hutool.core.bean.BeanUtil;
    import com.rzdata.framework.utils.PageUtils;
import com.rzdata.framework.core.page.PagePlus;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.utils.StringUtils;
import org.springframework.stereotype.Service;
import com.rzdata.framework.core.mybatisplus.core.ServicePlusImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.rzdata.setting.domain.bo.CodeRuleDetailBo;
import com.rzdata.setting.domain.vo.CodeRuleDetailVo;
import com.rzdata.setting.domain.CodeRuleDetail;
import com.rzdata.setting.mapper.CodeRuleDetailMapper;
import com.rzdata.setting.service.ICodeRuleDetailService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 编号规则明细Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-12-27
 */
@Service
public class CodeRuleDetailServiceImpl extends ServicePlusImpl<CodeRuleDetailMapper, CodeRuleDetail, CodeRuleDetailVo> implements ICodeRuleDetailService {

    @Override
    public CodeRuleDetailVo queryById(String id){
        return getVoById(id);
    }

    @Override
    public TableDataInfo<CodeRuleDetailVo> queryPageList(CodeRuleDetailBo bo) {
        PagePlus<CodeRuleDetail, CodeRuleDetailVo> result = pageVo(PageUtils.buildPagePlus(), buildQueryWrapper(bo));
        return PageUtils.buildDataInfo(result);
    }

    @Override
    public List<CodeRuleDetailVo> queryList(CodeRuleDetailBo bo) {
        return listVo(buildQueryWrapper(bo));
    }

    private LambdaQueryWrapper<CodeRuleDetail> buildQueryWrapper(CodeRuleDetailBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<CodeRuleDetail> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getRuleId()), CodeRuleDetail::getRuleId, bo.getRuleId());
        lqw.eq(StringUtils.isNotBlank(bo.getRuleType()), CodeRuleDetail::getRuleType, bo.getRuleType());
        lqw.eq(StringUtils.isNotBlank(bo.getRuleValue()), CodeRuleDetail::getRuleValue, bo.getRuleValue());
        lqw.eq(bo.getOrderBy() != null, CodeRuleDetail::getOrderBy, bo.getOrderBy());
        lqw.in(bo.getRuleTypeList() != null && bo.getRuleTypeList().size()>0, CodeRuleDetail::getRuleType, bo.getRuleTypeList());
        return lqw;
    }

    @Override
    public Boolean insertByBo(CodeRuleDetailBo bo) {
        CodeRuleDetail add = BeanUtil.toBean(bo, CodeRuleDetail.class);
        validEntityBeforeSave(add);
        boolean flag = save(add);
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    @Override
    public Boolean updateByBo(CodeRuleDetailBo bo) {
        CodeRuleDetail update = BeanUtil.toBean(bo, CodeRuleDetail.class);
        validEntityBeforeSave(update);
        return updateById(update);
    }

    /**
     * 保存前的数据校验
     *
     * @param entity 实体类数据
     */
    private void validEntityBeforeSave(CodeRuleDetail entity){
        //TODO 做一些数据校验,如唯一约束
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return removeByIds(ids);
    }
}

package com.rzdata.setting.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.rzdata.framework.constant.Constants;
import com.rzdata.framework.utils.PageUtils;
import com.rzdata.framework.core.page.PagePlus;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.utils.StringUtils;
import com.rzdata.process.domain.DocLinkLog;
import com.rzdata.process.domain.ModifyApplyLink;
import com.rzdata.process.domain.Version;
import com.rzdata.process.enums.LinkTypeEnum;
import com.rzdata.process.mapper.DocLinkLogMapper;
import com.rzdata.process.mapper.VersionMapper;
import com.rzdata.process.service.IModifyApplyLinkService;
import com.rzdata.setting.domain.DocPrepareId;
import com.rzdata.setting.mapper.DocPrepareIdMapper;
import com.rzdata.setting.service.IDocPrepareIdService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.rzdata.framework.core.mybatisplus.core.ServicePlusImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.rzdata.setting.domain.bo.CodeRuleLogBo;
import com.rzdata.setting.domain.vo.CodeRuleLogVo;
import com.rzdata.setting.domain.CodeRuleLog;
import com.rzdata.setting.mapper.CodeRuleLogMapper;
import com.rzdata.setting.service.ICodeRuleLogService;

import javax.annotation.Resource;
import java.util.*;

/**
 * 编号规则日志Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-12-27
 */
@Service
public class CodeRuleLogServiceImpl extends ServicePlusImpl<CodeRuleLogMapper, CodeRuleLog, CodeRuleLogVo> implements ICodeRuleLogService {

    @Resource
    private DocPrepareIdMapper docPrepareIdMapper;

    @Autowired
    private VersionMapper versionMapper;
    @Autowired
    private IModifyApplyLinkService modifyApplyLinkService;
    @Resource
    private DocLinkLogMapper docLinkLogMapper;

    @Override
    public CodeRuleLogVo queryById(String id){
        return getVoById(id);
    }

    @Override
    public TableDataInfo<CodeRuleLogVo> queryPageList(CodeRuleLogBo bo) {
        PagePlus<CodeRuleLog, CodeRuleLogVo> result = pageVo(PageUtils.buildPagePlus(), buildQueryWrapper(bo));
        return PageUtils.buildDataInfo(result);
    }

    @Override
    public List<CodeRuleLogVo> queryList(CodeRuleLogBo bo) {
        return listVo(buildQueryWrapper(bo));
    }

    private LambdaQueryWrapper<CodeRuleLog> buildQueryWrapper(CodeRuleLogBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<CodeRuleLog> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getRuleId()), CodeRuleLog::getRuleId, bo.getRuleId());
        lqw.eq(bo.getNumberValue() != null, CodeRuleLog::getNumberValue, bo.getNumberValue());
        lqw.eq(StringUtils.isNotBlank(bo.getRuleValue()), CodeRuleLog::getRuleValue, bo.getRuleValue());
        lqw.eq(StringUtils.isNotBlank(bo.getBusinessId()), CodeRuleLog::getBusinessId, bo.getBusinessId());
        return lqw;
    }

    @Override
    public Boolean insertByBo(CodeRuleLogBo bo) {
        CodeRuleLog add = BeanUtil.toBean(bo, CodeRuleLog.class);
        validEntityBeforeSave(add);
        boolean flag = save(add);
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    @Override
    public Boolean updateByBo(CodeRuleLogBo bo) {
        CodeRuleLog update = BeanUtil.toBean(bo, CodeRuleLog.class);
        validEntityBeforeSave(update);
        return updateById(update);
    }

    /**
     * 保存前的数据校验
     *
     * @param entity 实体类数据
     */
    private void validEntityBeforeSave(CodeRuleLog entity){
        //TODO 做一些数据校验,如唯一约束
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return removeByIds(ids);
    }

    @Override
    public CodeRuleLog selectLogByValue(String ruleId, String businessId, String ruleValue, Long numberValue) {
        return this.baseMapper.selectLogByValue(ruleId, businessId, ruleValue, numberValue);
    }

    @Override
    public CodeRuleLog docIdExist(String ruleValue) {
        return getOne(new LambdaQueryWrapper<CodeRuleLog>().eq(CodeRuleLog::getRuleValue,ruleValue).last("limit 1"));
    }

    @Override
    public void updateStatus(String bizId,String docId){
        if (docPrepareIdMapper.selectCount(new LambdaQueryWrapper<DocPrepareId>().eq(DocPrepareId::getDocId,docId))==0){
            // 在预制表里没有就删除
            remove(new LambdaQueryWrapper<CodeRuleLog>().eq(CodeRuleLog::getRuleValue,docId));
        }else {
            // 在预制表里有就改预制表状态
            updateUseStatus(null,bizId,docId);
        }
    }

    @Override
    public void updateLogBizId(String docId, String bizId,String oldDocId) {
        if (oldDocId!=null&&!oldDocId.equals(docId)) {
            //旧预制编号 改回原来的样子
            update(new LambdaUpdateWrapper<CodeRuleLog>().eq(CodeRuleLog::getRuleValue,oldDocId).set(CodeRuleLog::getBusinessId,oldDocId));
        }
        if (StringUtils.isNotEmpty(docId)) {
            //预制编号的业务id是编号本身，此时同步修改业务id
            update(new LambdaUpdateWrapper<CodeRuleLog>().eq(CodeRuleLog::getRuleValue,docId).set(CodeRuleLog::getBusinessId,bizId));
        }
    }

    @Override
    public void cancelDocId(String applyId,String docId) {
        //主文件
        if (StringUtils.isNotEmpty(docId)) {
            //主文件文件编号在doc_version表里没有就可以回收编号
            if(versionMapper.selectCount(new LambdaQueryWrapper<Version>().eq(Version::getDocId,docId))==0){
                updateStatus(applyId,docId);
            }
        }
        //记录文件
        List<ModifyApplyLink> recordList = modifyApplyLinkService.list(new LambdaQueryWrapper<ModifyApplyLink>()
                .eq(ModifyApplyLink::getApplyId,applyId)
                .eq(ModifyApplyLink::getLinkType, LinkTypeEnum.RECORD.name())
                .isNotNull(ModifyApplyLink::getDocId));
        for (ModifyApplyLink record : recordList) {
            //记录文件文件编号在doc_link_log表里没有就可以回收编号
            if (StringUtils.isNotEmpty(record.getDocId())) {
                if(docLinkLogMapper.selectCount(new LambdaQueryWrapper<DocLinkLog>().eq(DocLinkLog::getLinkCode,record.getDocId()))==0){
                    updateStatus(applyId,record.getDocId());
                }
            }
        }
    }

    @Override
    public void updateUseStatus(String docId, String bizId,String oldDocId) {
        if (StringUtils.isNotEmpty(oldDocId)&&!oldDocId.equals(docId)) {
            //旧预制编号 改成未使用
            docPrepareIdMapper.update(null,new LambdaUpdateWrapper<DocPrepareId>().eq(DocPrepareId::getDocId,oldDocId).set(DocPrepareId::getUseStatus, Constants.ZERO).set(DocPrepareId::getUseTime,null));
        }
        if (StringUtils.isNotEmpty(docId)) {
            //新预制编号 改成已使用
            docPrepareIdMapper.update(null,new LambdaUpdateWrapper<DocPrepareId>().eq(DocPrepareId::getDocId,docId).set(DocPrepareId::getUseStatus,Constants.ONE).set(DocPrepareId::getUseTime, DateUtil.date()));
        }
        //预制编号的业务id是编号本身，此时同步修改业务id
        updateLogBizId(docId,bizId,oldDocId);
    }
}

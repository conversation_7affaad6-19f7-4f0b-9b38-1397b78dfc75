package com.rzdata.setting.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.rzdata.framework.constant.Constants;
import com.rzdata.framework.utils.PageUtils;
import com.rzdata.framework.core.page.PagePlus;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.utils.StringUtils;
import com.rzdata.process.domain.ModifyApply;
import com.rzdata.process.domain.Standard;
import com.rzdata.process.domain.Version;
import com.rzdata.process.domain.dto.DocIdNumDTO;
import com.rzdata.process.enums.ApplyTypeEnum;
import com.rzdata.process.enums.LinkTypeEnum;
import com.rzdata.process.service.IGenerateIdService;
import com.rzdata.process.service.IModifyApplyService;
import com.rzdata.process.service.IStandardService;
import com.rzdata.process.service.IVersionService;
import com.rzdata.setting.service.IDocClassService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.rzdata.framework.core.mybatisplus.core.ServicePlusImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.rzdata.setting.domain.bo.CodeRuleLogBo;
import com.rzdata.setting.domain.vo.CodeRuleLogVo;
import com.rzdata.setting.domain.CodeRuleLog;
import com.rzdata.setting.mapper.CodeRuleLogMapper;
import com.rzdata.setting.service.ICodeRuleLogService;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * 编号规则日志Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-12-27
 */
@Service
public class CodeRuleLogServiceImpl extends ServicePlusImpl<CodeRuleLogMapper, CodeRuleLog, CodeRuleLogVo> implements ICodeRuleLogService {

    @Autowired
    private IDocClassService iDocClassService;

    @Autowired
    private IVersionService iVersionService;

    @Autowired
    private IStandardService iStandardService;

    @Autowired
    private IModifyApplyService iModifyApplyService;

    @Autowired
    private IGenerateIdService iGenerateIdService;

    @Override
    public CodeRuleLogVo queryById(String id){
        return getVoById(id);
    }

    @Override
    public TableDataInfo<CodeRuleLogVo> queryPageList(CodeRuleLogBo bo) {
        PagePlus<CodeRuleLog, CodeRuleLogVo> result = pageVo(PageUtils.buildPagePlus(), buildQueryWrapper(bo));
        return PageUtils.buildDataInfo(result);
    }

    @Override
    public List<CodeRuleLogVo> queryList(CodeRuleLogBo bo) {
        return listVo(buildQueryWrapper(bo));
    }

    private LambdaQueryWrapper<CodeRuleLog> buildQueryWrapper(CodeRuleLogBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<CodeRuleLog> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getRuleId()), CodeRuleLog::getRuleId, bo.getRuleId());
        lqw.eq(bo.getNumberValue() != null, CodeRuleLog::getNumberValue, bo.getNumberValue());
        lqw.eq(StringUtils.isNotBlank(bo.getRuleValue()), CodeRuleLog::getRuleValue, bo.getRuleValue());
        lqw.eq(StringUtils.isNotBlank(bo.getBusinessId()), CodeRuleLog::getBusinessId, bo.getBusinessId());
        return lqw;
    }

    @Override
    public Boolean insertByBo(CodeRuleLogBo bo) {
        CodeRuleLog add = BeanUtil.toBean(bo, CodeRuleLog.class);
        validEntityBeforeSave(add);
        boolean flag = save(add);
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    @Override
    public Boolean updateByBo(CodeRuleLogBo bo) {
        CodeRuleLog update = BeanUtil.toBean(bo, CodeRuleLog.class);
        validEntityBeforeSave(update);
        return updateById(update);
    }

    /**
     * 保存前的数据校验
     *
     * @param entity 实体类数据
     */
    private void validEntityBeforeSave(CodeRuleLog entity){
        //TODO 做一些数据校验,如唯一约束
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return removeByIds(ids);
    }

    @Override
    public CodeRuleLog selectLogByValue(String ruleId, String businessId, String ruleValue, Long numberValue) {
        return this.baseMapper.selectLogByValue(ruleId, businessId, ruleValue, numberValue);
    }

    @Override
    public CodeRuleLog docIdExist(String ruleValue) {
        return getOne(new LambdaQueryWrapper<CodeRuleLog>().eq(CodeRuleLog::getRuleValue,ruleValue).last("limit 1"));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void initDocId() {
        //新增流程增加的文件编号
        List<ModifyApply> maList = iModifyApplyService.list(new LambdaQueryWrapper<ModifyApply>().eq(ModifyApply::getChangeType, ApplyTypeEnum.ADD.name()).isNotNull(ModifyApply::getDocId));
        List<CodeRuleLog> list = new ArrayList<>();
        List<String> codeList = new ArrayList<>();
        for (ModifyApply ma:maList) {
            codeList.add(ma.getDocId());
            CodeRuleLog crl = new CodeRuleLog();
            crl.setRuleId(LinkTypeEnum.DOC.name().equals(ma.getClassType())?"1683660833555456002":"1683362740343631873");
            Map<String, Object> map = new HashMap<String,Object>();
            map.put("docClass",ma.getDocClass());
            DocIdNumDTO numDto = iGenerateIdService.getNumberValueByDocId(ma.getDocId(),crl.getRuleId(),map);
            crl.setNumberValue(numDto.getValue());
            crl.setRuleValue(ma.getDocId());
            crl.setBusinessId(ma.getId());
            crl.setFileType(LinkTypeEnum.DOC.name().equals(ma.getClassType())?ma.getDocClass():ma.getParentDocId());
            list.add(crl);
        }

        List<Standard> sList =  iStandardService.list(new LambdaQueryWrapper<Standard>()
                .notIn(codeList.size()>0,Standard::getDocCode,codeList)
                .isNotNull(Standard::getDocCode));

        for (Standard s:sList) {
            CodeRuleLog crl = new CodeRuleLog();
            Version v = iVersionService.getOne(new LambdaQueryWrapper<Version>()
                    .eq(Version::getStandardId,s.getId()).orderByAsc(Version::getStatus).last("LIMIT 1"));
            String classType=v.getClassType();
            crl.setRuleId(LinkTypeEnum.DOC.name().equals(classType)?"1683660833555456002":"1683362740343631873");
            Map<String, Object> map = new HashMap<String,Object>();
            map.put("docClass",s.getDocClass());
            DocIdNumDTO numDto = iGenerateIdService.getNumberValueByDocId(s.getDocCode(),crl.getRuleId(),map);
            crl.setNumberValue(numDto.getValue());
            crl.setRuleValue(s.getDocCode());
            crl.setBusinessId(s.getId());
            crl.setFileType(LinkTypeEnum.DOC.name().equals(classType)?s.getDocClass():v.getParentDocId());
            list.add(crl);
        }

        remove(new LambdaQueryWrapper<CodeRuleLog>().ne(CodeRuleLog::getRuleId,"FLOW_RULE"));
        saveBatch(list);
    }


}

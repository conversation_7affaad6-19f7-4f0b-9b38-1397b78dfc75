package com.rzdata.setting.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.rzdata.framework.constant.CommonI18nConstant;
import com.rzdata.framework.constant.Constants;
import com.rzdata.framework.core.domain.entity.SysDept;
import com.rzdata.framework.core.domain.entity.SysDictData;
import com.rzdata.framework.core.mybatisplus.core.ServicePlusImpl;
import com.rzdata.framework.core.page.PagePlus;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.exception.ServiceException;
import com.rzdata.framework.utils.I18nUtils;
import com.rzdata.framework.utils.PageUtils;
import com.rzdata.framework.utils.StringUtils;
import com.rzdata.process.domain.dto.DocIdNumDTO;
import com.rzdata.process.service.IGenerateIdService;
import com.rzdata.process.service.IModifyApplyService;
import com.rzdata.process.service.IStandardService;
import com.rzdata.process.service.IVersionService;
import com.rzdata.setting.domain.CodeRule;
import com.rzdata.setting.domain.CodeRuleDetail;
import com.rzdata.setting.domain.CodeRuleLog;
import com.rzdata.setting.domain.DocClass;
import com.rzdata.setting.domain.bo.CodeRuleBo;
import com.rzdata.setting.domain.bo.CodeRuleDetailBo;
import com.rzdata.setting.domain.vo.CodeRuleDetailVo;
import com.rzdata.setting.domain.vo.CodeRuleVo;
import com.rzdata.setting.mapper.CodeRuleMapper;
import com.rzdata.setting.service.ICodeRuleDetailService;
import com.rzdata.setting.service.ICodeRuleLogService;
import com.rzdata.setting.service.ICodeRuleService;
import com.rzdata.setting.service.IDocClassService;
import com.rzdata.system.domain.ProjectInfo;
import com.rzdata.system.service.IProjectInfoService;
import com.rzdata.system.service.ISysDeptService;
import com.rzdata.system.service.ISysDictTypeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.rmi.ServerException;
import java.util.*;

/**
 * 编号规则Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-12-27
 */
@Slf4j
@Service
public class CodeRuleServiceImpl extends ServicePlusImpl<CodeRuleMapper, CodeRule, CodeRuleVo> implements ICodeRuleService {

    @Autowired
    ICodeRuleDetailService iCodeRuleDetailService;

    @Autowired
    ICodeRuleLogService iCodeRuleLogService;

    @Autowired
    IDocClassService iDocClassService;

    @Autowired
    IStandardService iStandardService;
    @Autowired
    IVersionService versionService;

    @Autowired
    IModifyApplyService iModifyApplyService;

    @Autowired
    ISysDictTypeService iSysDictTypeService;

    @Autowired
    private IProjectInfoService projectInfoService;

    @Autowired
    private ISysDeptService iSysDeptService;

    @Autowired
    private IGenerateIdService iGenerateIdService;
    @Override
    public CodeRuleVo queryById(String id) {
        CodeRuleVo vo = getVoById(id);
        if (ObjectUtil.isNotEmpty(vo)) {
            QueryWrapper<CodeRuleDetail> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(CodeRuleDetail::getRuleId, id);
            queryWrapper.lambda().orderByAsc(CodeRuleDetail::getOrderBy);
            vo.setRuleDetailList(iCodeRuleDetailService.list(queryWrapper));
        }
        return vo;
    }

    @Override
    public List<CodeRuleDetailVo> getInfoByDocClass(CodeRuleDetailBo bo) throws ServerException {
        String ruleId = iGenerateIdService.getDocRuleId(bo.getDocClass());
        bo.setRuleId(ruleId);
        return iCodeRuleDetailService.queryList(bo);
    }

    @Override
    public TableDataInfo<CodeRuleVo> queryPageList(CodeRuleBo bo) {
        PagePlus<CodeRule, CodeRuleVo> result = pageVo(PageUtils.buildPagePlus(), buildQueryWrapper(bo));
        return PageUtils.buildDataInfo(result);
    }

    @Override
    public List<CodeRuleVo> queryList(CodeRuleBo bo) {
        return listVo(buildQueryWrapper(bo));
    }

    private LambdaQueryWrapper<CodeRule> buildQueryWrapper(CodeRuleBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<CodeRule> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getRuleName()), CodeRule::getRuleName, bo.getRuleName());
        lqw.eq(StringUtils.isNotBlank(bo.getResetCycle()), CodeRule::getResetCycle, bo.getResetCycle());
        lqw.eq(bo.getNumberInitValue() != null, CodeRule::getNumberInitValue, bo.getNumberInitValue());
        lqw.eq(bo.getNumberDigit() != null, CodeRule::getNumberDigit, bo.getNumberDigit());
        return lqw;
    }

    @Override
    public String insertByBo(CodeRuleBo bo) {
        CodeRule add = BeanUtil.toBean(bo, CodeRule.class);
        add.setCreateTime(new Date());
        validEntityBeforeSave(add);
        boolean flag = save(add);
        if (flag) {
            bo.setId(add.getId());
            handleRuleDetail(bo);
        }
        return add.getId();
    }

    /**
     * 处理详情
     *
     * @param bo
     */
    private void handleRuleDetail(CodeRuleBo bo) {
        if (ObjectUtil.isNotEmpty(bo.getId())) {
            QueryWrapper<CodeRuleDetail> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(CodeRuleDetail::getRuleId, bo.getId());
            iCodeRuleDetailService.remove(queryWrapper);
            if (ObjectUtil.isNotEmpty(bo.getRuleDetailList())) {
                for (CodeRuleDetailBo codeRuleDetailBo : bo.getRuleDetailList()) {
                    codeRuleDetailBo.setRuleId(bo.getId());
                    iCodeRuleDetailService.insertByBo(codeRuleDetailBo);
                }
            }
        }
    }

    @Override
    public Boolean updateByBo(CodeRuleBo bo) {
        CodeRule update = BeanUtil.toBean(bo, CodeRule.class);
        validEntityBeforeSave(update);
        update.setUpdateTime(new Date());
        handleRuleDetail(bo);
        return updateById(update);
    }

    /**
     * 保存前的数据校验
     *
     * @param entity 实体类数据
     */
    private void validEntityBeforeSave(CodeRule entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        for (String id : ids) {
            QueryWrapper<DocClass> docClassQueryWrapper = new QueryWrapper<>();
            docClassQueryWrapper.lambda().eq(DocClass::getCodeId, id);
            if (iDocClassService.count(docClassQueryWrapper) > 0) {
                throw new ServiceException(I18nUtils.getTitle(CommonI18nConstant.SERVICE_CAN_NOT_DELETE_EXIST_CODE_RULE));
            }
            QueryWrapper<CodeRuleDetail> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(CodeRuleDetail::getRuleId, id);
            iCodeRuleDetailService.remove(queryWrapper);
        }
        return removeByIds(ids);
    }

    public static void main(String[] args) {
        Integer initValue = 99;
        int num = 2;
        while (initValue.toString().length()>=num) {
            num+=1;
        }
        System.out.println("num = " + num);
    }





    @Override
    public Boolean checkNoIsExist(String busId, String newNo) {
        QueryWrapper<CodeRuleLog> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(StringUtils.isNotBlank(busId), CodeRuleLog::getBusinessId, busId);
        queryWrapper.lambda().eq(StringUtils.isNotBlank(newNo), CodeRuleLog::getRuleValue, newNo);
        return iCodeRuleLogService.count(queryWrapper) > 0 ? true : false;
    }

    @Override
    public void removeByRule(String busId, String newNo) {
        QueryWrapper<CodeRuleLog> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(StringUtils.isNotBlank(busId), CodeRuleLog::getBusinessId, busId);
        queryWrapper.lambda().eq(CodeRuleLog::getRuleValue, newNo);
        iCodeRuleLogService.remove(queryWrapper);
    }



    /**
     * 获取最大流水号
     * @param codeRule
     * @return
     */
    @Override
    public void getMaxSerialNumber(CodeRule codeRule,CodeRuleLog codeRuleLog){
        Date date=DateUtil.date();
        LambdaQueryWrapper<CodeRuleLog> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(CodeRuleLog::getRuleId,codeRule.getId());
        queryWrapper.orderByDesc(CodeRuleLog::getNumberValue);
        queryWrapper.last("limit 1");

        // 按照天重置 加入条件查当天的最大流水号
        queryWrapper.like(Constants.CYCLE_D.equalsIgnoreCase(codeRule.getResetCycle()),CodeRuleLog::getCreateTime,DateUtil.format(date, DatePattern.NORM_DATE_PATTERN));
        // 按照月重置 加入条件查当月的最大流水号
        queryWrapper.like(Constants.CYCLE_M.equalsIgnoreCase(codeRule.getResetCycle()),CodeRuleLog::getCreateTime,DateUtil.format(date, DatePattern.NORM_MONTH_PATTERN));
        // 按照年重置 加入条件查当年的最大流水号
        queryWrapper.like(Constants.CYCLE_Y.equalsIgnoreCase(codeRule.getResetCycle()),CodeRuleLog::getCreateTime,DateUtil.year(date));
        // 按照表单字段重置 加入条件查同一类型的最大流水号
        queryWrapper.eq(StringUtils.isNotEmpty(codeRuleLog.getFileType()),CodeRuleLog::getFileType,codeRuleLog.getFileType());

        //获取当前规则最大流水号数据
        CodeRuleLog log = iCodeRuleLogService.getOne(queryWrapper);
        if(log==null){
            codeRuleLog.setNumberValue(codeRule.getNumberInitValue());
        } else {
            if (codeRule.getNumberInitValue()>log.getNumberValue()) {
                codeRuleLog.setNumberValue(codeRule.getNumberInitValue());
            }else {
                codeRuleLog.setNumberValue(log.getNumberValue()+1);
            }
        }
        codeRuleLog.setRuleId(codeRule.getId());
        codeRuleLog.setCreateTime(date);
        iCodeRuleLogService.saveOrUpdate(codeRuleLog);
    }

    @Override
    public CodeRuleLog generatorEncodingRule(String ruleId, String businessId, Map<String, Object> bizMap) {
        //根据ruleId和businessId应保持只有唯一的数据
        List<CodeRuleLog> list = iCodeRuleLogService.list(new QueryWrapper<>(CodeRuleLog.builder().ruleId(ruleId).businessId(businessId).build()));
        if(list.size() > 0){
            //如果当前业务表单对应的规则已经生成了编号，则直接返回
            return list.get(0);
        }
        CodeRuleLog codeRuleLog = new CodeRuleLog();
        CodeRule codeRule = this.baseMapper.selectById(ruleId);
        logSetFileType(codeRule,codeRuleLog,bizMap);
        generatorLog(codeRule,businessId,bizMap,codeRuleLog);
        return codeRuleLog;
    }

    @Override
    public void logSetFileType( CodeRule codeRule,CodeRuleLog codeRuleLog,Map<String, Object> bizMap){
        List<String> resetCycle = Arrays.asList(codeRule.getResetCycle().split(","));
        List<SysDictData> dictDataList =iSysDictTypeService.selectDictDataByType(Constants.DICT_TYPE_RESET_CYCLE);
        if (dictDataList.stream().noneMatch(dictData->resetCycle.contains(dictData.getDictValue()))){
            codeRuleLog.setFileType(null);
            resetCycle.forEach(item->{
                codeRuleLog.setFileType(codeRuleLog.getFileType()!=null?codeRuleLog.getFileType()+","+getMapValue(bizMap,item): getMapValue(bizMap,item));
            });
        }
    }

    private void generatorLog(CodeRule codeRule, String businessId, Map<String, Object> bizMap,CodeRuleLog codeRuleLog){
        QueryWrapper<CodeRuleDetail> detailQueryWrapper = new QueryWrapper<>();
        detailQueryWrapper.lambda().eq(CodeRuleDetail::getRuleId, codeRule.getId());
        detailQueryWrapper.lambda().orderByAsc(CodeRuleDetail::getOrderBy);
        List<CodeRuleDetail> details = iCodeRuleDetailService.list(detailQueryWrapper);
        //根据规则生成编号
        StringBuffer snNumberBuffer = new StringBuffer();
        for(CodeRuleDetail detail:details){
            String suffixValue = "";
            String ruType = detail.getRuleType();// 规则类型
            String ruVal = detail.getRuleValue();// 规则值
            if (ruType.equalsIgnoreCase(Constants.RU_TYPE_FD)) {
                suffixValue = getMapValue(bizMap,ruVal);
            } else if (ruType.equalsIgnoreCase(Constants.RU_TYPE_DATE)){
                suffixValue = DateUtil.format(DateUtil.date(), ruVal);
            } else if (ruType.equalsIgnoreCase(Constants.RU_TYPE_STR)){
                suffixValue = ruVal;
            } else if (ruType.equalsIgnoreCase(Constants.RU_TYPE_SNUM)){
                this.getMaxSerialNumber(codeRule,codeRuleLog);
                suffixValue = String.format("%0"+codeRule.getNumberDigit()+"d",codeRuleLog.getNumberValue());
            }else if (ruType.equalsIgnoreCase(Constants.RU_TYPE_DICT)){
                String[] ruVals = ruVal.split("@");
                List<SysDictData> dictDataList= iSysDictTypeService.selectDictDataByType(ruVals[0]);
                String dictValue = getMapValue(bizMap,ruVals[1]);
                Optional<SysDictData> optional = dictDataList.stream().filter(dictData->dictData.getDictValue().equals(dictValue)).findFirst();
                if (optional.isPresent()) {
                    suffixValue = optional.get().getRemark()==null?"":optional.get().getRemark();
                }
            }
            snNumberBuffer.append(suffixValue);
            if(StrUtil.isNotBlank(detail.getSlicerValue()) && !Constants.CYCLE_NO.equals(detail.getSlicerValue())){
                snNumberBuffer.append(detail.getSlicerValue());
            }
        }
        if (StringUtils.isEmpty(codeRuleLog.getId())) {
            //未设置流水号 不去验证重复
            codeRuleLog.setNumberValue(1L);
            codeRuleLog.setRuleId(codeRule.getId());
            codeRuleLog.setCreateTime(DateUtil.date());
            codeRuleLog.setBusinessId(StringUtils.isNotEmpty(businessId)?businessId:snNumberBuffer.toString());
            codeRuleLog.setRuleValue(snNumberBuffer.toString());
            iCodeRuleLogService.save(codeRuleLog);
            return;
        }
        CodeRuleLog log = iCodeRuleLogService.docIdExist(snNumberBuffer.toString());
        if(ObjectUtil.isNotEmpty(log)){
            generatorLog(codeRule,businessId,bizMap,codeRuleLog);
        }else {
            codeRuleLog.setBusinessId(StringUtils.isNotEmpty(businessId)?businessId:snNumberBuffer.toString());
            codeRuleLog.setRuleValue(snNumberBuffer.toString());
            iCodeRuleLogService.updateById(codeRuleLog);
        }
    }

    /**
     * 用于给配置map中没有的表单字段赋值
     * @param map map
     * @param key 键
     * @return value
     */
    @Override
    public String getMapValue(Map<String, Object> map,String key){
        if (map.containsKey(key)) {
            return StringUtils.isNotEmpty(map.get(key))? (String) map.get(key) :"";
        }else {
            String[] keys = key.split("@");
            switch (keys[0]) {
                case "classCode":
                    String docClassId = map.get("docClass").toString();
                    if (keys.length==2) {
                        docClassId = iDocClassService.getPDocClass(docClassId,keys[1]);
                    }
                    String classCode = iDocClassService.getOne(new LambdaQueryWrapper<DocClass>().eq(DocClass::getId,docClassId)).getClassCode();
                    return classCode==null?"":classCode;
                case "num":
                    if (keys.length==2) {
                        if (ObjectUtil.isNotEmpty(map.get(keys[1]))) {
                            String ruleValue = map.get(keys[1]).toString();
                            CodeRuleLog parentLog= iCodeRuleLogService.getOne(new LambdaQueryWrapper<CodeRuleLog>().eq(CodeRuleLog::getRuleValue,ruleValue));
                            DocIdNumDTO numDto = iGenerateIdService.getNumberValueByDocId(ruleValue,parentLog.getRuleId(),map);
                            return String.format("%0"+numDto.getDigit()+"d",numDto.getValue());
                        }
                    }
                    return "";
                case "deptCode":
                    String deptId = "";
                    if (keys.length==2) {
                        deptId = getMapValue(map,keys[1]);
                    }else {
                        deptId = map.get("deptId").toString();
                    }
                    if (StringUtils.isNotEmpty(deptId)) {
                        SysDept dept = iSysDeptService.getOne(new LambdaQueryWrapper<SysDept>().eq(SysDept::getDeptId,deptId).select(SysDept::getDeptCode));
                        if (dept!=null&&StringUtils.isNotEmpty(dept.getDeptCode())) {
                            return dept.getDeptCode();
                        } else {
                            throw new ServiceException(I18nUtils.getTitle(CommonI18nConstant.SERVICE_DEPT_CODE_NOT_MAINTAINED));
                        }
                    }else {
                        throw new ServiceException(I18nUtils.getTitle(CommonI18nConstant.SERVICE_DEPT_CODE_NOT_MAINTAINED));
                    }
                default:
                    throw new ServiceException("【"+key+"】"+I18nUtils.getTitle(CommonI18nConstant.SERVICE_FORM_NOT_FOUND_VALUE));
            }
        }
    }
}

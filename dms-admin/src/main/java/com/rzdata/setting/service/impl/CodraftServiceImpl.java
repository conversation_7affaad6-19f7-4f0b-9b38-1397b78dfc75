package com.rzdata.setting.service.impl;

import cn.hutool.core.bean.BeanUtil;
    import com.rzdata.framework.utils.PageUtils;
import com.rzdata.framework.core.page.PagePlus;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.rzdata.framework.core.mybatisplus.core.ServicePlusImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.rzdata.setting.domain.bo.CodraftBo;
import com.rzdata.setting.domain.vo.CodraftVo;
import com.rzdata.setting.domain.Codraft;
import com.rzdata.setting.mapper.CodraftMapper;
import com.rzdata.setting.service.ICodraftService;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 合稿管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-03-02
 */
@Service
public class CodraftServiceImpl extends ServicePlusImpl<CodraftMapper, Codraft, CodraftVo> implements ICodraftService {

    @Autowired
    CodraftMapper codraftMapper;

    @Override
    public CodraftVo queryById(Long id){
        return getVoById(id);
    }

    @Override
    public TableDataInfo<CodraftVo> queryPageList(CodraftBo bo) {
        PagePlus<Codraft, CodraftVo> result = pageVo(PageUtils.buildPagePlus(), buildQueryWrapper(bo));
        return PageUtils.buildDataInfo(result);
    }

    @Override
    public List<CodraftVo> queryList(CodraftBo bo) {
        return listVo(buildQueryWrapper(bo));
    }

    private LambdaQueryWrapper<Codraft> buildQueryWrapper(CodraftBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<Codraft> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getCodraftName()), Codraft::getCodraftName, bo.getCodraftName());
        lqw.eq(StringUtils.isNotBlank(bo.getFileId()), Codraft::getFileId, bo.getFileId());
        lqw.eq(StringUtils.isNotBlank(bo.getCodraftStatus()), Codraft::getCodraftStatus, bo.getCodraftStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getClassId()), Codraft::getClassId, bo.getClassId());
        lqw.eq(StringUtils.isNotBlank(bo.getBizId()), Codraft::getBizId, bo.getBizId());
        return lqw;
    }

    @Override
    public Boolean insertByBo(CodraftBo bo) {
        Codraft add = BeanUtil.toBean(bo, Codraft.class);
        add.setCreateTime(new Date());
        validEntityBeforeSave(add);
        boolean flag = save(add);
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    @Override
    public Boolean updateByBo(CodraftBo bo) {
        Codraft update = BeanUtil.toBean(bo, Codraft.class);
        validEntityBeforeSave(update);
        return updateById(update);
    }

    /**
     * 保存前的数据校验
     *
     * @param entity 实体类数据
     */
    private void validEntityBeforeSave(Codraft entity){
        //TODO 做一些数据校验,如唯一约束
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return removeByIds(ids);
    }

    @Override
    public Boolean removeByBizId(String bizId){
       return codraftMapper.delByBizId(bizId);
    }

    /**
     * 根据文件类型id查询合稿设置模板
     * @param classId
     * @return
     */
    public List<Codraft> selectByClassId(String classId){
        //根据ClassId找到对应个合稿设置模板
        return codraftMapper.selectByClassId(classId);
    }
}

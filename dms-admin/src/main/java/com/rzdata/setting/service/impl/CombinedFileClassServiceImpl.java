package com.rzdata.setting.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.rzdata.framework.core.mybatisplus.core.ServicePlusImpl;
import com.rzdata.framework.core.page.PagePlus;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.exception.ServiceException;
import com.rzdata.framework.utils.PageUtils;
import com.rzdata.framework.utils.SecurityUtils;
import com.rzdata.framework.utils.StringUtils;
import com.rzdata.setting.domain.CombinedFileClass;
import com.rzdata.setting.domain.CombinedMgr;
import com.rzdata.setting.domain.DocClass;
import com.rzdata.setting.domain.bo.CombinedFileClassBo;
import com.rzdata.setting.domain.vo.CombinedFileClassVo;
import com.rzdata.setting.mapper.CombinedFileClassMapper;
import com.rzdata.setting.service.ICodraftService;
import com.rzdata.setting.service.ICombinedFileClassService;
import com.rzdata.setting.service.ICombinedMgrService;
import com.rzdata.setting.service.IDocClassService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 合稿文件分类Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Service
public class CombinedFileClassServiceImpl extends ServicePlusImpl<CombinedFileClassMapper, CombinedFileClass, CombinedFileClassVo> implements ICombinedFileClassService {


    @Autowired
    IDocClassService docClassService;
    @Autowired
    ICodraftService codraftService;
    @Autowired
    ICombinedMgrService combinedMgrService;

    @Override
    public CombinedFileClassVo queryById(String id){
        return getVoById(id);
    }

    @Override
    public TableDataInfo<CombinedFileClassVo> queryPageList(CombinedFileClassBo bo) {
        PagePlus<CombinedFileClass, CombinedFileClassVo> result = pageVo(PageUtils.buildPagePlus(), buildQueryWrapper(bo));
        return PageUtils.buildDataInfo(result);
    }

    @Override
    public List<CombinedFileClassVo> queryList(CombinedFileClassBo bo) {
        return listVo(buildQueryWrapper(bo));
    }

    private LambdaQueryWrapper<CombinedFileClass> buildQueryWrapper(CombinedFileClassBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<CombinedFileClass> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getCombinedMgrId()), CombinedFileClass::getCombinedMgrId, bo.getCombinedMgrId());
        lqw.eq(StringUtils.isNotBlank(bo.getFileClassId()), CombinedFileClass::getFileClassId, bo.getFileClassId());
        return lqw;
    }

    @Override
    public Boolean saveCombinedFileClass(CombinedFileClassBo bo) {
        List<String> combinedMgrIdList = bo.getCombinedMgrIdList();
        if(CollUtil.isEmpty(bo.getCombinedMgrIdList())){
            throw new ServiceException("文件分类ID不能为空");
        }

        //校验文件分类id只能被一个合稿管理关联
        List<CombinedFileClass> combinedFileClasses = this.list(new LambdaQueryWrapper<CombinedFileClass>().in(CombinedFileClass::getFileClassId, combinedMgrIdList)
                .ne(CombinedFileClass::getCombinedMgrId, bo.getCombinedMgrId()));
        if(CollUtil.isNotEmpty(combinedFileClasses)){
            List<String> mgrId = combinedFileClasses.stream().map(item -> item.getCombinedMgrId()).collect(Collectors.toList());
            List<String> ids = combinedFileClasses.stream().map(item -> item.getFileClassId()).collect(Collectors.toList());

            List<CombinedMgr> combinedMgrList = combinedMgrService.list(new LambdaQueryWrapper<CombinedMgr>().in(CombinedMgr::getId, mgrId));
            StringBuilder mgrName = new StringBuilder();
            if(CollUtil.isNotEmpty(combinedMgrList)){
                for (CombinedMgr combinedMgr : combinedMgrList) {
                    mgrName.append(combinedMgr.getName());
                    mgrName.append(";");
                }
            }

            List<DocClass> docClassList = docClassService.list(new LambdaQueryWrapper<DocClass>().in(DocClass::getId, ids));
            StringBuilder className = new StringBuilder();
            if(CollUtil.isNotEmpty(docClassList)){
                for (DocClass docClass : docClassList) {
                    className.append(docClass.getClassName());
                    className.append(";");
                }
            }
            if(CollUtil.isNotEmpty(docClassList) && CollUtil.isNotEmpty(combinedMgrList)){
                throw new ServiceException("文件分类：【"+className.toString()+"】被【"+mgrName.toString()+"】合稿关联，请先解除关联");
            }
        }


        //先删除后新增
        this.remove(new LambdaQueryWrapper<CombinedFileClass>().eq(CombinedFileClass::getCombinedMgrId, bo.getCombinedMgrId()));
        List<CombinedFileClass> addList = new ArrayList<>();
        for (String classID : combinedMgrIdList) {
            CombinedFileClass combinedFileClass = new CombinedFileClass();
            combinedFileClass.setCombinedMgrId(bo.getCombinedMgrId());
            combinedFileClass.setFileClassId(classID);
            combinedFileClass.setCreateTime(new Date());
            combinedFileClass.setCreateBy(SecurityUtils.getLoginUser().getUsername());
            addList.add(combinedFileClass);
        }
        //更新数据
        List<DocClass> list = docClassService.list(new LambdaQueryWrapper<DocClass>().in(DocClass::getId, combinedMgrIdList));
        list.forEach(item->{
            item.setCombinedMgrId(bo.getCombinedMgrId());
            docClassService.updateById(item);
        });
        if (combinedMgrIdList.size() > 0) {
            return this.saveBatch(addList);
        }
        return false;
    }

    @Override
    public Boolean updateByBo(CombinedFileClassBo bo) {
        CombinedFileClass update = BeanUtil.toBean(bo, CombinedFileClass.class);
        validEntityBeforeSave(update);
        return updateById(update);
    }

    /**
     * 保存前的数据校验
     *
     * @param entity 实体类数据
     */
    private void validEntityBeforeSave(CombinedFileClass entity){
        //TODO 做一些数据校验,如唯一约束
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return removeByIds(ids);
    }
}

package com.rzdata.setting.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.rzdata.framework.core.mybatisplus.core.ServicePlusImpl;
import com.rzdata.framework.core.page.PagePlus;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.utils.PageUtils;
import com.rzdata.framework.utils.StringUtils;
import com.rzdata.setting.domain.Codraft;
import com.rzdata.setting.domain.CombinedMgr;
import com.rzdata.setting.domain.bo.CodraftBo;
import com.rzdata.setting.domain.bo.CombinedMgrBo;
import com.rzdata.setting.domain.vo.CodraftVo;
import com.rzdata.setting.domain.vo.CombinedMgrVo;
import com.rzdata.setting.mapper.CombinedMgrMapper;
import com.rzdata.setting.service.ICombinedMgrService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * 合稿管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Service
public class CombinedMgrServiceImpl extends ServicePlusImpl<CombinedMgrMapper, CombinedMgr, CombinedMgrVo>
        implements ICombinedMgrService {

    @Autowired
    CodraftServiceImpl codraftService;

    @Override
    public CombinedMgrVo queryById(String id) {
        CombinedMgrVo voById = getVoById(id);
        //获取子表数据
        CodraftBo codraftBo = new CodraftBo();
        codraftBo.setBizId(id);
        List<CodraftVo> list = codraftService.queryList(codraftBo);
        voById.setCodraftVoList(list);
        return getVoById(id);
    }

    @Override
    public TableDataInfo<CombinedMgrVo> queryPageList(CombinedMgrBo bo) {
        PagePlus<CombinedMgr, CombinedMgrVo> result = pageVo(PageUtils.buildPagePlus(), buildQueryWrapper(bo));
        return PageUtils.buildDataInfo(result);
    }

    @Override
    public List<CombinedMgrVo> queryList(CombinedMgrBo bo) {
        return listVo(buildQueryWrapper(bo));
    }

    private LambdaQueryWrapper<CombinedMgr> buildQueryWrapper(CombinedMgrBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<CombinedMgr> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getName()), CombinedMgr::getName, bo.getName());
        lqw.eq(bo.getIsDeleted() != null, CombinedMgr::getIsDeleted, bo.getIsDeleted());
        lqw.orderByDesc(CombinedMgr::getCreateTime);
        return lqw;
    }

    @Override
    public Boolean insertByBo(CombinedMgrBo bo) {
        if (bo == null) {
            throw new IllegalArgumentException("CombinedMgrBo 不能为 null");
        }

        CombinedMgr add = BeanUtil.toBean(bo, CombinedMgr.class);
        bo.setIsDeleted(0);
        validEntityBeforeSave(add);

        boolean flag = save(add);
        if (flag) {
            bo.setId(add.getId());
        }
        List<Codraft> addCorList = new ArrayList<>();
        if (CollUtil.isNotEmpty(bo.getCodraftBoList())) {
            for (CodraftBo codraftBo : bo.getCodraftBoList()) {
                Codraft codraft = BeanUtil.toBean(codraftBo, Codraft.class);
                codraft.setCreateTime(new Date());
                codraft.setBizId(add.getId());
                addCorList.add(codraft);
            }
        }

        if (CollUtil.isNotEmpty(addCorList)) {
            codraftService.saveBatch(addCorList);
        }

        return flag;
    }

    @Override
    @Transactional( rollbackFor = Exception.class)
    public Boolean updateByBo(CombinedMgrBo bo) {
        CombinedMgr update = BeanUtil.toBean(bo, CombinedMgr.class);
        validEntityBeforeSave(update);
        boolean result = updateById(update);
        //先删除
        codraftService.removeByBizId(update.getId());
        //后新增
        List<Codraft> addCorList = new ArrayList<>();
        if (CollUtil.isNotEmpty(bo.getCodraftBoList())) {
            for (CodraftBo codraftBo : bo.getCodraftBoList()) {
                Codraft codraft = BeanUtil.toBean(codraftBo, Codraft.class);
                codraft.setCreateTime(new Date());
                codraft.setBizId(update.getId());
                addCorList.add(codraft);
            }
        }

        if (CollUtil.isNotEmpty(addCorList)) {
            codraftService.saveBatch(addCorList);
        }
        return result;
    }

    /**
     * 保存前的数据校验
     *
     * @param entity 实体类数据
     */
    private void validEntityBeforeSave(CombinedMgr entity) {
        // TODO 做一些数据校验,如唯一约束
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        if (isValid) {
            // TODO 做一些业务上的校验,判断是否需要校验
        }
        return removeByIds(ids);
    }
}

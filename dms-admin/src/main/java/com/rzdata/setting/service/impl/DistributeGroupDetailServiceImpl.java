package com.rzdata.setting.service.impl;

import cn.hutool.core.bean.BeanUtil;
    import com.rzdata.framework.utils.PageUtils;
import com.rzdata.framework.core.page.PagePlus;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.utils.StringUtils;
import org.springframework.stereotype.Service;
import com.rzdata.framework.core.mybatisplus.core.ServicePlusImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.rzdata.setting.domain.bo.DistributeGroupDetailBo;
import com.rzdata.setting.domain.vo.DistributeGroupDetailVo;
import com.rzdata.setting.domain.DistributeGroupDetail;
import com.rzdata.setting.mapper.DistributeGroupDetailMapper;
import com.rzdata.setting.service.IDistributeGroupDetailService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 预设分组人员详情Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-11-26
 */
@Service
public class DistributeGroupDetailServiceImpl extends ServicePlusImpl<DistributeGroupDetailMapper, DistributeGroupDetail, DistributeGroupDetailVo> implements IDistributeGroupDetailService {

    @Override
    public DistributeGroupDetailVo queryById(String id){
        return getVoById(id);
    }

    @Override
    public TableDataInfo<DistributeGroupDetailVo> queryPageList(DistributeGroupDetailBo bo) {
        PagePlus<DistributeGroupDetail, DistributeGroupDetailVo> result = pageVo(PageUtils.buildPagePlus(), buildQueryWrapper(bo));
        return PageUtils.buildDataInfo(result);
    }

    @Override
    public List<DistributeGroupDetailVo> queryList(DistributeGroupDetailBo bo) {
        return listVo(buildQueryWrapper(bo));
    }

    private LambdaQueryWrapper<DistributeGroupDetail> buildQueryWrapper(DistributeGroupDetailBo bo) {
        LambdaQueryWrapper<DistributeGroupDetail> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getGroupId()), DistributeGroupDetail::getGroupId, bo.getGroupId());
        lqw.eq(StringUtils.isNotBlank(bo.getTenantId()), DistributeGroupDetail::getTenantId, bo.getTenantId());
        lqw.like(StringUtils.isNotBlank(bo.getReceiveUserName()), DistributeGroupDetail::getReceiveUserName, bo.getReceiveUserName());
        lqw.like(StringUtils.isNotBlank(bo.getReceiveNickName()), DistributeGroupDetail::getReceiveNickName, bo.getReceiveNickName());
        lqw.eq(StringUtils.isNotBlank(bo.getReceiveUserDeptId()), DistributeGroupDetail::getReceiveUserDeptId, bo.getReceiveUserDeptId());
        lqw.eq(StringUtils.isNotBlank(bo.getReceiveUserDept()), DistributeGroupDetail::getReceiveUserDept, bo.getReceiveUserDept());
        lqw.eq(StringUtils.isNotBlank(bo.getType()), DistributeGroupDetail::getType, bo.getType());
        lqw.eq(StringUtils.isNotBlank(bo.getCategory()), DistributeGroupDetail::getCategory, bo.getCategory());
        return lqw;
    }

    @Override
    public Boolean insertByBo(DistributeGroupDetailBo bo) {
        DistributeGroupDetail add = BeanUtil.toBean(bo, DistributeGroupDetail.class);
        validEntityBeforeSave(add);
        boolean flag = save(add);
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    @Override
    public Boolean updateByBo(DistributeGroupDetailBo bo) {
        DistributeGroupDetail update = BeanUtil.toBean(bo, DistributeGroupDetail.class);
        validEntityBeforeSave(update);
        return updateById(update);
    }

    /**
     * 保存前的数据校验
     *
     * @param entity 实体类数据
     */
    private void validEntityBeforeSave(DistributeGroupDetail entity){
        //TODO 做一些数据校验,如唯一约束
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return removeByIds(ids);
    }
}

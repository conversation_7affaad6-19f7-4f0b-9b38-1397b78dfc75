package com.rzdata.setting.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.rzdata.framework.constant.Constants;
import com.rzdata.framework.utils.PageUtils;
import com.rzdata.framework.core.page.PagePlus;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.utils.StringUtils;
import com.rzdata.setting.domain.DistributeGroupDetail;
import com.rzdata.setting.domain.DocClassSetting;
import com.rzdata.setting.domain.VersionRuleDetail;
import com.rzdata.setting.service.IDistributeGroupDetailService;
import com.rzdata.setting.service.IDocClassSettingService;
import com.rzdata.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.rzdata.framework.core.mybatisplus.core.ServicePlusImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.rzdata.setting.domain.bo.DistributeGroupBo;
import com.rzdata.setting.domain.vo.DistributeGroupVo;
import com.rzdata.setting.domain.DistributeGroup;
import com.rzdata.setting.mapper.DistributeGroupMapper;
import com.rzdata.setting.service.IDistributeGroupService;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 预设分组人员Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-11-26
 */
@Service
public class DistributeGroupServiceImpl extends ServicePlusImpl<DistributeGroupMapper, DistributeGroup, DistributeGroupVo> implements IDistributeGroupService {

    @Autowired
    private IDistributeGroupDetailService iDistributeGroupDetailService;

    @Autowired
    private IDocClassSettingService iDocClassSettingService;

    @Autowired
    private ISysUserService iSysUserService;

    @Override
    public DistributeGroupVo queryById(String id){
        return getVoById(id);
    }

    @Override
    public TableDataInfo<DistributeGroupVo> queryPageList(DistributeGroupBo bo) {
        PagePlus<DistributeGroup, DistributeGroupVo> result = pageVo(PageUtils.buildPagePlus(), buildQueryWrapper(bo));
        return PageUtils.buildDataInfo(result);
    }

    @Override
    public List<DistributeGroupVo> queryList(DistributeGroupBo bo) {
        return listVo(buildQueryWrapper(bo));
    }

    private LambdaQueryWrapper<DistributeGroup> buildQueryWrapper(DistributeGroupBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<DistributeGroup> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getGroupName()), DistributeGroup::getGroupName, bo.getGroupName());
        lqw.eq(StringUtils.isNotBlank(bo.getType()), DistributeGroup::getType, bo.getType());
        lqw.eq(StringUtils.isNotBlank(bo.getTenantId()), DistributeGroup::getTenantId, bo.getTenantId());
        return lqw;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertByBo(DistributeGroupBo bo) {
        DistributeGroup add = BeanUtil.toBean(bo, DistributeGroup.class);
        validEntityBeforeSave(add);
        boolean flag = save(add);
        if (flag) {
            updateDetail(add.getId(),bo.getItemList());
            bo.setId(add.getId());
        }
        return flag;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateByBo(DistributeGroupBo bo) {
        DistributeGroup update = BeanUtil.toBean(bo, DistributeGroup.class);
        validEntityBeforeSave(update);
        updateDetail(bo.getId(),bo.getItemList());
        return updateById(update);
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateDetail(String groupId, List<DistributeGroupDetail> itemList){
        iDistributeGroupDetailService.remove(new LambdaQueryWrapper<DistributeGroupDetail>().eq(DistributeGroupDetail::getGroupId,groupId));
        if (itemList!=null&&itemList.size()>0) {
            itemList.forEach(item->{
                item.setGroupId(groupId);
            });
            iDistributeGroupDetailService.saveAll(itemList);
        }
    }

    /**
     * 保存前的数据校验
     *
     * @param entity 实体类数据
     */
    private void validEntityBeforeSave(DistributeGroup entity){
        //TODO 做一些数据校验,如唯一约束
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        iDistributeGroupDetailService.remove(new LambdaQueryWrapper<DistributeGroupDetail>().in(DistributeGroupDetail::getGroupId,ids));
        return removeByIds(ids);
    }

    @Override
    public List<DistributeGroupVo> listByDocClass(String docClass) {
        DocClassSetting setting =  iDocClassSettingService.getSettingStatus(docClass,"distribute",null);
        List<DistributeGroupVo> list = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(setting)) {

            //打印
            DistributeGroupVo print = getVoById(setting.getRuleId());
            if (print!=null) {
                print.setItemList(iDistributeGroupDetailService.list(new LambdaQueryWrapper<DistributeGroupDetail>().eq(DistributeGroupDetail::getGroupId,print.getId())));
                list.add(print);
            }

            //权限
            DistributeGroupVo purview = getVoById(setting.getSettingId());
            if (purview!=null) {
                List<DistributeGroupDetail> itemList = iDistributeGroupDetailService.list(new LambdaQueryWrapper<DistributeGroupDetail>().eq(DistributeGroupDetail::getGroupId, purview.getId()));
                //权限是部门的重新获取人数
                itemList.forEach(item -> {
                    if (Constants.TYPE_DEPT.equals(item.getType())) {
                        item.setNums(iSysUserService.getNumByDeptId(item.getReceiveUserDeptId()));
                    }
                });
                purview.setItemList(itemList);
                list.add(purview);
            }
            return list;
        }
        return list;
    }
}

package com.rzdata.setting.service.impl;

import cn.hutool.core.bean.BeanUtil;
    import com.rzdata.framework.utils.PageUtils;
import com.rzdata.framework.core.page.PagePlus;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.utils.StringUtils;
import org.springframework.stereotype.Service;
import com.rzdata.framework.core.mybatisplus.core.ServicePlusImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.rzdata.setting.domain.bo.DocClassFlowNodeDetailBo;
import com.rzdata.setting.domain.vo.DocClassFlowNodeDetailVo;
import com.rzdata.setting.domain.DocClassFlowNodeDetail;
import com.rzdata.setting.mapper.DocClassFlowNodeDetailMapper;
import com.rzdata.setting.service.IDocClassFlowNodeDetailService;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 文件分类设置-节点明细设置Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-01-10
 */
@Service
public class DocClassFlowNodeDetailServiceImpl extends ServicePlusImpl<DocClassFlowNodeDetailMapper, DocClassFlowNodeDetail, DocClassFlowNodeDetailVo> implements IDocClassFlowNodeDetailService {

    @Override
    public DocClassFlowNodeDetailVo queryById(String id){
        return getVoById(id);
    }

    @Override
    public TableDataInfo<DocClassFlowNodeDetailVo> queryPageList(DocClassFlowNodeDetailBo bo) {
        PagePlus<DocClassFlowNodeDetail, DocClassFlowNodeDetailVo> result = pageVo(PageUtils.buildPagePlus(), buildQueryWrapper(bo));
        return PageUtils.buildDataInfo(result);
    }

    @Override
    public List<DocClassFlowNodeDetailVo> queryList(DocClassFlowNodeDetailBo bo) {
        return listVo(buildQueryWrapper(bo));
    }

    /**
     * 根据文件分类流程配置ID和流程环节，获取环节授权明细
     *
     * @param flowId
     * @param nodeCode
     * @return
     */
    @Override
    public List<DocClassFlowNodeDetail> queryList(String flowId, String nodeCode) {
        HashMap<String,String> map = new HashMap<>();
        map.put("flowId",flowId);
        map.put("nodeCode",nodeCode);
        return this.baseMapper.queryList(map);
    }

    private LambdaQueryWrapper<DocClassFlowNodeDetail> buildQueryWrapper(DocClassFlowNodeDetailBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<DocClassFlowNodeDetail> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getTenantId()), DocClassFlowNodeDetail::getTenantId, bo.getTenantId());
        lqw.eq(StringUtils.isNotBlank(bo.getNodeId()), DocClassFlowNodeDetail::getNodeId, bo.getNodeId());
        lqw.eq(StringUtils.isNotBlank(bo.getType()), DocClassFlowNodeDetail::getType, bo.getType());
        lqw.eq(StringUtils.isNotBlank(bo.getCode()), DocClassFlowNodeDetail::getCode, bo.getCode());
        lqw.like(StringUtils.isNotBlank(bo.getName()), DocClassFlowNodeDetail::getName, bo.getName());
        lqw.like(StringUtils.isNotBlank(bo.getFunName()), DocClassFlowNodeDetail::getFunName, bo.getFunName());
        lqw.eq(StringUtils.isNotBlank(bo.getFunCondition()), DocClassFlowNodeDetail::getFunCondition, bo.getFunCondition());
        lqw.eq(bo.getSort() != null, DocClassFlowNodeDetail::getSort, bo.getSort());
        lqw.orderByAsc(DocClassFlowNodeDetail::getSort);
        return lqw;
    }

    @Override
    public Boolean insertByBo(DocClassFlowNodeDetailBo bo) {
        DocClassFlowNodeDetail add = BeanUtil.toBean(bo, DocClassFlowNodeDetail.class);
        validEntityBeforeSave(add);
        boolean flag = save(add);
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    @Override
    public Boolean updateByBo(DocClassFlowNodeDetailBo bo) {
        DocClassFlowNodeDetail update = BeanUtil.toBean(bo, DocClassFlowNodeDetail.class);
        validEntityBeforeSave(update);
        return updateById(update);
    }

    /**
     * 保存前的数据校验
     *
     * @param entity 实体类数据
     */
    private void validEntityBeforeSave(DocClassFlowNodeDetail entity){
        //TODO 做一些数据校验,如唯一约束
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return removeByIds(ids);
    }
}

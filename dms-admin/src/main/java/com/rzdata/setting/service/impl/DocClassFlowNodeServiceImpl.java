package com.rzdata.setting.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.rzdata.framework.utils.PageUtils;
import com.rzdata.framework.core.page.PagePlus;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.utils.StringUtils;
import com.rzdata.setting.domain.DocClassFlowNodeDetail;
import com.rzdata.setting.service.IDocClassFlowNodeDetailService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import com.rzdata.framework.core.mybatisplus.core.ServicePlusImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.rzdata.setting.domain.bo.DocClassFlowNodeBo;
import com.rzdata.setting.domain.vo.DocClassFlowNodeVo;
import com.rzdata.setting.domain.DocClassFlowNode;
import com.rzdata.setting.mapper.DocClassFlowNodeMapper;
import com.rzdata.setting.service.IDocClassFlowNodeService;

import javax.annotation.Resource;
import java.util.*;

/**
 * 文件分类设置-流程节点设置Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-01-10
 */
@Slf4j
@Service
public class DocClassFlowNodeServiceImpl extends ServicePlusImpl<DocClassFlowNodeMapper, DocClassFlowNode, DocClassFlowNodeVo> implements IDocClassFlowNodeService {

    @Resource
    private IDocClassFlowNodeDetailService classFlowNodeDetailService;


    @Override
    public DocClassFlowNodeVo queryById(String id){
        return getVoById(id);
    }

    @Override
    public TableDataInfo<DocClassFlowNodeVo> queryPageList(DocClassFlowNodeBo bo) {
        PagePlus<DocClassFlowNode, DocClassFlowNodeVo> result = pageVo(PageUtils.buildPagePlus(), buildQueryWrapper(bo));
        return PageUtils.buildDataInfo(result);
    }

    @Override
    public List<DocClassFlowNodeVo> queryList(DocClassFlowNodeBo bo) {
        return listVo(buildQueryWrapper(bo));
    }

    private LambdaQueryWrapper<DocClassFlowNode> buildQueryWrapper(DocClassFlowNodeBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<DocClassFlowNode> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getTenantId()), DocClassFlowNode::getTenantId, bo.getTenantId());
        lqw.eq(StringUtils.isNotBlank(bo.getFlowId()), DocClassFlowNode::getFlowId, bo.getFlowId());
        lqw.eq(StringUtils.isNotBlank(bo.getNodeCode()), DocClassFlowNode::getNodeCode, bo.getNodeCode());
        lqw.like(StringUtils.isNotBlank(bo.getNodeName()), DocClassFlowNode::getNodeName, bo.getNodeName());
        lqw.eq(StringUtils.isNotBlank(bo.getPageMode()), DocClassFlowNode::getPageMode, bo.getPageMode());
        lqw.eq(StringUtils.isNotBlank(bo.getAppendixMode()), DocClassFlowNode::getAppendixMode, bo.getAppendixMode());
        lqw.eq(StringUtils.isNotBlank(bo.getModuleConfig()), DocClassFlowNode::getModuleConfig, bo.getModuleConfig());
        lqw.eq(StringUtils.isNotBlank(bo.getDeleteFlag()), DocClassFlowNode::getDeleteFlag, bo.getDeleteFlag());
        lqw.eq(bo.getSort() != null, DocClassFlowNode::getSort, bo.getSort());
        lqw.orderByAsc(DocClassFlowNode::getSort);
        return lqw;
    }

    @Override
    public Boolean insertByBo(DocClassFlowNodeBo bo) {
        DocClassFlowNode add = BeanUtil.toBean(bo, DocClassFlowNode.class);
        validEntityBeforeSave(add);
        boolean flag = save(add);
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    @Override
    public Boolean updateByBo(DocClassFlowNodeBo bo) {
        DocClassFlowNode update = BeanUtil.toBean(bo, DocClassFlowNode.class);
        validEntityBeforeSave(update);
        return updateById(update);
    }

    /**
     * 保存前的数据校验
     *
     * @param entity 实体类数据
     */
    private void validEntityBeforeSave(DocClassFlowNode entity){
        //TODO 做一些数据校验,如唯一约束
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return removeByIds(ids);
    }

    @Override
    public void removeFlowNodeCascade(String flowId) {
        List<DocClassFlowNode> nodeList = this.list(new QueryWrapper<>(new DocClassFlowNode().setFlowId(flowId)));
        if(nodeList != null) {
            for(DocClassFlowNode nodeObj : nodeList) {
                // 删除流程环节下所有的权限按钮配置
                this.classFlowNodeDetailService.remove(new QueryWrapper<>(new DocClassFlowNodeDetail().setNodeId(nodeObj.getId())));
                // 删除该文件分类下的所有流程环节
                this.removeById(nodeObj.getId());
            }
        }
    }

}

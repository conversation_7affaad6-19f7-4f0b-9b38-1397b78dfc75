package com.rzdata.setting.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.rzdata.config.CustomConfig;
import com.rzdata.framework.core.domain.entity.SysDictData;
import com.rzdata.framework.core.mybatisplus.core.ServicePlusImpl;
import com.rzdata.framework.core.page.PagePlus;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.utils.PageUtils;
import com.rzdata.framework.utils.StringUtils;
import com.rzdata.setting.domain.DocClassFlow;
import com.rzdata.setting.domain.DocClassFlowNode;
import com.rzdata.setting.domain.DocClassFlowNodeDetail;
import com.rzdata.setting.domain.DocClassSetting;
import com.rzdata.setting.domain.bo.DocClassFlowBo;
import com.rzdata.setting.domain.bo.DocClassFlowNodeBo;
import com.rzdata.setting.domain.bo.DocClassFlowNodeDetailBo;
import com.rzdata.setting.domain.vo.DocClassFlowNodeVo;
import com.rzdata.setting.domain.vo.DocClassFlowVo;
import com.rzdata.setting.mapper.DocClassFlowMapper;
import com.rzdata.setting.mapper.DocClassFlowNodeMapper;
import com.rzdata.setting.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 文件分类设置-流程设置Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-01-10
 */
@Service
public class DocClassFlowServiceImpl extends ServicePlusImpl<DocClassFlowMapper, DocClassFlow, DocClassFlowVo> implements IDocClassFlowService {

    @Resource
    private IDocClassFlowNodeService classFlowNodeService;

    @Resource
    private DocClassFlowNodeMapper docClassFlowNodeMapper;

    @Resource
    private IDocClassFlowNodeDetailService classFlowNodeDetailService;

    @Autowired
    private IDocClassSettingService iDocClassSettingService;

    @Override
    public DocClassFlowVo queryById(String id){
        DocClassFlowVo vo = getVoById(id);
        return buildDeepClassFlowData(vo);
    }

    @Override
    public TableDataInfo<DocClassFlowVo> queryPageList(DocClassFlowBo bo) {
        LambdaQueryWrapper<DocClassFlow> lambdaQueryWrapper = buildQueryWrapper(bo);
        lambdaQueryWrapper.and(StringUtils.isNotEmpty(bo.getSearchValue()),v-> v.like(DocClassFlow::getFlowKey,
                bo.getSearchValue()).or().like(DocClassFlow::getFlowName,bo.getSearchValue()));
        PagePlus<DocClassFlow, DocClassFlowVo> result = pageVo(PageUtils.buildPagePlus(), lambdaQueryWrapper);
        return PageUtils.buildDataInfo(result);
    }

    @Override
    public List<DocClassFlowVo> queryList(DocClassFlowBo bo) {
        return listVo(buildQueryWrapper(bo));
    }

    private LambdaQueryWrapper<DocClassFlow> buildQueryWrapper(DocClassFlowBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<DocClassFlow> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getTenantId()), DocClassFlow::getTenantId, bo.getTenantId());
        lqw.eq(StringUtils.isNotBlank(bo.getDocClass()), DocClassFlow::getDocClass, bo.getDocClass());
        lqw.eq(StringUtils.isNotBlank(bo.getBizType()), DocClassFlow::getBizType, bo.getBizType());
        lqw.like(StringUtils.isNotBlank(bo.getBizName()), DocClassFlow::getBizName, bo.getBizName());
        lqw.like(StringUtils.isNotBlank(bo.getFlowKey()), DocClassFlow::getFlowKey, bo.getFlowKey());
        lqw.like(StringUtils.isNotBlank(bo.getFlowName()), DocClassFlow::getFlowName, bo.getFlowName());
        lqw.eq(StringUtils.isNotBlank(bo.getOpenFlag()), DocClassFlow::getOpenFlag, bo.getOpenFlag());
        lqw.eq(StringUtils.isNotBlank(bo.getApplyFlag()), DocClassFlow::getApplyFlag, bo.getApplyFlag());
        lqw.eq(bo.getDealTime() != null, DocClassFlow::getDealTime, bo.getDealTime());
        lqw.eq(StringUtils.isNotBlank(bo.getDealTimeUnit()), DocClassFlow::getDealTimeUnit, bo.getDealTimeUnit());
        lqw.isNull(bo.getNoClass(),DocClassFlow::getDocClass);
        return lqw;
    }

    @Override
    public Boolean insertByBo(DocClassFlowBo bo) {
        bo.setId(null);
        DocClassFlow add = BeanUtil.toBean(bo, DocClassFlow.class);
        validEntityBeforeSave(add);
        boolean flag = save(add);
        if (flag) {
            bo.setId(add.getId());
            updateNodeList(bo.getNodeList(),bo.getId());
        }
        return flag;
    }

    private void updateNodeList(List<DocClassFlowNodeBo> nodeList, String flowId){
        if(nodeList != null) {
            for(DocClassFlowNodeBo nodeObj : nodeList) {
                // 新增保存-该文件分类下的所有流程环节
                nodeObj.setId(null);
                nodeObj.setFlowId(flowId);
                nodeObj.setCreateTime(new Date());
                this.classFlowNodeService.insertByBo(nodeObj);
                List<DocClassFlowNodeDetailBo> nodeDetailList = nodeObj.getNodeDetailList();
                if(nodeDetailList != null) {
                    for(DocClassFlowNodeDetailBo nodeDetailObj : nodeDetailList) {
                        // 新增保存-流程环节下所有的权限按钮配置
                        nodeDetailObj.setId(null);
                        nodeDetailObj.setNodeId(nodeObj.getId());
                        nodeDetailObj.setCreateTime(new Date());
                        this.classFlowNodeDetailService.insertByBo(nodeDetailObj);
                    }
                }
            }
        }
    }

    @Override
    public Boolean updateByBo(DocClassFlowBo bo) {
        DocClassFlow update = BeanUtil.toBean(bo, DocClassFlow.class);
        validEntityBeforeSave(update);
        classFlowNodeService.removeFlowNodeCascade(bo.getId());
        updateNodeList(bo.getNodeList(),bo.getId());
        return updateById(update);
    }

    /**
     * 保存前的数据校验
     *
     * @param entity 实体类数据
     */
    private void validEntityBeforeSave(DocClassFlow entity){
        //TODO 做一些数据校验,如唯一约束
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return removeByIds(ids);
    }

    @Override
    public DocClassFlowVo buildDeepClassFlowData(DocClassFlowVo vo) {
        // DocClassFlowVo result = this.getVoOne(new QueryWrapper<DocClassFlow>(new DocClassFlow().setId(flowId)));
        if(vo != null) {
            DocClassFlowNodeBo bo = new DocClassFlowNodeBo();
            bo.setFlowId(vo.getId());
            List<DocClassFlowNodeVo> list = this.classFlowNodeService.queryList(bo);
            // 设置该流程对应的节点集合
            for(DocClassFlowNodeVo nodeVo : list) {
                // 设置节点环节的按钮权限清单
                nodeVo.setNodeDetailList(classFlowNodeDetailService.list(new LambdaQueryWrapper<DocClassFlowNodeDetail>().eq(DocClassFlowNodeDetail::getNodeId,nodeVo.getId())));
            }
            vo.setNodeList(list);
        }
        return vo;
    }

    public void removeDeepClassFlowData2(List<DocClassFlowBo> list) {
        for(DocClassFlowBo flowObj : list) {
            List<DocClassFlowNodeBo> nodeList = flowObj.getNodeList();
            if(nodeList != null) {

                for(DocClassFlowNodeBo nodeObj : nodeList) {
                    List<DocClassFlowNodeDetailBo> nodeDetailList = nodeObj.getNodeDetailList();
                    if(nodeDetailList != null) {
                        for(DocClassFlowNodeDetailBo nodeDetailObj : nodeDetailList) {
                            // 删除流程环节下所有的权限按钮配置
                            this.classFlowNodeDetailService.removeById(nodeDetailObj.getId());
                        }
                    }
                    // 删除该文件分类下的所有流程环节
                    this.classFlowNodeService.removeById(nodeObj.getId());
                }
            }
            // 删除该文件分类流程KEY配置
            this.removeById(flowObj.getId());
        }
    }

    @Override
    public void removeClassFlowCascade(List<DocClassFlowBo> list) {
        String docClass = "";
        if(list != null) {
            docClass = list.get(0).getDocClass();
        }
        List<DocClassFlow> flowList = this.list(new QueryWrapper<>(new DocClassFlow().setDocClass(docClass)));
        for(DocClassFlow flowObj : flowList) {
            this.classFlowNodeService.removeFlowNodeCascade(flowObj.getId());
            // 删除该文件分类流程KEY配置
            this.removeById(flowObj.getId());
        }
    }

    @Override
    public void insertClassFlowCascade(List<DocClassFlowBo> list) {
        for(DocClassFlowBo flowObj : list) {
            // 新增保存-该文件分类流程KEY配置
            flowObj.setCreateTime(new Date());
            this.insertByBo(flowObj);
        }
    }


    @Override
    public DocClassFlowVo getByUpDocClassAndBizType(String docClass, String bizType,String applyFlag) {
        DocClassFlowVo result = null;
        //根据文件类型获取配置
        if (StringUtils.isEmpty(docClass)) {
            result = getVoOne(new LambdaQueryWrapper<DocClassFlow>()
                    .eq(DocClassFlow::getBizType,bizType)
                    .last("limit 1")
                    .orderByDesc(DocClassFlow::getCreateTime));
        } else {
            DocClassSetting setting= iDocClassSettingService.getSettingStatus(docClass,bizType,null);
            if (ObjectUtil.isNotEmpty(setting) && StringUtils.isNotEmpty(setting.getRuleId())) {
                result = getVoById(setting.getRuleId());
            }
        }

//        QueryWrapper<DocClassFlow> queryWrapper = new QueryWrapper();
//        if (ObjectUtil.isNotEmpty(docClass)) {
//            queryWrapper.lambda().eq(DocClassFlow::getDocClass, docClass);
//        }
//        if (ObjectUtil.isNotEmpty(bizType)) {
//            queryWrapper.lambda().eq(DocClassFlow::getBizType, bizType);
//        }
//        if(StringUtils.isNotEmpty(applyFlag)) { // 是否应用到子分类
//            queryWrapper.eq("apply_flag",applyFlag);
//        }
//        DocClassFlowVo result = this.getVoOne(queryWrapper);
//        if((result != null && "N".equals(result.getOpenFlag())) || result == null) { // 当前分类流程配置未生效或者未配置分类流程信息
//            result = null;
//            // 根据文件分类树形结构级联
//            DocClassVo docClassObj = this.docClassService.queryById(docClass);
//            if(!"0".equals(docClassObj.getParentClassId())) { // 非顶级分类
//                // 上级分类的配置需要设置 应用到子分类， applyFlag=Y
//                result = this.getByUpDocClassAndBizType(docClassObj.getParentClassId(),bizType,"Y");
//            }
//        }
//        if(result != null) { // 拼接流程节点
//            List<DocClassFlowNodeVo> nodeList = this.classFlowNodeService.listVo(new QueryWrapper<>(new DocClassFlowNode().setFlowId(result.getId())));
//            result.setNodeList(nodeList);
//        }
        return result;
    }

    @Override
    public DocClassFlowVo getByBizType(String bizType,String applyFlag) {
        QueryWrapper query = new QueryWrapper<>(new DocClassFlow().setBizType(bizType));
        if(StringUtils.isNotEmpty(applyFlag)) { // 是否应用到子分类
            query.eq("apply_flag",applyFlag);
        }
        DocClassFlowVo result = this.getVoOne(query);
        if(result != null) { // 拼接流程节点
            List<DocClassFlowNodeVo> nodeList = this.classFlowNodeService.listVo(new QueryWrapper<>(new DocClassFlowNode().setFlowId(result.getId())));
            result.setNodeList(nodeList);
        }
        return result;
    }

    @Override
    public List<DocClassFlowNodeVo> getNodeList(String docClass, String bizType, String code) {
        List<DocClassFlowNodeVo> result = null;
        DocClassFlowVo flowObj = getByUpDocClassAndBizType(docClass,bizType,"");
        if(flowObj != null) {
            // 已经找到相应的文件分类的流程配置 --> 再获取流程节点清单
            result = docClassFlowNodeMapper.getNodeList(flowObj.getId(),code);
        }
        return result;
    }
}

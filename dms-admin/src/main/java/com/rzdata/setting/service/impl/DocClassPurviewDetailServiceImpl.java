package com.rzdata.setting.service.impl;

import cn.hutool.core.bean.BeanUtil;
    import com.rzdata.framework.utils.PageUtils;
import com.rzdata.framework.core.page.PagePlus;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.utils.StringUtils;
import org.springframework.stereotype.Service;
import com.rzdata.framework.core.mybatisplus.core.ServicePlusImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.rzdata.setting.domain.bo.DocClassPurviewDetailBo;
import com.rzdata.setting.domain.vo.DocClassPurviewDetailVo;
import com.rzdata.setting.domain.DocClassPurviewDetail;
import com.rzdata.setting.mapper.DocClassPurviewDetailMapper;
import com.rzdata.setting.service.IDocClassPurviewDetailService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 文件类型权限详情Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-12-01
 */
@Service
public class DocClassPurviewDetailServiceImpl extends ServicePlusImpl<DocClassPurviewDetailMapper, DocClassPurviewDetail, DocClassPurviewDetailVo> implements IDocClassPurviewDetailService {

    @Override
    public DocClassPurviewDetailVo queryById(String id){
        return getVoById(id);
    }

    @Override
    public TableDataInfo<DocClassPurviewDetailVo> queryPageList(DocClassPurviewDetailBo bo) {
        PagePlus<DocClassPurviewDetail, DocClassPurviewDetailVo> result = pageVo(PageUtils.buildPagePlus(), buildQueryWrapper(bo));
        return PageUtils.buildDataInfo(result);
    }

    @Override
    public List<DocClassPurviewDetailVo> queryList(DocClassPurviewDetailBo bo) {
        return listVo(buildQueryWrapper(bo));
    }

    private LambdaQueryWrapper<DocClassPurviewDetail> buildQueryWrapper(DocClassPurviewDetailBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<DocClassPurviewDetail> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getPurviewId()), DocClassPurviewDetail::getPurviewId, bo.getPurviewId());
        lqw.eq(StringUtils.isNotBlank(bo.getType()), DocClassPurviewDetail::getType, bo.getType());
        lqw.eq(StringUtils.isNotBlank(bo.getValue()), DocClassPurviewDetail::getValue, bo.getValue());
        return lqw;
    }

    @Override
    public Boolean insertByBo(DocClassPurviewDetailBo bo) {
        DocClassPurviewDetail add = BeanUtil.toBean(bo, DocClassPurviewDetail.class);
        validEntityBeforeSave(add);
        boolean flag = save(add);
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    @Override
    public Boolean updateByBo(DocClassPurviewDetailBo bo) {
        DocClassPurviewDetail update = BeanUtil.toBean(bo, DocClassPurviewDetail.class);
        validEntityBeforeSave(update);
        return updateById(update);
    }

    /**
     * 保存前的数据校验
     *
     * @param entity 实体类数据
     */
    private void validEntityBeforeSave(DocClassPurviewDetail entity){
        //TODO 做一些数据校验,如唯一约束
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return removeByIds(ids);
    }
}

package com.rzdata.setting.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.rzdata.framework.constant.Constants;
import com.rzdata.framework.core.domain.entity.SysRole;
import com.rzdata.framework.core.domain.entity.SysUser;
import com.rzdata.framework.core.domain.model.LoginUser;
import com.rzdata.framework.utils.PageUtils;
import com.rzdata.framework.core.page.PagePlus;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.utils.SecurityUtils;
import com.rzdata.framework.utils.StringUtils;
import com.rzdata.process.domain.Version;
import com.rzdata.process.enums.ApplyTypeEnum;
import com.rzdata.setting.domain.DocClass;
import com.rzdata.setting.domain.DocClassPurviewDetail;
import com.rzdata.setting.service.IDocClassPurviewDetailService;
import com.rzdata.setting.service.IDocClassService;
import com.rzdata.system.service.ISysUserService;
import com.rzdata.system.service.ITenantService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.rzdata.framework.core.mybatisplus.core.ServicePlusImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.rzdata.setting.domain.bo.DocClassPurviewBo;
import com.rzdata.setting.domain.vo.DocClassPurviewVo;
import com.rzdata.setting.domain.DocClassPurview;
import com.rzdata.setting.mapper.DocClassPurviewMapper;
import com.rzdata.setting.service.IDocClassPurviewService;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 文件类型权限Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-12-01
 */
@Service
public class DocClassPurviewServiceImpl extends ServicePlusImpl<DocClassPurviewMapper, DocClassPurview, DocClassPurviewVo> implements IDocClassPurviewService {

    @Autowired
    private IDocClassPurviewDetailService iDocClassPurviewDetailService;

    @Autowired
    private IDocClassService iDocClassService;

    @Override
    public DocClassPurviewVo queryById(String id){
        return getVoById(id);
    }

    @Override
    public TableDataInfo<DocClassPurviewVo> queryPageList(DocClassPurviewBo bo) {
        PagePlus<DocClassPurview, DocClassPurviewVo> result = pageVo(PageUtils.buildPagePlus(), buildQueryWrapper(bo));
        return PageUtils.buildDataInfo(result);
    }

    @Override
    public List<DocClassPurviewVo> queryList(DocClassPurviewBo bo) {
        return listVo(buildQueryWrapper(bo));
    }

    private LambdaQueryWrapper<DocClassPurview> buildQueryWrapper(DocClassPurviewBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<DocClassPurview> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getApplyFlag()), DocClassPurview::getApplyFlag, bo.getApplyFlag());
        lqw.eq(StringUtils.isNotBlank(bo.getOpenFlag()), DocClassPurview::getOpenFlag, bo.getOpenFlag());
        return lqw;
    }

    @Override
    public Boolean insertByBo(DocClassPurviewBo bo) {
        DocClassPurview add = BeanUtil.toBean(bo, DocClassPurview.class);
        validEntityBeforeSave(add);
        boolean flag = save(add);
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateByBo(DocClassPurviewBo bo) {
        DocClassPurview update = BeanUtil.toBean(bo, DocClassPurview.class);
        validEntityBeforeSave(update);
        updateDetailList(bo.getDetailList(),bo.getId());
        return saveOrUpdate(update);
    }

    private void updateDetailList(List<DocClassPurviewDetail> detailList,String purviewId){
        iDocClassPurviewDetailService.remove(new LambdaQueryWrapper<DocClassPurviewDetail>().eq(DocClassPurviewDetail::getPurviewId,purviewId));
        iDocClassPurviewDetailService.saveAll(detailList);
    }

    /**
     * 保存前的数据校验
     *
     * @param entity 实体类数据
     */
    private void validEntityBeforeSave(DocClassPurview entity){
        //TODO 做一些数据校验,如唯一约束
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return removeByIds(ids);
    }

    @Override
    public DocClassPurview getPurviewStatus(String id,String parentClassId) {
        DocClassPurview vo=getById(id);
        if (ObjectUtil.isEmpty(vo)||Constants.VALUE_N.equals(vo.getOpenFlag())) {
            // 没配置或配置为关闭状态 获取父类型状态
           return getParentPurviewStatus(parentClassId);
        }
        return vo;
    }

    private DocClassPurview getParentPurviewStatus(String parentClassId){
        //parentClassId为0 是顶级类型不用查了
        if (!Constants.ZERO.equals(parentClassId)) {
            DocClassPurview vo = getById(parentClassId);
            if (ObjectUtil.isEmpty(vo)||Constants.VALUE_N.equals(vo.getOpenFlag())) {
                DocClass dc = iDocClassService.getOne(new LambdaQueryWrapper<DocClass>()
                        .eq(DocClass::getId,parentClassId)
                        .select(DocClass::getId,DocClass::getParentClassId));
                if(ObjectUtil.isNotEmpty(dc)){
                    return vo;
                }
                return getParentPurviewStatus(dc.getParentClassId());
            }
            return vo;
        }
        return null;
    }

    @Override
    public boolean getPurviewDetailStatus(String purviewId,SysUser user) {
        //公司company
        String [] company = user.getTenantIds();
        List<String> list = new ArrayList<>(Arrays.asList(company));
        //部门dept
        String dept = user.getDept().getDeptId();
        list.add(dept);
        // 角色role
        user.getRoles().forEach(role->{
            list.add(String.valueOf(role.getRoleId()));
        });
        //个人person
        String userName = user.getUserName();
        list.add(userName);
        long count=iDocClassPurviewDetailService.count(new LambdaQueryWrapper<DocClassPurviewDetail>()
                .eq(DocClassPurviewDetail::getPurviewId,purviewId)
                .in(DocClassPurviewDetail::getValue,list));
        return count>0;
    }
}

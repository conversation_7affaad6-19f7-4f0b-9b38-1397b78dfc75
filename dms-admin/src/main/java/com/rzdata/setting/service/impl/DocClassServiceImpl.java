package com.rzdata.setting.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeNodeConfig;
import cn.hutool.core.lang.tree.TreeUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.rzdata.framework.constant.Constants;
import com.rzdata.framework.core.domain.entity.SysDept;
import com.rzdata.framework.core.domain.entity.SysRole;
import com.rzdata.framework.core.domain.entity.SysUser;
import com.rzdata.framework.core.domain.model.LoginUser;
import com.rzdata.framework.core.mybatisplus.core.ServicePlusImpl;
import com.rzdata.framework.core.page.PagePlus;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.exception.ServiceException;
import com.rzdata.framework.utils.PageUtils;
import com.rzdata.framework.utils.SecurityUtils;
import com.rzdata.framework.utils.StringUtils;
import com.rzdata.framework.utils.TreeBuildUtils;
import com.rzdata.process.domain.BasicFile;
import com.rzdata.process.domain.PrintTask;
import com.rzdata.process.domain.Tenant;
import com.rzdata.process.enums.MergeTypeEnum;
import com.rzdata.process.service.IBasicFileService;
import com.rzdata.setting.domain.*;
import com.rzdata.setting.domain.bo.*;
import com.rzdata.setting.domain.vo.*;
import com.rzdata.setting.mapper.DocClassMapper;
import com.rzdata.setting.service.*;
import com.rzdata.system.service.ISysUserService;
import com.rzdata.system.service.ITenantService;
import com.rzdata.system.service.impl.SysDeptServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.rmi.ServerException;
import java.util.*;
import java.util.stream.Collectors;

import static com.rzdata.framework.utils.ValidatorUtils.validate;

/**
 * 文件类型Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-12-27
 */
@Service
@Slf4j
public class DocClassServiceImpl extends ServicePlusImpl<DocClassMapper, DocClass, DocClassVo> implements IDocClassService {

    @Autowired
    IBasicFileService iBasicFileService;

    @Resource
    IDocClassMergeService iDocClassMergeService;

    @Resource
    IDocClassSignatureService iDocClassSignatureService;

    @Resource
    ISignatureService signatureService;

    @Resource
    IDocClassPurviewService iDocClassPurviewService;

    @Autowired
    private ISysUserService userService;

    @Autowired
    private ITenantService iTenantService;

    @Autowired
    IDocClassWatermarkSettingService iDocClassWatermarkSettingService;
    @Autowired
    private SysDeptServiceImpl sysDeptServiceImpl;

    @Autowired
    private ICombinedFileClassService combinedFileClassService;


    @Autowired
    CodraftServiceImpl codraftService;

    @Override
    public DocClassVo queryById(String id) {
        DocClassVo vo = getVoById(id);
        if (ObjectUtil.isNotEmpty(vo)) {
            QueryWrapper<BasicFile> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(BasicFile::getId, vo.getFileId());
            vo.setFileList(iBasicFileService.list(queryWrapper));
            //查出合稿设置
            LambdaQueryWrapper<DocClassMerge> query = new LambdaQueryWrapper<>();
            query.eq(DocClassMerge::getDocClass, vo.getId());
            List<DocClassMergeVo> list = iDocClassMergeService.listVo(query);
            vo.setDocClassMergeVoList(list);

            //查出签章设置
            LambdaQueryWrapper<DocClassSignature> querySignature = new LambdaQueryWrapper<>();
            querySignature.eq(DocClassSignature::getDocClass, vo.getId());
            querySignature.orderByAsc(DocClassSignature::getSort);
            List<DocClassSignatureVo> signatureList = iDocClassSignatureService.listVo(querySignature);
            vo.setDocClassSignatureVoList(signatureList);

        }

        return vo;
    }

    @Override
    public TableDataInfo<DocClassVo> queryPageList(DocClassBo bo) {
        // 是否开启权限过滤，admin用户不进行过滤
        if(SecurityUtils.isAdmin(SecurityUtils.getUserId())){
            PagePlus<DocClass, DocClassVo> result = pageVo(PageUtils.buildPagePlus(), buildQueryWrapper(bo));
            result.getRecordsVo().forEach(item -> {
                item.setPurview(true);
            });
            return PageUtils.buildDataInfo(result);
        }else {
            SysUser user = userService.selectUserById(SecurityUtils.getUserId());
            user.setTenantIds(iTenantService.selectTenantByUser(user.getUserId()).stream().map(Tenant::getId).toArray(String[]::new));
            //公司company
            String [] company = user.getTenantIds();
            List<String> companys = new ArrayList<>(Arrays.asList(company));
            //部门dept
            String dept = user.getDept().getDeptId();
            List<String> depts = new ArrayList<>(Arrays.asList(dept));
            // 角色role
            List<Long> roles = user.getRoles().stream().map(SysRole::getRoleId).collect(Collectors.toList());
            //个人person
            String userName = user.getUserName();
            List<String> persons = new ArrayList<>(Arrays.asList(userName));

            List result = this.getClassByPerm(companys,depts,roles,persons, buildQueryWrapper(bo));

            return PageUtils.buildDataInfo(result);
        }
    }

    @Override
    public List<DocClassVo> queryChildList(String docClass) {
        List<DocClassVo> list = new ArrayList<>();
        if (StringUtils.isNotEmpty(docClass)) {
            List<DocClassVo> childList = listVo(new LambdaQueryWrapper<DocClass>().eq(DocClass::getParentClassId,docClass).eq(DocClass::getClassStatus, Constants.ONE));
            for (DocClassVo vo:childList) {
                list.addAll(queryChildList(vo.getId()));
            }
            list.addAll(childList);
        }
        return list;
    }

    @Override
    public List<Tree<String>> buildTreeSelect(List<DocClassVo> docClassList) {
        //配置
        TreeNodeConfig treeNodeConfig = new TreeNodeConfig();
        // 自定义属性名 都要默认值的
        treeNodeConfig.setNameKey("label");
        List<Tree<String>> treeNodes = TreeUtil.build(docClassList, "0", treeNodeConfig,
                (treeNode, tree) -> {
                    tree.setId(treeNode.getId());
                    tree.setParentId(treeNode.getParentClassId());
                    tree.setWeight(treeNode.getClassLevel());
                    tree.setName(treeNode.getClassName());
                    // 扩展属性 ...
                    tree.putExtra("classStatus", treeNode.getClassStatus());
                    tree.putExtra("classLevel", treeNode.getClassLevel());
                    tree.putExtra("expiration", treeNode.getExpiration());
                    tree.putExtra("reviewCycle", treeNode.getReviewCycle());
                    tree.putExtra("checkType", treeNode.getCheckType());
                    tree.putExtra("encryptType", treeNode.getEncryptType());
                    tree.putExtra("yNPrint", treeNode.getYNPrint());
                    tree.putExtra("yNRecyle", treeNode.getYNRecyle());
                    tree.putExtra("mergeDocConfig", treeNode.getMergeDocConfig());
                    tree.putExtra("codeId", treeNode.getCodeId());
                    tree.putExtra("fileId", treeNode.getFileId());
                    tree.putExtra("createTime", treeNode.getCreateTime());
                    tree.putExtra("updateTime", treeNode.getUpdateTime());
                });
        return treeNodes;
    }

    @Override
    public List<DocClassVo> queryList(DocClassBo bo) {
        return listVo(buildQueryWrapper(bo,false).orderByAsc(DocClass::getAncestors).orderByAsc(DocClass::getSort));
    }
    private LambdaQueryWrapper<DocClass> buildQueryWrapper(DocClassBo bo){
        return buildQueryWrapper(bo, true);
    }

    private LambdaQueryWrapper<DocClass> buildQueryWrapper(DocClassBo bo,Boolean sort) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<DocClass> lqw = Wrappers.lambdaQuery();
        //文件类型
        lqw.eq(StringUtils.isNotBlank(bo.getId()), DocClass::getId, bo.getId());
        lqw.like(StringUtils.isNotBlank(bo.getClassName()), DocClass::getClassName, bo.getClassName());
        lqw.eq(StringUtils.isNotBlank(bo.getParentClassId()), DocClass::getParentClassId, bo.getParentClassId());
        lqw.eq(StringUtils.isNotBlank(bo.getClassStatus()), DocClass::getClassStatus, bo.getClassStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getClassLevel()), DocClass::getClassLevel, bo.getClassLevel());
        lqw.eq(bo.getExpiration() != null, DocClass::getExpiration, bo.getExpiration());
        lqw.eq(bo.getReviewCycle() != null, DocClass::getReviewCycle, bo.getReviewCycle());
        lqw.eq(StringUtils.isNotBlank(bo.getCheckType()), DocClass::getCheckType, bo.getCheckType());
        lqw.eq(StringUtils.isNotBlank(bo.getEncryptType()), DocClass::getEncryptType, bo.getEncryptType());
        lqw.eq(StringUtils.isNotBlank(bo.getYNPrint()), DocClass::getYNPrint, bo.getYNPrint());
        lqw.eq(StringUtils.isNotBlank(bo.getYNRecyle()), DocClass::getYNRecyle, bo.getYNRecyle());
        lqw.eq(StringUtils.isNotBlank(bo.getMergeDocConfig()), DocClass::getMergeDocConfig, bo.getMergeDocConfig());
        lqw.eq(StringUtils.isNotBlank(bo.getCodeId()), DocClass::getCodeId, bo.getCodeId());
        lqw.eq(StringUtils.isNotBlank(bo.getFileId()), DocClass::getFileId, bo.getFileId());
        lqw.eq(StringUtils.isNotBlank(bo.getDataType()), DocClass::getDataType, bo.getDataType());
        lqw.eq(StringUtils.isNotBlank(bo.getClassType()), DocClass::getClassType, bo.getClassType());
        lqw.in(ObjectUtil.isNotEmpty(bo.getClassTypeList()), DocClass::getClassType, bo.getClassTypeList());
        lqw.eq(StringUtils.isNotBlank(bo.getClassCode()), DocClass::getClassCode, bo.getClassCode());
        lqw.eq(StringUtils.isNotBlank(bo.getIsFileType()), DocClass::getIsFileType, bo.getIsFileType());
        lqw.ne(StringUtils.isNotBlank(bo.getNeClassType()), DocClass::getClassType, bo.getNeClassType());
        lqw.ne(StringUtils.isNotBlank(bo.getNeClassLevel()), DocClass::getClassLevel, bo.getNeClassLevel());
        lqw.apply(StringUtils.isNotBlank(bo.getParentId()),"find_in_set({0}, ancestors)", bo.getParentId());
        if(StringUtils.isNotEmpty(bo.getDown()) && bo.getDown()){
            lqw.apply(StringUtils.isNotBlank(bo.getClassId()),"find_in_set({0}, ancestors)", bo.getClassId());
        }else{
            lqw.eq(StringUtils.isNotBlank(bo.getClassId()), DocClass::getId, bo.getClassId());
        }
        /*if(sort){*/
            lqw.orderByAsc(DocClass::getSort).orderByAsc(DocClass::getAncestors);
        /*}*/
        return lqw;
    }

    private String getAncestors(String id){
        DocClass docClass = getOne(new LambdaQueryWrapper<DocClass>().eq(DocClass::getId,id).select(DocClass::getAncestors,DocClass::getId));
        return docClass!=null&&ObjectUtil.isNotEmpty(docClass.getAncestors())?docClass.getAncestors():"0";
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String insertByBo(DocClassBo bo) throws ServerException {
        DocClass add = BeanUtil.toBean(bo, DocClass.class);
        add.setId(IdWorker.getIdStr(add));
        add.setAncestors(getAncestors(add.getParentClassId()) + "," + add.getId());
        validEntityBeforeSave(add);
        add.setCreateTime(new Date());
        boolean flag = save(add);
        if (flag) {
            bo.setId(add.getId() + "");
        }
        String docClass = bo.getId();
        if (ObjectUtil.isNotEmpty(bo.getDocClassSignatureBoList())) {
            iDocClassSignatureService.insertOrUpdateByBoList(bo.getDocClassSignatureBoList());
        }else {
            //没有值说明是新增 设置默认值
            LambdaQueryWrapper<Signature> querySignature = new LambdaQueryWrapper<>();
            querySignature.eq(Signature::getDeleteFlag, 0);
            querySignature.orderByDesc(Signature::getCreateTime);
            List<SignatureVo> signatureVoList = signatureService.listVo(querySignature);

            String signatureId = signatureVoList.get(0).getId();
            List<DocClassSignatureBo> defaultList = new ArrayList<>();
            getSignatureDefault("公司章",docClass,signatureId,1,1,defaultList);
            getSignatureDefault("部门章",docClass,signatureId,1,2,defaultList);
            getSignatureDefault("文件生效章",docClass,signatureId,1,3,defaultList);
            getSignatureDefault("文件失效章",docClass,signatureId,1,4,defaultList);
            iDocClassSignatureService.insertOrUpdateByBoList(defaultList);
        }
        // 初始化水印信息
        iDocClassWatermarkSettingService.initWatermarkSetting(bo.getId());
        return add.getId() + "";
    }

    private void getDefault(String mergeFactorName, String docClass, String codraftId, Integer anotherPage, Integer range, Integer mergeFactor, MergeTypeEnum type, List<DocClassMergeBo> defaultList) {
        Integer defaultFlag = 1;
        //正文
        DocClassMergeBo text = new DocClassMergeBo();
        //.id(null)
        text.setMergeFactorName(mergeFactorName);
        text.setMergeFactorUse(1);
        text.setCodraftId(codraftId);
        text.setAnotherPage(anotherPage);
        text.setRangeValue(range);
        text.setType(type);
        text.setMergeFactor(mergeFactor);
        text.setDocClass(docClass);
        text.setCreateTime(new Date());
        text.setDefaultFlag(defaultFlag);
        defaultList.add(text);
    }

    private void getSignatureDefault(String signatureName, String docClass,String signatureId ,Integer range,Integer sort,List<DocClassSignatureBo> defaultList ){
        //正文
        DocClassSignatureBo signatureBo = new DocClassSignatureBo();
        signatureBo.setSort(sort);
        signatureBo.setDocClass(docClass);
        signatureBo.setSignatureFactorName(signatureName);
        signatureBo.setSignatureFactorUse(1);
        signatureBo.setSignatureId(signatureId);
        signatureBo.setRangeValue(range);
        signatureBo.setCreateTime(new Date());
        signatureBo.setDefaultFlag(1);
        defaultList.add(signatureBo);
    }
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateByBo(DocClassBo bo) {
        updateChildrenAncestors(bo);
        DocClass update = BeanUtil.toBean(bo, DocClass.class);
        update.setUpdateTime(new Date());
        validEntityBeforeSave(update);
        /*if (ObjectUtil.isNotEmpty(bo.getDocClassMergeBoList())) {
            iDocClassMergeService.insertOrUpdateByBoList(bo.getDocClassMergeBoList());
        }*/
        if (ObjectUtil.isNotEmpty(bo.getDocClassSignatureBoList())) {
            iDocClassSignatureService.insertOrUpdateByBoList(bo.getDocClassSignatureBoList());
        }
        //合稿逻辑处理
        if (ObjectUtil.isNotEmpty(bo.getCombinedMgrId())) {
            //先处理中间表
            CombinedFileClassBo combinedFileClassBo = new CombinedFileClassBo();
            combinedFileClassBo.setFileClassId(bo.getId());
            List<CombinedFileClassVo> combinedFileClassVos = combinedFileClassService.queryList(combinedFileClassBo);
            if (ObjectUtil.isNotEmpty(combinedFileClassVos)) {
                CombinedFileClassVo combinedFileClassVo = combinedFileClassVos.get(0);
                //如果合稿id不同则修改合稿id
                if(!combinedFileClassVo.getCombinedMgrId().equals(bo.getCombinedMgrId())){
                    CombinedFileClass combinedFileClass = new CombinedFileClass();
                    combinedFileClass.setId(combinedFileClassVo.getId());
                    combinedFileClass.setCombinedMgrId(bo.getCombinedMgrId());
                    combinedFileClass.setUpdateTime(new Date());
                    combinedFileClass.setUpdateBy(SecurityUtils.getLoginUser().getUsername());
                    combinedFileClassService.updateById(combinedFileClass);
                }
            }else{
                CombinedFileClass combinedFileClass = new CombinedFileClass();
                combinedFileClass.setFileClassId(bo.getId());
                combinedFileClass.setCombinedMgrId(bo.getCombinedMgrId());
                combinedFileClass.setUpdateTime(new Date());
                combinedFileClass.setUpdateBy(SecurityUtils.getLoginUser().getUsername());
                combinedFileClassService.save(combinedFileClass);
            }
        }
        return updateById(update);
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateChildrenAncestors(DocClassBo bo){
        if (StringUtils.isNotEmpty(bo.getParentClassId())) {
            String newAncestors = getAncestors(bo.getParentClassId()) + "," + bo.getId();
            List<DocClass> children = list(new LambdaQueryWrapper<DocClass>()
                    .apply("find_in_set({0},ancestors)", bo.getId()).select(DocClass::getId,DocClass::getAncestors));
            for (DocClass child : children) {
                child.setAncestors(child.getAncestors().replaceFirst(bo.getAncestors(), newAncestors));
            }
            if (children.size() > 0) {
                updateBatchById(children);
            }
            bo.setAncestors(newAncestors);
        }
    }
    /**
     * 保存前的数据校验
     *
     * @param entity 实体类数据
     */
    private void validEntityBeforeSave(DocClass entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        LambdaQueryWrapper<DocClassMerge> queryWrapper = new LambdaQueryWrapper<>();
        LambdaQueryWrapper<DocClassSignature> lambdaQueryWrapper = new LambdaQueryWrapper<>();

        ids.forEach(id->{
            queryWrapper.eq(DocClassMerge::getDocClass,id);
            lambdaQueryWrapper.eq(DocClassSignature::getDocClass,id);
            iDocClassMergeService.remove(queryWrapper);
            iDocClassSignatureService.remove(lambdaQueryWrapper);
        });
        return removeByIds(ids);
    }

    @Override
    public void revDisable(String id, String status) {
        DocClass docClass = new DocClass();
        docClass.setId(id);
        docClass.setUpdateTime(new Date());
        docClass.setClassStatus(status);
        this.baseMapper.updateById(docClass);
    }


    /**
     * 向上迭代构造路径（反向的：子找父）
     *
     * @param classId
     * @return
     */
    private String recursionUpPath(String classId) {
        String result = "";
        DocClass docClassObj = this.getById(classId);
        result = docClassObj.getClassName();
        if(!docClassObj.getParentClassId().equals("0")) {
            result += "/"+this.getClassFullPath(docClassObj.getParentClassId());
        }
        return result;
    }

    /**
     * 获取分类的全路径（正向的）
     *
     * @param classId
     * @return
     */
    @Override
    public String getClassFullPath(String classId) {
        if(StringUtils.isEmpty(classId)) {
            return "";
        }
        String result = "";
        String path = this.recursionUpPath(classId);
        if(StringUtils.isNotEmpty(path)) {
            String[] array = path.split("/");
            if(array.length == 1) {
                result = array[0];
            } else {
                for(int i= array.length-1;i>=0;i--) {
                    if(StringUtils.isNotEmpty(result)) {
                        result += "/";
                    }
                    result += array[i];
                }
            }
        }
        return result;
    }

    /**
     * 获取该分类的文件类型（对应的顶级分类对象）
     *
     * @param classId
     * @return
     */
    @Override
    public DocClass getClassType(String classId) {
        DocClass result = null;
        DocClass docClassObj = this.getById(classId);
        result = docClassObj;
        if(!docClassObj.getParentClassId().equals("0")) {
            // 非顶级节点，继续往上找
            result = this.getClassType(docClassObj.getParentClassId());
        }
        return result;
    }

    /**
     *获取相应层级的父节点文件类型
     * @param docClass
     * @return
     */
    @Override
    public String getPDocClass(String docClass, String lv) {
        DocClass docClassObj = getOne(new LambdaQueryWrapper<DocClass>().eq(DocClass::getId,docClass)
                .select(DocClass::getId,DocClass::getParentClassId,DocClass::getClassLevel));
        if (lv.equals(docClassObj.getClassLevel())) {
            return docClassObj.getId();
        }else {
            return getPDocClass(docClassObj.getParentClassId(),lv);
        }
    }

    @Override
    public List<String> getClassByParentId(String parentId) {
        QueryWrapper<DocClass> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(DocClass:: getParentClassId, parentId);
        return this.list(queryWrapper).stream().map(DocClass::getId).collect(Collectors.toList());
    }

    @Override
    public void initAncestors(){
        List<DocClass> list = list();
        List<Tree<String>> treeList= TreeBuildUtils.build(list, (dept, tree) ->
                tree.setId(dept.getId())
                        .setParentId(dept.getParentClassId())
                        .setName(dept.getClassName())
                        .setWeight(dept.getSort()));
        for (Tree<String> tree:treeList) {
            updateAncestors(TreeUtil.getParentsId(tree,true),tree.getId());
            if (ObjectUtil.isNotEmpty(tree.getChildren())) {
              setChildrenAncestors(tree.getChildren());
            }
        }
    }

    @Override
    public String importData(List<DocClass> docList, boolean updateSupport, String dataType) {
        if (StringUtils.isNull(docList) || docList.size() == 0) {
            throw new ServiceException("导入数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        List<DocClass> allList = this.list();
        for (DocClass docClass : docList) {
            try {
                docClass.setDataType(dataType);
                DocClassImportBo docClassImportBo = BeanUtil.copyProperties(docClass, DocClassImportBo.class);
                validate(docClassImportBo, AddGroup.class);
                // 验证是否存在这条记录
                if (allList.stream().noneMatch(d -> d.getId().equals(docClass.getId()))) {
                    this.save(docClass);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、文件分类 " + docClass.getClassName() + " 导入成功");
                } else if (updateSupport) {
                    this.updateById(docClass);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、文件分类 " + docClass.getClassName() + " 更新成功");
                } else {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、文件分类 " + docClass.getClassName() + " 已存在");
                }
            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、文件分类 " + docClass.getClassName() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }

    private void setChildrenAncestors(List<Tree<String>> list){
        for (Tree<String> tree:list) {
            updateAncestors(TreeUtil.getParentsId(tree,true),tree.getId());
            if (ObjectUtil.isNotEmpty(tree.getChildren())) {
                setChildrenAncestors(tree.getChildren());
            }
        }
    }

    private void updateAncestors(List<String> ancestors,String id){
        Collections.reverse(ancestors);
        update(new LambdaUpdateWrapper<DocClass>().set(DocClass::getAncestors, String.join(",", ancestors)).set(DocClass::getClassLevel,ancestors.size()-1).eq(DocClass::getId,id));
    }

    public List<DocClassVo> getClassByPerm(List<String> companyIds, List<String> deptIds, List<Long> roleIds, List<String> person, LambdaQueryWrapper<DocClass> wrapper) {
        return baseMapper.getClassByPerm(companyIds, deptIds, roleIds, person, wrapper);
    }

    @Override
    public String getClassName(String id) {
        if (StringUtils.isNotEmpty(id)) {
            DocClassVo docClass= getVoOne(new LambdaQueryWrapper<DocClass>().eq(DocClass::getId,id).select(DocClass::getClassName));
            return docClass!=null?docClass.getClassName():null;
        }else {
            return null;
        }
    }
}

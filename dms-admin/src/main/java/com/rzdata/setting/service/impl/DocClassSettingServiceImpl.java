package com.rzdata.setting.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.rzdata.framework.constant.Constants;
import com.rzdata.framework.utils.PageUtils;
import com.rzdata.framework.core.page.PagePlus;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.utils.StringUtils;
import com.rzdata.setting.domain.DocClass;
import com.rzdata.setting.domain.DocClassPurview;
import com.rzdata.setting.service.IDocClassService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.rzdata.framework.core.mybatisplus.core.ServicePlusImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.rzdata.setting.domain.bo.DocClassSettingBo;
import com.rzdata.setting.domain.vo.DocClassSettingVo;
import com.rzdata.setting.domain.DocClassSetting;
import com.rzdata.setting.mapper.DocClassSettingMapper;
import com.rzdata.setting.service.IDocClassSettingService;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 文件类型设置Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-12-28
 */
@Service
public class DocClassSettingServiceImpl extends ServicePlusImpl<DocClassSettingMapper, DocClassSetting, DocClassSettingVo> implements IDocClassSettingService {

    @Autowired
    private IDocClassService iDocClassService;

    @Override
    public DocClassSettingVo queryById(String id){
        return getVoById(id);
    }

    @Override
    public TableDataInfo<DocClassSettingVo> queryPageList(DocClassSettingBo bo) {
        PagePlus<DocClassSetting, DocClassSettingVo> result = pageVo(PageUtils.buildPagePlus(), buildQueryWrapper(bo));
        return PageUtils.buildDataInfo(result);
    }

    @Override
    public DocClassSettingVo queryInfo(DocClassSettingBo bo) {
        return getVoOne(buildQueryWrapper(bo));
    }

    @Override
    public List<DocClassSettingVo> queryList(DocClassSettingBo bo) {
        return listVo(buildQueryWrapper(bo));
    }

    private LambdaQueryWrapper<DocClassSetting> buildQueryWrapper(DocClassSettingBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<DocClassSetting> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getDocClass()), DocClassSetting::getDocClass, bo.getDocClass());
        lqw.eq(StringUtils.isNotBlank(bo.getType()), DocClassSetting::getType, bo.getType());
        lqw.eq(StringUtils.isNotBlank(bo.getApplyFlag()), DocClassSetting::getApplyFlag, bo.getApplyFlag());
        lqw.eq(StringUtils.isNotBlank(bo.getOpenFlag()), DocClassSetting::getOpenFlag, bo.getOpenFlag());
        return lqw;
    }

    @Override
    public Boolean insertByBo(DocClassSettingBo bo) {
        DocClassSetting add = BeanUtil.toBean(bo, DocClassSetting.class);
        validEntityBeforeSave(add);
        boolean flag = save(add);
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String updateByBo(DocClassSettingBo bo) {
        DocClassSetting update = BeanUtil.toBean(bo, DocClassSetting.class);
        validEntityBeforeSave(update);
        saveOrUpdate(update);
        return update.getId();
    }

    /**
     * 保存前的数据校验
     *
     * @param entity 实体类数据
     */
    private void validEntityBeforeSave(DocClassSetting entity){
        //TODO 做一些数据校验,如唯一约束
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return removeByIds(ids);
    }

    @Override
    public DocClassSetting getSettingStatus(String docClass, String type,String applyFlag) {
        DocClassSetting vo=getOne(new LambdaQueryWrapper<DocClassSetting>()
                .eq(DocClassSetting::getDocClass,docClass)
                .eq(DocClassSetting::getType,type)
                .eq(StringUtils.isNotEmpty(applyFlag),DocClassSetting::getApplyFlag,applyFlag),false);
        if (StringUtils.isNotEmpty(docClass)&&!Constants.ZERO.equals(docClass)) {
            //parentClassId为0是顶级类型不用查了
            if (ObjectUtil.isEmpty(vo)|| Constants.VALUE_N.equals(vo.getOpenFlag())) {
                // 没配置或配置为关闭状态 获取父类型状态
                DocClass dc = iDocClassService.getOne(new LambdaQueryWrapper<DocClass>()
                        .eq(DocClass::getId,docClass)
                        .select(DocClass::getId,DocClass::getParentClassId));
                return getSettingStatus(dc.getParentClassId(),type,Constants.VALUE_Y);
            }
        }
        return vo;
    }
}

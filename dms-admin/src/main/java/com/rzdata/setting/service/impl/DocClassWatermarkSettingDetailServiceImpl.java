package com.rzdata.setting.service.impl;

import com.rzdata.framework.core.mybatisplus.core.ServicePlusImpl;
import com.rzdata.setting.domain.DocClassWatermarkSettingDetail;
import com.rzdata.setting.domain.vo.DocClassWatermarkSettingDetailVo;
import com.rzdata.setting.mapper.DocClassWatermarkSettingDetailMapper;
import com.rzdata.setting.service.IDocClassWatermarkSettingDetailService;
import org.springframework.stereotype.Service;

/**
 * 文件水印设置明细Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-04-29
 */
@Service
public class DocClassWatermarkSettingDetailServiceImpl extends ServicePlusImpl<DocClassWatermarkSettingDetailMapper, DocClassWatermarkSettingDetail, DocClassWatermarkSettingDetailVo> implements IDocClassWatermarkSettingDetailService {

}


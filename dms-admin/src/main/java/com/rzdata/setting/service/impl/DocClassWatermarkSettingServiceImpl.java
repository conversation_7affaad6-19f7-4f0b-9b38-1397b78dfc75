package com.rzdata.setting.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.rzdata.framework.constant.Constants;
import com.rzdata.framework.core.domain.entity.SysDictData;
import com.rzdata.framework.core.mybatisplus.core.ServicePlusImpl;
import com.rzdata.framework.utils.SecurityUtils;
import com.rzdata.setting.domain.*;
import com.rzdata.setting.domain.bo.DocClassWatermarkSettingBo;
import com.rzdata.setting.domain.bo.DocClassWatermarkSettingDetailBo;
import com.rzdata.setting.domain.vo.DocClassWatermarkSettingVo;
import com.rzdata.setting.mapper.DocClassMapper;
import com.rzdata.setting.mapper.DocClassWatermarkSettingMapper;
import com.rzdata.setting.service.IDocClassWatermarkSettingDetailService;
import com.rzdata.setting.service.IDocClassWatermarkSettingService;
import com.rzdata.system.service.ISysDictDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.rmi.ServerException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 文件水印设置Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-04-29
 */
@Service
public class DocClassWatermarkSettingServiceImpl extends ServicePlusImpl<DocClassWatermarkSettingMapper, DocClassWatermarkSetting, DocClassWatermarkSettingVo> implements IDocClassWatermarkSettingService {

    @Autowired
    ISysDictDataService iSysDictDataService;

    @Autowired
    IDocClassWatermarkSettingDetailService docClassWatermarkSettingDetailService;

    @Autowired
    DocClassMapper docClassMapper;


    @Override
    public void initWatermarkSetting(String id) throws ServerException {
        List<String> bizTypeList = Arrays.asList("ADD", "UPDATE", "DISUSE", "PRINT");
        List<DocClassWatermarkSetting> docClassWatermarkSettingList = new ArrayList<>();
        List<DocClassWatermarkSettingDetail> docClassWatermarkSettingDetailList = new ArrayList<>();
        for (String bizType : bizTypeList) {
            DocClassWatermarkSetting isData = this.getOne(new LambdaQueryWrapper<DocClassWatermarkSetting>().eq(DocClassWatermarkSetting::getDocClass,id).eq(DocClassWatermarkSetting::getBizType,bizType));
            if(ObjectUtil.isNotEmpty(isData)){
                continue;
            }
            String uuid = IdUtil.simpleUUID();
            DocClassWatermarkSetting docClassWatermarkSetting = new DocClassWatermarkSetting();
            docClassWatermarkSetting.setId(uuid);
            docClassWatermarkSetting.setDocClass(id);
            docClassWatermarkSetting.setBizType(bizType);
            docClassWatermarkSetting.setOpenFlag(Constants.VALUE_N);
            docClassWatermarkSetting.setApplyFlag(Constants.VALUE_N);
            docClassWatermarkSetting.setCreateTime(new Date());
            docClassWatermarkSetting.setCreateBy(SecurityUtils.getLoginUser().getUsername());
            docClassWatermarkSettingList.add(docClassWatermarkSetting);
            List<SysDictData> sysDictDataList = iSysDictDataService.list(new LambdaQueryWrapper<SysDictData>().eq(SysDictData::getDictType,Constants.WATERMARK_TYPE_KEY));
            if(!sysDictDataList.isEmpty()){
                int sort = 1;
                for (SysDictData sysDictData:sysDictDataList) {
                    DocClassWatermarkSettingDetail docClassWatermarkSettingDetail = new DocClassWatermarkSettingDetail();
                    docClassWatermarkSettingDetail.setBizId(uuid);
                    // 水印类型，字典中带image是图片，否则是文本
                    docClassWatermarkSettingDetail.setWatermarkSettingType(sysDictData.getDictValue().contains(Constants.IMAGE)?Constants.IMAGE:Constants.TEXT);
                    docClassWatermarkSettingDetail.setWatermarkSettingName(sysDictData.getDictLabel());
                    docClassWatermarkSettingDetail.setWatermarkSettingCode(sysDictData.getDictValue());
                    docClassWatermarkSettingDetail.setWatermarkSettingFactorUse(2);
                    docClassWatermarkSettingDetail.setCreateTime(new Date());
                    docClassWatermarkSettingDetail.setSort(sort++);
                    docClassWatermarkSettingDetailList.add(docClassWatermarkSettingDetail);
                }
            }
        }
        this.saveBatch(docClassWatermarkSettingList);
        docClassWatermarkSettingDetailService.saveBatch(docClassWatermarkSettingDetailList);
    }

    @Override
    public List<DocClassWatermarkSettingVo> assemblyDetailData(List<DocClassWatermarkSettingVo> voList) {
        for (DocClassWatermarkSettingVo vo:voList) {
            vo.setNodeList(docClassWatermarkSettingDetailService.listVo(new LambdaQueryWrapper<>(new DocClassWatermarkSettingDetail().setBizId(vo.getId())).orderByAsc(DocClassWatermarkSettingDetail::getSort)));
        }
        return voList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void savaWatermarkSetting(List<DocClassWatermarkSettingBo> list) {
        String docClass = "";
        if(list != null) {
            docClass = list.get(0).getDocClass();
        }
        // 先根据文件分类进行物理删除相关表数据
        List<DocClassWatermarkSetting> oldList = this.list(new QueryWrapper<>(new DocClassWatermarkSetting().setDocClass(docClass)));
        // 拿到明细表List的ID集合
        List<String> ids = oldList.stream().map(DocClassWatermarkSetting::getId).collect(Collectors.toList());
        // 根据明细表ID集合批量删除明细表数据（basic_doc_class_watermark_setting_detail）
        if(!ids.isEmpty()){
            docClassWatermarkSettingDetailService.remove(new LambdaQueryWrapper<DocClassWatermarkSettingDetail>().in(DocClassWatermarkSettingDetail::getBizId,ids));
        }
        // 删除主表数据（basic_doc_class_watermark_setting）
        this.remove(new LambdaQueryWrapper<DocClassWatermarkSetting>().eq(DocClassWatermarkSetting::getDocClass,docClass));
        // 保存相关表数据
        List<DocClassWatermarkSetting> docClassWatermarkSettingList = new ArrayList<>();
        List<DocClassWatermarkSettingDetail> docClassWatermarkSettingDetailList = new ArrayList<>();

        for (DocClassWatermarkSettingBo bo:list) {
            DocClassWatermarkSetting docClassWatermarkSetting = BeanUtil.toBean(bo, DocClassWatermarkSetting.class);
            String uuid = IdUtil.simpleUUID();
            docClassWatermarkSetting.setId(uuid);
            docClassWatermarkSetting.setCreateTime(new Date());
            docClassWatermarkSetting.setUpdateTime(new Date());
            docClassWatermarkSetting.setCreateBy(SecurityUtils.getLoginUser().getUsername());
            docClassWatermarkSetting.setUpdateBy(SecurityUtils.getLoginUser().getUsername());
            docClassWatermarkSettingList.add(docClassWatermarkSetting);
            int sort = 1;
            for (DocClassWatermarkSettingDetailBo detailBo:bo.getNodeList()) {
                DocClassWatermarkSettingDetail docClassWatermarkSettingDetail = BeanUtil.toBean(detailBo, DocClassWatermarkSettingDetail.class);
                docClassWatermarkSettingDetail.setBizId(uuid);
                docClassWatermarkSettingDetail.setBizId(docClassWatermarkSetting.getId());
                docClassWatermarkSettingDetail.setCreateTime(new Date());
                docClassWatermarkSettingDetail.setUpdateTime(new Date());
                docClassWatermarkSettingDetail.setSort(sort++);
                docClassWatermarkSettingDetailList.add(docClassWatermarkSettingDetail);
            }
        }
        this.saveBatch(docClassWatermarkSettingList);
        docClassWatermarkSettingDetailService.saveBatch(docClassWatermarkSettingDetailList);
    }

    /**
     * 根据文件分类ID，类型，查询文件水印设置功能，如果没有则向上查找
     * @param docClassId
     * @param bizType
     * @return
     */
    @Override
    public DocClassWatermarkSetting getWatermarkSettingByDocClassAndType(String docClassId, String bizType, Boolean isFirst) {
        if(ObjectUtil.isEmpty(docClassId) || ObjectUtil.isEmpty(bizType)){
            return null;
        }
        DocClass docClass = docClassMapper.selectById(docClassId);
        if(ObjectUtil.isEmpty(docClass)){
            return null;
        }
        Boolean isExist = false;
        DocClassWatermarkSetting docClassWatermarkSetting = this.getOne(new LambdaQueryWrapper<DocClassWatermarkSetting>().eq(DocClassWatermarkSetting::getDocClass,docClassId).eq(DocClassWatermarkSetting::getBizType,bizType));
        if(ObjectUtil.isNotEmpty(docClassWatermarkSetting)){
            if(isFirst){
                if(Constants.VALUE_Y.equals(docClassWatermarkSetting.getOpenFlag())){
                    isExist = true;
                }
            }else{
                if(Constants.VALUE_Y.equals(docClassWatermarkSetting.getOpenFlag()) && Constants.VALUE_Y.equals(docClassWatermarkSetting.getApplyFlag())){
                    isExist = true;
                }
            }
        }
        if(isExist){
            return docClassWatermarkSetting;
        }else{
            return getWatermarkSettingByDocClassAndType(docClass.getParentClassId(),bizType,false);
        }
    }

    /**
     * 根据条件查询水印设置明细
     * @param docClassId---分类ID
     * @param bizType---业务类型;文件新增add_doc、文件修订update_doc、文件作废disuse_doc、文件打印print_doc
     * @param isFirst---首次进入，这里默认传true
     * @param appliedRange---应用范围：（主文件：main，版本附件：attach，都支持：all）
     * @return
     */
    @Override
    public List<DocClassWatermarkSettingDetail> getDocClassWatermarkSettingDetailList(String docClassId, String bizType, Boolean isFirst, String appliedRange) {
        DocClassWatermarkSetting docClassWatermarkSetting = this.getWatermarkSettingByDocClassAndType(docClassId, bizType, true);
        List<DocClassWatermarkSettingDetail> docClassWatermarkSettingDetailList = new ArrayList<>();
        if(ObjectUtil.isNotEmpty(docClassWatermarkSetting)){
            docClassWatermarkSettingDetailList = docClassWatermarkSettingDetailService.list(new LambdaQueryWrapper<DocClassWatermarkSettingDetail>()
                    .eq(DocClassWatermarkSettingDetail::getBizId, docClassWatermarkSetting.getId())
                    .in(DocClassWatermarkSettingDetail::getAppliedRange,"all",appliedRange)
                    .eq(DocClassWatermarkSettingDetail::getWatermarkSettingFactorUse,1));
        }
        return docClassWatermarkSettingDetailList;
    }
}

package com.rzdata.setting.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.rzdata.framework.constant.Constants;
import com.rzdata.framework.core.mybatisplus.core.ServicePlusImpl;
import com.rzdata.framework.core.page.PagePlus;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.utils.PageUtils;
import com.rzdata.framework.utils.StringUtils;
import com.rzdata.process.domain.vo.DocNoVo;
import com.rzdata.process.enums.LinkTypeEnum;
import com.rzdata.process.service.IGenerateIdService;
import com.rzdata.setting.domain.DocClass;
import com.rzdata.setting.domain.DocPrepareId;
import com.rzdata.setting.domain.bo.DocPrepareIdBo;
import com.rzdata.setting.domain.vo.DocPrepareIdVo;
import com.rzdata.setting.mapper.DocPrepareIdMapper;
import com.rzdata.setting.service.IDocClassService;
import com.rzdata.setting.service.IDocPrepareIdService;
import com.rzdata.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 预制编号Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-10-07
 */
@Service
public class DocPrepareIdServiceImpl extends ServicePlusImpl<DocPrepareIdMapper, DocPrepareId, DocPrepareIdVo> implements IDocPrepareIdService {
    @Autowired
    private IGenerateIdService iGenerateIdService;
    @Autowired
    private ISysUserService iSysUserService;
    @Autowired
    private IDocClassService iDocClassService;
    @Override
    public DocPrepareIdVo queryById(String docId){
        return getVoById(docId);
    }

    @Override
    public TableDataInfo<DocPrepareIdVo> queryPageList(DocPrepareIdBo bo) {
        PagePlus<DocPrepareId, DocPrepareIdVo> result = pageVo(PageUtils.buildPagePlus(), buildQueryWrapper(bo));
        result.getRecordsVo().forEach(item->{
            item.setApplyNickName(iSysUserService.getNickName(item.getApplyBy()));
        });
        return PageUtils.buildDataInfo(result);
    }

    @Override
    public List<DocPrepareIdVo> queryList(DocPrepareIdBo bo) {
        return listVo(buildQueryWrapper(bo));
    }

    private LambdaQueryWrapper<DocPrepareId> buildQueryWrapper(DocPrepareIdBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<DocPrepareId> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getDocId()), DocPrepareId::getDocId, bo.getDocId());
        lqw.eq(StringUtils.isNotBlank(bo.getCodeType()), DocPrepareId::getCodeType, bo.getCodeType());
        lqw.eq(StringUtils.isNotBlank(bo.getDataType()), DocPrepareId::getDataType, bo.getDataType());
        lqw.eq(StringUtils.isNotBlank(bo.getDocClass()), DocPrepareId::getDocClass, bo.getDocClass());
        lqw.eq(StringUtils.isNotBlank(bo.getParentDocId()), DocPrepareId::getParentDocId, bo.getParentDocId());
        lqw.eq(StringUtils.isNotBlank(bo.getApplyBy()), DocPrepareId::getApplyBy, bo.getApplyBy());
        lqw.eq(StringUtils.isNotBlank(bo.getUseStatus()), DocPrepareId::getUseStatus, bo.getUseStatus());
        lqw.eq(bo.getUseTime() != null, DocPrepareId::getUseTime, bo.getUseTime());
        lqw.eq(bo.getCreateTime() != null, DocPrepareId::getCreateTime, bo.getCreateTime());
        lqw.ge(StringUtils.isNotBlank(bo.getStartTime()), DocPrepareId::getCreateTime, bo.getStartTime());
        lqw.le(StringUtils.isNotBlank(bo.getEndTime()), DocPrepareId::getCreateTime, bo.getEndTime());
        lqw.or().eq(StringUtils.isNotBlank(bo.getOldDocId()), DocPrepareId::getDocId, bo.getOldDocId());
        return lqw;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertByBo(DocPrepareIdBo bo) throws Exception {
        DocPrepareId add = BeanUtil.toBean(bo, DocPrepareId.class);
        Map<String,Object> bizMap =  BeanUtil.beanToMap(bo);
        DocNoVo docNoVo = iGenerateIdService.generateDocId(bizMap);
        add.setDocId(docNoVo.getDocId());
        DocClass DocClass = iDocClassService.getById(bo.getDocClass());
        //add.setCodeType(LinkTypeEnum.DOC.name());
        add.setCodeType(DocClass.getDataType());
        String docClass= "STDD-R";
        if (Constants.DATA_TYPE_STDD.equals(bo.getDataType())){
            add.setProjectId(null);
        }else {
            docClass = "PROJECT-R";
        }
        List<DocPrepareId> list = new ArrayList<>();
        if(bo.getRecordCount()!=null&&bo.getRecordCount()>0){
            for (int i=0;i<bo.getRecordCount();i++) {
                DocPrepareId docPrepareId = new DocPrepareId();
                docPrepareId.setCodeType(LinkTypeEnum.RECORD.name());
                docPrepareId.setParentDocId(docNoVo.getDocId());
                docPrepareId.setProjectId(bo.getProjectId());
                docPrepareId.setApplyBy(add.getApplyBy());
                docPrepareId.setDocClass(docClass);
                docPrepareId.setDataType(add.getDataType());
                docPrepareId.setRemark(add.getRemark());
                Map<String,Object> map =  BeanUtil.beanToMap(docPrepareId);
                DocNoVo recordNoVo = iGenerateIdService.generateRecordDocId(map);
                docPrepareId.setDocId(recordNoVo.getDocId());
                list.add(docPrepareId);
            }
        }
        list.add(add);
        return saveBatch(list);
    }

    @Override
    public Boolean updateByBo(DocPrepareIdBo bo) {
        DocPrepareId update = BeanUtil.toBean(bo, DocPrepareId.class);
        validEntityBeforeSave(update);
        return updateById(update);
    }

    /**
     * 保存前的数据校验
     *
     * @param entity 实体类数据
     */
    private void validEntityBeforeSave(DocPrepareId entity){
        //TODO 做一些数据校验,如唯一约束
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return removeByIds(ids);
    }

    @Override
    public Boolean validatePrepareId(DocPrepareIdBo bo){
        DocPrepareIdVo vo=baseMapper.validatePrepareId(bo);
        return vo!=null&&!vo.getBusinessId().equals(bo.getBusinessId());
    }
}

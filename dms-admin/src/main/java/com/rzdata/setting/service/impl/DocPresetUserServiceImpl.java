package com.rzdata.setting.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.rzdata.framework.core.mybatisplus.core.ServicePlusImpl;
import com.rzdata.framework.core.page.PagePlus;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.utils.PageUtils;
import com.rzdata.framework.utils.StringUtils;
import com.rzdata.setting.domain.DocPresetUser;
import com.rzdata.setting.domain.bo.DocPresetUserBo;
import com.rzdata.setting.domain.vo.DocPresetUserVo;
import com.rzdata.setting.mapper.DocPresetUserMapper;
import com.rzdata.setting.service.IDocClassFlowNodeService;
import com.rzdata.setting.service.IDocClassFlowService;
import com.rzdata.setting.service.IDocPresetUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 预选环节用户Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-11-01
 */
@Service
public class DocPresetUserServiceImpl extends ServicePlusImpl<DocPresetUserMapper, DocPresetUser, DocPresetUserVo> implements IDocPresetUserService {
    @Autowired
    private IDocClassFlowService iDocClassFlowService;
    @Autowired
    private IDocClassFlowNodeService iDocClassFlowNodeService;
    @Override
    public DocPresetUserVo queryById(String id){
        return getVoById(id);
    }

    @Override
    public TableDataInfo<DocPresetUserVo> queryPageList(DocPresetUserBo bo) {
        PagePlus<DocPresetUser, DocPresetUserVo> result = pageVo(PageUtils.buildPagePlus(), buildQueryWrapper(bo));
        return PageUtils.buildDataInfo(result);
    }

    @Override
    public List<DocPresetUserVo> queryList(DocPresetUserBo bo) {
        return listVo(buildQueryWrapper(bo));
    }

    private LambdaQueryWrapper<DocPresetUser> buildQueryWrapper(DocPresetUserBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<DocPresetUser> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getBizId()), DocPresetUser::getBizId, bo.getBizId());
        lqw.eq(StringUtils.isNotBlank(bo.getNodeCode()), DocPresetUser::getNodeCode, bo.getNodeCode());
        lqw.eq(StringUtils.isNotBlank(bo.getUsers()), DocPresetUser::getUsers, bo.getUsers());
        return lqw;
    }

    @Override
    public Boolean insertByBo(DocPresetUserBo bo) {
        DocPresetUser add = BeanUtil.toBean(bo, DocPresetUser.class);
        validEntityBeforeSave(add);
        boolean flag = save(add);
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    @Override
    public Boolean updateByBo(DocPresetUserBo bo) {
        DocPresetUser update = BeanUtil.toBean(bo, DocPresetUser.class);
        validEntityBeforeSave(update);
        return updateById(update);
    }

    /**
     * 保存前的数据校验
     *
     * @param entity 实体类数据
     */
    private void validEntityBeforeSave(DocPresetUser entity){
        //TODO 做一些数据校验,如唯一约束
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return removeByIds(ids);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updatePresetUser(List<DocPresetUser> list, String bizId) {
        if (ObjectUtil.isEmpty(list)) {
            return true;
        }
        List<String> ids = new ArrayList<>();
        list.forEach(item->{
            item.setBizId(bizId);
            if (StringUtils.isNotEmpty(item.getId())) {
                ids.add(item.getId());
            }
        });
        remove(new LambdaQueryWrapper<DocPresetUser>().eq(DocPresetUser::getBizId,bizId).notIn(ids.size()>0,DocPresetUser::getId,ids));
        return saveOrUpdateBatch(list);
    }

    @Override
    public List<DocPresetUserVo> listByBizId(String id) {
        return listVo(new LambdaQueryWrapper<DocPresetUser>().eq(DocPresetUser::getBizId,id));
    }
}

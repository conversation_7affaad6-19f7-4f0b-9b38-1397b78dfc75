package com.rzdata.setting.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import com.rzdata.framework.exception.ServiceException;
import com.rzdata.framework.utils.PageUtils;
import com.rzdata.framework.core.page.PagePlus;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.utils.StringUtils;
import com.rzdata.setting.domain.DocClassSetting;
import com.rzdata.setting.domain.VersionRule;
import com.rzdata.setting.service.IDocClassSettingService;
import com.rzdata.setting.service.IVersionRuleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.rzdata.framework.core.mybatisplus.core.ServicePlusImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.rzdata.setting.domain.bo.VersionRuleDetailBo;
import com.rzdata.setting.domain.vo.VersionRuleDetailVo;
import com.rzdata.setting.domain.VersionRuleDetail;
import com.rzdata.setting.mapper.VersionRuleDetailMapper;
import com.rzdata.setting.service.IVersionRuleDetailService;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Collection;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 版本规则明细Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-12-27
 */
@Service
@Slf4j
public class VersionRuleDetailServiceImpl extends ServicePlusImpl<VersionRuleDetailMapper, VersionRuleDetail, VersionRuleDetailVo> implements IVersionRuleDetailService {

    @Autowired
    private IDocClassSettingService iDocClassSettingService;

    @Autowired
    private IVersionRuleService iVersionRuleService;

    @Override
    public VersionRuleDetailVo queryById(String id){
        return getVoById(id);
    }

    @Override
    public TableDataInfo<VersionRuleDetailVo> queryPageList(VersionRuleDetailBo bo) {
        PagePlus<VersionRuleDetail, VersionRuleDetailVo> result = pageVo(PageUtils.buildPagePlus(), buildQueryWrapper(bo));
        return PageUtils.buildDataInfo(result);
    }

    @Override
    public List<VersionRuleDetailVo> queryList(VersionRuleDetailBo bo) {
        return listVo(buildQueryWrapper(bo));
    }

    private LambdaQueryWrapper<VersionRuleDetail> buildQueryWrapper(VersionRuleDetailBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<VersionRuleDetail> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getRuleId()), VersionRuleDetail::getRuleId, bo.getRuleId());
        lqw.eq(StringUtils.isNotBlank(bo.getRuleType()), VersionRuleDetail::getRuleType, bo.getRuleType());
        lqw.eq(StringUtils.isNotBlank(bo.getStartValue()), VersionRuleDetail::getStartValue, bo.getStartValue());
        lqw.eq(StringUtils.isNotBlank(bo.getEndValue()), VersionRuleDetail::getEndValue, bo.getEndValue());
        lqw.eq(bo.getOrderBy() != null, VersionRuleDetail::getOrderBy, bo.getOrderBy());
        lqw.eq(StringUtils.isNotBlank(bo.getTenantId()), VersionRuleDetail::getTenantId, bo.getTenantId());
        return lqw;
    }

    @Override
    public Boolean insertByBo(VersionRuleDetailBo bo) {
        VersionRuleDetail add = BeanUtil.toBean(bo, VersionRuleDetail.class);
        validEntityBeforeSave(add);
        boolean flag = save(add);
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    @Override
    public Boolean updateByBo(VersionRuleDetailBo bo) {
        VersionRuleDetail update = BeanUtil.toBean(bo, VersionRuleDetail.class);
        validEntityBeforeSave(update);
        return updateById(update);
    }

    /**
     * 保存前的数据校验
     *
     * @param entity 实体类数据
     */
    private void validEntityBeforeSave(VersionRuleDetail entity){
        //TODO 做一些数据校验,如唯一约束
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return removeByIds(ids);
    }

    @Override
    public String getNextVersion(String version,String docClass) {
        //根据文件类型获取配置
        DocClassSetting setting= iDocClassSettingService.getSettingStatus(docClass,"version",null);
        if (ObjectUtil.isEmpty(setting)||StringUtils.isEmpty(setting.getRuleId())) {
            //没配置就返回当前版本号
            return version;
        }
        try {
            return generateVersion(version,setting);
        } catch (Exception e) {
            log.error(e.getMessage());
            log.error("版本解析失败，无法生成下一版本，传空字符重新生成！");
            return generateVersion("",setting);
        }
    }

    private String generateVersion(String version,DocClassSetting setting){
        List<VersionRuleDetailVo> list = getDetail(version,setting.getRuleId());
        if (StringUtils.isEmpty(version)) {
            VersionRule versionRule= iVersionRuleService.getById(setting.getRuleId());
            if (versionRule!=null&&StringUtils.isNotEmpty(versionRule.getStartValue())){
                return versionRule.getStartValue();
            }else {
                increment(list,list.size()-1,0);
            }
        }else {
            increment(list,list.size()-1,1);
        }
        return list.stream().map(VersionRuleDetailVo::getValue).collect(Collectors.joining());
    }

    private List<VersionRuleDetailVo> getDetail(String version,String ruleId){
        List<VersionRuleDetailVo> list = listVo(new LambdaQueryWrapper<VersionRuleDetail>()
                .eq(VersionRuleDetail::getRuleId,ruleId)
                .orderByAsc(VersionRuleDetail::getOrderBy));
        //判断该版本号是否超出规则
        boolean bool = true;
        //规则确定的总位数
        int digit = 0;
        //分解版本号 到对应规则
        for (int i = 0; i < list.size(); i++) {
            VersionRuleDetailVo item = list.get(i);
            if (digit +list.get(i).getDigit()<=version.length()) {
                if (list.size()==i+1) {
                    item.setValue(version.substring(digit));
                }else {
                    item.setValue(version.substring(digit, digit +item.getDigit()));
                }
            }else {
                item.setValue(item.getStartValue());
            }
            digit += item.getDigit();
             //判断该版本号是否超出规则
            if ("NUM".equals(item.getRuleType())) {
                if (!NumberUtil.isInteger(item.getValue())) {
                    throw new ServiceException("数字不是整数");
                }
                if (Integer.parseInt(item.getValue())<Integer.parseInt(item.getEndValue())) {
                    bool = false;
                }
            }else if ("ALP".equals(item.getRuleType())){
                if (!ALP.contains(item.getValue())) {
                    throw new ServiceException("字母不存在");
                }
                if (ALP.indexOf(item.getValue().toUpperCase())<ALP.indexOf(item.getEndValue())) {
                    bool = false;
                }
            }else if ("STR".equals(item.getRuleType())){
                if (!item.getValue().equals(item.getStartValue())) {
                    throw new ServiceException("字符不匹配");
                }
            }
        }
        //判断该版本号是否超出规则
        if (bool) {
            VersionRule rule= iVersionRuleService.getById(ruleId);
            if (StringUtils.isNotEmpty(rule.getNextId())) {
                //当前规则已满足获取下个规则
                return getDetail(version,rule.getNextId());
            }
        }
        return list;
    }

    private static final String ALP = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";

    public boolean increment(List<VersionRuleDetailVo> list,int index,int step){
        if (index<0) {
            return false;
        }
        VersionRuleDetailVo vo= list.get(index);
        switch (vo.getRuleType()) {
            case "NUM":
                int num = Integer.parseInt(vo.getValue()) + step;
                if (num>Integer.parseInt(vo.getEndValue())) {
                    boolean bool = increment(list,index-1,step);
                    String value = String.format("%0"+vo.getDigit()+"d",bool?Integer.parseInt(vo.getStartValue()):num);
                    vo.setValue(value);
                }else {
                    vo.setValue(String.format("%0"+vo.getDigit()+"d",num));
                    if (BooleanUtil.isTrue(vo.getSync())) {
                        increment(list,index-1,step);
                    }
                }
                return true;
            case "DATE":
                vo.setValue(DateUtil.format(new Date(),vo.getEndValue()));
                return increment(list,index-1,step);
            case "STR":
                vo.setValue(vo.getEndValue());
                return increment(list,index-1,step);
            case "ALP":
                int vi = ALP.indexOf(vo.getValue().toUpperCase())+step;
                int ei = ALP.indexOf(vo.getEndValue());
                if (vi>ei) {
                    boolean bool = increment(list,index-1,step);
                    vo.setValue(bool?vo.getStartValue():vo.getEndValue());
                    return bool;
                }else {
                    vo.setValue(ALP.substring(vi,vi+1));
                    if (BooleanUtil.isTrue(vo.getSync())) {
                        increment(list,index-1,step);
                    }
                    return true;
                }
            default:
                return false;
        }
    }
}

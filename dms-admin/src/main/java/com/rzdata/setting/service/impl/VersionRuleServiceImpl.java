package com.rzdata.setting.service.impl;

import cn.hutool.core.bean.BeanUtil;
    import com.rzdata.framework.utils.PageUtils;
import com.rzdata.framework.core.page.PagePlus;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.utils.StringUtils;
import com.rzdata.setting.domain.VersionRuleDetail;
import com.rzdata.setting.service.IVersionRuleDetailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.rzdata.framework.core.mybatisplus.core.ServicePlusImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.rzdata.setting.domain.bo.VersionRuleBo;
import com.rzdata.setting.domain.vo.VersionRuleVo;
import com.rzdata.setting.domain.VersionRule;
import com.rzdata.setting.mapper.VersionRuleMapper;
import com.rzdata.setting.service.IVersionRuleService;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 版本规则Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-12-27
 */
@Service
public class VersionRuleServiceImpl extends ServicePlusImpl<VersionRuleMapper, VersionRule, VersionRuleVo> implements IVersionRuleService {

    @Autowired
    private IVersionRuleDetailService iVersionRuleDetailService;

    @Override
    public VersionRuleVo queryById(String id){
        return getVoById(id);
    }

    @Override
    public TableDataInfo<VersionRuleVo> queryPageList(VersionRuleBo bo) {
        PagePlus<VersionRule, VersionRuleVo> result = pageVo(PageUtils.buildPagePlus(), buildQueryWrapper(bo));
        return PageUtils.buildDataInfo(result);
    }

    @Override
    public List<VersionRuleVo> queryList(VersionRuleBo bo) {
        return listVo(buildQueryWrapper(bo));
    }

    private LambdaQueryWrapper<VersionRule> buildQueryWrapper(VersionRuleBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<VersionRule> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getRuleName()), VersionRule::getRuleName, bo.getRuleName());
        lqw.eq(StringUtils.isNotBlank(bo.getTenantId()), VersionRule::getTenantId, bo.getTenantId());
        lqw.ne(StringUtils.isNotBlank(bo.getNeId()), VersionRule::getId, bo.getNeId());
        return lqw;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertByBo(VersionRuleBo bo) {
        VersionRule add = BeanUtil.toBean(bo, VersionRule.class);
        validEntityBeforeSave(add);
        boolean flag = save(add);
        if (flag) {
            bo.setId(add.getId());
            updateDetail(bo.getId(),bo.getRuleDetailList());
        }
        return flag;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateByBo(VersionRuleBo bo) {
        VersionRule update = BeanUtil.toBean(bo, VersionRule.class);
        validEntityBeforeSave(update);
        updateDetail(bo.getId(),bo.getRuleDetailList());
        return updateById(update);
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateDetail(String ruleId, List<VersionRuleDetail> ruleDetailList){
        iVersionRuleDetailService.remove(new LambdaQueryWrapper<VersionRuleDetail>().eq(VersionRuleDetail::getRuleId,ruleId));
        ruleDetailList.forEach(item->{
            item.setRuleId(ruleId);
        });
        iVersionRuleDetailService.saveAll(ruleDetailList);
    }

    /**
     * 保存前的数据校验
     *
     * @param entity 实体类数据
     */
    private void validEntityBeforeSave(VersionRule entity){
        //TODO 做一些数据校验,如唯一约束
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        iVersionRuleDetailService.remove(new LambdaQueryWrapper<VersionRuleDetail>().in(VersionRuleDetail::getRuleId,ids));
        return removeByIds(ids);
    }
}

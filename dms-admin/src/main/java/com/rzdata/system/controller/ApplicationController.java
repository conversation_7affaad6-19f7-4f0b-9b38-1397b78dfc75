package com.rzdata.system.controller;

import cn.hutool.core.util.StrUtil;
import com.rzdata.framework.annotation.Log;
import com.rzdata.framework.constant.CommonI18nConstant;
import com.rzdata.framework.constant.UserConstants;
import com.rzdata.framework.core.controller.BaseController;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.core.domain.entity.SysUser;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.enums.BusinessType;
import com.rzdata.framework.utils.I18nUtils;
import com.rzdata.framework.utils.SecurityUtils;
import com.rzdata.framework.utils.StringUtils;
import com.rzdata.system.service.ISysUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import java.util.HashMap;
import java.util.Map;

@Api(value = "应用账号控制器", tags = {"应用账号管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/system/application")
public class ApplicationController extends BaseController {

    private final ISysUserService userService;

    @ApiOperation("获取应用账号列表")
    @PreAuthorize("@ss.hasPermi('system:applicaiton:list')")
    @GetMapping("/list")
    public TableDataInfo<SysUser> list(SysUser user) {
        return userService.selectPageApplicationUserList(user);
    }

    /**
     * 根据应用账号编号获取详细信息
     */
    @ApiOperation("根据应用账号编号获取详细信息")
    @PreAuthorize("@ss.hasPermi('system:applicaiton:query')")
    @GetMapping(value = {"/", "/{userId}"})
    public AjaxResult<Map<String, Object>> getInfo(@ApiParam("用户ID") @PathVariable(value = "userId", required = false) String userId) {
        Map<String, Object> ajax = new HashMap<>();
        if (StringUtils.isNotNull(userId)) {
            ajax.put("user", userService.selectUserById(userId));
        }
        return AjaxResult.success(ajax);
    }

    /**
     * 新增应用账号
     */
    @ApiOperation("新增应用账号")
    @PreAuthorize("@ss.hasPermi('system:applicaiton:add')")
    @Log(title = "应用账号管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult<Void> add(@Validated @RequestBody SysUser user) {
        if (UserConstants.NOT_UNIQUE.equals(userService.checkUserNameUnique(user.getUserName()))) {
            //return AjaxResult.error("新增用户'" + user.getUserName() + "'失败，登录账号已存在");
            return AjaxResult.error(I18nUtils.getTitleFormat(CommonI18nConstant.SYS_USER_ADD_FAIL_USERNAME_EXIST, user.getUserName()));
        }
        user.setCreateBy(getUsername());
        return toAjax(userService.insertApplicationUser(user));
    }

    /**
     * 修改应用账号
     */
    @ApiOperation("修改应用账号")
    @PreAuthorize("@ss.hasPermi('system:applicaiton:edit')")
    @Log(title = "应用账号管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult<Void> edit(@Validated @RequestBody SysUser user) {
        userService.checkUserAllowed(user);
        user.setUpdateBy(getUsername());
        return toAjax(userService.updateApplicationUser(user));
    }

    /**
     * 状态修改
     */
    @ApiOperation("状态修改")
      @PreAuthorize("@ss.hasPermi('system:applicaiton:edit')")
    @Log(title = "应用账号管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult<Void> changeStatus(@RequestBody SysUser user) {
        userService.checkUserAllowed(user);
        return toAjax(userService.updateApplicationUser(user));
    }

    @ApiOperation("重置密码")
    @PreAuthorize("@ss.hasPermi('system:applicaiton:resetPwd')")
    @Log(title = "应用账号管理", businessType = BusinessType.UPDATE)
    @PutMapping("/resetPwd")
    public AjaxResult<Void> resetPwd(@RequestBody SysUser user) {
        userService.checkUserAllowed(user);
        user.setPassword(SecurityUtils.encryptPassword(user.getPassword()));
        return toAjax(userService.updateApplicationUser(user));
    }

}

package com.rzdata.system.controller;

import java.util.List;
import java.util.Arrays;

import lombok.RequiredArgsConstructor;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.rzdata.framework.annotation.RepeatSubmit;
import com.rzdata.framework.annotation.Log;
import com.rzdata.framework.core.controller.BaseController;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import com.rzdata.framework.core.validate.QueryGroup;
import com.rzdata.framework.enums.BusinessType;
import com.rzdata.framework.utils.poi.ExcelUtil;
import com.rzdata.system.domain.vo.BasicUserSignatureVo;
import com.rzdata.system.domain.bo.BasicUserSignatureBo;
import com.rzdata.system.service.IBasicUserSignatureService;
import com.rzdata.framework.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiOperation;

/**
 * 用户签名管理Controller
 *
 * <AUTHOR>
 * @date 2024-05-08
 */
@Validated
@Api(value = "用户签名管理控制器", tags = {"用户签名管理管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/system/userSignature")
public class BasicUserSignatureController extends BaseController {

    private final IBasicUserSignatureService iBasicUserSignatureService;


    @Autowired
    private IBasicUserSignatureService basicUserSignatureService;

    /**
     * 查询用户签名管理列表
     */
    @ApiOperation("查询用户签名管理列表")
    @PreAuthorize("@ss.hasPermi('system:userSignature:list')")
    @GetMapping("/list")
    public TableDataInfo<BasicUserSignatureVo> list(@Validated(QueryGroup.class) BasicUserSignatureBo bo) {
        return iBasicUserSignatureService.queryPageList(bo);
    }

    /**
     * 查询用户签名管理列表
     */
    @ApiOperation("查询用户签名管理列表")
    @PreAuthorize("@ss.hasPermi('system:userSignature:list')")
    @GetMapping("/getPageList")
    public TableDataInfo<BasicUserSignatureVo> getPageList(@Validated(QueryGroup.class) BasicUserSignatureBo bo) {
        return iBasicUserSignatureService.getPageList(bo);
    }

    /**
     * 导出用户签名管理列表
     */
    @ApiOperation("导出用户签名管理列表")
    @Log(title = "用户签名管理", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public void export(@Validated BasicUserSignatureBo bo, HttpServletResponse response) {
        List<BasicUserSignatureVo> list = iBasicUserSignatureService.queryList(bo);
        ExcelUtil.exportExcel(list, "用户签名管理", BasicUserSignatureVo.class, response);
    }

    /**
     * 获取用户签名管理详细信息
     */
    @ApiOperation("获取用户签名管理详细信息")
    @GetMapping("/{id}")
    public AjaxResult<BasicUserSignatureVo> getInfo(@ApiParam("主键")
                                                  @NotNull(message = "主键不能为空")
                                                  @PathVariable("id") Long id) {
        return AjaxResult.success(iBasicUserSignatureService.queryById(id));
    }

    /**
     * 新增用户签名管理
     */
    @ApiOperation("新增用户签名管理")
    @Log(title = "用户签名管理", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public AjaxResult<Void> add(@Validated(AddGroup.class) @RequestBody BasicUserSignatureBo bo) {
        try {
            return toAjax(iBasicUserSignatureService.insertByBo(bo) ? 1 : 0);
        } catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 修改用户签名管理
     */
    @ApiOperation("修改用户签名管理")
    @Log(title = "用户签名管理", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public AjaxResult<Void> edit(@Validated(EditGroup.class) @RequestBody BasicUserSignatureBo bo) {
        try {
            return toAjax(iBasicUserSignatureService.updateByBo(bo) ? 1 : 0);
        } catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 删除用户签名管理
     */
    @ApiOperation("删除用户签名管理")
    @Log(title = "用户签名管理" , businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult<Void> remove(@ApiParam("主键串")
                                       @NotEmpty(message = "主键不能为空")
                                       @PathVariable Long[] ids) {
        return toAjax(iBasicUserSignatureService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }

    /**
     * 删除用户签名管理
     */
    @ApiOperation("删除用户签名管理")
    @Log(title = "用户签名管理" , businessType = BusinessType.DELETE)
    @GetMapping("/validate")
    public AjaxResult<Boolean> validate(@RequestParam("userCode") String userCode) {
        return AjaxResult.success(iBasicUserSignatureService.validateByUserCode(userCode));
    }
}

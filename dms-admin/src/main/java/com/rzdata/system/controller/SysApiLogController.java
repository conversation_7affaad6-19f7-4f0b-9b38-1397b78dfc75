package com.rzdata.system.controller;

import java.util.List;
import java.util.Arrays;

import lombok.RequiredArgsConstructor;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.rzdata.framework.annotation.RepeatSubmit;
import com.rzdata.framework.annotation.Log;
import com.rzdata.framework.core.controller.BaseController;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import com.rzdata.framework.core.validate.QueryGroup;
import com.rzdata.framework.enums.BusinessType;
import com.rzdata.framework.utils.poi.ExcelUtil;
import com.rzdata.system.domain.vo.SysApiLogVo;
import com.rzdata.system.domain.bo.SysApiLogBo;
import com.rzdata.system.service.ISysApiLogService;
import com.rzdata.framework.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiOperation;

/**
 * API调用日志Controller
 *
 * <AUTHOR>
 * @date 2024-09-25
 */
@Validated
@Api(value = "API调用日志控制器", tags = {"API调用日志管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/system/apiLog")
public class SysApiLogController extends BaseController {

    private final ISysApiLogService iSysApiLogService;

    /**
     * 查询API调用日志列表
     */
    @ApiOperation("查询API调用日志列表")
    @PreAuthorize("@ss.hasPermi('system:apiLog:list')")
    @GetMapping("/list")
    public TableDataInfo<SysApiLogVo> list(@Validated(QueryGroup.class) SysApiLogBo bo) {
        return iSysApiLogService.queryPageList(bo);
    }

    /**
     * 导出API调用日志列表
     */
    @ApiOperation("导出API调用日志列表")
    @PreAuthorize("@ss.hasPermi('system:apiLog:export')")
    @Log(title = "API调用日志", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public void export(@Validated SysApiLogBo bo, HttpServletResponse response) {
        List<SysApiLogVo> list = iSysApiLogService.queryList(bo);
        ExcelUtil.exportExcel(list, "API调用日志", SysApiLogVo.class, response);
    }

    /**
     * 获取API调用日志详细信息
     */
    @ApiOperation("获取API调用日志详细信息")
    @PreAuthorize("@ss.hasPermi('system:apiLog:query')")
    @GetMapping("/{id}")
    public AjaxResult<SysApiLogVo> getInfo(@ApiParam("主键")
                                                  @NotNull(message = "主键不能为空")
                                                  @PathVariable("id") String id) {
        return AjaxResult.success(iSysApiLogService.queryById(id));
    }

    /**
     * 新增API调用日志
     */
    @ApiOperation("新增API调用日志")
    @PreAuthorize("@ss.hasPermi('system:apiLog:add')")
    @Log(title = "API调用日志", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public AjaxResult<Void> add(@Validated(AddGroup.class) @RequestBody SysApiLogBo bo) {
        return toAjax(iSysApiLogService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改API调用日志
     */
    @ApiOperation("修改API调用日志")
    @PreAuthorize("@ss.hasPermi('system:apiLog:edit')")
    @Log(title = "API调用日志", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public AjaxResult<Void> edit(@Validated(EditGroup.class) @RequestBody SysApiLogBo bo) {
        return toAjax(iSysApiLogService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除API调用日志
     */
    @ApiOperation("删除API调用日志")
    @PreAuthorize("@ss.hasPermi('system:apiLog:remove')")
    @Log(title = "API调用日志" , businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult<Void> remove(@ApiParam("主键串")
                                       @NotEmpty(message = "主键不能为空")
                                       @PathVariable String[] ids) {
        return toAjax(iSysApiLogService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}

package com.rzdata.system.controller;

import com.rzdata.framework.annotation.Log;
import com.rzdata.framework.annotation.RepeatSubmit;
import com.rzdata.framework.constant.PermissionConstants;
import com.rzdata.framework.core.controller.BaseController;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.core.domain.model.LoginUser;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import com.rzdata.framework.core.validate.QueryGroup;
import com.rzdata.framework.enums.BusinessType;
import com.rzdata.framework.utils.SecurityUtils;
import com.rzdata.framework.utils.poi.ExcelUtil;
import com.rzdata.system.domain.bo.SysGroupBo;
import com.rzdata.system.domain.vo.SysGroupVo;
import com.rzdata.system.service.ISysGroupService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;
import java.util.Set;

/**
 * 项目组Controller
 *
 * <AUTHOR>
 * @date 2023-06-19
 */
@Validated
@Api(value = "项目组控制器", tags = {"项目组管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/system/group")
public class SysGroupController extends BaseController {

    private final ISysGroupService iSysGroupService;

    /**
     * 查询项目组列表
     */
    @ApiOperation("查询项目组列表")
    @GetMapping("/list")
    public TableDataInfo<SysGroupVo> list(@Validated(QueryGroup.class) SysGroupBo bo) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        Set<String> menuPermission = loginUser.getPermissions();
        if(menuPermission.contains("system:userGroup:mange")){
            bo.setCreateBy(loginUser.getUsername());
        }

        return iSysGroupService.queryPageList(bo);
    }

    /**
     * 导出项目组列表
     */
    @ApiOperation("导出项目组列表")
    @PreAuthorize("@ss.hasPermi('system:group:export')")
    @Log(title = "项目组", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public void export(@Validated SysGroupBo bo, HttpServletResponse response) {
        List<SysGroupVo> list = iSysGroupService.queryList(bo);
        ExcelUtil.exportExcel(list, "项目组", SysGroupVo.class, response);
    }

    /**
     * 获取项目组详细信息
     */
    @ApiOperation("获取项目组详细信息")
    @PreAuthorize("@ss.hasPermi('system:group:query')")
    @GetMapping("/{id}")
    public AjaxResult<SysGroupVo> getInfo(@ApiParam("主键")
                                                  @NotNull(message = "主键不能为空")
                                                  @PathVariable("id") String id) {
        return AjaxResult.success(iSysGroupService.queryById(id));
    }

    /**
     * 新增项目组
     */
    @ApiOperation("新增项目组")
    @PreAuthorize("@ss.hasPermi('system:group:add')")
    @Log(title = "项目组", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public AjaxResult<Void> add(@Validated(AddGroup.class) @RequestBody SysGroupBo bo) {
        return toAjax(iSysGroupService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改项目组
     */
    @ApiOperation("修改项目组")
    @PreAuthorize("@ss.hasPermi('system:group:edit')")
    @Log(title = "项目组", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public AjaxResult<Void> edit(@Validated(EditGroup.class) @RequestBody SysGroupBo bo) {
        return toAjax(iSysGroupService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 复制项目组
     */
    @ApiOperation("复制项目组")
    @Log(title = "项目组", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/copy")
    public AjaxResult<Void> copy(@Validated(EditGroup.class) @RequestBody SysGroupBo bo) {
        return toAjax(iSysGroupService.copy(bo) ? 1 : 0);
    }

    /**
     * 删除项目组
     */
    @ApiOperation("删除项目组")
    @PreAuthorize("@ss.hasPermi('system:group:remove')")
    @Log(title = "项目组" , businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult<Void> remove(@ApiParam("主键串")
                                       @NotEmpty(message = "主键不能为空")
                                       @PathVariable String[] ids) {
        return toAjax(iSysGroupService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}

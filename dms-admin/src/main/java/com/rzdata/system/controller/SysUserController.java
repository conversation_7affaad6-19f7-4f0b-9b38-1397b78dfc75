package com.rzdata.system.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ObjectUtil;
import com.rzdata.framework.annotation.Log;
import com.rzdata.framework.constant.UserConstants;
import com.rzdata.framework.core.controller.BaseController;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.core.domain.entity.SysDept;
import com.rzdata.framework.core.domain.entity.SysRole;
import com.rzdata.framework.core.domain.entity.SysUser;
import com.rzdata.framework.core.domain.model.LoginBody;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.enums.BusinessType;
import com.rzdata.framework.utils.SecurityUtils;
import com.rzdata.framework.utils.StringUtils;
import com.rzdata.framework.utils.poi.ExcelUtil;
import com.rzdata.system.domain.vo.SysUserExportVo;
import com.rzdata.system.domain.vo.SysUserImportVo;
import com.rzdata.system.domain.vo.SysUserTenantVo;
import com.rzdata.system.domain.vo.SysUserVo;
import com.rzdata.system.service.*;
import io.swagger.annotations.*;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 用户信息
 *
 * <AUTHOR> Li
 */
@Validated
@Api(value = "用户信息控制器", tags = {"用户信息管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/system/user")
public class SysUserController extends BaseController {

    private final ISysUserService userService;
    private final ISysRoleService roleService;
    private final ISysPostService postService;
    private final ISysUserTenantService sysUserTenantService;
    private final ITenantService tenantService;
    private final ISysUserTenantService iSysUserTenantService;
    private final SysLoginService sysLoginService;

    /**
     * 获取用户列表
     */
    @ApiOperation("获取用户列表")
//    @PreAuthorize("@ss.hasPermi('system:user:list')")
    @GetMapping("/list")
    public TableDataInfo<SysUser> list(SysUser user) {
        return userService.selectPageUserList(user);
    }


    /**
     * 通过部门id和菜单权限拿到该部门下具有该权限的用户
     */
    @ApiOperation("通过部门id和菜单权限拿到该部门下具有该权限的用户")
    @GetMapping("/listByPerms")
    public TableDataInfo<SysUser> list(@RequestParam("deptId") String deptId,@RequestParam("perms") String perms) {
        return userService.selectUserByDeptIdAndPerms(deptId,perms);
    }

    /**
     * 获取用户列表
     */
    @ApiOperation("获取用户列表")
//    @PreAuthorize("@ss.hasPermi('system:user:list')")
    @GetMapping("/listForGroup")
    public TableDataInfo<SysUser> listForGroup(SysUser user) {
        return userService.selectPageUserListForGroup(user);
    }

    /**
     * 获取用户列表
     */
    @ApiOperation("获取用户列表")
    @GetMapping("/list/all")
    public AjaxResult<List<SysUser>> listAll(SysUser user) {
        return AjaxResult.success(userService.selectUserList(user));
    }

    /**
     * 获取用户列表
     */
    @ApiOperation("获取用户列表")
    @GetMapping("/list/{roleKey}")
    public AjaxResult<List<SysUser>> listAllByRoleKey(@PathVariable("roleKey") String roleKey) {
        return AjaxResult.success(userService.selectListAllByRoleKey(roleKey));
    }

    @ApiOperation("导出用户列表")
    @Log(title = "用户管理", businessType = BusinessType.EXPORT)
    @PreAuthorize("@ss.hasPermi('system:user:export')")
    @PostMapping("/export")
    public void export(SysUser user, HttpServletResponse response) {
        List<SysUser> list = userService.selectUserList(user);
        List<SysUserExportVo> listVo = BeanUtil.copyToList(list, SysUserExportVo.class);
        for (int i = 0; i < list.size(); i++) {
            SysDept dept = list.get(i).getDept();
            SysUserExportVo vo = listVo.get(i);
            if (ObjectUtil.isNotEmpty(dept)) {
                vo.setDeptName(dept.getDeptName());
                vo.setLeader(dept.getLeader());
            }
        }
        ExcelUtil.exportExcel(listVo, "用户数据", SysUserExportVo.class, response);
    }


    @Log(title = "身份二次验证", businessType = BusinessType.VERIFY)
    @ApiOperation("身份二次验证")
    @PostMapping("/verifyIdentity")
    public AjaxResult verifyIdentity(@RequestBody LoginBody loginBody){
      boolean status = sysLoginService.verifyIdentity(loginBody.getPassword());
      AjaxResult result = status ? AjaxResult.success() : AjaxResult.error("身份二次验证失败");
      return result;
    }

    @ApiOperation("导入用户列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "file", value = "导入文件", dataType = "java.io.File", required = true),
    })
    @Log(title = "用户管理", businessType = BusinessType.IMPORT)
    @PreAuthorize("@ss.hasPermi('system:user:import')")
    @PostMapping("/importData")
    public AjaxResult<Void> importData(@RequestPart("file") MultipartFile file, boolean updateSupport) throws Exception {
        List<SysUserImportVo> userListVo = ExcelUtil.importExcel(file.getInputStream(), SysUserImportVo.class);
        List<SysUser> userList = BeanUtil.copyToList(userListVo, SysUser.class);
        String operName = getUsername();
        String message = userService.importUser(userList, updateSupport, operName);
        return AjaxResult.success(message);
    }

    @ApiOperation("下载导入模板")
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil.exportExcel(new ArrayList<>(), "用户数据", SysUserImportVo.class, response);
    }

    /**
     * 根据用户编号获取详细信息
     */
    @ApiOperation("根据用户编号获取详细信息")
    @PreAuthorize("@ss.hasPermi('system:user:query')")
    @GetMapping(value = {"/", "/{userId}"})
    public AjaxResult<Map<String, Object>> getInfo(@ApiParam("用户ID") @PathVariable(value = "userId", required = false) String userId) {
        userService.checkUserDataScope(userId);
        Map<String, Object> ajax = new HashMap<>();
        List<SysRole> roles = roleService.selectRoleAll();
        ajax.put("roles", SysUser.isAdmin(userId) ? roles : roles.stream().filter(r -> !r.isAdmin()).collect(Collectors.toList()));
        ajax.put("posts", postService.selectPostAll());
        ajax.put("tenants", tenantService.list());
        if (StringUtils.isNotNull(userId)) {
            ajax.put("user", userService.selectUserById(userId));
            ajax.put("postIds", postService.selectPostListByUserId(userId));
            ajax.put("roleIds", roleService.selectRoleListByUserId(userId));
            ajax.put("tenantIds", sysUserTenantService.selectTenantListByUserId(userId));
        }
        return AjaxResult.success(ajax);
    }

    /**
     * 新增用户
     */
    @ApiOperation("新增用户")
    @PreAuthorize("@ss.hasPermi('system:user:add')")
    @Log(title = "用户管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult<Void> add(@Validated @RequestBody SysUser user) {
        if (UserConstants.NOT_UNIQUE.equals(userService.checkUserNameUnique(user.getUserName()))) {
            return AjaxResult.error("新增用户'" + user.getUserName() + "'失败，登录账号已存在");
        } else if (StringUtils.isNotEmpty(user.getPhonenumber())
                && UserConstants.NOT_UNIQUE.equals(userService.checkPhoneUnique(user))) {
            return AjaxResult.error("新增用户'" + user.getUserName() + "'失败，手机号码已存在");
        } else if (StringUtils.isNotEmpty(user.getEmail())
                && UserConstants.NOT_UNIQUE.equals(userService.checkEmailUnique(user))) {
            return AjaxResult.error("新增用户'" + user.getUserName() + "'失败，邮箱账号已存在");
        }
        user.setPassword(SecurityUtils.encryptPassword(user.getPassword()));
        return toAjax(userService.insertUser(user));
    }

    /**
     * 修改用户
     */
    @ApiOperation("修改用户")
    @PreAuthorize("@ss.hasPermi('system:user:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult<Void> edit(@Validated @RequestBody SysUser user) {
        userService.checkUserAllowed(user);
        if (StringUtils.isNotEmpty(user.getPhonenumber())
                && UserConstants.NOT_UNIQUE.equals(userService.checkPhoneUnique(user))) {
            return AjaxResult.error("修改用户'" + user.getUserName() + "'失败，手机号码已存在");
        } else if (StringUtils.isNotEmpty(user.getEmail())
                && UserConstants.NOT_UNIQUE.equals(userService.checkEmailUnique(user))) {
            return AjaxResult.error("修改用户'" + user.getUserName() + "'失败，邮箱账号已存在");
        }
        return toAjax(userService.updateUser(user));
    }

    /**
     * 删除用户
     */
    @ApiOperation("删除用户")
    @PreAuthorize("@ss.hasPermi('system:user:remove')")
    @Log(title = "用户管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{userIds}")
    public AjaxResult<Void> remove(@ApiParam("角色ID串") @PathVariable String[] userIds) {
        if (ArrayUtil.contains(userIds, getUserId())) {
            return error("当前用户不能删除");
        }
        return toAjax(userService.deleteUserByIds(userIds));
    }

    /**
     * 重置密码
     */
    @ApiOperation("重置密码")
    @PreAuthorize("@ss.hasPermi('system:user:resetPwd')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/resetPwd")
    public AjaxResult<Void> resetPwd(@RequestBody SysUser user) {
        userService.checkUserAllowed(user);
        user.setPassword(SecurityUtils.encryptPassword(user.getPassword()));
        return toAjax(userService.resetPwd(user));
    }

    /**
     * 状态修改
     */
    @ApiOperation("状态修改")
    @PreAuthorize("@ss.hasPermi('system:user:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult<Void> changeStatus(@RequestBody SysUser user) {
        userService.checkUserAllowed(user);
        return toAjax(userService.updateUserStatus(user));
    }

    /**
     * 根据用户编号获取授权角色
     */
    @ApiOperation("根据用户编号获取授权角色")
    @PreAuthorize("@ss.hasPermi('system:user:query')")
    @GetMapping("/authRole/{userId}")
    public AjaxResult<Map<String, Object>> authRole(@ApiParam("用户ID") @PathVariable("userId") String userId) {
        SysUser user = userService.selectUserById(userId);
        List<SysRole> roles = roleService.selectRolesByUserId(userId);
        Map<String, Object> ajax = new HashMap<>();
        ajax.put("user", user);
        ajax.put("roles", SysUser.isAdmin(userId) ? roles : roles.stream().filter(r -> !r.isAdmin()).collect(Collectors.toList()));
        return AjaxResult.success(ajax);
    }

    /**
     * 用户授权角色
     */
    @ApiOperation("用户授权角色")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "userId", value = "用户Id", paramType = "query", dataTypeClass = String.class),
        @ApiImplicitParam(name = "roleIds", value = "角色ID串", paramType = "query", dataTypeClass = String.class)
    })
    @PreAuthorize("@ss.hasPermi('system:user:distributeRole')")
    @Log(title = "用户管理", businessType = BusinessType.GRANT)
    @PutMapping("/authRole")
    public AjaxResult<Void> insertAuthRole(String userId, Long[] roleIds) {
        userService.insertUserAuth(userId, roleIds);
        return success();
    }

    @GetMapping("/selectUserToAiShu")
    @ApiOperation("爱数用户同步查询接口")
    public List<SysUserVo> selectUserToAiShu() {
        return userService.selectUserToAiShu();
    }


    /**
     * 获取部门下拉树列表
     */
    @ApiOperation("获取部门人员树形结构")
    @GetMapping("/treeselect")
    public AjaxResult<List<Tree<String>>> treeselect(SysDept dept) {
        return AjaxResult.success(userService.buildDeptTreeSelect());
    }

    @ApiOperation("根据用户名获取公司")
    @GetMapping("/queryCompanyList/{userName}")
    public AjaxResult<List<SysUserTenantVo>> queryCompanyList(@ApiParam("用户名") @PathVariable(value = "userName") String userName) {
        return AjaxResult.success(iSysUserTenantService.queryCompanyList(userName));
    }

    @ApiOperation("根据二级部门获取人数")
    @GetMapping("/getNumByDeptId/{deptId}")
    public AjaxResult<Integer> getNumByDeptId(@ApiParam("二级部门Id") @PathVariable(value = "deptId") String deptId) {
        return AjaxResult.success(userService.getNumByDeptId(deptId));
    }

    @ApiOperation("根据公司获取人数")
    @GetMapping("/getNumByTenantId/{tenantId}")
    public AjaxResult<Integer> getNumByTenantId(@ApiParam("公司ID") @PathVariable(value = "tenantId") String tenantId) {
        return AjaxResult.success(userService.getNumByTenantId(tenantId));
    }

    @ApiOperation("根据角色向上查找部门人员")
    @GetMapping("/getFullPathRoleUserList")
    public AjaxResult<List<SysUser>> getFullPathRoleUserList(@RequestParam(value = "roleKey") String roleKey,@RequestParam("deptId") String deptId) {
        return AjaxResult.success(userService.getFullPathRoleUserList(roleKey,deptId));
    }

    @ApiOperation("根据用户名和部门查询领导")
    @GetMapping("/leader")
    public AjaxResult<SysUser> getLeader(@RequestParam(value = "userName") String userName,@RequestParam(value = "deptId") String deptId) {
        return AjaxResult.success(userService.getLeader(userName,deptId));
    }

    @ApiOperation("根据用户名和部门查询分管领导")
    @GetMapping("/divisionLeader")
    public AjaxResult<SysUser> getDivisionLeader(@RequestParam(value = "deptId") String deptId) {
        return AjaxResult.success(userService.getDivisionLeader(deptId));
    }

    @ApiOperation("根据部门查询文控")
    @GetMapping("/docManagers")
    public AjaxResult<List<SysUser>> getDocManagersByDeptId(@RequestParam(value = "deptId") String deptId) {
        return AjaxResult.success(userService.getDocManagersByDeptId(deptId));
    }

    @ApiOperation("根据账号查询所在部门")
    @GetMapping("/getDeptByUserName")
    public AjaxResult<String> getDeptByUserName(@RequestParam(value = "userName") String userName) {
        return AjaxResult.success("",userService.getDeptByUserName(userName));
    }

    @ApiOperation("根据逗号隔开userName查询用户List")
    @GetMapping("/getUserByUserNames")
    public AjaxResult<List<SysUser>> getUserByUserNames(@RequestParam(value = "userNames") String userNames) {
        return AjaxResult.success(userService.getUserByUserNames(userNames));
    }
}

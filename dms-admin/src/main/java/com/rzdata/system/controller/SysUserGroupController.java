package com.rzdata.system.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.rzdata.framework.annotation.Log;
import com.rzdata.framework.annotation.RepeatSubmit;
import com.rzdata.framework.core.controller.BaseController;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import com.rzdata.framework.core.validate.QueryGroup;
import com.rzdata.framework.enums.BusinessType;
import com.rzdata.framework.utils.poi.ExcelUtil;
import com.rzdata.system.domain.SysUserGroup;
import com.rzdata.system.domain.bo.SysUserGroupBo;
import com.rzdata.system.domain.vo.SysUserGroupVo;
import com.rzdata.system.service.ISysUserGroupService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * 用户和群组关联Controller
 *
 * <AUTHOR>
 * @date 2023-06-20
 */
@Validated
@Api(value = "用户和群组关联控制器", tags = {"用户和群组关联管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/system/userGroup")
public class SysUserGroupController extends BaseController {

    private final ISysUserGroupService iSysUserGroupService;

    /**
     * 查询用户和群组关联列表
     */
    @ApiOperation("查询用户和群组关联列表")
    @GetMapping("/list")
    public TableDataInfo<SysUserGroupVo> list(@Validated(QueryGroup.class) SysUserGroupBo bo) {
        return iSysUserGroupService.queryPageList(bo);
    }

    /**
     * 导出用户和群组关联列表
     */
    @ApiOperation("导出用户和群组关联列表")
    @PreAuthorize("@ss.hasPermi('system:userGroup:export')")
    @Log(title = "用户和群组关联", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public void export(@Validated SysUserGroupBo bo, HttpServletResponse response) {
        List<SysUserGroupVo> list = iSysUserGroupService.queryList(bo);
        ExcelUtil.exportExcel(list, "用户和群组关联", SysUserGroupVo.class, response);
    }

    /**
     * 获取用户和群组关联详细信息
     */
    @ApiOperation("获取用户和群组关联详细信息")
    @PreAuthorize("@ss.hasPermi('system:userGroup:query')")
    @GetMapping("/{tenantId}")
    public AjaxResult<SysUserGroupVo> getInfo(@ApiParam("主键")
                                                  @NotNull(message = "主键不能为空")
                                                  @PathVariable("tenantId") String tenantId) {
        return AjaxResult.success(iSysUserGroupService.queryById(tenantId));
    }

    /**
     * 新增用户和群组关联
     */
    @ApiOperation("新增用户和群组关联")
    @PreAuthorize("@ss.hasPermi('system:userGroup:add')")
    @Log(title = "用户和群组关联", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public AjaxResult<Void> add(@Validated(AddGroup.class) @RequestBody SysUserGroupBo bo) {
        return toAjax(iSysUserGroupService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 批量新增用户和群组关联
     */
    @ApiOperation("批量新增用户和群组关联")
    @PreAuthorize("@ss.hasPermi('system:userGroup:add')")
    @Log(title = "批量新增用户和群组关联", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/batchAdd")
    public AjaxResult<Void> batchAdd(@Validated(AddGroup.class) @RequestBody List<SysUserGroupBo> boList, HttpServletRequest request) {
        // 先删除此群组的成员列表
        String groupId = request.getParameter("groupId");
        if(StringUtils.isNotEmpty(groupId)) {
            this.iSysUserGroupService.getBaseMapper().delete(new QueryWrapper<>(new SysUserGroup().setGroupId(groupId)));
        }
        // 往该群组添加成员列表
        int fact = 0;
        for(SysUserGroupBo bo : boList) {
            fact += iSysUserGroupService.insertByBo(bo) ? 1 : 0;
        }
        return toAjax(1);
    }

    /**
     * 修改用户和群组关联
     */
    @ApiOperation("修改用户和群组关联")
    @PreAuthorize("@ss.hasPermi('system:userGroup:edit')")
    @Log(title = "用户和群组关联", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public AjaxResult<Void> edit(@Validated(EditGroup.class) @RequestBody SysUserGroupBo bo) {
        return toAjax(iSysUserGroupService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除用户和群组关联
     */
    @ApiOperation("删除用户和群组关联")
    @PreAuthorize("@ss.hasPermi('system:userGroup:remove')")
    @Log(title = "用户和群组关联" , businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult<Void> remove(@ApiParam("主键串")
                                       @NotEmpty(message = "主键不能为空")
                                       @PathVariable String[] ids) {
        return toAjax(iSysUserGroupService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}

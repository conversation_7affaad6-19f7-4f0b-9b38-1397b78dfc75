package com.rzdata.system.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.rzdata.framework.core.domain.BaseEntity;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * VIEW对象 act_hi_taskinst
 *
 * <AUTHOR>
 * @date 2024-08-30
 */
@Data
@Accessors(chain = true)
@TableName("act_hi_taskinst")
public class ActHiTaskinst extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 
     */
    private String id;
    /**
     * 
     */
    private String procDefId;
    /**
     * 
     */
    private String taskDefKey;
    /**
     * 
     */
    private String procInstId;
    /**
     * 
     */
    private String executionId;
    /**
     * 
     */
    private String name;
    /**
     * 
     */
    private String parentTaskId;
    /**
     * 
     */
    private String description;
    /**
     * 
     */
    private String owner;
    /**
     * 
     */
    private String assignee;
    /**
     * 
     */
    private Date startTime;
    /**
     * 
     */
    private Date claimTime;
    /**
     * 
     */
    private Date endTime;
    /**
     * 
     */
    private Long duration;
    /**
     * 
     */
    private String deleteReason;
    /**
     * 
     */
    private Long priority;
    /**
     * 
     */
    private Date dueDate;
    /**
     * 
     */
    private String formKey;
    /**
     * 
     */
    private String category;
    /**
     * 
     */
    private String tenantId;
    /**
     * 
     */
    private String procTitle;
    /**
     * 
     */
    private String SENDER;
    /**
     * 
     */
    private String preTaskDefKey;
    /**
     * 
     */
    private String preTaskId;
    /**
     * 
     */
    private String preTaskDefName;
    /**
     * 
     */
    private String actionType;
    /**
     * 
     */
    private String topExecutionId;
    /**
     * 
     */
    private String sendUserName;
    /**
     * 
     */
    private String senderOrgId;
    /**
     * 
     */
    private String senderOrgName;
    /**
     * 
     */
    private String assigneeUserName;
    /**
     * 
     */
    private String assigneeOrgId;
    /**
     * 
     */
    private String assigneeOrgName;
    /**
     * 
     */
    private String assigneeUserId;
    /**
     * 
     */
    private String sendUserId;
    /**
     * 
     */
    private String procDefName;

}

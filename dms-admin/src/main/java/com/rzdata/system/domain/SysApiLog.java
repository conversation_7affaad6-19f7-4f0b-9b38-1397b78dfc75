package com.rzdata.system.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;
import com.rzdata.framework.core.domain.BaseEntity;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * API调用日志对象 sys_api_log
 *
 * <AUTHOR>
 * @date 2024-09-25
 */
@Data
@Accessors(chain = true)
@TableName("sys_api_log")
public class SysApiLog extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 主键ID
     */
    @TableId(value = "id")
    private String id;
    /**
     * 租户ID
     */
    private String tenantId;
    /**
     * 关联业务对象ID
     */
    private String sourceId;
    /**
     * 关联业务场景
     */
    private String sourceType;
    /**
     * API归属的应用ID
     */
    private String appId;
    /**
     * API归属的应用名称
     */
    private String appName;
    /**
     * 调用的API类型  POST GET  DELETE
     */
    private String apiType;
    /**
     * 调用的API地址
     */
    private String apiUrl;
    /**
     * API请求参数
     */
    private String requestParam;
    /**
     * API请求时间
     */
    private Date requestTime;
    /**
     * API调用状态  0 成功  1 失败
     */
    private Long status;
    /**
     * API响应状态码
     */
    private String responseStatus;
    /**
     * API响应信息
     */
    private String responseInfo;
    /**
     * API响应时间
     */
    private Date responseTime;
    /**
     * 备注
     */
    private String remark;

}

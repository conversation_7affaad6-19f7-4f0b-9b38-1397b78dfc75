package com.rzdata.system.domain.bo;

import com.rzdata.framework.core.domain.BaseEntity;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * API调用日志业务对象 sys_api_log
 *
 * <AUTHOR>
 * @date 2024-09-25
 */

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("API调用日志业务对象")
public class SysApiLogBo extends BaseEntity {

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID", required = true)
    @NotBlank(message = "主键ID不能为空", groups = { EditGroup.class })
    private String id;

    /**
     * 租户ID
     */
    @ApiModelProperty(value = "租户ID", required = true)
    @NotBlank(message = "租户ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private String tenantId;

    /**
     * 关联业务对象ID
     */
    @ApiModelProperty(value = "关联业务对象ID", required = true)
    @NotBlank(message = "关联业务对象ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private String sourceId;

    /**
     * 关联业务场景
     */
    @ApiModelProperty(value = "关联业务场景", required = true)
    @NotBlank(message = "关联业务场景不能为空", groups = { AddGroup.class, EditGroup.class })
    private String sourceType;

    /**
     * API归属的应用ID
     */
    @ApiModelProperty(value = "API归属的应用ID", required = true)
    @NotBlank(message = "API归属的应用ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private String appId;

    /**
     * API归属的应用名称
     */
    @ApiModelProperty(value = "API归属的应用名称", required = true)
    @NotBlank(message = "API归属的应用名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String appName;

    /**
     * 调用的API类型  POST GET  DELETE
     */
    @ApiModelProperty(value = "调用的API类型  POST GET  DELETE", required = true)
    @NotBlank(message = "调用的API类型  POST GET  DELETE不能为空", groups = { AddGroup.class, EditGroup.class })
    private String apiType;

    /**
     * 调用的API地址
     */
    @ApiModelProperty(value = "调用的API地址", required = true)
    @NotBlank(message = "调用的API地址不能为空", groups = { AddGroup.class, EditGroup.class })
    private String apiUrl;

    /**
     * API请求参数
     */
    @ApiModelProperty(value = "API请求参数", required = true)
    @NotBlank(message = "API请求参数不能为空", groups = { AddGroup.class, EditGroup.class })
    private String requestParam;

    /**
     * API请求时间
     */
    @ApiModelProperty(value = "API请求时间", required = true)
    @NotNull(message = "API请求时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date requestTime;

    /**
     * API调用状态  1 成功  0 失败
     */
    @ApiModelProperty(value = "API调用状态  0 成功  1 失败", required = true)
    @NotNull(message = "API调用状态  0 成功  1 失败不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long status;

    /**
     * API响应状态码
     */
    @ApiModelProperty(value = "API响应状态码", required = true)
    @NotBlank(message = "API响应状态码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String responseStatus;

    /**
     * API响应信息
     */
    @ApiModelProperty(value = "API响应信息", required = true)
    @NotBlank(message = "API响应信息不能为空", groups = { AddGroup.class, EditGroup.class })
    private String responseInfo;

    /**
     * API响应时间
     */
    @ApiModelProperty(value = "API响应时间", required = true)
    @NotNull(message = "API响应时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date responseTime;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注", required = true)
    @NotBlank(message = "备注不能为空", groups = { AddGroup.class, EditGroup.class })
    private String remark;


    /**
     * 分页大小
     */
    @ApiModelProperty("分页大小")
    private Integer pageSize;

    /**
     * 当前页数
     */
    @ApiModelProperty("当前页数")
    private Integer pageNum;

    /**
     * 排序列
     */
    @ApiModelProperty("排序列")
    private String orderByColumn;

    /**
     * 排序的方向desc或者asc
     */
    @ApiModelProperty(value = "排序的方向", example = "asc,desc")
    private String isAsc;

}

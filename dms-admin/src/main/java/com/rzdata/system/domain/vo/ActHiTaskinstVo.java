package com.rzdata.system.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;


/**
 * VIEW视图对象 act_hi_taskinst
 *
 * <AUTHOR>
 * @date 2024-08-30
 */
@Data
@ApiModel("VIEW视图对象")
@ExcelIgnoreUnannotated
public class ActHiTaskinstVo {

	private static final long serialVersionUID = 1L;

    /**
     * 
     */
	@ExcelProperty(value = "")
	@ApiModelProperty("")
	private String id;

    /**
     * 
     */
	@ExcelProperty(value = "")
	@ApiModelProperty("")
	private String procDefId;

    /**
     * 
     */
	@ExcelProperty(value = "")
	@ApiModelProperty("")
	private String taskDefKey;

    /**
     * 
     */
	@ExcelProperty(value = "")
	@ApiModelProperty("")
	private String procInstId;

    /**
     * 
     */
	@ExcelProperty(value = "")
	@ApiModelProperty("")
	private String executionId;

    /**
     * 
     */
	@ExcelProperty(value = "")
	@ApiModelProperty("")
	private String name;

    /**
     * 
     */
	@ExcelProperty(value = "")
	@ApiModelProperty("")
	private String parentTaskId;

    /**
     * 
     */
	@ExcelProperty(value = "")
	@ApiModelProperty("")
	private String description;

    /**
     * 
     */
	@ExcelProperty(value = "")
	@ApiModelProperty("")
	private String owner;

    /**
     * 
     */
	@ExcelProperty(value = "")
	@ApiModelProperty("")
	private String assignee;

    /**
     * 
     */
	@ExcelProperty(value = "")
	@ApiModelProperty("")
	private Date startTime;

    /**
     * 
     */
	@ExcelProperty(value = "")
	@ApiModelProperty("")
	private Date claimTime;

    /**
     * 
     */
	@ExcelProperty(value = "")
	@ApiModelProperty("")
	private Date endTime;

    /**
     * 
     */
	@ExcelProperty(value = "")
	@ApiModelProperty("")
	private Long duration;

    /**
     * 
     */
	@ExcelProperty(value = "")
	@ApiModelProperty("")
	private String deleteReason;

    /**
     * 
     */
	@ExcelProperty(value = "")
	@ApiModelProperty("")
	private Long priority;

    /**
     * 
     */
	@ExcelProperty(value = "")
	@ApiModelProperty("")
	private Date dueDate;

    /**
     * 
     */
	@ExcelProperty(value = "")
	@ApiModelProperty("")
	private String formKey;

    /**
     * 
     */
	@ExcelProperty(value = "")
	@ApiModelProperty("")
	private String category;

    /**
     * 
     */
	@ExcelProperty(value = "")
	@ApiModelProperty("")
	private String tenantId;

    /**
     * 
     */
	@ExcelProperty(value = "")
	@ApiModelProperty("")
	private String procTitle;

    /**
     * 
     */
	@ExcelProperty(value = "")
	@ApiModelProperty("")
	private String SENDER;

    /**
     * 
     */
	@ExcelProperty(value = "")
	@ApiModelProperty("")
	private String preTaskDefKey;

    /**
     * 
     */
	@ExcelProperty(value = "")
	@ApiModelProperty("")
	private String preTaskId;

    /**
     * 
     */
	@ExcelProperty(value = "")
	@ApiModelProperty("")
	private String preTaskDefName;

    /**
     * 
     */
	@ExcelProperty(value = "")
	@ApiModelProperty("")
	private String actionType;

    /**
     * 
     */
	@ExcelProperty(value = "")
	@ApiModelProperty("")
	private String topExecutionId;

    /**
     * 
     */
	@ExcelProperty(value = "")
	@ApiModelProperty("")
	private String sendUserName;

    /**
     * 
     */
	@ExcelProperty(value = "")
	@ApiModelProperty("")
	private String senderOrgId;

    /**
     * 
     */
	@ExcelProperty(value = "")
	@ApiModelProperty("")
	private String senderOrgName;

    /**
     * 
     */
	@ExcelProperty(value = "")
	@ApiModelProperty("")
	private String assigneeUserName;

    /**
     * 
     */
	@ExcelProperty(value = "")
	@ApiModelProperty("")
	private String assigneeOrgId;

    /**
     * 
     */
	@ExcelProperty(value = "")
	@ApiModelProperty("")
	private String assigneeOrgName;

    /**
     * 
     */
	@ExcelProperty(value = "")
	@ApiModelProperty("")
	private String assigneeUserId;

    /**
     * 
     */
	@ExcelProperty(value = "")
	@ApiModelProperty("")
	private String sendUserId;

    /**
     * 
     */
	@ExcelProperty(value = "")
	@ApiModelProperty("")
	private String procDefName;


}

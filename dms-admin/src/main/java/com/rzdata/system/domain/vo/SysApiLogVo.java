package com.rzdata.system.domain.vo;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.rzdata.framework.annotation.ExcelDictFormat;
import com.rzdata.framework.convert.ExcelDictConvert;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * API调用日志视图对象 sys_api_log
 *
 * <AUTHOR>
 * @date 2024-09-25
 */
@Data
@ApiModel("API调用日志视图对象")
@ExcelIgnoreUnannotated
public class SysApiLogVo {

	private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
	@ExcelProperty(value = "主键ID")
	@ApiModelProperty("主键ID")
	private String id;

    /**
     * 租户ID
     */
	@ExcelProperty(value = "租户ID")
	@ApiModelProperty("租户ID")
	private String tenantId;

    /**
     * 关联业务对象ID
     */
	@ExcelProperty(value = "关联业务对象ID")
	@ApiModelProperty("关联业务对象ID")
	private String sourceId;

    /**
     * 关联业务场景
     */
	@ExcelProperty(value = "关联业务场景")
	@ApiModelProperty("关联业务场景")
	private String sourceType;

    /**
     * API归属的应用ID
     */
	@ExcelProperty(value = "API归属的应用ID")
	@ApiModelProperty("API归属的应用ID")
	private String appId;

    /**
     * API归属的应用名称
     */
	@ExcelProperty(value = "API归属的应用名称")
	@ApiModelProperty("API归属的应用名称")
	private String appName;

    /**
     * 调用的API类型  POST GET  DELETE
     */
	@ExcelProperty(value = "调用的API类型  POST GET  DELETE")
	@ApiModelProperty("调用的API类型  POST GET  DELETE")
	private String apiType;

    /**
     * 调用的API地址
     */
	@ExcelProperty(value = "调用的API地址")
	@ApiModelProperty("调用的API地址")
	private String apiUrl;

    /**
     * API请求参数
     */
	@ExcelProperty(value = "API请求参数")
	@ApiModelProperty("API请求参数")
	private String requestParam;

    /**
     * API请求时间
     */
	@ExcelProperty(value = "API请求时间")
	@ApiModelProperty("API请求时间")
	private Date requestTime;

    /**
     * API调用状态  0 成功  1 失败
     */
	@ExcelProperty(value = "API调用状态  0 成功  1 失败")
	@ApiModelProperty("API调用状态  0 成功  1 失败")
	private Long status;

    /**
     * API响应状态码
     */
	@ExcelProperty(value = "API响应状态码")
	@ApiModelProperty("API响应状态码")
	private String responseStatus;

    /**
     * API响应信息
     */
	@ExcelProperty(value = "API响应信息")
	@ApiModelProperty("API响应信息")
	private String responseInfo;

    /**
     * API响应时间
     */
	@ExcelProperty(value = "API响应时间")
	@ApiModelProperty("API响应时间")
	private Date responseTime;

    /**
     * 备注
     */
	@ExcelProperty(value = "备注")
	@ApiModelProperty("备注")
	private String remark;


}

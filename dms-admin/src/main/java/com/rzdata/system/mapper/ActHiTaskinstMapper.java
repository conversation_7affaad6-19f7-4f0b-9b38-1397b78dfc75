package com.rzdata.system.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rzdata.framework.core.mybatisplus.core.BaseMapperPlus;
import com.rzdata.process.domain.vo.UniteworkTaskVo;
import com.rzdata.system.domain.ActHiTaskinst;
import com.rzdata.system.domain.bo.ActHiTaskinstBo;
import com.rzdata.process.domain.bo.UniteworkTaskBo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * VIEWMapper接口
 *
 * <AUTHOR>
 * @date 2024-08-30
 */
public interface ActHiTaskinstMapper extends BaseMapperPlus<ActHiTaskinst> {

    List<ActHiTaskinst> getList(@Param("bo") ActHiTaskinstBo paramActHiTaskinstBo);

    Page<UniteworkTaskVo> getUniteworkTaskList(@Param("page") Page<UniteworkTaskBo> page, @Param("bo") UniteworkTaskBo bo,@Param("bpmDataSource") String bpmDataSource);
}

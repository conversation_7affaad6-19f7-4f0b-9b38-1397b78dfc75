package com.rzdata.system.mapper;

import com.rzdata.framework.core.domain.entity.SysDept;
import com.rzdata.framework.core.mybatisplus.core.BaseMapperPlus;
import com.rzdata.system.domain.vo.SysDeptVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 部门管理 数据层
 *
 * <AUTHOR> <PERSON>
 */
public interface SysDeptMapper extends BaseMapperPlus<SysDept> {

    /**
     * 查询部门管理数据
     *
     * @param dept 部门信息
     * @return 部门信息集合
     */
    List<SysDept> selectDeptList(SysDept dept);

    /**
     * 根据角色ID查询部门树信息
     *
     * @param roleId            角色ID
     * @param deptCheckStrictly 部门树选择项是否关联显示
     * @return 选中部门列表
     */
    List<Integer> selectDeptListByRoleId(@Param("roleId") Long roleId, @Param("deptCheckStrictly") boolean deptCheckStrictly);

    /**
     * 修改子元素关系
     *
     * @param depts 子元素
     * @return 结果
     */
    int updateDeptChildren(@Param("depts") List<SysDept> depts);

    List<SysDeptVo> selectDeptListToAiShu();

    List<SysDept> findParentOrgTree(String deptId);

    SysDept getCompanyByDeptId(String deptId);

    List<SysDept> selectDeptListDetail(SysDept dept);

    int updateDeptView(@Param("dept") SysDept dept,@Param("ubaseDataSource") String ubaseDataSource);

    int insertDeptView(@Param("dept") SysDept dept,@Param("ubaseDataSource") String ubaseDataSource);
}

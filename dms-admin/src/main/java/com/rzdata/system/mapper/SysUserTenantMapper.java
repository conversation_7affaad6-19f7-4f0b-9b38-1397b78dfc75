package com.rzdata.system.mapper;

import com.rzdata.framework.core.domain.entity.SysDept;
import com.rzdata.framework.core.mybatisplus.core.BaseMapperPlus;
import com.rzdata.system.domain.SysUserTenant;
import com.rzdata.system.domain.vo.SysUserTenantVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/3/10 10:55
 * @Version 1.0
 * @Description
 */
@Mapper
public interface SysUserTenantMapper extends BaseMapperPlus<SysUserTenant> {
    List<String> selectTenantListByUserId(String userId);

    List<SysUserTenantVo> getTenantListByTenantId(String tenantId);

    int insertUserTenantView(@Param("userTenant") SysUserTenant userTenant, @Param("ubaseDataSource") String ubaseDataSource);

    int deleteUserTenantByUserId(@Param("userId") String userId, @Param("ubaseDataSource") String ubaseDataSource);
}

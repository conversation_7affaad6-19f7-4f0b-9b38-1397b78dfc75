package com.rzdata.system.service;

import com.rzdata.system.domain.SysApiLog;
import com.rzdata.system.domain.vo.SysApiLogVo;
import com.rzdata.system.domain.bo.SysApiLogBo;
import com.rzdata.framework.core.mybatisplus.core.IServicePlus;
import com.rzdata.framework.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * API调用日志Service接口
 *
 * <AUTHOR>
 * @date 2024-09-25
 */
public interface ISysApiLogService extends IServicePlus<SysApiLog, SysApiLogVo> {
	/**
	 * 查询单个
	 * @return
	 */
	SysApiLogVo queryById(String id);

	/**
	 * 查询列表
	 */
    TableDataInfo<SysApiLogVo> queryPageList(SysApiLogBo bo);

	/**
	 * 查询列表
	 */
	List<SysApiLogVo> queryList(SysApiLogBo bo);

	/**
	 * 根据新增业务对象插入API调用日志
	 * @param bo API调用日志新增业务对象
	 * @return
	 */
	Boolean insertByBo(SysApiLogBo bo);

	/**
	 * 根据编辑业务对象修改API调用日志
	 * @param bo API调用日志编辑业务对象
	 * @return
	 */
	Boolean updateByBo(SysApiLogBo bo);

	/**
	 * 校验并删除数据
	 * @param ids 主键集合
	 * @param isValid 是否校验,true-删除前校验,false-不校验
	 * @return
	 */
	Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);
}

package com.rzdata.system.service;

import cn.hutool.core.lang.tree.Tree;
import com.baomidou.mybatisplus.extension.service.IService;
import com.rzdata.framework.core.domain.entity.SysDept;
import com.rzdata.framework.core.domain.entity.SysDeptDetail;
import com.rzdata.system.domain.vo.SysDeptVo;

import java.util.List;
import java.util.Map;

/**
 * 部门管理 服务层
 *
 * <AUTHOR> Li
 */
public interface ISysDeptDetailService extends IService<SysDeptDetail> {

    SysDeptDetail loadDeptDetails(String deptId);
}

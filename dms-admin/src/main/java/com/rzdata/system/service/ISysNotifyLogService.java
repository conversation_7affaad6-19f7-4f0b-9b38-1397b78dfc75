package com.rzdata.system.service;

import com.rzdata.system.domain.SysNotifyLog;
import com.rzdata.system.domain.vo.SysNotifyLogVo;
import com.rzdata.system.domain.bo.SysNotifyLogBo;
import com.rzdata.framework.core.mybatisplus.core.IServicePlus;
import com.rzdata.framework.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 消息记录Service接口
 *
 * <AUTHOR>
 * @date 2024-04-18
 */
public interface ISysNotifyLogService extends IServicePlus<SysNotifyLog, SysNotifyLogVo> {
	/**
	 * 查询单个
	 * @return
	 */
	SysNotifyLogVo queryById(Long logId);

	/**
	 * 查询列表
	 */
    TableDataInfo<SysNotifyLogVo> queryPageList(SysNotifyLogBo bo);

	/**
	 * 查询列表
	 */
	List<SysNotifyLogVo> queryList(SysNotifyLogBo bo);

	/**
	 * 根据新增业务对象插入消息记录
	 * @param bo 消息记录新增业务对象
	 * @return
	 */
	Boolean insertByBo(SysNotifyLogBo bo);

	/**
	 * 根据编辑业务对象修改消息记录
	 * @param bo 消息记录编辑业务对象
	 * @return
	 */
	Boolean updateByBo(SysNotifyLogBo bo);

	/**
	 * 校验并删除数据
	 * @param ids 主键集合
	 * @param isValid 是否校验,true-删除前校验,false-不校验
	 * @return
	 */
	Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

	/**
	 * 异步，发送邮件，并且记录日志
	 * @param sendType 消息编码
	 * @param nickName 发送人员名称
	 * @param email 发送人员联系方式
	 * @param userId 通知的用户id
	 * @param param 参数
	 */
	public void sendEmail(String sendType, String nickName, String email, String userId, Map<String, String> param);

	/**
	 * 不是异步处理，发送邮件，并且记录日志
	 * @param sendType
	 * @param nickName
	 * @param email
	 * @param userId
	 * @param param
	 */
	public void notAsyncSendEmail(String sendType, String nickName, String email, String userId, Map<String, String> param);
}

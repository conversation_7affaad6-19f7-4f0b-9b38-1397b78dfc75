package com.rzdata.system.service;

import cn.hutool.core.lang.tree.Tree;
import com.baomidou.mybatisplus.extension.service.IService;
import com.rzdata.framework.core.domain.entity.SysUser;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.core.service.UserService;
import com.rzdata.system.domain.vo.SysUserVo;

import java.util.List;
import java.util.Map;

/**
 * 用户 业务层
 *
 * <AUTHOR> Li
 */
public interface ISysUserService extends IService<SysUser>, UserService {


    TableDataInfo<SysUser> selectPageUserList(SysUser user);

    TableDataInfo<SysUser> selectPageUserListForGroup(SysUser user);

    /**
     * 查询用户昵称
     * @param userNames
     * @return
     */
    Map<String, String> queryNickNameByUserName(List<String> userNames);


    /**
     * 所有正常的账号
     * @param
     * @return
     */
    List<SysUser> selectList();

    /**
     * 根据角色key查询用户列表
     *
     * @param roleKey 角色key
     * @return 用户信息集合信息
     */
    List<SysUser> selectListAllByRoleKey(String roleKey);

    /**
     * 根据条件分页查询用户列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    List<SysUser> selectUserList(SysUser user);

    /**
     * 根据条件分页查询已分配用户角色列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    TableDataInfo<SysUser> selectAllocatedList(SysUser user);

    /**
     * 根据条件分页查询未分配用户角色列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    TableDataInfo<SysUser> selectUnallocatedList(SysUser user);

    /**
     * 通过用户名查询用户
     *
     * @param userName 用户名
     * @return 用户对象信息
     */
    @Override
    SysUser selectUserByUserName(String userName);


    /**
     * 通过用户第三方ID查询用户
     *
     * @param thirdId 用户第三方ID
     * @return 用户对象信息
     */
    SysUser selectUserByThirdId(String thirdId);


    /**
     * 通过用户ID查询用户
     *
     * @param userId 用户ID
     * @return 用户对象信息
     */
    @Override
    SysUser selectUserById(String userId);

    /**
     * 根据用户ID查询用户所属角色组
     *
     * @param userName 用户名
     * @return 结果
     */
    String selectUserRoleGroup(String userName);

    /**
     * 根据用户ID查询用户所属岗位组
     *
     * @param userName 用户名
     * @return 结果
     */
    String selectUserPostGroup(String userName);

    /**
     * 校验用户名称是否唯一
     *
     * @param userName 用户名称
     * @return 结果
     */
    String checkUserNameUnique(String userName);

    /**
     * 校验手机号码是否唯一
     *
     * @param user 用户信息
     * @return 结果
     */
    String checkPhoneUnique(SysUser user);

    /**
     * 校验email是否唯一
     *
     * @param user 用户信息
     * @return 结果
     */
    String checkEmailUnique(SysUser user);

    /**
     * 校验用户是否允许操作
     *
     * @param user 用户信息
     */
    void checkUserAllowed(SysUser user);

    /**
     * 校验用户是否有数据权限
     *
     * @param userId 用户id
     */
    void checkUserDataScope(String userId);

    /**
     * 新增用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    int insertUser(SysUser user);

    /**
     * 注册用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    boolean registerUser(SysUser user);

    /**
     * 修改用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    int updateUser(SysUser user);

    /**
     * 用户授权角色
     *
     * @param userId  用户ID
     * @param roleIds 角色组
     */
    void insertUserAuth(String userId, Long[] roleIds);

    /**
     * 修改用户状态
     *
     * @param user 用户信息
     * @return 结果
     */
    int updateUserStatus(SysUser user);

    /**
     * 修改用户基本信息
     *
     * @param user 用户信息
     * @return 结果
     */
    int updateUserProfile(SysUser user);

    /**
     * 修改用户头像
     *
     * @param userName 用户名
     * @param avatar   头像地址
     * @return 结果
     */
    boolean updateUserAvatar(String userName, String avatar);

    /**
     * 重置用户密码
     *
     * @param user 用户信息
     * @return 结果
     */
    int resetPwd(SysUser user);

    /**
     * 重置用户密码
     *
     * @param userName 用户名
     * @param password 密码
     * @return 结果
     */
    int resetUserPwd(String userName, String password);

    /**
     * 通过用户ID删除用户
     *
     * @param userId 用户ID
     * @return 结果
     */
    int deleteUserById(String userId);

    /**
     * 批量删除用户信息
     *
     * @param userIds 需要删除的用户ID
     * @return 结果
     */
    int deleteUserByIds(String[] userIds);

    /**
     * 导入用户数据
     *
     * @param userList        用户数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName        操作用户
     * @return 结果
     */
    String importUser(List<SysUser> userList, Boolean isUpdateSupport, String operName);

    /**
     * 通过部门id查出部门文件管理员
     * @param deptId
     * @return
     */

    List<SysUser> selectDeptFileManagerByDeptId(String deptId);

    /**
     * 查找质量负责人和生产负责人
     * @return
     */
    List<SysUser> selectQaAndSc();

    boolean isQa(String username);

    List<SysUserVo> selectUserToAiShu();

    /**
     *  查出指定部门指定岗位指定租户的用户
     *  定时任务专用
     * @return 用户列表
     */
    List<SysUserVo> selectUserByDeptPost(List<String> deptIds,List<Integer> postIds,String tenantId);


    /**
     * 通过部门id和菜单权限代码(sys_menu的perms) 分页查询出该部门下具有该权限的用户
     * @param deptId 部门id
     * @param perms 权限代码(sys_menu的perms字段)
     * @return
     */
    TableDataInfo<SysUser> selectUserByDeptIdAndPerms(String deptId,String perms);


    /**
     * 构建前端所需要下拉树结构
     *
     * @return 下拉树结构列表
     */
    List<Tree<String>> buildDeptTreeSelect();

    /**
     * 根据账号获取昵称
     *
     * @return 下拉树结构列表
     */
    String getNickName(String userName);

    Integer getNumByDeptId(String deptId);

    List<SysUser> getUserByDeptId(String deptId);

    Integer getNumByTenantId(String secDeptId);

    List<SysUser> getFullPathRoleUserList(String roleKey,String deptId);


    /**
     * 根据租户查询所有用户
     * @param tenantId
     * @return
     */
    public List<SysUser> getListByTenantId(String tenantId);

    SysUser getLeader(String userName,String deptId);

    SysUser getDivisionLeader(String deptId);

    List<SysUser> getDocManagersByDeptId(String deptId);

    String getDeptByUserName(String userName);

    /**
     * 查询应用账号列表
     * @param user
     * @return
     */
    TableDataInfo<SysUser> selectPageApplicationUserList(SysUser user);

    /**
     * 新增应用账号
     * @param user
     * @return
     */
    int insertApplicationUser(SysUser user);

    /**
     * 修改应用账号
     * @param user
     * @return
     */
    int updateApplicationUser(SysUser user);
    /**
     * 根据用户名列表查询用户昵称映射
     * @param userNames 用户名列表
     * @return Map<用户名, 昵称>
     */
    Map<String, String> selectUserMapByUserNames(List<String> userNames);

    List<SysUser> getUserByUserNames(String userNames);
}

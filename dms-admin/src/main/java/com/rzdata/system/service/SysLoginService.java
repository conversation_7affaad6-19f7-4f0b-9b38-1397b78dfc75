package com.rzdata.system.service;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.jeecg.qywx.api.core.common.AccessToken;
import com.jeecg.qywx.api.user.vo.User;
import com.rzdata.config.CustomConfig;
import com.rzdata.framework.constant.Constants;
import com.rzdata.framework.core.domain.entity.SysDept;
import com.rzdata.framework.core.domain.entity.SysUser;
import com.rzdata.framework.core.domain.model.CorpInfo;
import com.rzdata.framework.core.domain.model.LoginUser;
import com.rzdata.framework.core.redis.RedisCache;
import com.rzdata.framework.core.service.LogininforService;
import com.rzdata.framework.core.service.QywxAuthService;
import com.rzdata.framework.core.service.TokenService;
import com.rzdata.framework.enums.ApiTypeEnum;
import com.rzdata.framework.exception.ServiceException;
import com.rzdata.framework.exception.user.CaptchaException;
import com.rzdata.framework.exception.user.CaptchaExpireException;
import com.rzdata.framework.exception.user.UserPasswordNotMatchException;
import com.rzdata.framework.utils.*;
import com.rzdata.framework.utils.spring.SpringUtils;
import com.rzdata.system.domain.SysOperLog;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Service;
import org.springframework.security.crypto.password.PasswordEncoder;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.Date;

/**
 * 登录校验方法
 *
 * <AUTHOR> Li
 */
@Service
@Slf4j
public class SysLoginService {

    @Autowired
    private TokenService tokenService;

    @Resource
    private AuthenticationManager authenticationManager;

    @Autowired
    private ISysUserService userService;

    @Autowired
    private ISysConfigService configService;

    @Autowired
    private LogininforService asyncService;
    @Resource
    private PasswordEncoder passwordEncoder;
    @Resource
    private ISysOperLogService operLogService;

    /**
     * 权限服务类
     */
    @Autowired
    SysPermissionService permissionService;

    /**
     * 部门服务类
     */
    @Autowired
    ISysDeptService sysDeptService;

    @Autowired
    QywxAuthService qywxAuthService;

    @Resource
    CustomConfig customConfig;

    public boolean verifyIdentity(String password) {
        // 获取当前登录用户信息
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if (loginUser == null) {
            throw new ServiceException("用户未登录");
        }
        // 记录操作日志对象
        SysOperLog operLog = new SysOperLog();
        /*operLog.setOperTime(new Date());
        operLog.setOperName(loginUser.getUsername());
        operLog.setTitle("身份验证");
        operLog.setOperIp(SecurityUtils.getIpAddr());*/

        try {
            // 验证密码
            boolean matches = passwordEncoder.matches(password, loginUser.getPassword());
            // 设置操作状态
            operLog.setStatus(matches ? 0 : 1);
            operLog.setErrorMsg(matches ? "验证成功" : "密码错误");

            // 记录日志
            /*operLogService.insertOperlog(operLog);*/
            
            if (!matches) {
                throw new ServiceException("密码错误");
            }
            
            return true;
        } catch (Exception e) {
            // 设置错误信息
            // 设置错误信息
            /*operLog.setStatus(1);
            operLog.setErrorMsg(e.getMessage());
            // 记录日志
            operLogService.insertOperlog(operLog);*/
            throw e;
        }
    }

    /**
     * 登录验证
     *
     * @param username 用户名
     * @param password 密码
     * @param code     验证码
     * @param uuid     唯一标识
     * @param tenantId 租户id
     * @return 结果
     */
    public String login(String username, String password, String code, String uuid, String tenantId, ApiTypeEnum type) {
        HttpServletRequest request = ServletUtils.getRequest();
        boolean captchaOnOff = configService.selectCaptchaOnOff();
        // 验证码开关 只有网页端才需要验证码判断
        if (captchaOnOff && ObjectUtil.equals(ApiTypeEnum.PC, type)) {
            validateCaptcha(username, code, uuid, request);
        }
        // 用户验证
        Authentication authentication;
        try {
            // 该方法会去调用UserDetailsServiceImpl.loadUserByUsername
            authentication = authenticationManager
                    .authenticate(new UsernamePasswordAuthenticationToken(username, password));
        } catch (Exception e) {
            if (e instanceof BadCredentialsException) {
                asyncService.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.password.not.match"), request);
                throw new UserPasswordNotMatchException();
            } else {
                asyncService.recordLogininfor(username, Constants.LOGIN_FAIL, e.getMessage(), request);
                throw new ServiceException(e.getMessage());
            }
        }
        asyncService.recordLogininfor(username, Constants.LOGIN_SUCCESS, MessageUtils.message("user.login.success"), request);
        LoginUser loginUser = (LoginUser) authentication.getPrincipal();
        if (ObjectUtil.isNotEmpty(tenantId) && ObjectUtil.isEmpty(loginUser.getTenantId())) {
            loginUser.setTenantId(tenantId);
        }
        recordLoginInfo(loginUser.getUserId(), username);
        // 生成token
        return tokenService.createToken(loginUser);
    }

    /**
     * 单点登录验证
     *
     * @param username 用户名
     * @return 结果
     */
    public String sso(String username) {
        String result = "";
        HttpServletRequest request = ServletUtils.getRequest();
        SysUser userObj = this.userService.selectUserByUserName(username);
        if (userObj != null) {
            if (userObj.getStatus().equals("0")) {
                // 账号启用的情况下
                asyncService.recordLogininfor(username, Constants.LOGIN_SUCCESS, MessageUtils.message("user.login.success") + " " + request.getParameter("client"), request);
                recordLoginInfo(userObj.getUserId(), username);
                // 用户挂靠的组织对象
                SysDept deptObj = this.sysDeptService.getById(userObj.getDeptId());
                // 用户所属的二级部门对象
                SysDept secDeptObj = null;
                if (StringUtils.isNotEmpty(deptObj.getSecDeptId())) {
                    secDeptObj = this.sysDeptService.getById(deptObj.getSecDeptId());
                }
                LoginUser loginUser = new LoginUser(userObj.getUserId(), userObj.getDeptId(), deptObj.getDeptName(),
                        secDeptObj != null ? secDeptObj.getDeptId() : null,
                        secDeptObj != null ? secDeptObj.getDeptName() : null,
                        userObj.getUserName(),
                        userObj.getPassword(), permissionService.getMenuPermission(userObj),
                        userObj.getDept().getDeptCode(), userObj.getNickName(), userObj.getTenantId(),deptObj.getAncestors());
                result = tokenService.createToken(loginUser);
            }
        }
        // 生成token
        return result;
    }


    /**
     * 企业微信SSO登录-getCode
     *
     * @return
     */
    public void getCorpWechatCode(HttpServletResponse response) {
        try {
            CorpInfo corpInfo = qywxAuthService.getCorpInfo();
            if (null != corpInfo) {
                log.error("corpInfo======>" + JSON.toJSONString(corpInfo));
            }
            QywxUtil.authorize(corpInfo.getCorpId(), customConfig.getQywxRedirectUri(), "STATE",response); //发起企业微信网页授权，获取code
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }


    /**
     * 解析重定向URI
     * 该方法用于解析从 HttpServletRequest 中获取的重定向URI
     * 可以用于处理例如单点登录（SSO）服务重定向回来的请求，从中提取有用信息
     *
     * @param request HttpServletRequest对象，包含服务器请求和响应的读写器和各种实用方法
     *                用于获取重定向回来的URI中的参数等信息
     */
    public void parseRedirectUri(HttpServletRequest request, HttpServletResponse response) {
        //企业微信生成的code，用于获取企业微信用户信息
        String code = request.getParameter("code");
        String state = request.getParameter("state");
        String token = "";
        if (StringUtils.isNotEmpty(code)) {
            try {
                token = getTokenByQywxCode(code, request);
                response.sendRedirect(customConfig.getTokenRedirectUri() + "?token=" + token);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
    }


    /**
     * 根据企业微信Code,获取token
     *
     * @param code 企业微信code
     * @return
     */
    public String getTokenByQywxCode(String code, HttpServletRequest request) {
        log.error("code:" + code);
        try {
            CorpInfo corpInfo = qywxAuthService.getCorpInfo();
            if (StringUtils.isNotEmpty(code)) {
                AccessToken accessToken = qywxAuthService.getAccessToken(corpInfo.getCorpId(), corpInfo.getSecret()); //通过QywxService 获取
                if (ObjectUtil.isNotNull(accessToken)) {
                    log.error(JSON.toJSONString(accessToken));
                }
                User wechatUser = QywxUtil.getUserInfoByCode(code, accessToken.getAccesstoken());
                if (null != wechatUser) {
                    log.error(JSON.toJSONString(wechatUser));
                    //根据企业微信user获取DMS中的dmsUser, 并将dmsUser转化为loginUser
                    LoginUser loginUser = qywxAuthService.getLoginUser(wechatUser.getUserid());
                    if (ObjectUtil.isNotNull(loginUser)) {
                        log.error(JSON.toJSONString(loginUser));
                    }
                    String token = qywxAuthService.loginSSO(loginUser.getUsername());
                    if (StringUtils.isNull(SecurityUtils.getAuthentication())) {
                        UsernamePasswordAuthenticationToken authenticationToken = new UsernamePasswordAuthenticationToken(loginUser, null, loginUser.getAuthorities());
                        authenticationToken.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
                        SecurityContextHolder.getContext().setAuthentication(authenticationToken);
                    }

                    return token;
                }
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return null;
    }

    /**
     * 校验验证码
     *
     * @param username 用户名
     * @param code     验证码
     * @param uuid     唯一标识
     * @return 结果
     */
    public void validateCaptcha(String username, String code, String uuid, HttpServletRequest request) {
        String verifyKey = Constants.CAPTCHA_CODE_KEY + uuid;
        String captcha = SpringUtils.getBean(RedisCache.class).getCacheObject(verifyKey);
        SpringUtils.getBean(RedisCache.class).deleteObject(verifyKey);
        if (captcha == null) {
            asyncService.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.jcaptcha.expire"), request);
            throw new CaptchaExpireException();
        }
        if (!code.equalsIgnoreCase(captcha)) {
            asyncService.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.jcaptcha.error"), request);
            throw new CaptchaException();
        }
    }

    /**
     * 记录登录信息
     *
     * @param userId 用户ID
     */
    public void recordLoginInfo(String userId, String username) {
        SysUser sysUser = new SysUser();
        sysUser.setUserId(userId);
        sysUser.setLoginIp(ServletUtils.getClientIP());
        sysUser.setLoginDate(DateUtils.getNowDate());
        sysUser.setUpdateBy(username);
        userService.updateUserProfile(sysUser);
    }

    /**
     * 刷新令牌
     * @return 新的token
     */
    public String refreshToken(String token) {
        // 获取当前的LoginUser
        LoginUser loginUser=null;
        try{
            loginUser = (LoginUser) SecurityUtils.getLoginUser();
        }catch(Exception e){
            // 重新生成token
            try{
                String username  = tokenService.getUsernameFromToken(token);
                if(StringUtils.isNotEmpty(username)){
                    SysUser userObj = this.userService.selectUserByUserName(username);
                    loginUser = new LoginUser(userObj.getUserId(), userObj.getDeptId(), null,
                    null, null,
                    userObj.getUserName(), userObj.getPassword(), permissionService.getMenuPermission(userObj),
                    userObj.getDept().getDeptCode(), userObj.getNickName(), userObj.getTenantId(),userObj.getDept().getAncestors());
                }
            }catch(Exception e2){
                throw new ServiceException("token 有误");
            }
        }
        if(loginUser == null){
            throw new ServiceException("用户不存在");
        }
        return tokenService.createToken(loginUser);
    }
}


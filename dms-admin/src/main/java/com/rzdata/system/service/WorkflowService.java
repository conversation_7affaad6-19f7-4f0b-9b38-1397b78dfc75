package com.rzdata.system.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.blueland.bpmclient.BpmClient;
import com.blueland.bpmclient.model.*;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.rzdata.config.CustomConfig;
import com.rzdata.config.ProcessConfig;
import com.rzdata.eventbus.ProcessEventBus;
import com.rzdata.eventbus.ProcessResultEvent;
import com.rzdata.framework.constant.CommonI18nConstant;
import com.rzdata.framework.constant.Constants;
import com.rzdata.framework.constant.DocMsgConstants;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.core.domain.entity.SysDept;
import com.rzdata.framework.core.domain.entity.SysDictData;
import com.rzdata.framework.core.domain.entity.SysUser;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.core.redis.RedisCache;
import com.rzdata.framework.core.service.UserService;
import com.rzdata.framework.enums.ApiTypeEnum;
import com.rzdata.framework.utils.*;
import com.rzdata.framework.utils.spring.SpringUtils;
import com.rzdata.oa.config.OaProperties;
import com.rzdata.oa.service.TodoService;
import com.rzdata.oa.webservice.NotifyTodoRemoveContext;
import com.rzdata.oa.webservice.NotifyTodoSendContext;
import com.rzdata.process.domain.DocMessage;
import com.rzdata.process.domain.WorkFlowInfo;
import com.rzdata.process.domain.WorkflowApplyLog;
import com.rzdata.process.domain.WorkflowLog;
import com.rzdata.process.domain.bo.BpmClientInputModelBo;
import com.rzdata.process.domain.bo.ProcessBo;
import com.rzdata.process.domain.bo.UniteworkTaskBo;
import com.rzdata.process.domain.dto.UniteWorkTaskDTO;
import com.rzdata.process.domain.vo.UniteworkTaskVo;
import com.rzdata.process.domain.vo.WorkFlowPageVo;
import com.rzdata.process.enums.MsgTypeEnum;
import com.rzdata.process.service.*;
import com.rzdata.process.service.impl.WorkflowAsyncService;
import com.rzdata.process.utils.MsgTemplateUtils;
import com.rzdata.setting.domain.DocClassFlowNode;
import com.rzdata.setting.domain.DocClassFlowNodeDetail;
import com.rzdata.setting.domain.vo.DocClassFlowNodeVo;
import com.rzdata.setting.mapper.DocClassFlowNodeDetailMapper;
import com.rzdata.setting.mapper.DocClassFlowNodeMapper;
import com.rzdata.system.mapper.ActHiTaskinstMapper;
import com.rzdata.system.mapper.WorkflowMapper;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.net.URLEncoder;
import java.rmi.ServerException;
import java.util.*;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

import static com.rzdata.framework.constant.Constants.PROCESS_KEY;

@Slf4j
@Service("workflowService")
public class WorkflowService {
    private static int FLOW_STATUS_ERROR = 0;// 异常
    private static int FLOW_STATUS_UNREAD = 1;// 待办
    private static int FLOW_STATUS_READ = 2;// 已办
    private static int FLOW_STATUS_FINISH = 3;// 办结
    private static String END_ACT_NAME = "结束";
    private static String OA_APP_NAME = "dms";
    /**
     * 是否从通讯录选择下一步人员
     */
    private static String SHOW_TXL_SELECT = "show_txl_select";
    @Autowired
    private CustomConfig customConfig;

    // @Autowired
    // private ProcessConfig processConfig;

    @Autowired
    private UserService userService;

    @Autowired
    ISysConfigService configService;

    @Autowired
    IWorkflowLogService iWorkflowLogService;

    @Autowired
    ISysDeptService sysDeptService;

    @Autowired
    TodoService todoService;

    @Autowired
    private OaProperties oaProperties;

    @Autowired
    private IMessageSendEntryService messageSendEntryService;

    @Autowired
    private ISysNotifyLogService sysNotifyLogService;
    @Autowired
    private ISysUserService sysUserService;

    @Resource
    private DocClassFlowNodeMapper docClassFlowNodeMapper;

    @Resource
    private DocClassFlowNodeDetailMapper docClassFlowNodeDetailMapper;

    @Resource
    private WorkflowMapper workflowMapper;

    @Autowired
    private WorkflowAsyncService workflowAsyncService;

    @Autowired
    private ISysDictDataService iSysDictDataService;

    @Autowired
    private IWorkflowApplyLogService workflowApplyLogService;

    @Autowired
    ActHiTaskinstMapper actHiTaskinstMapper;

    @Autowired
    IDocMessageService docMessageService;

    private final static String CUR_ACT_DEF_KEY = "curActDef";
    private final static String CUR_ACT_INST_KEY = "curActInst";
    private final static String PROC_INST_KEY = "procInst";
    private final static String PROC_DEF_KEY = "procDef";
    private final static String ACT_OPPOWERS = "actOppowers";// 下一环节显示按钮

    public enum ENUM_ACTION {
        /**
         * // 暂存
         */
        save,
        /**
         * // 提交
         */
        submit,
        /**
         * // 作废
         */
        cancel,
        /**
         * // 转派
         */
        transfer,
        /**
         * // 退回拟稿人
         */
        backtostart,
        /**
         * // 退回上一步
         */
        backtoprev,
        /**
         * // 撤回
         */
        back,
        /**
         * // 驳回
         */
        reject,
    }

    ;

    BpmClient getBpmClient() {
        WorkflowConfig workflowConfig = new WorkflowConfig();
        workflowConfig.setTenantId(customConfig.getBpmTenantId());
        workflowConfig.setBaseURL(customConfig.getWorkflowServiceUrl());
        BpmClient bpmClient = new BpmClient(workflowConfig);
        return bpmClient;
    }

    /**
     * 开启并提交流程
     *
     * @param title      标题
     * @param procDefKey 流程key
     * @param userId     用户id
     * @param deptId     用户部门Id
     * @param businessId 业务id
     * @return
     * @throws Exception
     */
    public ProcessInstanceModel startAndSubmitProcess(String title, String procDefKey, String userId, String deptId,
            String businessId) throws Exception {
        ProcessConfig processConfig = JSONUtil.toBean(configService.selectConfigByKey(PROCESS_KEY),
                ProcessConfig.class);
        // 1、根据key 获取流程信息
        ProcessDefinitionModel key = getProcessDefinitionModel(procDefKey);
        if (ObjectUtil.isNotEmpty(key)) {
            // 2、 获取第一个环节的定义
            ActivityDefinitionModel activity = getStartactDef(key.getProcDefId());
            // 获取下一环节流程定义
            SearchQuery searchQuery = new SearchQuery();
            searchQuery.setProcDefId(key.getProcDefId());
            searchQuery.setCurActDefId(activity.getActDefId());
            List<ActivityDefinitionModel> nextList = getNextActs(searchQuery);
            if (ObjectUtil.isNotEmpty(nextList)) {
                // 获取下一环节人员定义
                SearchQuery searchQuery1 = new SearchQuery();
                // String userId = SecurityUtils.getLoginUser().getUsername();
                searchQuery1.setUserId(userId);
                searchQuery1.setUserOrgId(deptId);
                searchQuery1.setCurActDefId(activity.getActDefId());
                searchQuery1.setDestActDefId(nextList.get(0).getActDefId());
                searchQuery1.setProcDefId(key.getProcDefId());
                List<ActivityResourceModel> userList = getNextActUsers(searchQuery1);
                userList = userList.stream().filter(x -> x.getType().equals("USER")).collect(Collectors.toList());
                // 提交流程
                BpmClientInputModel bpmClientInputModel = new BpmClientInputModel();
                bpmClientInputModel.setWf_procTitle(title);
                bpmClientInputModel.setWf_procDefKey(procDefKey);
                bpmClientInputModel.setWf_sendUserId(userId);
                bpmClientInputModel.setWf_sendUserOrgId(deptId);
                bpmClientInputModel.setWf_businessKey(businessId);
                String url = processConfig.getUniteworkUrl() + "?businessKey=" + businessId;
                bpmClientInputModel.setWf_uniteworkUrl(url);
                bpmClientInputModel.setWf_nextActDefId(nextList.get(0).getProcDefId());
                bpmClientInputModel.setWf_nextActDefName(nextList.get(0).getActDefName());
                bpmClientInputModel.setWf_procDefId(key.getProcDefId());
                List<Map<String, Object>> usersList = new ArrayList<>();
                if (ObjectUtil.isNotEmpty(userList)) {
                    // userList = CollectionUtil.page(1, 10, userList);
                    for (ActivityResourceModel user : userList) {
                        Map<String, Object> map = new HashMap<>();
                        map.put("receiveUserId", user.getId());
                        // map.put("receiveUserOrgId", user.getParentId());
                        usersList.add(map);
                    }

                    // bpmClientInputModel.setWf_receiver(userList.get(0).getId());
                } else {
                    throw new ServerException(
                            I18nUtils.getTitle(CommonI18nConstant.SERVICE_WORKFLOW_SET_SESSION_HANDLER));
                }
                bpmClientInputModel.setWf_receivers(usersList);
                bpmClientInputModel.setWf_sendUserCompanyId(deptId);
                ProcessInstanceModel processInst = new ProcessInstanceModel();
                processInst.setProcInstId(activity.getProcDefId());
                // processInst.setTopProcInstId(activity.getProcDefId());
                bpmClientInputModel.setWf_curActDefName(activity.getActDefName());
                bpmClientInputModel.setWf_curActDefName(null);
                if (StrUtil.isBlank(bpmClientInputModel.getWf_businessKey())) {
                    bpmClientInputModel.setWf_businessKey(IdUtil.simpleUUID());
                }
                BpmClient bpmClient = getBpmClient();
                processInst = bpmClient.submitFlowInstance(bpmClientInputModel);
                return processInst;
                // processProcInst(bpmClientInputModel, ENUM_ACTION.submit.name());
            } else {
                throw new ServerException(I18nUtils.getTitle(CommonI18nConstant.SERVICE_WORKFLOW_QUERY_NEXT_ERR));
            }
        } else {
            throw new ServerException(I18nUtils.getTitle(CommonI18nConstant.SERVICE_WORKFLOW_QUERY_FLOW_ERR));
        }
    }

    /**
     * 暂存流程
     *
     * @param bpmClientInputModelBo
     * @param businessId            业务id
     * @return
     * @throws Exception
     */
    public ProcessInstanceModel saveExecute(BpmClientInputModelBo bpmClientInputModelBo, String businessId)
            throws Exception {
        ProcessConfig processConfig = JSONUtil.toBean(configService.selectConfigByKey(PROCESS_KEY),
                ProcessConfig.class);
        bpmClientInputModelBo.getModel().setWf_businessKey(businessId);
        bpmClientInputModelBo.getModel().setWf_uniteworkUrl(processConfig.getUniteworkUrl());
        return processProcInst(bpmClientInputModelBo, ENUM_ACTION.save.name());
    }

    /**
     * 下一步流程
     *
     * @param bpmClientInputModelBo
     * @param businessId            业务id
     * @return
     * @throws Exception
     */
    public ProcessInstanceModel nextExecute(BpmClientInputModelBo bpmClientInputModelBo, String businessId)
            throws Exception {
        ProcessConfig processConfig = JSONUtil.toBean(configService.selectConfigByKey(PROCESS_KEY),
                ProcessConfig.class);
        bpmClientInputModelBo.getModel().setWf_businessKey(businessId);
        bpmClientInputModelBo.getModel().setWf_uniteworkUrl(processConfig.getUniteworkUrl());
        return processProcInst(bpmClientInputModelBo, ENUM_ACTION.submit.name());
    }

    public ProcessInstanceModel cancelExecute(BpmClientInputModelBo bpmClientInputModelBo, String businessId)
            throws Exception {
        ProcessConfig processConfig = JSONUtil.toBean(configService.selectConfigByKey(PROCESS_KEY),
                ProcessConfig.class);
        bpmClientInputModelBo.getModel().setWf_businessKey(businessId);
        bpmClientInputModelBo.getModel().setWf_uniteworkUrl(processConfig.getUniteworkUrl());
        return processProcInst(bpmClientInputModelBo, ENUM_ACTION.cancel.name());
    }

    /**
     * 处理流程实例
     *
     * @param action 处理操作类型
     * @return
     */
    public ProcessInstanceModel processProcInst(BpmClientInputModelBo bpmClientInputModelBo, String action)
            throws Exception {
        ProcessInstanceModel processInst = new ProcessInstanceModel();
        BpmClientInputModel bpmClientInputModel = bpmClientInputModelBo.getModel();
        String nextActDefId = bpmClientInputModel.getWf_nextActDefId();
        List<Map<String, Object>> receivers = bpmClientInputModel.getWf_receivers();
        if (bpmClientInputModelBo.getRedirectDefId() != null) {
            bpmClientInputModel.setWf_nextActDefId(bpmClientInputModelBo.getRedirectDefId());
            bpmClientInputModel.setWf_receivers(bpmClientInputModelBo.getRedirectReceivers());
        }
        // 待办URL
        if (StringUtils.isEmpty(bpmClientInputModelBo.getOrder())) {
            bpmClientInputModelBo.setOrder(Constants.ZERO);
        }
        String url = this.customConfig.getWorkflowUniteWorkUrl() + "?businessKey="
                + bpmClientInputModel.getWf_businessKey() +
                "&type=" + bpmClientInputModelBo.getType() + "&order=" + bpmClientInputModelBo.getOrder() + "&mark="
                + bpmClientInputModelBo.getMark();
        if (BooleanUtil.isTrue(bpmClientInputModelBo.getJointReview())) {
            url = url + "&batch=" + DateUtil.current();
        } else if (StringUtils.isNotEmpty(bpmClientInputModelBo.getBatch())) {
            url = url + "&batch=" + bpmClientInputModelBo.getBatch();
        }
        bpmClientInputModel.setWf_uniteworkUrl(url);
        BpmClient bpmClient = getBpmClient();
        // ProcessDefinitionModel defModel = null;
        // if (WorkflowService.ENUM_ACTION.save.name().equals(action)) {
        // // 暂存
        // defModel = bpmClient.getProcessDef(bpmClientInputModel.getWf_procDefKey());
        // } else {
        // defModel =
        // bpmClient.getProcessDef(bpmClientInputModel.getWf_procDefId().split(":")[0]);
        // }
        //
        // if (StringUtils.isNotEmpty(defModel.getDescription())) {
        // // 设置流程标题-统一增加前缀
        // bpmClientInputModel.setWf_procTitle(defModel.getDescription() +
        // bpmClientInputModel.getWf_procTitle());
        // }
        // 设置流程标题-统一增加前缀
        bpmClientInputModel
                .setWf_procTitle(addPrefix(bpmClientInputModel.getWf_procTitle(), bpmClientInputModelBo.getBizType()));
        // 防止改了部门 再查一遍
        bpmClientInputModel
                .setWf_sendUserOrgId(sysUserService.getDeptByUserName(bpmClientInputModel.getWf_sendUserId()));
        if (bpmClientInputModel.getWf_receivers() != null) {
            bpmClientInputModel.getWf_receivers().forEach(item -> {
                item.put("receiveUserOrgId", sysUserService.getDeptByUserName((String) item.get("receiveUserId")));
            });
        }
        switch (ENUM_ACTION.valueOf(action)) {
            case cancel:// 作废
                processInst = bpmClient.cancelProcInst(bpmClientInputModel);
                break;
            case backtostart:// 返回拟稿人
                processInst = bpmClient.callBackStart(bpmClientInputModel);
                nextActDefId = processInst.getNextActInsts().get(0).getActDefId();
                bpmClientInputModel.setWf_nextActDefName(processInst.getNextActInsts().get(0).getActDefName());
                break;
            case backtoprev:// 返回上一步
                processInst = bpmClient.backToPrev(bpmClientInputModel);
                break;
            case back:// 撤回
                processInst = bpmClient.callBack(bpmClientInputModel);
                break;
            case transfer:// 转派
                processInst = bpmClient.transfer(bpmClientInputModel);
                break;
            case reject:// 驳回
                processInst = bpmClient.rejectFlow(bpmClientInputModel);
                break;
            case save:
                if (StrUtil.isBlank(bpmClientInputModel.getWf_businessKey())) {
                    bpmClientInputModel.setWf_businessKey(IdUtil.simpleUUID());
                }
                processInst = bpmClient.saveFlowInstance(bpmClientInputModel);
                break;
            case submit:
                if (StrUtil.isBlank(bpmClientInputModel.getWf_businessKey())) {
                    bpmClientInputModel.setWf_businessKey(IdUtil.simpleUUID());
                }
                processInst = bpmClient.submitFlowInstance(bpmClientInputModel);
                processInst.setProcInstTitle(bpmClientInputModel.getWf_procTitle());

                // 异步修改流程标题
                workflowAsyncService.updateProcTitleAsync(bpmClientInputModel, processInst);
                break;
            default:
                break;
        }
        bpmClientInputModel.setWf_nextActDefId(nextActDefId);
        bpmClientInputModel.setWf_receivers(receivers);
        iWorkflowLogService.saveProcLog(bpmClientInputModelBo, processInst, action);
        // oa 待办对接
        /*
         * if(oaProperties.getTodo().isEnabled()) {
         * handleOaTodo(bpmClientInputModel, processInst);
         * }
         */
        // 流程事件
        bpmClientInputModelBo.setBpmClientInputModel(bpmClientInputModel);
        bpmClientInputModelBo.setProcessInst(processInst);
        // 流程处理完后，会调用此注解（@AllowConcurrentEvents）相关方法
        ProcessEventBus.post(new ProcessResultEvent(bpmClientInputModelBo, ""));
        // 复审自动提交
        if (ENUM_ACTION.submit.name().equals(action)) {
            ExecutorService executor = Executors.newFixedThreadPool(1);
            Callable<Void> task = () -> {
                iWorkflowLogService.automaticSubmit(bpmClientInputModelBo, getBpmClient());
                return null;
            };
            executor.submit(task);
            executor.shutdown();
        }
        // 通过企业微信发送流程待办消息
        if (bpmClientInputModelBo.getIsLast() == null || bpmClientInputModelBo.getIsLast()) {
            messageSendEntryService.sendWorkFlowMessagQywx(bpmClientInputModelBo, DocMsgConstants.MSG_TYPE_QYWX);
        }

        /** 文件变更申请标识，不为空时，更新站内消息表的pcUrl和mobileUrl **/
        if (StringUtils.isNotBlank(bpmClientInputModelBo.getApplyIdTemp())) {
            updateDocMsgLinkUrl(bpmClientInputModelBo, processInst);
        }
        return processInst;
    }

    /**
     * 文件变更申请才会触发
     *
     * @param bpmClientInputModelBo
     * @param processInst
     */
    private void updateDocMsgLinkUrl(BpmClientInputModelBo bpmClientInputModelBo, ProcessInstanceModel processInst) {
        try {
            String applyId = bpmClientInputModelBo.getApplyIdTemp();
            // 查询
            List<DocMessage> docMessageList = docMessageService.list(new LambdaQueryWrapper<DocMessage>()
                    .eq(DocMessage::getApplyId, applyId).eq(DocMessage::getMsgType, DocMsgConstants.MSG_TYPE_MSG));
            if (CollUtil.isEmpty(docMessageList)) {
                return;
            }
            DocMessage docMessage = docMessageList.get(0);

            BpmClientInputModel bpmClientInputModel = bpmClientInputModelBo.getBpmClientInputModel();
            String url = bpmClientInputModel.getWf_uniteworkUrl();
            // 构建pc端链接
            String pcLink = buildPcLink(url, processInst.getTopProcInstId(), processInst.getProcInstId(), bpmClientInputModel.getWf_curActInstId());
            // 构建移动端链接
            String mobileLink = buildMobileLink(url, processInst.getTopProcInstId(), processInst.getProcInstId(), bpmClientInputModel.getWf_curActInstId(), false);

            docMessage.setPcUrl(pcLink);
            docMessage.setMobileUrl(mobileLink);
            docMessageService.updateById(docMessage);
        } catch (Exception e) {
            log.error("发送待办邮件失败========", e);
        }
    }

    /**
     * 发送待办邮件信息
     *
     * @param bpmClientInputModel
     * @param processInst
     */
    public void sendPendingEmail(BpmClientInputModel bpmClientInputModel, ProcessInstanceModel processInst,
            Map<String, String> param, String sendType) {
        try {
            List<Map<String, Object>> receivers = bpmClientInputModel.getWf_receivers();
            if (CollUtil.isEmpty(receivers)) {
                return;
            }
            List<String> receiveUserIdList = new ArrayList<>();
            for (Map<String, Object> receiver : receivers) {
                receiveUserIdList.add(String.valueOf(receiver.get("receiveUserId")));
            }
            List<SysUser> sysUserList = sysUserService
                    .list(new LambdaQueryWrapper<SysUser>().in(SysUser::getUserName, receiveUserIdList));
            if (CollUtil.isEmpty(sysUserList)) {
                return;
            }
            List<SysUser> sysUsers = sysUserList.stream().filter(item -> StrUtil.isNotBlank(item.getEmail()))
                    .collect(Collectors.toList());
            if (CollUtil.isEmpty(sysUsers)) {
                return;
            }
            String url = bpmClientInputModel.getWf_uniteworkUrl();
            List<ActivitiInstanceModel> nextActInsts = processInst.getNextActInsts();
            if (CollUtil.isEmpty(nextActInsts)) {
                return;
            }
            for (ActivitiInstanceModel nextActInst : nextActInsts) {
                // 构建pc端链接
                String pcLink = buildPcLink(url, processInst.getTopProcInstId(), nextActInst.getProcInstId(), nextActInst.getActInstId());
                // 构建移动端链接
                String mobileLink = buildMobileLink(url, processInst.getTopProcInstId(), nextActInst.getProcInstId(), nextActInst.getActInstId(), true);
                param.put("pcLink", pcLink);
                param.put("mobileLink", mobileLink);
                log.error("邮件跳转URL：{}", pcLink);
                for (SysUser sysUser : sysUsers) {
                    // 发送站内信
                    docMessageService.sendInstationMessage("", "", "",
                            "", "", "",
                            "", "", sysUser.getUserName(), sysUser.getUserId(), bpmClientInputModel.getWf_procTitle(),
                            DocMsgConstants.MSG_TYPE_MSG, MsgTypeEnum.PRINT.getType(), pcLink, mobileLink,
                            SecurityUtils.getLoginUser().getTenantId());

                    if (nextActInst.getReceiverUserId().equals(sysUser.getUserName())) {
                        sysNotifyLogService.sendEmail(sendType, sysUser.getNickName(), sysUser.getEmail(),
                                sysUser.getUserId(), param);
                    }
                }
            }
        } catch (Exception e) {
            log.error("发送待办邮件失败========", e);
        }
    }

    /**
     * 流程待办处理完成后，处理oa 待办
     *
     * @param bpmClientInputModel
     * @param processInst
     */
    public void handleOaTodo(BpmClientInputModel bpmClientInputModel, ProcessInstanceModel processInst) {
        try {
            if (StringUtils.isNotEmpty(bpmClientInputModel.getWf_curActInstId())) {
                NotifyTodoRemoveContext todoDoneTo = new NotifyTodoRemoveContext();
                todoDoneTo.setAppName(OA_APP_NAME);
                todoDoneTo.setModelId(bpmClientInputModel.getWf_curActInstId());
                todoDoneTo.setModelName(bpmClientInputModel.getWf_procDefKey());
                todoDoneTo.setOptType(1);
                // 处理当前待办
                todoService.doneTodo(todoDoneTo);
            }
            List<Map<String, Object>> receivers = bpmClientInputModel.getWf_receivers();

            List<String> receiveUserIdList = new ArrayList<>();
            if (receivers != null) {
                for (Map<String, Object> receiver : receivers) {
                    receiveUserIdList.add(String.valueOf(receiver.get("receiveUserId")));
                }
            }

            String url = bpmClientInputModel.getWf_uniteworkUrl();
            url = url.substring(url.indexOf("/workflow"));
            List<ActivitiInstanceModel> nextActInsts = processInst.getNextActInsts();
            if (CollUtil.isEmpty(nextActInsts)) {
                List<ProcessLogBean> logBeans = getCurActLogs(processInst.getProcInstId(),
                        bpmClientInputModel.getWf_curActInstId());
                // 删除待办
                if (CollUtil.isNotEmpty(logBeans)) {
                    NotifyTodoRemoveContext removedTo = new NotifyTodoRemoveContext();
                    removedTo.setAppName(OA_APP_NAME);
                    removedTo.setModelName(bpmClientInputModel.getWf_procDefKey());
                    removedTo.setOptType(1);
                    logBeans.forEach(v -> {
                        removedTo.setModelId(v.getCurActInstId());
                        todoService.deleteTodo(removedTo);
                    });
                }
                return;
            }
            for (ActivitiInstanceModel nextActInst : nextActInsts) {
                String linkUrl = url + "&topProcInstId=" + processInst.getTopProcInstId()
                        + "&procInstId=" + nextActInst.getProcInstId() + "&actInstId=" + nextActInst.getActInstId()
                        + "&status=" + getFlowStatus(nextActInst.getProcInstId(), nextActInst.getActInstId());
                linkUrl = oaProperties.getAuth().getAccessUrl() + "?redirectUri=" + URLEncoder.encode(linkUrl, "utf-8");
                log.error("oa 待办地址：" + linkUrl);
                for (String username : receiveUserIdList) {
                    if (nextActInst.getReceiverUserId().equals(username)) {
                        Map map = new HashMap(3);
                        String domain = username.indexOf("@mehowcy.com") > 0 ? "@mehowcy.com" : "@mehowmy.com";
                        map.put("PersonNo", username.replace(domain, ""));

                        // 发送待办
                        NotifyTodoSendContext notifyTodoSendTo = new NotifyTodoSendContext();
                        notifyTodoSendTo.setAppName(OA_APP_NAME);
                        notifyTodoSendTo.setModelName(bpmClientInputModel.getWf_procDefKey());
                        notifyTodoSendTo.setModelId(nextActInst.getActInstId());
                        notifyTodoSendTo.setSubject(bpmClientInputModel.getWf_procTitle());
                        notifyTodoSendTo.setLink("https://sso.mehow.com.cn/portal/toDoLoad.action?appSn="
                                + oaProperties.getAuth().getClient() + "&redirectUri="
                                + URLEncoder.encode(linkUrl, "utf-8"));
                        notifyTodoSendTo.setType(1);
                        notifyTodoSendTo.setTargets(JSONUtil.toJsonStr(map));
                        notifyTodoSendTo.setCreateTime(DateUtil.formatDateTime(processInst.getCreateTime()));
                        todoService.sendTodo(notifyTodoSendTo);
                    }
                }
            }

        } catch (Exception e) {
            log.error("发送待办邮件失败========", e);
        }

    }

    /**
     * 获取流程当前环节待办的日志(排除当前待办)
     *
     * @param procInstId
     * @param curActInstId
     * @return
     */
    @SneakyThrows
    public List<ProcessLogBean> getCurActLogs(String procInstId, String curActInstId) {
        ActivitiInstanceModel activitiInstanceModel = getBpmClient().getActivityInstance(curActInstId);
        String previousActInstId = activitiInstanceModel.getPrevActInstId();
        String strLog = getBpmClient().getProcInstLog(procInstId);
        cn.hutool.json.JSONArray jsonArray = JSONUtil.parseObj(strLog).getJSONArray("procLogs");
        List<ProcessLogBean> processLogBeans = jsonArray.toList(ProcessLogBean.class).stream()
                .filter(v -> previousActInstId.equals(v.getPrevActInstId())
                        && curActInstId.equals(v.getPrevActInstId()))
                .collect(Collectors.toList());
        return processLogBeans;
    }

    /**
     * 获取流程实例完整信息，包含流程实例、当前环节实例、流程定义等信息
     *
     * @return
     */
    public HashMap<String, Object> getFLowInfo(SearchQuery searchQuery) throws Exception {
        // TODO Auto-generated method stub
        HashMap<String, Object> returnDataMap = Maps.newHashMap();
        BpmClient bpmClient = getBpmClient();
        JSONObject jo = new JSONObject();
        try {

            jo = getFlowInfo(searchQuery);
            if (jo == null) {
                return null;
            }
            if (!jo.containsKey(CUR_ACT_DEF_KEY) || !jo.containsKey(CUR_ACT_INST_KEY) || !jo.containsKey(PROC_INST_KEY)
                    || !jo.containsKey(PROC_DEF_KEY)) {
                throw new Exception(I18nUtils.getTitle(CommonI18nConstant.SERVICE_WORKFLOW_DATA_INCOMPLETE));
            }
            returnDataMap.put(CUR_ACT_DEF_KEY, bpmClient.parserToMap(jo.getJSONObject(CUR_ACT_DEF_KEY)));
            returnDataMap.put(CUR_ACT_INST_KEY, bpmClient.parserToMap(jo.getJSONObject(CUR_ACT_INST_KEY)));
            returnDataMap.put(PROC_INST_KEY, bpmClient.parserToMap(jo.getJSONObject(PROC_INST_KEY)));
            returnDataMap.put(PROC_DEF_KEY, bpmClient.parserToMap(jo.getJSONObject(PROC_DEF_KEY)));
            returnDataMap.put(ACT_OPPOWERS, jo.getJSONArray(ACT_OPPOWERS));
            /**
             * JSONArray ja_log = jo.getJSONArray("procLogs"); ArrayList<HashMap<String,
             * Object>> list_log = Lists.newArrayList(); for (int i = 0; i < ja_log.size();
             * i++) { HashMap<String, Object> commentMap = Maps.newHashMap();
             * commentMap.put("actDefName",
             * ja_log.getJSONObject(i).getString("prevActDefName"));
             * commentMap.put("commentTime", ja_log.getJSONObject(i).get("startTime"));
             * commentMap.put("actDefId", ja_log.getJSONObject(i).get("prevActDefId"));
             * String orgId = ja_log.getJSONObject(i).getString("sendOrgId"); PubOrgEntity
             * deptEntry = pubOrgDao.get(orgId); String userName =
             * String.format("(%s-%s)%s", deptEntry.getCompanyName(),
             * deptEntry.getOrgName(), ja_log.getJSONObject(i).getString("sendUserName"));
             * commentMap.put("commentUserName", userName); commentMap.put("userName",
             * ja_log.getJSONObject(i).getString("sendUserName"));
             * commentMap.put("commentFullMessage",
             * ja_log.getJSONObject(i).get("commentFullMessage")); list_log.add(commentMap);
             * } returnDataMap.put("procLogs", list_log);
             */
            // returnDataMap.put("transferLog",
            // projectCommentEntryMapper.findCommentListByParams(params));//获取流程实例转派审批日志
        } catch (Exception ex) {
            log.error("getFLowInfo==============" + JSON.toJSONString(searchQuery), ex);
        }
        return returnDataMap;
    }

    JSONObject getFlowInfo(SearchQuery searchQuery) {
        String returnData = "";
        BpmClient bpmClient = getBpmClient();
        try {
            switch (searchQuery.getStatus()) {
                case 1:
                    returnData = bpmClient.getToDoFlowInfo(searchQuery);
                    break;
                case 2:
                    returnData = bpmClient.getYibanFlowInfo(searchQuery);
                    break;
                case 3:
                    returnData = bpmClient.getBanJieFlowInfo(searchQuery);
                    break;
                default:
                    break;
            }
            return JSON.parseObject(returnData);
        } catch (Exception ex) {
            log.error("=======流程平台返回信息:" + JSON.toJSONString(searchQuery), ex);
        }
        return null;
    }

    /**
     * 获取流程实例精简信息
     *
     * @return
     */
    public HashMap<String, String> getSimpleFLowInfo(SearchQuery searchQuery) {
        HashMap<String, String> returnDataMap = Maps.newHashMap();
        try {

            JSONObject jo = getFlowInfo(searchQuery);
            returnDataMap.put("curActDefName", jo.getJSONObject("curActInst").getString("actDefName"));
            returnDataMap.put("curActDefId", jo.getJSONObject("curActInst").getString("actDefId"));
            returnDataMap.put("curActInstId", jo.getJSONObject("curActInst").getString("actInstId"));
            returnDataMap.put("procInstId", jo.getJSONObject("curActInst").getString("procInstId"));
            returnDataMap.put("businessKey", jo.getJSONObject("procInst").getString("businessKey"));
        } catch (Exception ex) {
            log.error("getFLowInfo==============", ex);
        }
        return returnDataMap;
    }

    /**
     * 获取流程实例日志
     *
     * @param bpmClientInputModel
     * @return
     */
    public HashMap<String, Object> getProcInstLog(BpmClientInputModel bpmClientInputModel) {
        HashMap<String, Object> returnDataMap = Maps.newHashMap();
        List<Map<String, Object>> procLog;
        BpmClient bpmClient = getBpmClient();
        try {
            if (StrUtil.isNotBlank(bpmClientInputModel.getWf_procInstId())) {
                String returnData = bpmClient.getProcInstLog(bpmClientInputModel.getWf_procInstId());
                Map<String, Object> procInstLog = JSONObject.parseObject(returnData, Map.class);
                if (null != procInstLog && procInstLog.containsKey("procLogs")) {
                    // 获取日志实例
                    procLog = (List<Map<String, Object>>) procInstLog.get("procLogs");
                    procLog = dealEchoMap(procLog);
                    JSONObject jo = JSON.parseObject(returnData);
                    returnDataMap.put("procInst", bpmClient.parserToMap(jo.getJSONObject("procInst")));
                    returnDataMap.put("procLogs", procLog);
                }
            }
        } catch (Exception ex) {
            log.error("getProcInstLog==============" + JSON.toJSONString(bpmClientInputModel), ex);
        }
        return returnDataMap;
    }

    /**
     * 去掉相关部门重复数据
     *
     * @param procLog
     * @return
     */
    private List<Map<String, Object>> dealEchoMap(List<Map<String, Object>> procLog) {
        List<Map<String, Object>> newList = new ArrayList<Map<String, Object>>();
        // 去重
        for (int i = 0; i < procLog.size(); i++) {
            Map<String, Object> oldMap = procLog.get(i);
            if (newList.size() > 0) {
                boolean isContain = false;
                for (int j = 0; j < newList.size(); j++) {
                    Map<String, Object> newMap = newList.get(j);
                    if (newMap.get("prevActInstId").equals(oldMap.get("prevActInstId"))) {
                        for (String k : oldMap.keySet()) {
                            newMap.put(k, oldMap.get(k));
                        }
                        isContain = true;
                        break;
                    }
                }

                if (!isContain) {
                    newList.add(oldMap);
                }

            } else {
                newList.add(oldMap);
            }
        }
        return newList;

    }

    /**
     * 查询待办列表
     *
     * @param searchQuery
     * @return
     */
    public PageResultModel findRecordList(SearchQuery searchQuery) {
        // TODO Auto-generated method stub
        BpmClient bpmClient = getBpmClient();
        try {
            return bpmClient.getRecordList(searchQuery);
        } catch (Exception ex) {
            log.error("findRecordList===" + JSON.toJSONString(searchQuery), ex);
        }
        return null;
    }

    public TableDataInfo<UniteworkTaskVo> selectToDoList(UniteworkTaskBo bo) {
        Page<UniteworkTaskBo> page = PageUtils.buildPage(bo.getPageNum(), bo.getPageSize(), bo.getOrderByColumn(),
                bo.getIsAsc());
        Page<UniteworkTaskVo> iPage = actHiTaskinstMapper.getUniteworkTaskList(page, bo,
                customConfig.getBpmDataSource());
        for(UniteworkTaskVo uniteworkTask:iPage.getRecords()){
            uniteworkTask.setDoing(BooleanUtil.isTrue(SpringUtils.getBean(RedisCache.class).getCacheObject(uniteworkTask.getCurActInstId())));
        }
        return PageUtils.buildDataInfo(iPage);
    }

    /**
     * 查询待办列表
     *
     * @param processBo
     * @param type      类型 1= PC端 2=移动端
     * @return
     */
    public WorkFlowPageVo findToDoList(ProcessBo processBo, ApiTypeEnum type) {
        JSONObject jsonObject = JSONObject.parseObject(processBo.toJSONString());
        BpmClient bpmClient = getBpmClient();
        SearchQuery searchQuery = new SearchQuery();
        WorkFlowPageVo pageVo = new WorkFlowPageVo();
        try {
            // 所属流程平台租户
            searchQuery.setTenantId(customConfig.getBpmTenantId());
            searchQuery.setRecUserId(SecurityUtils.getUsername());
            searchQuery.setStatus(processBo.getStatus());
            searchQuery.setPageNumber(processBo.getPageNumber());
            searchQuery.setPageSize(processBo.getPageSize());
            // 操作类型
            searchQuery.setProcDefKey(processBo.getProcDefKey());
            searchQuery.setTitle(processBo.getTitle());
            PageResultModel pageResultModel = bpmClient.getRecordList(searchQuery);
            pageVo.setPageSize(pageResultModel.getPageSize());
            pageVo.setPageNumber(pageResultModel.getThisPageNumber());
            pageVo.setTotalCount(pageResultModel.getTotalCount());
            List<WorkFlowInfo> workFlowInfos = JSONUtil.toList(JSONUtil.toJsonStr(pageResultModel.getResult()),
                    WorkFlowInfo.class);
            workFlowInfos.forEach(item -> {
                if (item.getUrl().contains("businessKey")) {
                    String key = UrlUtils.getParam(item.getUrl(), "businesskey").toUpperCase();
                    item.setBusinessData(key);
                }
                item.setDoing(BooleanUtil.isTrue(SpringUtils.getBean(RedisCache.class).getCacheObject(item.getCurActInstId())));
            });
            pageVo.setResult(workFlowInfos);
            // if (ObjectUtil.isNotEmpty(pageResultModel) && pageResultModel.getTotalCount()
            // > 0) {
            // // 获取详情信息
            // List<WorkFlowInfo> workFlowInfos =
            // JSONUtil.toList(JSONUtil.toJsonStr(pageResultModel.getResult()),
            // WorkFlowInfo.class);
            // List<WorkFlowInfo> infos = handleBusinessData(workFlowInfos, processBo);

            // if (processBo.getHaveData() > 0) {
            // pageVo.setPageSize(pageResultModel.getPageSize());
            // pageVo.setPageNumber(pageResultModel.getThisPageNumber());
            // pageVo.setTotalCount(StringUtils.isBlank(jsonObject.getString("docName")) ?
            // pageResultModel.getTotalCount() : infos.size());
            // // 获取详情信息
            // switch (processBo.getStatus()) {
            // case 1:
            // pageVo.setResult(infos.stream().sorted(Comparator.comparing(WorkFlowInfo::getSendTime,
            // Comparator.nullsLast(Comparator.naturalOrder())).reversed()).collect(Collectors.toList()));
            // break;
            // case 2:
            // case 3:
            // pageVo.setResult(infos.stream().sorted(Comparator.comparing(WorkFlowInfo::getDealTime,
            // Comparator.nullsLast(Comparator.naturalOrder())).reversed()).collect(Collectors.toList()));
            // break;
            // default:
            // break;
            // }
            // } else {
            // pageVo.setPageSize(processBo.getPageSize());
            // pageVo.setPageNumber(0);
            // pageVo.setTotalCount(0);
            // pageResultModel.setTotalCount(0);
            // pageVo.setResult(new ArrayList<>());
            // }
            // } else {
            // pageVo.setPageSize(processBo.getPageSize());
            // pageVo.setPageNumber(processBo.getPageNumber());
            // pageVo.setTotalCount(0);
            // }
            // 如果是移动端请求且查询待办 过滤掉培训待办(因为培训待办需要上传文件,移动端无法上传文件)
            // if (ObjectUtil.equal(ApiTypeEnum.MOBILE,type)&&processBo.getStatus()==1){
            // List<WorkFlowInfo> result = pageVo.getResult();
            // int afterTotal = result.size();
            // result = result.stream().filter(workFlowInfo ->
            // !ObjectUtil.equal("peixun",workFlowInfo.getCurActDefId())).collect(Collectors.toList());
            // int beforeTotal = result.size();
            // pageVo.setResult(result);
            // pageVo.setTotalCount(processBo.getHaveData()-(afterTotal-beforeTotal));
            // }
            return pageVo;
        } catch (Exception ex) {
            log.error("findRecordList===" + JSON.toJSONString(searchQuery), ex);
        }
        return pageVo;
    }

    /**
     * 获取下一环节定义
     *
     * @param searchQuery
     * @return
     */
    public List<ActivityDefinitionModel> getNextActs(SearchQuery searchQuery) {
        BpmClient bpmClient = getBpmClient();
        List<ActivityDefinitionModel> actDefList = Lists.newArrayList();
        try {
            List<ActivityDefinitionModel> nextacts = bpmClient.getNextacts(searchQuery);

            for (ActivityDefinitionModel activityDefinitionModel : nextacts) {
                activityDefinitionModel.setShowTxlSelect("N");
                // 流程平台如果有输出线名称 返回当前环节名称自动为输出线名称 由于其他业务系统已经使用 该处做特殊处理 （不读输出线名称）
                /*
                 * if (StrUtil.isNotBlank(activityDefinitionModel.getDescription())) {
                 * // 描述名称 为流程平台返回的当前环节名称
                 * activityDefinitionModel.setActDefName(activityDefinitionModel.getDescription(
                 * ));
                 * }
                 */
                if (END_ACT_NAME.equalsIgnoreCase(activityDefinitionModel.getActDefName())) {
                    actDefList.add(activityDefinitionModel);
                    continue;
                }
                List<ExtAttributeModel> extAttrlist = bpmClient.getActDefExtAttrs(searchQuery.getProcDefId(),
                        activityDefinitionModel.getActDefId());
                for (ExtAttributeModel extMap : extAttrlist) {
                    // 根据流程环节自定义标签判断是否显示"通讯录选择人员"按钮
                    if (SHOW_TXL_SELECT.equalsIgnoreCase(extMap.getObjKey().trim())) {
                        activityDefinitionModel.setShowTxlSelect(extMap.getObjValue());
                        break;
                    }
                }
                actDefList.add(activityDefinitionModel);
            }

        } catch (Exception e) {
            log.error("获取下一步环节定义列表失败,参数:" + JSON.toJSONString(searchQuery), e);
        }

        return actDefList;
    }

    /**
     * 获取下一环节用户信息
     *
     * @param searchQuery
     * @return
     */
    public List<ActivityResourceModel> getNextActUsers(SearchQuery searchQuery) {
        BpmClient bpmClient = getBpmClient();
        List<ActivityResourceModel> nextactUsers = Lists.newArrayList();
        try {
            nextactUsers = bpmClient.getNextactUsers(searchQuery);
        } catch (Exception e) {
            log.error("获取下一步环节用户列表失败,参数:" + JSON.toJSONString(searchQuery), e);
        }
        return nextactUsers;
    }

    public List<ExtAttributeModel> getExtAttributeModel(String procDefId, String actDefId) {
        BpmClient bpmClient = getBpmClient();
        List<ExtAttributeModel> list = Lists.newArrayList();
        try {
            list = bpmClient.getActDefExtAttrs(procDefId, actDefId);
        } catch (Exception e) {
            log.error("获取扩展属性失败", e);
        }
        return list;
    }

    /**
     * 判断下一环节是否支持自动提交
     *
     * @param searchQuery
     * @return
     */
    public String checkAutoCommit(SearchQuery searchQuery) throws Exception {

        BpmClient bpmClient = getBpmClient();

        try {
            String actDefId = "";
            String procDefId = "";
            // 待办提交
            if (StrUtil.isNotBlank(searchQuery.getCurActInstId())) {
                ActivitiInstanceModel activitiInstanceModel = bpmClient
                        .getActivityInstance(searchQuery.getCurActInstId());
                actDefId = activitiInstanceModel.getActDefId();
                procDefId = activitiInstanceModel.getProcDefId();
            } else { // 新建提交
                ActivityDefinitionModel activityDefinitionModel = bpmClient.getActDef(searchQuery.getProcDefId(),
                        searchQuery.getCurActDefId());
                actDefId = activityDefinitionModel.getActDefId();
                procDefId = activityDefinitionModel.getProcDefId();
            }
            List<ActivityDefinitionModel> netxtActDefList = bpmClient.getNextacts(searchQuery);
            if (netxtActDefList != null && netxtActDefList.size() == 1) {
                ActivityDefinitionModel netxtActDef = netxtActDefList.get(0);
                ActivityDefinitionModel actDef = bpmClient.getActDef(procDefId, actDefId);
                if (("auto".equalsIgnoreCase(actDef.getJumpType()) || netxtActDef.isNotSelectReceiver()
                        || "endEvent".equals(netxtActDef.getActDefType()))) {
                    return netxtActDef.getActDefId();
                }
            }
        } catch (Exception ex) {
            log.error("checkAutoCommit==" + JSON.toJSONString(searchQuery), ex);
            throw new Exception(ex);
        }

        return "";
    }

    /**
     * 获取第一个环节定义信息
     *
     * @param procDefId
     * @return
     */
    public ActivityDefinitionModel getStartactDef(String procDefId) throws Exception {
        BpmClient bpmClient = getBpmClient();
        try {
            return bpmClient.getStartactDef(procDefId);
        } catch (Exception ex) {
            // TODO Auto-generated catch block
            log.error("getStartactDef==" + procDefId, ex);
            throw new Exception(ex);
        }
    }

    /**
     * 获取环节定义信息
     *
     * @param procDefId 流程定义Id
     * @param actDefId  环节定义Id
     * @return
     * @throws Exception
     */
    public ActivityDefinitionModel getActDef(String procDefId, String actDefId) throws Exception {
        BpmClient bpmClient = getBpmClient();
        try {
            return bpmClient.getActDef(procDefId, actDefId);
        } catch (Exception ex) {
            // TODO Auto-generated catch block
            log.error("getActDef==" + procDefId + "====" + actDefId, ex);
            throw new Exception(ex);
        }
    }

    /**
     * 获取环节定义信息
     *
     * @param procDefId 流程定义Id
     * @return
     * @throws Exception
     */
    public List<ActivityDefinitionModel> getActDef(String procDefId) throws Exception {
        BpmClient bpmClient = getBpmClient();
        try {
            return bpmClient.getActDef(procDefId);
        } catch (Exception ex) {
            log.error("getActDef==" + procDefId, ex);
            throw new Exception(ex);
        }
    }

    /**
     * 根据流程定义Key获取流程定义信息
     *
     * @param procDefKey
     * @return
     */
    public ProcessDefinitionModel getProcessDefinitionModel(String procDefKey) throws Exception {
        BpmClient bpmClient = getBpmClient();
        try {
            return bpmClient.getProcessDef(procDefKey);
        } catch (Exception ex) {
            // TODO Auto-generated catch block
            log.error("getProcessDefinitionModel==" + procDefKey, ex);
            throw new Exception(ex);
        }
    }

    public String getHistasklogurl(String procInstId) throws Exception {
        BpmClient bpmClient = getBpmClient();
        try {
            Map<String, String> param = bpmClient.getHistoryUrl(procInstId);
            return param.get("url");
        } catch (Exception ex) {
            log.error("getHistasklogurl==" + ex);
            throw new Exception(ex);
        }
    }

    public String getImageUrl(String procDefId) throws Exception {
        BpmClient bpmClient = getBpmClient();
        try {
            Map<String, String> param = bpmClient.getImageUrl(procDefId);
            return param.get("url");
        } catch (Exception ex) {
            log.error("getImageUrl==" + ex);
            throw new Exception(ex);
        }
    }

    /**
     * 查询流程实例状态
     *
     * @param procInstId
     * @param curActInstId
     * @return
     */
    public int getFlowStatus(String procInstId, String curActInstId) {
        BpmClient bpmClient = getBpmClient();
        try {
            ActivitiInstanceModel actInstMap = bpmClient.getActivityInstance(curActInstId);
            if (actInstMap != null && actInstMap.getFinishTime() != null && actInstMap.getFinishTime().length() > 0) {
                ProcessInstanceModel procInst = bpmClient.getProcessInstById(procInstId);
                if (procInst != null && procInst.getFinishTime() != null) {
                    return FLOW_STATUS_FINISH;
                }
                return FLOW_STATUS_READ;
            }
            return FLOW_STATUS_UNREAD;
        } catch (Exception ex) {
            log.error("getFlowStatus===procInstId=" + procInstId + ",curActInstId==" + curActInstId, ex);
        }
        return FLOW_STATUS_ERROR;
    }

    /**
     * 查询流程实例状态,并判断当前用户是否有权限打开待办
     *
     * @param procInstId
     * @param curActInstId
     * @param userId
     * @return
     */
    public ProcessInstanceModel getProcessInstance(String procInstId, String curActInstId,
            String userId) throws Exception {
        BpmClient bpmClient = getBpmClient();
        ProcessInstanceModel procInst = null;
        ActivitiInstanceModel actInst = bpmClient.getActivityInstance(curActInstId);
        if (actInst == null || !StrUtil.equals(actInst.getReceiverUserId(), userId)) {
            throw new Exception(I18nUtils.getTitle(CommonI18nConstant.SERVICE_WORKFLOW_NO_LINK_EXAMPLE_FOUND));
        }
        procInst = bpmClient.getProcessInstById(procInstId);
        procInst.setCurrentActivitiInstance(actInst);
        Map<String, String> formUrlMap = getFlowActForm(procInst.getProcDefId(), actInst.getActDefId());
        if (formUrlMap.containsKey("formUrl")) {
            procInst.setFormUrl(formUrlMap.get("formUrl"));
        }
        procInst.setProcInstState(FLOW_STATUS_ERROR + "");
        if (actInst.getFinishTime() == null || actInst.getFinishTime().length() == 0) {
            procInst.setProcInstState(FLOW_STATUS_UNREAD + "");
            return procInst;
        }
        if (procInst.getFinishTime() != null) {
            procInst.setProcInstState(FLOW_STATUS_FINISH + "");
        } else {
            procInst.setProcInstState(FLOW_STATUS_READ + "");
        }
        return procInst;
    }

    public Map<String, String> getFlowActForm(String procDefId, String actDefId) throws Exception {
        BpmClient bpmClient = getBpmClient();
        Map<String, String> result = Maps.newHashMap();
        try {
            ActivityDefinitionModel act = bpmClient.getActDef(procDefId, actDefId);
            result.put("formUrl", act.getPageUrl());
            result.put("appFormUrl", act.getMportalUrl());
        } catch (Exception ex) {
            log.error("getFlowActForm==" + ex);
            throw new Exception(ex);
        }
        return result;
    }

    public Map<String, Object> getProcessInstById(String procInstId) {
        BpmClient bpmClient = getBpmClient();
        ProcessInstanceModel procInst = null;
        Map<String, Object> result = Maps.newHashMap();
        try {
            procInst = bpmClient.getProcessInstById(procInstId);
            result = BeanUtil.beanToMap(procInst);
            Map<String, Object> curActInfo = this.getCurActInfo(procInstId);
            result.put("curActInstId", curActInfo.get("curActInstId").toString());
            result.put("curActDefId", curActInfo.get("curActDefId").toString());
            result.put("curActDefName", curActInfo.get("curActDefName").toString());
            result.put("prevActDefId", curActInfo.get("prevActDefId").toString());

            result.put("sendUserId", curActInfo.get("receiveUserId").toString());
            result.put("sendUserOrgId", curActInfo.get("receiveOrgId").toString());
        } catch (Exception e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
        return result;
    }

    private Map<String, Object> getCurActInfo(String procInstId) throws Exception {
        BpmClient bpmClient = getBpmClient();
        Map<String, Object> result = new HashMap<String, Object>();
        // 根据流程实例id获取流程实例日志
        String logStr = bpmClient.getProcInstLog(procInstId);
        List<Map<String, Object>> procLog = new ArrayList<Map<String, Object>>();
        Map<String, Object> procInstLog = JSONObject.parseObject(logStr, Map.class);
        if (null != procInstLog && procInstLog.containsKey("procLogs")) {
            // 获取日志实例
            procLog = (List<Map<String, Object>>) procInstLog.get("procLogs");
            for (Map<String, Object> map : procLog) {
                if (null == map.get("endTime")) {
                    if ("pms_zhsqlc-03".equals(map.get("prevActDefId"))) {
                        result = map;
                    } else {
                        result = map;
                        break;
                    }
                }
            }
        }
        return result;
    }

    public Map<String, Object> procInstInfoAndStatus(String procInstId) {
        Map<String, Object> procInst = this.getProcessInstById(procInstId);
        int status = 0;
        try {
            status = this.getFlowStatus(procInstId, procInst.get("curActInstId").toString());
        } catch (Exception e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
        procInst.put("status", status + "");
        return procInst;
    }

    public Map<String, Object> checkAutoJump(SearchQuery searchQuery) throws Exception {
        BpmClient bpmClient = getBpmClient();
        ActivityDefinitionModel actDef = new ActivityDefinitionModel();
        try {
            String actDefId = "";
            String procDefId = "";
            // 待办提交
            if (StrUtil.isNotBlank(searchQuery.getCurActInstId())) {
                ActivitiInstanceModel activitiInstanceModel = bpmClient
                        .getActivityInstance(searchQuery.getCurActInstId());
                actDefId = activitiInstanceModel.getActDefId();
                procDefId = activitiInstanceModel.getProcDefId();
                actDef = bpmClient.getActDef(procDefId, actDefId);
            } else { // 新建提交
                actDef = bpmClient.getActDef(searchQuery.getProcDefId(), searchQuery.getCurActDefId());
            }
            Map<String, Object> result = new HashMap<String, Object>();
            if ("auto".equalsIgnoreCase(actDef.getJumpType())) {
                result.put("flag", true);
            } else {
                result.put("flag", false);
            }
            return result;
        } catch (Exception ex) {
            log.error("checkAutoCommit==" + JSON.toJSONString(searchQuery), ex);
            throw new Exception(ex);
        }
    }

    public Map<String, Integer> getRecordCount(SearchQuery searchQuery) throws Exception {
        BpmClient bpmClient = getBpmClient();
        try {
            return bpmClient.getRecordCount(searchQuery);
        } catch (Exception ex) {
            log.error("getRecordCount==" + JSON.toJSONString(searchQuery), ex);
            throw new Exception(ex);
        }
    }

    /**
     * 根据父流程实例id获取该流程的子流程，从而处理子流程。
     *
     * @param procInstId 父流程实例id
     * @return
     * @throws Exception
     */
    public List<Map<String, Object>> getSubprocess(String status, String procInstId) throws Exception {
        BpmClient bpmClient = getBpmClient();
        try {
            return bpmClient.getSubprocess(status, procInstId);
        } catch (Exception ex) {
            log.error("getSubprocess==" + procInstId, ex);
            throw new Exception(ex);
        }
    }

    /***
     *
     * @param userIdAndOrgIdStr
     * @return
     */
    public Set<ActivityResourceModel> filterActivityResource(String userIdAndOrgIdStr) {
        Set<ActivityResourceModel> treeNodeList = new HashSet<ActivityResourceModel>();
        try {

            Set<String> userIdAndOrgIdSet = new java.util.LinkedHashSet<String>();
            Set<SysDept> orgTree = new LinkedHashSet<SysDept>();
            if (userIdAndOrgIdStr.indexOf(",") != -1) {
                userIdAndOrgIdSet.addAll(Arrays.asList(userIdAndOrgIdStr.split(",")));
            } else {
                userIdAndOrgIdSet.add(userIdAndOrgIdStr);
            }
            if (userIdAndOrgIdSet == null || userIdAndOrgIdSet.isEmpty()) {
                return treeNodeList;
            }
            for (String userIdAndOrgId : userIdAndOrgIdSet) {
                if (StringUtils.isEmpty(userIdAndOrgId)) {
                    continue;
                }
                String[] userIdAndOrgIds = new String[2];
                if (userIdAndOrgId.indexOf("#") != -1) {
                    userIdAndOrgIds = userIdAndOrgId.split("#");
                } else {
                    userIdAndOrgIds[0] = userIdAndOrgId;
                }
                SysUser user = null;
                if (userIdAndOrgIds[1] == null) {// 当主兼人员处理
                    // user = userService.getUserById(userIdAndOrgIds[0]);
                    continue;// 为了与客户端接口保持一致必须传人员和组织ID
                } else {// 兼职人员处理
                        // user =
                        // userService.selectUserByUserName(userIdAndOrgIds[0],userIdAndOrgIds[1]);
                    user = userService.selectUserByUserName(userIdAndOrgIds[0]);
                }
                if (user == null) {
                    continue;
                }
                List<SysDept> orgs = this.sysDeptService.findParentOrgTree(user.getDeptId());
                if (orgs == null || orgs.isEmpty()) {
                    continue;
                }
                orgTree.addAll(orgs);
                ActivityResourceModel treeNode = new ActivityResourceModel(
                        user.getUserName(), String.valueOf(user.getDeptId()),
                        user.getNickName(), -1, "USER", 1, true);
                treeNodeList.add(treeNode);
            }
            for (SysDept org : orgTree) {
                ActivityResourceModel treeNode = new ActivityResourceModel(
                        String.valueOf(org.getDeptId()), String.valueOf(org
                                .getParentId()),
                        org.getDeptName(),
                        org.getDeptLevel(), org.getDeptType() != null ? org.getDeptType() : "ORG",
                        Integer.parseInt(org.getOrderNum()), false);
                treeNodeList.add(treeNode);
            }
        } catch (Exception e) {
            log.error("filterActivityResource异常", e);
        }
        return treeNodeList;
    }

    public boolean isYiBanTaskByTopId(String procInstId, String sourceActId, String targerActId) throws Exception {
        return getBpmClient().isYiBanTaskByTopId(procInstId, sourceActId, targerActId);
    }

    public boolean isYiBanTask(String procInstId, String sourceActId, String targerActId) throws Exception {
        return getBpmClient().isYiBanTask(procInstId, sourceActId, targerActId);
    }

    public List<UniteWorkTaskDTO> getRecordbyPorcInstId(String porcInstId) throws Exception {
        return iWorkflowLogService.getRecordbyPorcInstId(porcInstId, getBpmClient());
    }

    public ProcessInstanceModel backFlowToOne(BpmClientInputModelBo bpmClientInputModelBo) throws Exception {
        WorkflowLog logOne = iWorkflowLogService.getOne(new LambdaQueryWrapper<WorkflowLog>()
                .eq(WorkflowLog::getProcInstId, bpmClientInputModelBo.getModel().getWf_procInstId())
                .orderByAsc(WorkflowLog::getCreateTime)
                .last("limit 1"));
        // 防止调整了部门 主动查一遍
        String deptId = sysUserService.getDeptByUserName(bpmClientInputModelBo.getModel().getWf_sendUserId());
        bpmClientInputModelBo.getModel().setWf_sendUserOrgId(deptId);
        // 设置接收人
        List<Map<String, Object>> receivers = new ArrayList<>();
        Map<String, Object> map = new HashMap<>();
        map.put("receiveUserId", SecurityUtils.getUsername());
        map.put("receiveUserOrgId", SecurityUtils.getDeptId());
        receivers.add(map);
        bpmClientInputModelBo.getModel().setWf_receivers(receivers);
        bpmClientInputModelBo.getModel().setWf_nextActDefId(logOne.getActDefId());
        bpmClientInputModelBo.getModel().setWf_nextActDefName(logOne.getActDefName());
        try {
            return nextExecute(bpmClientInputModelBo, logOne.getBusinessId());
        } catch (Exception e) {
            return null;
        }
    }

    public AjaxResult<List<DocClassFlowNodeVo>> syncFlowPlatNodeList(String docClass, String bizType, String flowKey,
            String flowId) {
        List<DocClassFlowNodeVo> result = new ArrayList<>();
        try {
            ProcessDefinitionModel processDefinitionModel = getProcessDefinitionModel(flowKey);
            if (processDefinitionModel != null) {
                List<ActivityDefinitionModel> defModelList = getActDef(processDefinitionModel.getProcDefId());
                if (defModelList != null && defModelList.size() > 0) { // 流程平台模型，有配置环节
                    // 构造流程节点清单
                    boolean isAppendAuthList = StringUtils.isNotEmpty(flowId) ? true : false;
                    for (ActivityDefinitionModel defModel : defModelList) {
                        if ("endEvent".equals(defModel.getActDefType())) {
                            continue; // 结束节点，忽略
                        }
                        DocClassFlowNodeVo newNodeObj = new DocClassFlowNodeVo();
                        newNodeObj.setTenantId("1");
                        newNodeObj.setNodeCode(defModel.getActDefId());
                        newNodeObj.setNodeName(defModel.getActDefName());
                        newNodeObj.setDeleteFlag("N");
                        newNodeObj.setSort(defModel.getActDefOrder().longValue());
                        newNodeObj.setIsMulti(defModel.isMulti());
                        if (isAppendAuthList) {
                            DocClassFlowNode nodeObj = docClassFlowNodeMapper.selectOne(new QueryWrapper<>(
                                    new DocClassFlowNode().setFlowId(flowId).setNodeCode(defModel.getActDefId())));
                            if (nodeObj != null) {
                                List<DocClassFlowNodeDetail> nodeDetailList = this.docClassFlowNodeDetailMapper
                                        .selectList(new QueryWrapper<>(
                                                new DocClassFlowNodeDetail().setNodeId(nodeObj.getId())));
                                newNodeObj.setNodeDetailList(nodeDetailList);
                            }
                        }
                        result.add(newNodeObj);
                    }
                }
            }
        } catch (Exception e) {
            log.error("syncFlowPlatNodeList异常", e);
        }
        return AjaxResult.success(result);
    }

    public Map<String, String> compareFlowPlatNodeList(String flowKey, String flowId) {
        Map<String, String> result = new HashMap<>(); // 默认没有变更
        String delNodes = "";
        String addNodes = "";
        try {
            ProcessDefinitionModel processDefinitionModel = getProcessDefinitionModel(flowKey);
            if (processDefinitionModel != null) {
                List<ActivityDefinitionModel> defModelList = getActDef(processDefinitionModel.getProcDefId());
                // 构造和保存分类流程节点清单
                if (defModelList != null && defModelList.size() > 0) {
                    List<DocClassFlowNode> nodeList = docClassFlowNodeMapper
                            .selectList(new QueryWrapper<>(new DocClassFlowNode().setFlowId(flowId)));
                    // 判断流程平台的环节，是否在现有分类流程环节中可以找到
                    for (ActivityDefinitionModel defModel : defModelList) {
                        if ("endEvent".equals(defModel.getActDefType())) {
                            continue;
                        }
                        List<DocClassFlowNode> existList = nodeList.stream()
                                .filter(s -> s.getNodeCode().equals(defModel.getActDefId()))
                                .collect(Collectors.toList());
                        if (existList == null || existList.size() == 0) { // 未找到
                            if (StringUtils.isNotEmpty(addNodes)) {
                                addNodes += ",";
                            }
                            addNodes += defModel.getActDefName(); // 待新增-节点名称
                        }
                    }
                    // 判断体系文件分类流程配置的环节，是否在现有流程平台模型环节中可以找到
                    for (DocClassFlowNode nodeObj : nodeList) {
                        List<ActivityDefinitionModel> existList = defModelList.stream()
                                .filter(s -> s.getActDefId().equals(nodeObj.getNodeCode()))
                                .collect(Collectors.toList());
                        if (existList == null || existList.size() == 0) { // 未找到
                            if (StringUtils.isNotEmpty(delNodes)) {
                                delNodes += ",";
                            }
                            delNodes += nodeObj.getNodeName(); // 待删除-节点名称
                        }
                    }

                }
            }
        } catch (Exception e) {
            log.error("compareFlowPlatNodeList异常", e);
        }
        result.put("addNodes", addNodes);
        result.put("delNodes", delNodes);
        return result;
    }

    public List<SysDictData> queryFlowList(String key) {
        return workflowMapper.queryFlowList(key, customConfig.getBpmTenantId(), customConfig.getBpmDataSource());
    }

    public String updateFlowTitle(String procTitle, String businessKey) {
        WorkflowApplyLog log = workflowApplyLogService.getOne(new LambdaQueryWrapper<WorkflowApplyLog>()
                .eq(WorkflowApplyLog::getId, businessKey)
                .select(WorkflowApplyLog::getId, WorkflowApplyLog::getApplyClass, WorkflowApplyLog::getProcInstId));
        procTitle = addPrefix(procTitle, log.getApplyClass());
        workflowMapper.updateTaskTitle(procTitle, log.getProcInstId(), customConfig.getBpmDataSource());
        workflowMapper.updateFlowTitle(procTitle, log.getProcInstId(), customConfig.getBpmDataSource());
        workflowMapper.updateRuTaskTitle(procTitle, log.getProcInstId(), customConfig.getBpmDataSource());
        return procTitle;
    }

    public String addPrefix(String procTitle, String bizType) {
        String remark = iSysDictDataService.selectDictRemark("sys_biz_type", bizType);
        return StringUtils.isNotEmpty(remark) ? (remark + procTitle) : procTitle;
    }

    /**
     * 构建PC端链接
     */
    public String buildPcLink(String url, String topProcInstId, String procInstId, String actInstId) {
        String pcAfterUrl = customConfig.getFrontUrl() + "workflow";
        return url.replace(this.customConfig.getWorkflowUniteWorkUrl(), pcAfterUrl)
                + "&topProcInstId=" + topProcInstId
                + "&procInstId=" + procInstId
                + "&actInstId=" + actInstId
                + "&status=" + getFlowStatus(procInstId, actInstId)
                + "&invokerForm=systemMsg";
    }

    /**
     * 构建移动端链接
     */
    public String buildMobileLink(String url, String topProcInstId, String procInstId, String actInstId, Boolean isEmail) {
        //提取type参数
        String type = MsgTemplateUtils.getParameterValue(url, "type");
        String mobileAfterUrl = null;
        if(isEmail){
            mobileAfterUrl = customConfig.getMobileFrontUrl() + "pages/workflowList/addWorkflow/"+type;
        }else{
            mobileAfterUrl = "/pages/workflowList/addWorkflow/"+type;
        }
        String linkUrl = url.replace(this.customConfig.getWorkflowUniteWorkUrl(), mobileAfterUrl)
                + "&topProcInstId=" + topProcInstId
                + "&procInstId=" + procInstId
                + "&actInstId=" + actInstId
                + "&status=" + getFlowStatus(procInstId, actInstId)
                + "&invokerForm=systemMsgNotHandle";
        return linkUrl;
    }
}

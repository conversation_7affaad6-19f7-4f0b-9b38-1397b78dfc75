package com.rzdata.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.tree.Tree;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.rzdata.framework.constant.CommonI18nConstant;
import com.rzdata.framework.core.domain.entity.SysDept;
import com.rzdata.framework.core.mybatisplus.core.ServicePlusImpl;
import com.rzdata.framework.core.page.PagePlus;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.utils.*;
import com.rzdata.process.domain.ModifyApply;
import com.rzdata.process.domain.Standard;
import com.rzdata.process.service.IDocStatisticsService;
import com.rzdata.process.service.IModifyApplyService;
import com.rzdata.process.service.IStandardService;
import com.rzdata.setting.domain.DocClass;
import com.rzdata.setting.service.IDocClassService;
import com.rzdata.system.domain.ProjectInfo;
import com.rzdata.system.domain.bo.ProjectInfoBo;
import com.rzdata.system.domain.bo.ProjectInfoGroupBo;
import com.rzdata.system.domain.vo.ProjectInfoVo;
import com.rzdata.system.mapper.ProjectInfoMapper;
import com.rzdata.system.service.IProjectInfoGroupService;
import com.rzdata.system.service.IProjectInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.rmi.ServerException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 项目信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-06-20
 */
@Service
public class ProjectInfoServiceImpl extends ServicePlusImpl<ProjectInfoMapper, ProjectInfo, ProjectInfoVo> implements IProjectInfoService {

    @Autowired
    private IDocClassService docClassService;


    @Autowired
    private IProjectInfoGroupService projectInfoGroupService;
    @Autowired
    private IStandardService standardService;

    @Override
    public ProjectInfoVo queryById(String id){
        return getVoById(id);
    }

    @Override
    public TableDataInfo<ProjectInfoVo> queryPageList(ProjectInfoBo bo) {
        PagePlus<ProjectInfo, ProjectInfoVo> result = pageVo(PageUtils.buildPagePlus(), buildQueryWrapper(bo));
        TableDataInfo<ProjectInfoVo> table = PageUtils.buildDataInfo(result);
        // 设置项目已关联的项目组
        List<ProjectInfoVo> rows = table.getRows();
        for(ProjectInfoVo item : rows) {
            ProjectInfoGroupBo query = new ProjectInfoGroupBo();
            query.setProjectId(item.getId());
            item.setGroups(this.projectInfoGroupService.queryList(query));
        }
        return table;
    }

    @Override
    public List<ProjectInfoVo> queryList(ProjectInfoBo bo) {
        return listVo(buildQueryWrapper(bo));
    }

    private LambdaQueryWrapper<ProjectInfo> buildQueryWrapper(ProjectInfoBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<ProjectInfo> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getCode()), ProjectInfo::getCode, bo.getCode());
        lqw.like(StringUtils.isNotBlank(bo.getName()), ProjectInfo::getName, bo.getName());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), ProjectInfo::getStatus, bo.getStatus());
        return lqw;
    }

    @Override
    public Boolean insertByBo(ProjectInfoBo bo) {
        ProjectInfo add = BeanUtil.toBean(bo, ProjectInfo.class);
        validEntityBeforeSave(add);
        boolean flag = save(add);
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    @Override
    public Boolean updateByBo(ProjectInfoBo bo) {
        ProjectInfo update = BeanUtil.toBean(bo, ProjectInfo.class);
        validEntityBeforeSave(update);
        return updateById(update);
    }

    /**
     * 保存前的数据校验
     *
     * @param entity 实体类数据
     */
    private void validEntityBeforeSave(ProjectInfo entity){
        //TODO 做一些数据校验,如唯一约束
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) throws ServerException {
        if(isValid){
            LambdaQueryWrapper<Standard> lqw=new LambdaQueryWrapper<>();
            lqw.in(Standard::getProjectId,ids);
            long sum= standardService.count(lqw);
            if(sum>0){
                throw new ServerException(I18nUtils.getTitle(CommonI18nConstant.SERVICE_PROJECT_INFO_EXIST_FILE));
            }
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return removeByIds(ids);
    }

    @Override
    public List<Tree<String>> buildTreeSelect() {
        List<SysDept> nodeList = new ArrayList<>();
        // 查询项目集合（带权限控制的）
        List<ProjectInfo> projectList = this.queryUserProjectList();
        // 查询所有有效的用户数据 排序，数字越小靠前
        List<DocClass> typeList = this.docClassService.list(new QueryWrapper<>(new DocClass().setClassStatus("1").setDataType("project")).orderByAsc("sort"));
        for(int i=0;i<projectList.size();i++) {
            // 构造项目节点
            ProjectInfo proItem = projectList.get(i);
            SysDept proNode = new SysDept();
            proNode.setDeptId(proItem.getId());
            proNode.setParentId("1");
            proNode.setDeptName(proItem.getName());
            proNode.setOrderNum(String.valueOf(i));
            // 标记项目
            proNode.setStatus("project");
            nodeList.add(proNode);
            // 在项目节点下添加文件类型节点
            for(int b=0;b<typeList.size();b++) {
                DocClass classItem = typeList.get(b);
                SysDept classNode = new SysDept();
                // 文件类型主键进行组合
                classNode.setDeptId(proItem.getId()+"_"+classItem.getId());
                classNode.setParentId(proItem.getId());
                // 文件分类ID
                classNode.setDeptFullPathId(classItem.getId());
                classNode.setDeptName(classItem.getClassName());
                classNode.setOrderNum(classItem.getSort().toString());
                // 标记文件类型
                classNode.setStatus("class");
                nodeList.add(classNode);
            }
        }
        // 构造树形结构
        List<Tree<String>> result = TreeBuildUtils.build(nodeList, "1", (dept, tree) -> {
            tree.setId(dept.getDeptId());
            tree.setParentId(dept.getParentId());
            tree.setName(dept.getDeptName());
            tree.setWeight(dept.getOrderNum());
            // 文件分类ID
            tree.putExtra("classId",dept.getDeptFullPathId());
            tree.putExtra("sort",dept.getOrderNum());
            // 节点类型
            tree.putExtra("nodeType",dept.getStatus());
        });
        return result;
    }

    @Override
    public List<ProjectInfo> queryUserProjectList() {
        List<ProjectInfo> result = null;
        // 获取当前用户ID
        String currUserId = SecurityUtils.getUserId();
        if("1".equals(currUserId)) {
            // 超级管理员
            result = this.list(new QueryWrapper<>(new ProjectInfo().setStatus("Y")));
        } else {
            // 普通用户
            result = this.getBaseMapper().queryUserProjectList(currUserId);
        }
        return result;
    }
}

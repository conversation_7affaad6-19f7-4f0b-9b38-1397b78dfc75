package com.rzdata.system.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jeecg.qywx.api.core.common.AccessToken;
import com.rzdata.framework.constant.Constants;
import com.rzdata.framework.core.domain.entity.SysUser;
import com.rzdata.framework.core.domain.model.CorpInfo;
import com.rzdata.framework.core.domain.model.LoginUser;
import com.rzdata.framework.core.redis.RedisCache;
import com.rzdata.framework.core.service.QywxAuthService;
import com.rzdata.framework.utils.QywxUtil;
import com.rzdata.framework.utils.StringUtils;
import com.rzdata.framework.utils.spring.SpringUtils;
import com.rzdata.system.domain.SysConfig;
import com.rzdata.system.service.ISysConfigService;
import com.rzdata.system.service.ISysUserService;
import com.rzdata.system.service.SysLoginService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.util.concurrent.TimeUnit;

/**
 * @Description: 企业微信认证实现类
 * @author: gj
 * @date 2024/9/26
 */
@Service
public class QywxAuthServiceImpl implements QywxAuthService {


    /**
     * 企业微信-企业ID在配置表中的KEY名称
     */
    private static final String CORP_ID_KEY = "CORP_ID";
    /**
     * 企业微信-密钥在配置表中的KEY名称
     */
    private static final String CORP_SECRET_KEY = "SECRET";
    /**
     * 企业微信-应用ID在配置表中的KEY名称
     */
    private static final String AGENT_ID_KEY = "AGENT_ID";

    @Autowired
    private ISysConfigService sysConfigService;
    @Autowired
    private ISysUserService userService;
    @Autowired
    private UserDetailsServiceImpl userDetailsService;

    @Autowired
    private SysLoginService loginService;


    /**
     * 获取企业微信-企业信息
     *
     * @return CorpInfo 企业信息的实例对象
     */
    @Override
    public CorpInfo getCorpInfo() {
        try {
            CorpInfo corpInfo = new CorpInfo();
            corpInfo.setCorpId(sysConfigService.selectConfigByKey(CORP_ID_KEY));
            corpInfo.setSecret(sysConfigService.selectConfigByKey(CORP_SECRET_KEY));
            corpInfo.setAgentId(sysConfigService.selectConfigByKey(AGENT_ID_KEY));

            return corpInfo;
        } catch (Exception e) {
            e.printStackTrace();
        }

        return null;
    }

    /**
     * 获取访问令牌
     * 通过调用此方法，向服务器请求一个新的访问令牌 该令牌通常用于后续的接口调用，证明客户端的身份
     * 需要使用缓存机制，企业微信，获取AccessToken有频率限制
     *
     * @param corpID 企业唯一标识符
     * @param secret 安全密钥
     * @return 返回获取到的访问令牌
     */
    @Override
    public AccessToken getAccessToken(String corpID, String secret) {
        try {
            String tokenString = SpringUtils.getBean(RedisCache.class).getCacheObject(getCacheKey(corpID));
            if (StringUtils.isNotEmpty(tokenString)) {
                return JSON.parseObject(tokenString, AccessToken.class);
            }

            AccessToken token = QywxUtil.getAccessToken(corpID, secret);
            if (StringUtils.isNotNull(token)) {
                int expire = token.getExpiresIn() - 200; //缓存提前200秒过期
                SpringUtils.getBean(RedisCache.class).setCacheObject(getCacheKey(corpID), JSON.toJSONString(token),
                        new Integer(expire), TimeUnit.SECONDS);
                return token;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }


    /**
     * 根据企业微信用户ID获取DMS用户信息
     *
     * @param wechatUserId 企业微信用户ID
     * @return 返回与企业微信用户ID关联的登录用户信息
     */
    @Override
    public LoginUser getLoginUser(String wechatUserId) {
        try {
            SysUser sysUser = userService.selectUserByThirdId(wechatUserId);
            UserDetails user = userDetailsService.createLoginUser(sysUser);
            if (user instanceof LoginUser) {
                return (LoginUser) user;
            }
            return null;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 通过用户姓名获取token
     *
     * @param userName 用户名，用于生成登录令牌
     * @return 生成的登录令牌字符串
     */
    @Override
    public String loginSSO(String userName) {
        return loginService.sso(userName);
    }

    @Override
    public void qywxSSOLogin() {
        loginService.getCorpWechatCode(null);
    }

    /**
     * 设置cache key
     *
     * @param corpID 企业微信企业ID
     * @return 缓存键key
     */
    private String getCacheKey(String corpID) {
        return Constants.CORP_WECHAT_ACCESS_TOKEN_KEY + corpID;
    }
}

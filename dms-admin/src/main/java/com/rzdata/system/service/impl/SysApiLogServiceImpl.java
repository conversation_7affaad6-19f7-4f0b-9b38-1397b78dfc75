package com.rzdata.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
    import com.rzdata.framework.utils.PageUtils;
import com.rzdata.framework.core.page.PagePlus;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.utils.StringUtils;
import com.rzdata.system.domain.SysOperLog;
import org.springframework.stereotype.Service;
import com.rzdata.framework.core.mybatisplus.core.ServicePlusImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.rzdata.system.domain.bo.SysApiLogBo;
import com.rzdata.system.domain.vo.SysApiLogVo;
import com.rzdata.system.domain.SysApiLog;
import com.rzdata.system.mapper.SysApiLogMapper;
import com.rzdata.system.service.ISysApiLogService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * API调用日志Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-09-25
 */
@Service
public class SysApiLogServiceImpl extends ServicePlusImpl<SysApiLogMapper, SysApiLog, SysApiLogVo> implements ISysApiLogService {

    @Override
    public SysApiLogVo queryById(String id){
        return getVoById(id);
    }

    @Override
    public TableDataInfo<SysApiLogVo> queryPageList(SysApiLogBo bo) {
        PagePlus<SysApiLog, SysApiLogVo> result = pageVo(PageUtils.buildPagePlus(), buildQueryWrapper(bo));
        return PageUtils.buildDataInfo(result);
    }

    @Override
    public List<SysApiLogVo> queryList(SysApiLogBo bo) {
        return listVo(buildQueryWrapper(bo));
    }

    private LambdaQueryWrapper<SysApiLog> buildQueryWrapper(SysApiLogBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SysApiLog> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getTenantId()), SysApiLog::getTenantId, bo.getTenantId());
        lqw.eq(StringUtils.isNotBlank(bo.getSourceId()), SysApiLog::getSourceId, bo.getSourceId());
        lqw.eq(StringUtils.isNotBlank(bo.getSourceType()), SysApiLog::getSourceType, bo.getSourceType());
        lqw.eq(StringUtils.isNotBlank(bo.getAppId()), SysApiLog::getAppId, bo.getAppId());
        lqw.like(StringUtils.isNotBlank(bo.getAppName()), SysApiLog::getAppName, bo.getAppName());
        lqw.eq(StringUtils.isNotBlank(bo.getApiType()), SysApiLog::getApiType, bo.getApiType());
        lqw.like(StringUtils.isNotBlank(bo.getApiUrl()), SysApiLog::getApiUrl, bo.getApiUrl());
        lqw.eq(StringUtils.isNotBlank(bo.getRequestParam()), SysApiLog::getRequestParam, bo.getRequestParam());
        lqw.eq(bo.getRequestTime() != null, SysApiLog::getRequestTime, bo.getRequestTime());
        lqw.eq(bo.getStatus() != null, SysApiLog::getStatus, bo.getStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getResponseStatus()), SysApiLog::getResponseStatus, bo.getResponseStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getResponseInfo()), SysApiLog::getResponseInfo, bo.getResponseInfo());
        lqw.eq(bo.getResponseTime() != null, SysApiLog::getResponseTime, bo.getResponseTime());
        lqw.between(params.get("beginTime") != null && params.get("endTime") != null,
                SysApiLog::getRequestTime, params.get("beginTime"), params.get("endTime"));
        return lqw;
    }

    @Override
    public Boolean insertByBo(SysApiLogBo bo) {
        SysApiLog add = BeanUtil.toBean(bo, SysApiLog.class);
        validEntityBeforeSave(add);
        boolean flag = save(add);
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    @Override
    public Boolean updateByBo(SysApiLogBo bo) {
        SysApiLog update = BeanUtil.toBean(bo, SysApiLog.class);
        validEntityBeforeSave(update);
        return updateById(update);
    }

    /**
     * 保存前的数据校验
     *
     * @param entity 实体类数据
     */
    private void validEntityBeforeSave(SysApiLog entity){
        //TODO 做一些数据校验,如唯一约束
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return removeByIds(ids);
    }
}

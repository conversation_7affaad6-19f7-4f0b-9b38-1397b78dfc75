package com.rzdata.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.rzdata.framework.core.domain.entity.SysDeptDetail;

import com.rzdata.framework.core.mybatisplus.core.ServicePlusImpl;

import com.rzdata.system.mapper.SysDeptDetailMapper;
import com.rzdata.system.service.ISysDeptDetailService;
import com.rzdata.system.service.ISysDeptService;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 部门管理 服务实现
 *
 * <AUTHOR> Li
 */
@Service
public class SysDeptDetailServiceImpl extends ServicePlusImpl<SysDeptDetailMapper, SysDeptDetail, SysDeptDetail> implements ISysDeptDetailService {


    @Override
    public SysDeptDetail loadDeptDetails(String deptId) {
        return getOne(new LambdaQueryWrapper<SysDeptDetail>().eq(SysDeptDetail::getDeptId, deptId), false);
    }
}

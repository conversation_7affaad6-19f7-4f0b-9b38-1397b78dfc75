package com.rzdata.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.tree.Tree;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.google.common.collect.Lists;
import com.rzdata.config.CustomConfig;
import com.rzdata.config.DmsConfig;
import com.rzdata.framework.constant.CommonI18nConstant;
import com.rzdata.framework.constant.Constants;
import com.rzdata.framework.constant.UserConstants;
import com.rzdata.framework.core.domain.entity.SysDept;
import com.rzdata.framework.core.domain.entity.SysDeptDetail;
import com.rzdata.framework.core.domain.entity.SysRole;
import com.rzdata.framework.core.domain.entity.SysUser;
import com.rzdata.framework.core.mybatisplus.core.ServicePlusImpl;
import com.rzdata.framework.exception.ServiceException;
import com.rzdata.framework.utils.I18nUtils;
import com.rzdata.framework.utils.SecurityUtils;
import com.rzdata.framework.utils.StringUtils;
import com.rzdata.framework.utils.TreeBuildUtils;
import com.rzdata.framework.utils.spring.SpringUtils;
import com.rzdata.system.domain.vo.SysDeptVo;
import com.rzdata.system.mapper.SysDeptMapper;
import com.rzdata.system.mapper.SysRoleMapper;
import com.rzdata.system.mapper.SysUserMapper;
import com.rzdata.system.service.ISysDeptDetailService;
import com.rzdata.system.service.ISysDeptService;
import org.apache.ibatis.jdbc.SqlRunner;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 部门管理 服务实现
 *
 * <AUTHOR> Li
 */
@Service
public class SysDeptServiceImpl extends ServicePlusImpl<SysDeptMapper, SysDept, SysDept> implements ISysDeptService {

    @Autowired
    private SysRoleMapper roleMapper;

    @Autowired
    private SysUserMapper userMapper;

    @Override
    public Map<String, String> queryDeptName(List<String> deptIds) {
        if (CollectionUtil.isEmpty(deptIds)) {
            return new HashMap<>();
        }
        return Optional.ofNullable(this.listByIds(deptIds)).orElse(Lists.newArrayList())
                .stream().collect(Collectors.toMap(SysDept::getDeptId, SysDept::getDeptName));
    }
    /**
     * 查询部门管理数据
     *
     * @param dept 部门信息
     * @return 部门信息集合
     */
    @Override
//    @DataScope(deptAlias = "d")
    public List<SysDept> selectDeptList(SysDept dept) {
        return baseMapper.selectDeptList(dept);
    }
    /**
     * 查询部门管理数据
     *
     * @param dept 部门信息
     * @return 部门信息集合
     */
    @Override
//    @DataScope(deptAlias = "d")
    public List<SysDept> selectDeptListDetail(SysDept dept) {
        return baseMapper.selectDeptListDetail(dept);
    }

    @Override
    public List<SysDept> selectList() {
        return list(new LambdaQueryWrapper<SysDept>().eq(SysDept::getDelFlag, Constants.ZERO).eq(SysDept::getStatus,Constants.ZERO));
    }

    /**
     * 查询部门管理数据
     *
     * @param dept 部门信息
     * @return 部门信息集合
     */
    @Override
    public List<SysDept> selectDeptListForDist(SysDept dept) {
        return baseMapper.selectDeptList(dept);
    }

    /**
     * 构建前端所需要下拉树结构
     *
     * @param depts 部门列表
     * @return 下拉树结构列表
     */
    @Override
    public List<Tree<String>> buildDeptTreeSelect(List<SysDept> depts) {
        return TreeBuildUtils.build(depts, (dept, tree) ->
            tree.setId(dept.getDeptId())
                .setParentId(dept.getParentId())
                .setName(dept.getDeptName())
                .setWeight(dept.getOrderNum()));
    }

    /**
     * 根据角色ID查询部门树信息
     *
     * @param roleId 角色ID
     * @return 选中部门列表
     */
    @Override
    public List<Integer> selectDeptListByRoleId(Long roleId) {
        SysRole role = roleMapper.selectById(roleId);
        return baseMapper.selectDeptListByRoleId(roleId, role.isDeptCheckStrictly());
    }

    /**
     * 根据部门ID查询信息
     *
     * @param deptId 部门ID
     * @return 部门信息
     */
    @Override
    public SysDept selectDeptById(String deptId) {
        return getById(deptId);
    }

    /**
     * 根据ID查询所有子部门（正常状态）
     *
     * @param deptId 部门ID
     * @return 子部门数
     */
    @Override
    public Long selectNormalChildrenDeptById(String deptId) {
        return count(new LambdaQueryWrapper<SysDept>()
            .eq(SysDept::getStatus, 0)
            .apply("find_in_set({0}, ancestors)", deptId));
    }

    /**
     * 是否存在子节点
     *
     * @param deptId 部门ID
     * @return 结果
     */
    @Override
    public boolean hasChildByDeptId(String deptId) {
        long result = count(new LambdaQueryWrapper<SysDept>()
            .eq(SysDept::getParentId, deptId));
        return result > 0;
    }

    /**
     * 查询部门是否存在用户
     *
     * @param deptId 部门ID
     * @return 结果 true 存在 false 不存在
     */
    @Override
    public boolean checkDeptExistUser(String deptId) {
        long result = userMapper.selectCount(new LambdaQueryWrapper<SysUser>()
            .eq(SysUser::getDeptId, deptId));
        return result > 0;
    }

    /**
     * 校验部门名称是否唯一
     *
     * @param dept 部门信息
     * @return 结果
     */
    @Override
    public String checkDeptNameUnique(SysDept dept) {
        String deptId = StringUtils.isNull(dept.getDeptId()) ? "-1" : dept.getDeptId();
        long count = count(new LambdaQueryWrapper<SysDept>()
            .eq(SysDept::getDeptName, dept.getDeptName())
            .eq(SysDept::getParentId, dept.getParentId())
            .ne(SysDept::getDeptId, deptId));
        if (count > 0) {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 校验部门是否有数据权限
     *
     * @param deptId 部门id
     */
    @Override
    public void checkDeptDataScope(String deptId) {
        if (!SysUser.isAdmin(SecurityUtils.getUserId())) {
            SysDept dept = new SysDept();
            dept.setDeptId(deptId);
            List<SysDept> depts = SpringUtils.getAopProxy(this).selectDeptList(dept);
            if (StringUtils.isEmpty(depts)) {
                throw new ServiceException(I18nUtils.getTitle(CommonI18nConstant.SERVICE_SYS_DO_NOT_ACCESS_DEPT_DATA));
            }
        }
    }

    /**
     * 新增保存部门信息
     *
     * @param dept 部门信息
     * @return 结果
     */
    @Override
    public int insertDept(SysDept dept) {
        SysDept info = getById(dept.getParentId());
        // 如果父节点不为正常状态,则不允许新增子节点
        if (!UserConstants.DEPT_NORMAL.equals(info.getStatus())) {
            throw new ServiceException(I18nUtils.getTitle(CommonI18nConstant.SERVICE_SYS_DEPT_DEACTIVATE));
        }
        dept.setAncestors(info.getAncestors() + "," + dept.getParentId());
        dept.setDeptFullPathName(dept.getDeptName()+","+("0".equals(info.getParentId())?info.getDeptName():info.getDeptFullPathName()));
        CustomConfig customConfig = SpringUtils.getBean(CustomConfig.class);
        String ubaseDataSource = customConfig.getUbaseDataSource();
        dept.setDeptId(UUID.randomUUID().toString());
        baseMapper.insertDeptView(dept,ubaseDataSource);
        dept.setDeptFullPathId(dept.getDeptId()+","+("0".equals(info.getParentId())?dept.getParentId():info.getDeptFullPathId()));
        return baseMapper.updateById(dept);
    }

    /**
     * 修改保存部门信息
     *
     * @param dept 部门信息
     * @return 结果
     */
    @Override
    public int updateDept(SysDept dept) {
        SysDept newParentDept = getById(dept.getParentId());
        SysDept oldDept = getById(dept.getDeptId());
        if (StringUtils.isNotNull(newParentDept) && StringUtils.isNotNull(oldDept)) {
            String newAncestors = newParentDept.getAncestors() + "," + newParentDept.getDeptId();
            String oldAncestors = oldDept.getAncestors();
            dept.setAncestors(newAncestors);
            updateDeptChildren(dept.getDeptId(), newAncestors, oldAncestors);
        }
        CustomConfig customConfig = SpringUtils.getBean(CustomConfig.class);
        String ubaseDataSource = customConfig.getUbaseDataSource();
        int result = baseMapper.updateDeptView(dept,ubaseDataSource);
        if (UserConstants.DEPT_NORMAL.equals(dept.getStatus()) && StringUtils.isNotEmpty(dept.getAncestors())
            && !StringUtils.equals("0", dept.getAncestors())) {
            // 如果该部门是启用状态，则启用该部门的所有上级部门
            updateParentDeptStatusNormal(dept);
        }
        return result;
    }

    @Autowired
    private ISysDeptDetailService sysDeptDetailService;
    /**
     * 修改保存部门信息
     *
     * @param dept 部门信息
     * @return 结果
     */
    @Override
    public boolean updateDeptDetail(SysDept dept) {
        SysDeptDetail sysDeptDetail = new SysDeptDetail();
        BeanUtil.copyProperties(dept, sysDeptDetail);
        sysDeptDetailService.remove(new LambdaQueryWrapper<SysDeptDetail>().eq(SysDeptDetail::getDeptId, dept.getDeptId()));
        return sysDeptDetailService.save(sysDeptDetail);
    }

    /**
     * 修改该部门的父级部门状态
     *
     * @param dept 当前部门
     */
    private void updateParentDeptStatusNormal(SysDept dept) {
        String ancestors = dept.getAncestors();
        String[] deptIds = Convert.toStrArray(ancestors);
        update(null, new LambdaUpdateWrapper<SysDept>()
            .set(SysDept::getStatus, "0")
            .in(SysDept::getDeptId, Arrays.asList(deptIds)));
    }

    /**
     * 修改子元素关系
     *
     * @param deptId       被修改的部门ID
     * @param newAncestors 新的父ID集合
     * @param oldAncestors 旧的父ID集合
     */
    public void updateDeptChildren(String deptId, String newAncestors, String oldAncestors) {
        List<SysDept> children = list(new LambdaQueryWrapper<SysDept>()
            .apply("find_in_set({0},ancestors)", deptId));
        for (SysDept child : children) {
            child.setAncestors(child.getAncestors().replaceFirst(oldAncestors, newAncestors));
        }
        if (children.size() > 0) {
            baseMapper.updateDeptChildren(children);
        }
    }

    /**
     * 删除部门管理信息
     *
     * @param deptId 部门ID
     * @return 结果
     */
    @Override
    public int deleteDeptById(String deptId) {
        return baseMapper.deleteById(deptId);
    }

    @Override
    public List<SysDeptVo> selectDeptListToAiShu() {
        return this.baseMapper.selectDeptListToAiShu();
    }

    @Override
    public List<SysDept> findParentOrgTree(String deptId) {
        return this.baseMapper.findParentOrgTree(deptId);
    }

    @Override
    public String getDeptName(String deptId) {
        if (StringUtils.isNotEmpty(deptId)) {
            SysDept dept= getVoOne(new LambdaQueryWrapper<SysDept>().eq(SysDept::getDeptId,deptId).select(SysDept::getDeptName));
            return dept!=null?dept.getDeptName():null;
        }else {
            return null;
        }
    }

    @Override
    public String getSecDeptId(String deptId) {
        if (StringUtils.isNotEmpty(deptId)) {
            SysDept dept= getVoOne(new LambdaQueryWrapper<SysDept>().eq(SysDept::getDeptId,deptId).select(SysDept::getSecDeptId));
            return dept!=null?dept.getSecDeptId():null;
        }else {
            return null;
        }
    }

    @Override
    public List<SysDept> queryChildList(String deptId) {
        List<SysDept> list = new ArrayList<>();
        if (StringUtils.isNotEmpty(deptId)) {
            List<SysDept> childList = listVo(new LambdaQueryWrapper<SysDept>().eq(SysDept::getParentId,deptId)
                    .eq(SysDept::getStatus, Constants.ZERO)
                    .select(SysDept::getDeptId));
            for (SysDept vo:childList) {
                list.addAll(queryChildList(vo.getDeptId()));
            }
            list.addAll(childList);
        }
        return list;
    }

    @Override
    public SysDept getCompanyByDeptId(String deptId){
       return baseMapper.getCompanyByDeptId(deptId);
    }

    @Override
    public int updateDeptView(SysDept dept) {
        CustomConfig customConfig = SpringUtils.getBean(CustomConfig.class);
        String ubaseDataSource = customConfig.getUbaseDataSource();
       return baseMapper.updateDeptView(dept,ubaseDataSource);
    }
}

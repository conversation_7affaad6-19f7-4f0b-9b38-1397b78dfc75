package com.rzdata.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.rzdata.asas7.util.EmailUtil;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.core.mybatisplus.core.ServicePlusImpl;
import com.rzdata.framework.core.page.PagePlus;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.utils.PageUtils;
import com.rzdata.framework.utils.StringUtils;
import com.rzdata.plugins.mail.BizMailDto;
import com.rzdata.process.api.EmailController;
import com.rzdata.system.domain.SysNotifyLog;
import com.rzdata.system.domain.SysNotifyTemplate;
import com.rzdata.system.domain.bo.SysNotifyLogBo;
import com.rzdata.system.domain.vo.SysNotifyLogVo;
import com.rzdata.system.mapper.SysNotifyLogMapper;
import com.rzdata.system.service.ISysNotifyLogService;
import com.rzdata.system.service.ISysNotifyTemplateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 消息记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-04-18
 */
@Service
public class SysNotifyLogServiceImpl extends ServicePlusImpl<SysNotifyLogMapper, SysNotifyLog, SysNotifyLogVo> implements ISysNotifyLogService {

    @Autowired
    private ISysNotifyTemplateService sysNotifyTemplateService;

    @Autowired
    EmailController emailController;

    @Override
    public SysNotifyLogVo queryById(Long logId){
        return getVoById(logId);
    }

    @Override
    public TableDataInfo<SysNotifyLogVo> queryPageList(SysNotifyLogBo bo) {
        PagePlus<SysNotifyLog, SysNotifyLogVo> result = pageVo(PageUtils.buildPagePlus(), buildQueryWrapper(bo));
        return PageUtils.buildDataInfo(result);
    }

    @Override
    public List<SysNotifyLogVo> queryList(SysNotifyLogBo bo) {
        return listVo(buildQueryWrapper(bo));
    }

    private LambdaQueryWrapper<SysNotifyLog> buildQueryWrapper(SysNotifyLogBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SysNotifyLog> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getNickName()), SysNotifyLog::getNickName, bo.getNickName());
        lqw.eq(StringUtils.isNotBlank(bo.getUserMobile()), SysNotifyLog::getUserMobile, bo.getUserMobile());
        lqw.eq(StringUtils.isNotBlank(bo.getRemindType()), SysNotifyLog::getRemindType, bo.getRemindType());
        lqw.eq(StringUtils.isNotBlank(bo.getSendType()), SysNotifyLog::getSendType, bo.getSendType());
        lqw.eq(StringUtils.isNotBlank(bo.getTitle()), SysNotifyLog::getTitle, bo.getTitle());
        return lqw;
    }

    @Override
    public Boolean insertByBo(SysNotifyLogBo bo) {
        SysNotifyLog add = BeanUtil.toBean(bo, SysNotifyLog.class);
        validEntityBeforeSave(add);
        boolean flag = save(add);
        if (flag) {
            bo.setLogId(add.getLogId());
        }
        return flag;
    }

    @Override
    public Boolean updateByBo(SysNotifyLogBo bo) {
        SysNotifyLog update = BeanUtil.toBean(bo, SysNotifyLog.class);
        validEntityBeforeSave(update);
        return updateById(update);
    }

    /**
     * 保存前的数据校验
     *
     * @param entity 实体类数据
     */
    private void validEntityBeforeSave(SysNotifyLog entity){
        //TODO 做一些数据校验,如唯一约束
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return removeByIds(ids);
    }

    /**
     * 发送邮件，并且记录日志
     * @param sendType 消息编码
     * @param nickName 发送人员名称
     * @param email 发送人员联系方式
     * @param userId 通知的用户id
     * @param param 参数
     */
    @Async
    @Override
    public void sendEmail(String sendType, String nickName, String email, String userId, Map<String, String> param) {
        SysNotifyTemplate sysNotifyTemplate = sysNotifyTemplateService.getOne(new QueryWrapper<SysNotifyTemplate>(new SysNotifyTemplate()
                .setSendType(sendType)
                .setStatus(Long.valueOf(1))));
        String title = EmailUtil.replaceContent(sysNotifyTemplate.getTitle(),param);
        String content = EmailUtil.replaceContent(sysNotifyTemplate.getMessage(),param);

        SysNotifyLog sysNotifyLog = new SysNotifyLog();
        sysNotifyLog.setSendType(sendType);
        sysNotifyLog.setTitle(title);
        sysNotifyLog.setUserMobile(email);
        sysNotifyLog.setMessage(content);
        sysNotifyLog.setTemplateId(sysNotifyTemplate.getTemplateId());
        sysNotifyLog.setNickName(nickName);
        sysNotifyLog.setParamContent(JSON.toJSONString(param));
        sysNotifyLog.setRemindId(userId);
        sysNotifyLog.setRemindType(sysNotifyTemplate.getTemplateTypes());
        sysNotifyLog.setCreateTime(new Date());

        BizMailDto newDto = new BizMailDto();
        newDto.setTarget(sysNotifyLog.getUserMobile());
        newDto.setTitle(title);
        newDto.setContent(sysNotifyLog.getMessage());

        AjaxResult sendRes = this.emailController.push(newDto);
        if(sendRes.getCode() == 200) {
            sysNotifyLog.setStatus(Long.valueOf(1));
        } else {
            sysNotifyLog.setStatus(Long.valueOf(0));
            sysNotifyLog.setResultMgs(sendRes.getMsg());
        }
        this.save(sysNotifyLog);
    }


    /**
     * 不是异步处理的发送邮件，并且记录日志
     * @param sendType
     * @param nickName
     * @param email
     * @param userId
     * @param param
     */
    public void notAsyncSendEmail(String sendType, String nickName, String email, String userId, Map<String, String> param) {
        SysNotifyTemplate sysNotifyTemplate = sysNotifyTemplateService.getOne(new QueryWrapper<SysNotifyTemplate>(new SysNotifyTemplate()
                .setSendType(sendType)
                .setStatus(Long.valueOf(1))));
        String title = EmailUtil.replaceContent(sysNotifyTemplate.getTitle(),param);
        String content = EmailUtil.replaceContent(sysNotifyTemplate.getMessage(),param);

        SysNotifyLog sysNotifyLog = new SysNotifyLog();
        sysNotifyLog.setSendType(sendType);
        sysNotifyLog.setTitle(title);
        sysNotifyLog.setUserMobile(email);
        sysNotifyLog.setMessage(content);
        sysNotifyLog.setTemplateId(sysNotifyTemplate.getTemplateId());
        sysNotifyLog.setNickName(nickName);
        sysNotifyLog.setParamContent(JSON.toJSONString(param));
        sysNotifyLog.setRemindId(userId);
        sysNotifyLog.setRemindType(sysNotifyTemplate.getTemplateTypes());
        sysNotifyLog.setCreateTime(new Date());

        BizMailDto newDto = new BizMailDto();
        newDto.setTarget(sysNotifyLog.getUserMobile());
        newDto.setTitle(title);
        newDto.setContent(sysNotifyLog.getMessage());

        AjaxResult sendRes = this.emailController.push(newDto);
        if(sendRes.getCode() == 200) {
            sysNotifyLog.setStatus(Long.valueOf(1));
        } else {
            sysNotifyLog.setStatus(Long.valueOf(0));
            sysNotifyLog.setResultMgs(sendRes.getMsg());
        }
        this.save(sysNotifyLog);
    }

}

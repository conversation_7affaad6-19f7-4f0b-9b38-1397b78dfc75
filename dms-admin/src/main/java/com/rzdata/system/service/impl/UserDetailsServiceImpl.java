package com.rzdata.system.service.impl;


import com.rzdata.framework.constant.CommonI18nConstant;
import com.rzdata.framework.constant.Constants;
import com.rzdata.framework.core.domain.entity.SysDept;
import com.rzdata.framework.core.domain.entity.SysUser;
import com.rzdata.framework.core.domain.model.LoginUser;
import com.rzdata.framework.enums.UserStatus;
import com.rzdata.framework.exception.ServiceException;
import com.rzdata.framework.properties.TokenProperties;
import com.rzdata.framework.utils.I18nUtils;
import com.rzdata.framework.utils.StringUtils;
import com.rzdata.process.domain.Tenant;
import com.rzdata.system.service.ISysDeptService;
import com.rzdata.system.service.ISysUserService;
import com.rzdata.system.service.ITenantService;
import com.rzdata.system.service.SysPermissionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 用户验证处理
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class UserDetailsServiceImpl implements UserDetailsService {

    @Autowired
    private ISysUserService userService;

    @Autowired
    private ISysDeptService sysDeptService;

    @Autowired
    private SysPermissionService permissionService;

    @Autowired
    private ITenantService tenantService;

    @Autowired
    private TokenProperties tokenProperties;

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        SysUser user = userService.selectUserByUserName(username);
        if (StringUtils.isNull(user)) {
            log.info("登录用户：{} 不存在.", username);
            throw new ServiceException(username + "，" + I18nUtils.getTitle(CommonI18nConstant.LOGIN_USERNAME_NOT_PRESENT));
        } else if (UserStatus.DELETED.getCode().equals(user.getDelFlag())) {
            log.info("登录用户：{} 已被删除.", username);
            throw new ServiceException(username+ "，" + I18nUtils.getTitle(CommonI18nConstant.LOGIN_USERNAME_DELETE));
        } else if (UserStatus.DISABLE.getCode().equals(user.getStatus())) {
            log.info("登录用户：{} 已被停用.", username);
            throw new ServiceException(username + "，" + I18nUtils.getTitle(CommonI18nConstant.LOGIN_USERNAME_STOP));
        }

        return createLoginUser(user);
    }

    public UserDetails createLoginUser(SysUser user) {
        SysDept deptObj = this.sysDeptService.getById(user.getDeptId());
        SysDept secDeptObj = null;
        if(StringUtils.isNotEmpty(deptObj.getSecDeptId())) {
            secDeptObj = this.sysDeptService.getById(deptObj.getSecDeptId());
        }
        LoginUser loginUser = new LoginUser(user.getUserId(), user.getDeptId(),deptObj.getDeptName(),
                secDeptObj != null ? secDeptObj.getDeptId() : null,
                secDeptObj != null ? secDeptObj.getDeptName() : null,
                user.getUserName(),user.getPassword(), permissionService.getMenuPermission(user),
                user.getDept().getDeptCode(), user.getNickName(),user.getTenantId(),deptObj.getAncestors());
        //return loginUser;
        //LoginUser loginUser = new LoginUser(user.getUserId(), user.getDeptId(), user.getUserName(), user.getPassword(), permissionService.getMenuPermission(user));
        if(user.getUserId().equals("1")){
            loginUser.setTenantId(tenantService.list(null).get(0).getId());
        }
        else {
            List<Tenant> tenants= tenantService.selectTenantByUser(user.getUserId()+"");
            if(tenants.size()==0){
                throw new ServiceException(user.getUserName() + I18nUtils.getTitle(CommonI18nConstant.LOGIN_USERNAME_WITHOUT_CLIENT));
            }
            loginUser.setTenantId(tenants.get(0).getId());
        }
        //String userKey = getTokenKey(loginUser.getToken());
        //SpringUtils.getBean(RedisCache.class).setCacheObject(userKey, loginUser, tokenProperties.getExpireTime(), TimeUnit.MINUTES);
        return loginUser;
    }
    private String getTokenKey(String uuid) {
        return Constants.LOGIN_TOKEN_KEY + uuid;
    }
}

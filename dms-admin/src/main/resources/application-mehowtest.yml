logging:
  level:
    com.rzdata: debug
    org.springframework: error
rzdata:
  name: dms
  version: 0.1
  bpmTenantId: STANDARD_SYS
  bpmDataSource: dms_bpmc
  ubaseDataSource: dms_ubase
  workflowServiceUrl: http://*************:8071/ebpm-process-rest
  workflowUniteWorkUrl: /dms-front/#/workflow
  addressEnabled: true
  cacheLazy: true
  # 分发范围类型  部门dept 公司company
  distributeRangeType: company

  # 第三方消息通道
  msgThirdChannel: email
  # 流程结束参与者消息通知通道：txt站内消息、email邮件
  msgFlowNoticeChanel: email
  # 常规消息通知通道：txt站内消息、email邮件
  msgNormalNoticeChanel: txt
  # 流程结束（文件新增、文件修订、作废流程触发）参与者消息通知范围：flowHandler仅限流程参与者，allUser企业所有人员
  msgFlowNoticeScope: allUser

  # 复审是否开启 true开启、false停用
  reviewEnable: true
  # 复审流程KEY（见流程平台配置）
  reviewFlowKey: review_doc_apply_mh
  # 复审提前通知的天数配置
  reviewMsgForwardDay: 7,1
  # 复审通知人员
  # 文控company_file_manager、编制人editer、部门文控dept_file_manager
  reviewMsgReminder: editer
  # 流程提交对话框是否标记已办步骤

  flowStepMarkEnable: false
  # 流程提交对话框是否默认选择第一个步骤
  flowStepDefSelectEnable: true
  # 是否启用流程测试
  flowTestEnable: false
  # 流程测试人员，多个用逗号分隔
  flowTestUserNames: adminmh
  # 不需要签章的文件类型
  noSignDocType:
  # 不需要签章的文件扩展名
  noSignFileExdType: pdf
  # 不需要签章的文件添加方式 导入import 流程workflow
  noSignAddType: import
  # 是否添加文件编号水印
  signDocCodeEnable: true
  #前端地址
  frontUrl: http://localhost/dms-front/#/

dms:
  basePath: http://*************:3721
  bomPath: /bom-service
message:
  dingdingbak:
    enable: false
    desc: 客户正式信息
    AgentId: 2654238102
    AppKey: ding17bjbfkohmhh6hdd
    AppSecret: y6jNaQWX6q0SQXZugP95jygFMwPdXY5qPUcj2F9WacF5stkiOtKVXBUMpuEO82zg
    test: true
    testUserId: 16399654371881735
  dingding:
    enable: false
    desc: 测试信息
    AgentId: 2639644733
    AppKey: dingmixow0ag7k5yujvs
    AppSecret: UVbtUsDTJUyfyg9G4-fxL3ZVUFeHDRiQYoq2Er4StgVNgrWtytXcScq7Y8vJ646B
    test: false
    testUserId: 4038261335644901
word2pdf:
  enable: true
  # 是否异步
  asynchronous: true
  desc: Word转Pdf基础配置
  outPdfDirPath: doc2pdf
watermark:
  enable: true
  # 是否异步
  asynchronous: true
  desc: PDF文件水印基础配置
  scale: 0.5f
  outPdfDirPath: waterPdf
# office文件转pdf文件
forest:
  baseurl:
    # http://*************:8082/file-transformer
    # http://localhost:8010/file-transformer
    file-transformer: http://**************:38082/file-transformer
# 开发环境配置
server:
  # 服务器的HTTP端口，默认为8080
  port: 8080
  servlet:
    # 应用的访问路径
    context-path: /dms-admin-mehow
  # undertow 配置
  undertow:
    # HTTP post内容的最大大小。当值为-1时，默认值为大小是无限的
    max-http-post-size: -1
    # 以下的配置会影响buffer,这些buffer会用于服务器连接的IO操作,有点类似netty的池化内存管理
    # 每块buffer的空间大小,越小的空间被利用越充分
    buffer-size: 512
    # 是否分配的直接内存
    direct-buffers: true
    threads:
      # 设置IO线程数, 它主要执行非阻塞的任务,它们会负责多个连接, 默认设置每个CPU核心一个线程
      io: 8
      # 阻塞任务线程池, 当执行类似servlet请求阻塞操作, undertow会从这个线程池中取得线程,它的值设置取决于系统的负载
      worker: 256
--- # 流程 配置
#process:
#  uniteworkUrl: http://localhost:80/dms-front/
#  procDefKeyXJ: process_xunjian
#  procDefKeyWX: process_weixiu
#  procDefKeyBY: process_baoyang
--- # xxl-job 配置
xxl:
  job:
    # 执行器开关
    enabled: false
    # 调度中心地址：如调度中心集群部署存在多个地址则用逗号分隔。
    # http://*************:9100/xxl-job-admin
    # http://*************:9100/xxl-job-admin
    admin-addresses: http://127.0.0.1:8084/xxl-job-admin
    # 执行器通讯TOKEN：非空时启用
    access-token: default_token
    # 执行器配置
    executor:
      # 执行器AppName：执行器心跳注册分组依据；为空则关闭自动注册
      appname: dms-job-executor2
      # 执行器端口号 执行器从9101开始往后写
      port: 9109
      # 执行器注册：默认IP:PORT
      address:
      # 执行器IP：默认自动获取IP
      ip:
      # 执行器运行日志文件存储磁盘路径
      logpath: ./logs/xxl-job
      # 执行器日志文件保存天数：大于3生效
      logretentiondays: 30

--- # druid 配置
spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driverClassName: com.mysql.cj.jdbc.Driver
    url: jdbc:mysql://************:3307/dms_mehow?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=CST&serverTimezone=GMT%2B8&autoReconnect=true
    username: dms
    password: Dms@0821
    #url: ***************************************************************************************************************************************************************
    #username: root
    #password: root
    druid:
      webStatFilter:
        enabled: true
      statViewServlet:
        enabled: true
        # 设置白名单，不填则允许所有访问
        allow:
        url-pattern: /druid/*
        # 控制台管理用户名和密码
        login-username: rzdata
        login-password: 123456
      filter:
        stat:
          enabled: true
          # 慢SQL记录
          log-slow-sql: true
          slow-sql-millis: 1000
          merge-sql: true
        wall:
          config:
            multi-statement-allow: true

--- # redis 单机配置(单机与集群只能开启一个另一个需要注释掉)
spring:
  redis:
    # 地址
    host: *************
    # 端口，默认为6379
    port: 6379
    # 数据库索引
    database: 6
    # 密码
    password:
    # 连接超时时间
    timeout: 10s
    # 是否开启ssl
    ssl: false

#onlyoffice配置
document:
  server:
    # 本程序运行时的IP地址(如果有防火墙 开启了地址映射 请配置映射后地址) 先暂时写死
    host: http://*************:88/dev-dms-admin
    #host: http://************:8080/dev-dms-admin
    #登录地址
    loginUrl: http://*************:88/dev-dms-front/#/login
  cache:
    redis:
      # 是否使用Redis缓存
      enabled: true

qys:
  #契约锁服务地址
  url: 'https://oa.vazyme.com:9186'
  #契约锁accessKey
  accessKey: 'Em9DlpUe0y'
  #契约锁accessSecret
  accessSecret: '9YP1tnyX14zphdGoXaT7kF1Pw12dji'
  #契约锁业务ID
  bizId: '2935025908536250384'
  #契约锁印章ID
  sealId: '2708529673458024474'
  #发起方公司名称 必须要和契约锁的配置一致
  tenantName: '南京诺唯赞医疗科技有限公司'

drm:
  enabled: true

code:
  rule:
    record: 1503659081738682369

# 同步DMS发布文件到爱数文档库（包含生效文件、失效文件）
sync:
  enabled: true
  url: http://localhost:8089/dev-dms-sync
  account:
  password:

mail:
  # 邮件服务器的SMTP地址，可选，默认为smtp.<发件人邮箱后缀> smtp.exmail.qq.com
  host: smtp.qq.com
  # 邮件服务器的SMTP端口，可选，默认25
  port: 465
  # 发件人（必须正确，否则发送失败）
  from: <EMAIL>
  # 用户名，默认为发件人邮箱前缀
  user: <EMAIL>
  # 密码（注意，某些邮箱需要为SMTP服务单独设置授权码，详情查看相关帮助）
  pass: aohrxtmcrxepbcih
  #pass: hzrseorjjqffbdii
  # 使用SSL安全连接
  sslEnable: true
  # 使用STARTTLS安全连接
  auth: true
api:
  # 对外接口文件类型限制 只能查询这个文件分类下的
  limitDocClass: 3
supervise-strategy:
  corePoolSize: 5
  maxPoolSize: 5
  queueCapacity: 500
  keepAliveSeconds: 60
  sendEamil: true
  sendEmailTestUserName: adminmh

distribute-stat-strategy:
  corePoolSize: 5
  maxPoolSize: 5
  queueCapacity: 500
  keepAliveSeconds: 60

# 项目相关配置
rzdata:
  # 名称
  name: ehm-parent
  # 版本
  version: ${ruoyi-vue-plus.version}
  # 版权年份
  bpmTenantId: HEALTH_SYS
  # 实例演示开关
  workflowServiceUrl: http://*************:9089/ebpm-process-rest
  # 获取ip地址开关
  addressEnabled: true
  # 缓存懒加载
  cacheLazy: true
captcha:
  # 页面 <参数设置> 可开启关闭 验证码校验
  # 验证码类型 math 数组计算 char 字符验证
  type: MATH
  # line 线段干扰 circle 圆圈干扰 shear 扭曲干扰
  category: CIRCLE
  # 数字验证码位数
  numberLength: 1
  # 字符验证码长度
  charLength: 4

# 开发环境配置
server:
  # 服务器的HTTP端口，默认为8080
  port: 8080
  servlet:
    # 应用的访问路径
    context-path: /dms-admin-jiuli
  # undertow 配置
  undertow:
    # HTTP post内容的最大大小。当值为-1时，默认值为大小是无限的
    max-http-post-size: -1
    # 以下的配置会影响buffer,这些buffer会用于服务器连接的IO操作,有点类似netty的池化内存管理
    # 每块buffer的空间大小,越小的空间被利用越充分
    buffer-size: 512
    # 是否分配的直接内存
    direct-buffers: true
    threads:
      # 设置IO线程数, 它主要执行非阻塞的任务,它们会负责多个连接, 默认设置每个CPU核心一个线程
      io: 8
      # 阻塞任务线程池, 当执行类似servlet请求阻塞操作, undertow会从这个线程池中取得线程,它的值设置取决于系统的负载
      worker: 256


# tlog 全局访问性能拦截
tlog:
  enable-invoke-time-print: true

# Spring配置
spring:
  application:
    name: ehm-parent
  # 资源信息
  messages:
    # 国际化资源文件路径
    basename: i18n/messages
  profiles:
    active: prodtest
  # 文件上传
  servlet:
    multipart:
      # 单个文件大小
      max-file-size: 1000MB
      # 设置总上传的文件大小
      max-request-size: 1000MB
  # 服务模块
  devtools:
    restart:
      # 热部署开关
      enabled: true
  # 与vue整合部署使用
  thymeleaf:
    # 将系统模板放置到最前面 否则会与 springboot-admin 页面冲突
    template-resolver-order: 1
  jackson:
    # 日期格式化
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    serialization:
      # 格式化输出
      indent_output: false
      # 忽略无法转换的对象
      fail_on_empty_beans: false
      write-dates-as-timestamps: false
    deserialization:
      # 允许对象忽略json中不存在的属性
      fail_on_unknown_properties: false

# token配置
token:
  # 令牌自定义标识
  header: Authorization
  # 令牌密钥
  secret: live,travel,adventure,bless and don't be sorry
  # 令牌有效期（默认30分钟）
  expireTime: 120

# security配置
security:
  # 登出路径
  logout-url: /logout
  # 匿名路径
  anonymous:
    - /login
    - /as/api/initOrgAndUserJob
    - /as/sso/login
    - /as/sso/check
    - /register
    - /captchaImage
    # 集成流程平台待办消息
    - /process/api/flowPlat/receiveMsg
    # swagger 文档配置
    - /doc.html
    - /swagger-resources/**
    - /webjars/**
    - /*/api-docs
    # druid 监控配置
    - /druid/**
    # actuator 监控配置
    - /actuator
    - /actuator/**
    # 预览功能 提供给onlyOffice调用 不能使用权限验证, 因为onlyOffice不会发送token
    - /process/doc-preview/file/**
    - /process/doc-preview/callback
    # 契约锁签约完成后回调地址 需要取消token验证
    - /process/doc-signature/signature/callback
    - /sso/**
    - /setting/watermarkRule/preview
  # 用户放行
  permit-all:
    - /refreshToken
# 重复提交
repeat-submit:
  # 全局间隔时间(毫秒)
  interval: 5000

# MyBatisPlus配置
# https://baomidou.com/config/
mybatis-plus:
  # 不支持多包, 如有需要可在注解配置 或 提升扫包等级
  # 例如 com.**.**.mapper
  mapperPackage: com.rzdata.**.mapper
  # 对应的 XML 文件位置
  mapperLocations: classpath*:mapper/**/*Mapper.xml
  # 实体扫描，多个package用逗号或者分号分隔
  typeAliasesPackage: com.rzdata.**.domain
  # 启动时是否检查 MyBatis XML 文件的存在，默认不检查
  checkConfigLocation: false
  # 通过该属性可指定 MyBatis 的执行器，MyBatis 的执行器总共有三种：
  # SIMPLE：每个语句创建新的预处理器 REUSE：会复用预处理器 BATCH：批量执行所有的更新
  executorType: SIMPLE
  configuration:
    # 自动驼峰命名规则（camel case）映射
    mapUnderscoreToCamelCase: true
    # 当设置为 true 的时候，懒加载的对象可能被任何懒属性全部加载，否则，每个属性都按需加载。需要和 lazyLoadingEnabled 一起使用。
    aggressiveLazyLoading: true
    # MyBatis 自动映射策略
    # NONE：不启用 PARTIAL：只对非嵌套 resultMap 自动映射 FULL：对所有 resultMap 自动映射
    autoMappingBehavior: PARTIAL
    # MyBatis 自动映射时未知列或未知属性处理策
    # NONE：不做处理 WARNING：打印相关警告 FAILING：抛出异常和详细信息
    autoMappingUnknownColumnBehavior: NONE
    # Mybatis一级缓存，默认为 SESSION
    # SESSION session级别缓存 STATEMENT 关闭一级缓存
    localCacheScope: SESSION
    # 开启Mybatis二级缓存，默认为 true
    cacheEnabled: false
    # 更详细的日志输出 会有性能损耗 org.apache.ibatis.logging.stdout.StdOutImpl
    # 关闭日志记录 (可单纯使用 p6spy 分析) org.apache.ibatis.logging.nologging.NoLoggingImpl
    # 默认日志输出 org.apache.ibatis.logging.slf4j.Slf4jImpl
#    logImpl: org.apache.ibatis.logging.nologging.NoLoggingImpl
    logImpl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    sql-parser-cache: true
    # 是否打印 Logo banner
    banner: true
    # 是否初始化 SqlRunner
    enableSqlRunner: false
    dbConfig:
      # 主键类型
      # AUTO 自增 NONE 空 INPUT 用户输入 ASSIGN_ID 雪花 ASSIGN_UUID 唯一 UUID
      idType: AUTO
      # 表名是否使用驼峰转下划线命名,只对表名生效
      tableUnderline: true
      # 大写命名,对表名和字段名均生效
      capitalMode: false
      # 逻辑已删除值
      logicDeleteValue: 2
      # 逻辑未删除值
      logicNotDeleteValue: 0
      # 字段验证策略之 insert,在 insert 的时候的字段验证策略
      # IGNORED 忽略 NOT_NULL 非NULL NOT_EMPTY 非空 DEFAULT 默认 NEVER 不加入 SQL
      insertStrategy: NOT_NULL
      # 字段验证策略之 update,在 update 的时候的字段验证策略
      updateStrategy: NOT_NULL
      # 字段验证策略之 select,在 select 的时候的字段验证策略既 wrapper 根据内部 entity 生成的 where 条件
      where-strategy: NOT_NULL

# Swagger配置
swagger:
  # 是否开启swagger
  enabled: true
  # 请求前缀
  pathMapping: /
  # 标题
  title: '体系文件管理系统_接口文档'
  # 版本
  version: '版本号: ${ruoyi-vue-plus.version}'
  groups:
    - name: 1.系统模块
      basePackage: com.rzdata.system
    - name: 2.文件设置
      basePackage: com.rzdata.setting
    - name: 3.流程服务
      basePackage: com.rzdata.process

knife4j:
  # 是否开启Knife4j增强模式
  enable: true
  # 是否开启生产环境保护策略
  production: @knife4j.production@
  # 前端Ui的个性化配置属性
  setting:
    # 默认语言
    language: zh-CN
    # 是否显示Footer
    enableFooter: false
    # 是否开启动态参数调试功能
    enableDynamicParameter: true
    # 是否在每个Debug调试栏后显示刷新变量按钮
    enableReloadCacheParameter: true

# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice,/system/notifyTemplate/update,/system/notifyTemplate/add
  # 匹配链接
  urlPatterns: /system/*,/monitor/*,/tool/*

# 全局线程池相关配置
thread-pool:
  # 是否开启线程池
  enabled: false
  # 核心线程池大小
  corePoolSize: 8
  # 最大可创建的线程数
  maxPoolSize: 16
  # 队列最大长度
  queueCapacity: 128
  # 线程池维护线程所允许的空闲时间
  keepAliveSeconds: 300
  # 线程池对拒绝任务(无线程可用)的处理策略
  # CALLER_RUNS_POLICY 调用方执行
  # DISCARD_OLDEST_POLICY 放弃最旧的
  # DISCARD_POLICY 丢弃
  # ABORT_POLICY 中止
  rejectedExecutionHandler: CALLER_RUNS_POLICY

onlyoffice:
  document-server:
    # onlyOffice 安装的电脑的地址,一定要注意 本机和onlyOffice服务所在电脑一定要互通
    #host: 'http://*************:2080'
    #host: 'http://************:2080'
    host: 'https://office.rzdata.net'
    api:
      # 前端需要引入onlyOffice的js组件 才能实现在线预览
      js: '%s/web-apps/apps/api/documents/api.js'

mail:
# 邮件服务器的SMTP地址，可选，默认为smtp.<发件人邮箱后缀> smtp.exmail.qq.com
  host: smtp.qq.com
  # 邮件服务器的SMTP端口，可选，默认25
  port: 465
  # 发件人（必须正确，否则发送失败）
  from: <EMAIL>
  # 用户名，默认为发件人邮箱前缀
  user: <EMAIL>
  # 密码（注意，某些邮箱需要为SMTP服务单独设置授权码，详情查看相关帮助）
  pass: whklvlbsmcxabhai
  # 使用SSL安全连接
  sslEnable: true
sap:
  rfc:
    enabled: false
    jco:
      client:
        host: **********
        user: RFC
        password: 1234567890Qwert@1
        language: zh
        client: 300
        sysnr: 00
      destination:
        name: SAP
file:
  encrypt:
   username: DMS
   password: Mehow.2024@ipg
   host: http://127.0.0.1:8086
oa:
  todo:
    url: http://oadevapp.mehowcy.com:8080/
    enabled: false
  auth:
    #url: http://***********:8882
    url: http://localhost:8080/dev-dms-admin/sso/mock
    #accessUrl: http://************:8080/dms-admin/sso/oa/accessTodo
    accessUrl: http://localhost:8080/dev-dms-admin/sso/oa/accessTodo
    client: APP021
    secret: 5f743b96-7614-489b-864d-81d3b540
    #frontUrl: http://************/dms-front/#/oassologin
    frontUrl: http://localhost/dms-front/#/oassologin


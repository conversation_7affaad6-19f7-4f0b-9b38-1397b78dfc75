<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rzdata.process.mapper.ApplyRelationMapper">

    <resultMap type="com.rzdata.process.domain.ApplyRelation" id="ApplyRelationResult">
        <result property="id" column="id"/>
        <result property="applyId" column="apply_id"/>
        <result property="applyType" column="apply_type"/>
        <result property="relationApplyId" column="relation_apply_id"/>
        <result property="relationApplyType" column="relation_apply_type"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="isDeleted" column="is_deleted"/>
    </resultMap>


</mapper>

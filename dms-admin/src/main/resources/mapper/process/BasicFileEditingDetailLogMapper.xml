<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rzdata.process.mapper.BasicFileEditingDetailLogMapper">

    <resultMap type="com.rzdata.process.domain.BasicFileEditingDetailLog" id="BasicFileEditingDetailLogResult">
        <result property="id" column="id"/>
        <result property="fileId" column="file_id"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="status" column="status"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="bizId" column="biz_id"/>
        <result property="protoFileId" column="proto_file_id"/>
    </resultMap>

    <select id="selectLastEditLog" resultMap="BasicFileEditingDetailLogResult">
        SELECT * FROM basic_file_editing_detail_log
        WHERE proto_file_id = #{protoFileId}
        ORDER BY create_time DESC LIMIT 1
    </select>

    <update id="releaseFileEditLock" parameterType="com.rzdata.process.domain.BasicFileEditingDetailLog">
       update basic_file_editing_detail_log
         set status = #{bo.status} ,
             update_time=#{bo.updateTime} ,
             update_by=#{bo.updateBy}
       where proto_file_id = #{bo.protoFileId}
    </update>
</mapper>

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rzdata.process.mapper.BasicFileMapper">

    <resultMap type="com.rzdata.process.domain.BasicFile" id="BasicFileResult">
        <result property="id" column="id"/>
        <result property="fileName" column="file_name"/>
        <result property="fileSize" column="file_size"/>
        <result property="fileType" column="file_type"/>
        <result property="createTime" column="create_time"/>
        <result property="fileClass" column="file_class"/>
        <result property="businessId" column="business_id"/>
        <result property="filePath" column="file_path"/>
        <result property="externalFileId" column="external_file_id"/>
        <result property="externalFilePath" column="external_file_path"/>
        <result property="externalRev" column="external_rev"/>
        <result property="status" column="status"/>
    </resultMap>

    <select id="selectFileByDocId" resultType="map">
        select b.id, b.file_name
        from doc_link_log l
                 LEFT JOIN basic_file b ON l.link_code = b.id
                 LEFT JOIN doc_version v ON l.version_id = v.id
        where v.`status` = 1
          and b.id is not null
          and l.doc_id = #{docId}
    </select>

    <select id="selectBasicFileList" resultMap="BasicFileResult">
        SELECT
            *
        FROM
            basic_file
        WHERE
            `status` = 0
          AND external_file_id IS NOT NULL
          AND external_file_id != ''
    </select>
</mapper>

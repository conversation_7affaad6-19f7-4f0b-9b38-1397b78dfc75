<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rzdata.process.mapper.BorrowApplyMapper">

    <resultMap type="com.rzdata.process.domain.vo.BorrowApplyVo" id="BorrowApplyResult">
        <result property="id" column="id"/>
        <result property="applyTitle" column="apply_title"/>
        <result property="docId" column="doc_id"/>
        <result property="docName" column="doc_name"/>
        <result property="deptId" column="dept_id"/>
        <result property="startTime" column="start_time"/>
        <result property="endTime" column="end_time"/>
        <result property="userName" column="user_name"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="applyReason" column="apply_reason"/>
        <result property="status" column="status"/>
        <result property="borrowUser" column="borrow_user"/>
        <result property="borrowDeptName" column="dept_name"/>
        <result property="borrowId" column="borrowId"/>
        <result property="dayTime" column="day_time"/>
        <result property="docClass" column="doc_class"/>
        <result property="versionValue" column="version_value"/>
        <result property="versionId" column="version_id"/>
        <result property="makeDeptName" column="make_dept_name"/>
    </resultMap>

    <select id="selectBorrowApplyList" resultType="map">
        SELECT
        m.id,
        m.apply_title as applyTitle,
        <!--m.doc_ids as docIds,-->
        m.dept_id as deptId,
        m.start_time as startTime,
        m.end_time as endTime,
        m.user_name as userName,
        m.create_time as createTime,
        w.update_time as updateTime,
        m.apply_reason as applyReason,
        m.status,
        w.apply_class AS applyClass,
        w.apply_status AS applyStatus,
        w.doc_name AS docName,
        w.doc_id AS docId,
        w.doc_class AS docClass,
        w.version_id AS versionId,
        w.version_value AS versionValue,
        w.sender AS sender,
        d.dept_name AS deptName
        FROM
        doc_borrow_apply m
        LEFT JOIN doc_workflow_apply_log w ON m.id = w.id
        LEFT JOIN sys_dept d ON m.dept_id = d.dept_id
        <where>
            <!--发起人-->
            <if test="bo.startUserId != null and bo.startUserId != ''">
                and w.sender = #{bo.startUserId}
            </if>
            <if test="bo.procDefKey != null and bo.procDefKey != ''">
                and w.proc_def_key like  concat(#{bo.procDefKey}, '%')
            </if>
            <if test="bo.applyTitle != null and bo.applyTitle != ''">
                and m.apply_title like concat('%', #{bo.applyTitle}, '%')
            </if>
            <if test="bo.deptId != null">
                and m.dept_id = #{bo.deptId}
            </if>
            <if test="bo.userName != null and bo.userName != ''">
                and m.user_name like concat('%', #{bo.userName}, '%')
            </if>
            <if test="bo.applyReason != null and bo.applyReason != ''">
                and m.apply_reason like concat('%', #{bo.applyReason}, '%')
            </if>
            <if test="bo.applyStartTime != null">
                and m.start_time &gt;= #{bo.applyStartTime}
            </if>
            <if test="bo.applyEndTime != null">
                and m.end_time &lt;= #{bo.applyStartTime}
            </if>
            <if test="bo.searchValue != null and bo.searchValue != ''">
                and (
                m.apply_reason like concat('%', #{bo.searchValue}, '%')
                OR m.apply_title like concat('%', #{bo.searchValue}, '%')
                OR m.user_name like concat('%', #{bo.searchValue}, '%')
                OR w.doc_name like concat('%', #{bo.searchValue}, '%')
                )
            </if>
            <if test="bo.docName != null and bo.docName != ''">
                and w.doc_name like concat('%', #{bo.docName}, '%')
            </if>
            <if test="ids != null and ids.size > 0">
                and m.id in
                <foreach collection="ids" item="id" index="index" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
        ORDER BY m.create_time desc
    </select>
    <select id="selectBorrowApplyList4New" resultType="map">
        SELECT
        m.id,
        m.apply_title as applyTitle,

        m.dept_id as deptId,
        m.start_time as startTime,
        m.end_time as endTime,
        m.user_name as userName,
        m.create_time as createTime,
        m.update_time as updateTime,
        m.apply_reason as applyReason,
        m.status,
        w.apply_class AS applyClass,
        w.apply_status AS applyStatus,
        w.doc_name AS docName,
        w.doc_id AS docId,
        w.doc_class AS docClass,
        w.version_id AS versionId,
        w.version_value AS versionValue,
        w.sender AS sender,
        d.dept_name AS deptName
        FROM
        doc_borrow_apply m
        LEFT JOIN doc_workflow_apply_log w ON m.id = w.id
        LEFT JOIN sys_dept d ON m.dept_id = d.dept_id
        <where>
            <!--发起人-->
            <if test="bo.startUserId != null and bo.startUserId != ''">
                and w.sender = #{bo.startUserId}
            </if>
            <if test="bo.procDefKey != null and bo.procDefKey != ''">
                and w.proc_def_key like  concat(#{bo.procDefKey}, '%')
            </if>
            <if test="bo.applyTitle != null and bo.applyTitle != ''">
                and m.apply_title like concat('%', #{bo.applyTitle}, '%')
            </if>
            <if test="bo.deptId != null">
                and m.dept_id = #{bo.deptId}
            </if>
            <if test="bo.userName != null and bo.userName != ''">
                and m.user_name like concat('%', #{bo.userName}, '%')
            </if>
            <if test="bo.applyReason != null and bo.applyReason != ''">
                and m.apply_reason like concat('%', #{bo.applyReason}, '%')
            </if>
            <if test="bo.applyStartTime != null">
                and m.start_time &gt;= #{bo.applyStartTime}
            </if>
            <if test="bo.applyEndTime != null">
                and m.end_time &lt;= #{bo.applyStartTime}
            </if>
            <if test="bo.searchValue != null and bo.searchValue != ''">
                and (
                m.apply_reason like concat('%', #{bo.searchValue}, '%')
                OR m.apply_title like concat('%', #{bo.searchValue}, '%')
                OR m.user_name like concat('%', #{bo.searchValue}, '%')
                OR w.doc_name like concat('%', #{bo.searchValue}, '%')
                )
            </if>
            <if test="bo.docName != null and bo.docName != ''">
                and w.doc_name like concat('%', #{bo.docName}, '%')
            </if>
            <if test="ids != null and ids.size > 0">
                and m.id in
                <foreach collection="ids" item="id" index="index" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
        ORDER BY m.create_time desc
    </select>


    <select id="selectBorrowList" resultMap="BorrowApplyResult">
        SELECT b.*,
               u.id AS borrowId
        FROM doc_borrow_apply b
                 LEFT JOIN doc_borrow_apply_user u ON b.id = u.apply_id
        WHERE b.status = 1
    </select>

</mapper>

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rzdata.process.mapper.BorrowApplyItemMapper">

    <resultMap type="com.rzdata.process.domain.BorrowApplyItem" id="BorrowApplyItemResult">
        <result property="id" column="id"/>
        <result property="applyId" column="apply_id"/>
        <result property="borrowUser" column="borrow_user"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="isDeleted" column="is_deleted"/>
    </resultMap>

    <select id="getMyBorrowItemList" parameterType="BorrowApplyItemBo" resultType="BorrowApplyItemVo">
        select * from (
        SELECT dbai.*, if(dbai.is_forever='N'and SYSDATE()>dbai.end_time,1,0) borrowStatus,dv.`status`,dv.file_id
           FROM doc_borrow_apply_item dbai
                    LEFT JOIN doc_version dv on  dbai.version_id = dv.id
           where start_time is not null) b
        <where>
           <if test="bo.borrowStatus != null and bo.borrowStatus != ''">
             and b.borrowStatus = #{bo.borrowStatus}
           </if>
           <if test="bo.status != null and bo.status != ''">
             and b.`status` = #{bo.status}
           </if>
           <if test="bo.borrowUserName != null and bo.borrowUserName != ''">
             and b.`borrow_user_name` = #{bo.borrowUserName}
           </if>
           <if test="bo.searchValue != null and bo.searchValue != ''">
             and (b.doc_id like concat('%',#{bo.searchValue},'%')
             or b.doc_name like concat('%',#{bo.searchValue},'%')
             or b.version_value like concat('%',#{bo.searchValue},'%') )
           </if>
        </where>
    </select>

    <select id="isValidBorrowByVersionId" parameterType="string" resultType="int">
        SELECT sum(if(dbai.is_forever='N'and SYSDATE()>dbai.end_time,0,1))
        FROM doc_borrow_apply_item dbai
        where start_time is not null and version_id = #{versionId} and borrow_user_name = #{userName}
    </select>
</mapper>

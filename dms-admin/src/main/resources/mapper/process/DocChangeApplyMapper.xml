<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rzdata.process.mapper.DocChangeApplyMapper">

    <resultMap type="com.rzdata.process.domain.vo.DocChangeApplyVo" id="DocChangeApplyResult">
        <result property="id" column="id"/>
        <result property="applyTitle" column="apply_title"/>
        <result property="docId" column="doc_id"/>
        <result property="changeType" column="change_type"/>
        <result property="docClass" column="doc_class"/>
        <result property="docName" column="doc_name"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="deptId" column="dept_id"/>
        <result property="userName" column="user_name"/>
        <result property="changeReason" column="change_reason"/>
        <result property="content" column="content"/>
        <result property="remark" column="remark"/>
        <result property="status" column="status"/>
        <result property="versionValue" column="version_value"/>
        <result property="versionId" column="version_id"/>
        <result property="editUserName" column="edit_user_name"/>
        <result property="editDeptId" column="edit_dept_id"/>
        <result property="appendixs" column="appendixs"/>
        <result property="changeFactor" column="change_factor"/>
        <result property="standDeptName" column="stand_dept_name"/>
        <result property="standUserName" column="stand_user_name"/>
        <result property="applyUserName" column="applyUserName"/>
    </resultMap>

    <select id="selectChangeApplyList" resultType="map">
        SELECT
        c.id,
        c.apply_title as applyTitle,
        c.doc_id as docId,
        c.change_type as changeType,
        c.doc_class as docClass,
        c.doc_name as docName,
        c.create_time as createTime,
        w.update_time as updateTime,
        c.dept_id as deptId,
        c.user_name as userName,
        c.change_reason as changeReason,
        c.content,
        c.remark,
        c.status,
        w.apply_class AS applyClass,
        w.apply_status AS applyStatus,
        w.doc_name AS docName,
        w.doc_id AS docId,
        w.doc_class AS docClass,
        w.version_id AS versionId,
        w.version_value AS versionValue,
        w.sender AS sender,
        d.dept_name AS deptName
        FROM
        doc_change_apply c
        LEFT JOIN doc_workflow_apply_log w ON c.id = w.id
        LEFT JOIN sys_dept d ON c.dept_id = d.dept_id
        <where>
            <if test="bo.procDefKey != null and bo.procDefKey != ''">
                and w.proc_def_key like  concat(#{bo.procDefKey}, '%')
            </if>

            <!--发起人-->
            <if test="bo.startUserId != null and bo.startUserId != ''">
                and w.sender = #{bo.startUserId}
            </if>

            <if test="bo.applyTitle != null and bo.applyTitle != ''">
                and c.apply_title like concat('%', #{bo.searchValue}, '%')
            </if>
            <if test="bo.docId != null and bo.docId != ''">
                and c.doc_id like concat('%', #{bo.docId}, '%')
            </if>
            <if test="bo.docClass != null and bo.docClass != ''">
                and c.doc_class like concat('%', #{bo.docClass}, '%')
            </if>
            <if test="bo.changeType != null and bo.changeType != ''">
                and c.change_type = #{bo.changeType}
            </if>
            <if test="bo.version != null and bo.version != ''">
                and c.version = #{bo.version}
            </if>
            <if test="bo.deptId != null">
                and c.dept_id = #{bo.deptId}
            </if>
            <if test="bo.userName != null and bo.userName != ''">
                and c.user_name like concat('%', #{bo.userName}, '%')
            </if>
            <if test="bo.changeReason != null and bo.changeReason != ''">
                and c.change_reason like concat('%', #{bo.changeReason}, '%')
            </if>
            <if test="bo.content != null and bo.content != ''">
                and c.content like concat('%', #{bo.content}, '%')
            </if>
            <if test="bo.remark != null and bo.remark != ''">
                and c.remark like concat('%', #{bo.remark}, '%')
            </if>
            <if test="bo.applyStartTime != null">
                and c.create_time &gt;= #{bo.applyStartTime}
            </if>
            <if test="bo.applyEndTime != null">
                and c.create_time &lt;= #{bo.applyStartTime}
            </if>
            <if test="bo.searchValue != null and bo.searchValue != ''">
                and (
                c.apply_title like concat('%', #{bo.searchValue}, '%')
                OR c.doc_name like concat('%', #{bo.searchValue}, '%')
                OR c.user_name like concat('%', #{bo.searchValue}, '%')
                OR w.doc_name like concat('%', #{bo.searchValue}, '%')
                )
            </if>
            <if test="bo.docName != null and bo.docName != ''">
                and w.doc_name like concat('%', #{bo.docName}, '%')
            </if>
            <if test="ids != null and ids.size > 0">
                and c.id in
                <foreach collection="ids" item="id" index="index" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
        ORDER BY c.create_time DESC
    </select>
    <select id="selectChangeApplyList4New" resultType="map">
        SELECT
        c.id,
        c.apply_title as applyTitle,
        c.doc_id as docId,
        c.change_type as changeType,
        c.doc_class as docClass,
        c.doc_name as docName,
        c.create_time as createTime,
        c.update_time as updateTime,
        c.dept_id as deptId,
        c.user_name as userName,
        c.change_reason as changeReason,
        c.content,
        c.remark,
        c.status,
        w.apply_class AS applyClass,
        w.apply_status AS applyStatus,
        w.doc_name AS docName,
        w.doc_id AS docId,
        w.doc_class AS docClass,
        w.version_id AS versionId,
        w.version_value AS versionValue,
        w.sender AS sender,
        d.dept_name AS deptName
        FROM
        doc_change_apply c
        LEFT JOIN doc_workflow_apply_log w ON c.id = w.id
        LEFT JOIN sys_dept d ON c.dept_id = d.dept_id
        <where>
            <if test="bo.procDefKey != null and bo.procDefKey != ''">
                and w.proc_def_key like  concat(#{bo.procDefKey}, '%')
            </if>

            <!--发起人-->
            <if test="bo.startUserId != null and bo.startUserId != ''">
                and w.sender = #{bo.startUserId}
            </if>

            <if test="bo.applyTitle != null and bo.applyTitle != ''">
                and c.apply_title like concat('%', #{bo.searchValue}, '%')
            </if>
            <if test="bo.docId != null and bo.docId != ''">
                and c.doc_id like concat('%', #{bo.docId}, '%')
            </if>
            <if test="bo.docClass != null and bo.docClass != ''">
                and c.doc_class like concat('%', #{bo.docClass}, '%')
            </if>
            <if test="bo.changeType != null and bo.changeType != ''">
                and c.change_type = #{bo.changeType}
            </if>
            <if test="bo.version != null and bo.version != ''">
                and c.version = #{bo.version}
            </if>
            <if test="bo.deptId != null">
                and c.dept_id = #{bo.deptId}
            </if>
            <if test="bo.userName != null and bo.userName != ''">
                and c.user_name like concat('%', #{bo.userName}, '%')
            </if>
            <if test="bo.changeReason != null and bo.changeReason != ''">
                and c.change_reason like concat('%', #{bo.changeReason}, '%')
            </if>
            <if test="bo.content != null and bo.content != ''">
                and c.content like concat('%', #{bo.content}, '%')
            </if>
            <if test="bo.remark != null and bo.remark != ''">
                and c.remark like concat('%', #{bo.remark}, '%')
            </if>
            <if test="bo.applyStartTime != null">
                and c.create_time &gt;= #{bo.applyStartTime}
            </if>
            <if test="bo.applyEndTime != null">
                and c.create_time &lt;= #{bo.applyStartTime}
            </if>
            <if test="bo.searchValue != null and bo.searchValue != ''">
                and (
                c.apply_title like concat('%', #{bo.searchValue}, '%')
                OR c.doc_name like concat('%', #{bo.searchValue}, '%')
                OR c.user_name like concat('%', #{bo.searchValue}, '%')
                OR w.doc_name like concat('%', #{bo.searchValue}, '%')
                )
            </if>
            <if test="bo.docName != null and bo.docName != ''">
                and w.doc_name like concat('%', #{bo.docName}, '%')
            </if>
            <if test="ids != null and ids.size > 0">
                and c.id in
                <foreach collection="ids" item="id" index="index" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
        ORDER BY c.create_time DESC
    </select>

    <select id="selectDetailById" resultMap="DocChangeApplyResult">
        SELECT
            c.*,
            t.dept_name as stand_dept_name,
            u.nick_name as stand_user_name,
            uu.nick_name as applyUserName
        FROM
            doc_change_apply c
                LEFT JOIN sys_dept t ON c.edit_dept_id = t.dept_id
                LEFT JOIN sys_user u ON c.edit_user_name = u.user_name
                LEFT JOIN sys_user uu ON c.user_name = uu.user_name
        WHERE c.id = #{id}
    </select>

</mapper>

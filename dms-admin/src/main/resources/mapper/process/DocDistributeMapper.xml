<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rzdata.process.mapper.DocDistributeMapper">

    <resultMap type="com.rzdata.process.domain.DocDistribute" id="DocDistributeResult">
        <result property="id" column="id"/>
        <result property="tenantId" column="tenant_id"/>
        <result property="versionId" column="version_id"/>
        <result property="code" column="code"/>
        <result property="docId" column="doc_id"/>
        <result property="docClass" column="doc_class"/>
        <result property="docName" column="doc_name"/>
        <result property="nums" column="nums"/>
        <result property="printNums" column="print_nums"/>
        <result property="printFlag" column="print_flag"/>
        <result property="receiveUserName" column="receive_user_name"/>
        <result property="receiveNickName" column="receive_nick_name"/>
        <result property="receiveUserDeptId" column="receive_user_dept_id"/>
        <result property="receiveUserDept" column="receive_user_dept"/>
        <result property="receiveTime" column="receive_time"/>
        <result property="lostTime" column="lost_time"/>
        <result property="recoveryTime" column="recovery_time"/>
        <result property="receive" column="receive"/>
        <result property="lost" column="lost"/>
        <result property="recovery" column="recovery"/>
        <result property="status" column="status"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <select id="getMaxCodeByVersionId" parameterType="String" resultType="int">
        select IFNULL(MAX(`code`),0) from doc_distribute WHERE version_id = #{versionId}
    </select>

    <select id="queryPageDoc" resultType="com.rzdata.process.domain.vo.DocDistributeVo" parameterType="com.rzdata.process.domain.bo.DocDistributeBo">
        select dd.doc_class,bdc.class_name,dd.doc_name,dd.doc_id,dv.version_value,dv.release_time,dd.version_id from doc_distribute dd
        LEFT JOIN doc_version dv on dd.version_id = dv.id
        LEFT JOIN basic_doc_class bdc on dd.doc_class = bdc.id
        <where>
            <if test="bo.docId != null and bo.docId != ''">
               and dd.doc_id like concat('%', #{bo.docId}, '%')
            </if>
            <if test="bo.docName != null and bo.docName != ''">
               and dd.doc_name like concat('%', #{bo.docName}, '%')
            </if>
            <if test="bo.type != null and bo.type != ''">
                and dd.type =  #{bo.type}
            </if>
            <if test="bo.classType != null and bo.classType != ''">
                and dv.class_type =  #{bo.classType}
            </if>
            <choose>
                <when test="bo != null and bo.ids != null and bo.ids.size() > 0">
                    and dd.id in
                    <foreach item="id" collection="bo.ids" open="(" separator="," close=")">
                        (#{id})
                    </foreach>
                    <if test="bo.recordFilePrintPermission != null and bo.recordFilePrintPermission != '' and bo.recordFilePrintPermission == 'Y'.toString() ">
                        OR ( bdc.class_type = 'NOTE' AND dd.receive_user_name = #{bo.receiveUserName})
                    </if>
                </when>
                <otherwise>
                    <if test="bo.recordFilePrintPermission != null and bo.recordFilePrintPermission != '' and bo.recordFilePrintPermission == 'Y'.toString() ">
                        AND ( bdc.class_type = 'NOTE' AND dd.receive_user_name = #{bo.receiveUserName})
                    </if>
                </otherwise>
            </choose>

        </where>
        GROUP BY dd.version_id
    </select>

    <select id="getPrintGroupList" resultType="DocDistributeVo" parameterType="string">
        SELECT receive_user_dept_id,receive_user_dept,receive_user_name,receive_nick_name,print_paper_type,count(*) nums FROM `doc_distribute`
        where
            version_id = #{versionId}
          and type = 'print'
        group by receive_user_dept_id,receive_user_name,print_paper_type
    </select>

    <select id="postQueryList" resultType="com.rzdata.process.domain.vo.DocDistributeVo">
        SELECT *,
            (SELECT COUNT(*) FROM doc_print_file_detail t1 LEFT JOIN basic_file_pdf t2 ON t1.biz_id = t2.id LEFT JOIN doc_distribute t3 ON t3.id = t2.biz_id WHERE t3.id = t4.id) as print_total
        FROM doc_distribute t4
        <where>
            <if test="bo.versionId != '' and bo.versionId != null">
                and t4.version_id = #{bo.versionId}
            </if>
            <if test="bo.type != '' and bo.type != null">
                and t4.type = #{bo.type}
            </if>
            <if test="bo.deptIds != null and bo.deptIds.size > 0">
                and t4.receive_user_dept_id in
                <foreach collection="bo.deptIds" item="deptId" index="index" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
            </if>
            <if test="bo.startTime != null and bo.startTime != ''">
                and t4.receive_time >= #{bo.startTime}
            </if>
            <if test="bo.endTime != null and bo.endTime != ''">
                and t4.receive_time &lt;= #{bo.endTime}
            </if>
        </where>
         ORDER BY t4.CODE ASC
    </select>


    <!--查询阅读培训列表-->
    <select id="selectReadTranList" resultType="com.rzdata.process.domain.vo.DocDistributeVo" parameterType="com.rzdata.process.domain.bo.DocDistributeBo">
        SELECT  * FROM (
        select dv.doc_name,dv.doc_id,t.doc_class,dv.version_value,dv.dept_id,dv.start_date,dv.status,t.version_id,
        sum(t.num) num,sum(t.total) total from
        doc_version dv RIGHT JOIN
        (select IFNULL(sum(t2.sum),0) num,sum(t1.sum) total,t1.version_id,t1.doc_class
        from
        (select count(*) sum,dd.version_id,dd.doc_class
        from doc_distribute dd
        left join sys_dept sd on (dd.receive_user_dept_id = sd.dept_id or find_in_set(dd.receive_user_dept_id,sd.ancestors))
        left join sys_user su on su.dept_id = sd.dept_id
        where
        sd.`STATUS` = '0' AND sd.del_flag = '0'
        and su.`status`='0' and su.`del_flag` = '0' AND su.user_type = '00'
        and type = 'dept'
        GROUP BY dd.version_id) t1
        LEFT JOIN
        (select count(*) sum,dd.version_id
        from doc_distribute dd
        left join sys_dept sd on (dd.receive_user_dept_id = sd.dept_id or find_in_set(dd.receive_user_dept_id,sd.ancestors))
        left join doc_distribute_read_records ddrr on (ddrr.version_id = dd.version_id and ddrr.dept_id=sd.dept_id)
        where
        sd.`STATUS` = '0' AND sd.del_flag = '0'
        and type = 'dept'
        and ddrr.id is not null
        GROUP BY dd.version_id) t2 on t1.version_id = t2.version_id
        GROUP BY t1.version_id
        UNION all
        select count(ddrr.id) num,count(*) total,dd1.version_id,dd1.doc_class from doc_distribute dd1
        left join doc_distribute_read_records ddrr on (dd1.receive_user_name = ddrr.user_name and dd1.version_id = ddrr.version_id)
        where dd1.type = 'person'
        and dd1.receive_user_dept_id not in (
        select sd.dept_id
        from doc_distribute dd
        left join
        sys_dept sd on (dd.receive_user_dept_id = sd.dept_id or find_in_set(dd.receive_user_dept_id,sd.ancestors))
        where
        sd.`STATUS` = '0' AND sd.del_flag = '0'
        and dd.version_id = dd1.version_id
        and dd.type = 'dept')
        GROUP BY dd1.version_id) t
        on dv.id = t.version_id
        GROUP BY t.version_id) readDoc
        <where>
            <if test="bo.docClassList != null and bo.docClassList.size > 0">
                and readDoc.doc_class in
                <foreach collection="bo.docClassList" item="id" index="index" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="bo.status != null and bo.status != ''">
                and readDoc.status = #{bo.status}
            </if>
            <if test="bo.docClass != null and bo.docClass != ''">
                and readDoc.doc_class = #{bo.docClass}
            </if>
            <if test="bo.tranStatus != null and bo.tranStatus != ''">
                <choose>
                    <when test="bo.tranStatus == 1">
                        AND readDoc.total = readDoc.num
                    </when>
                    <when test="bo.tranStatus == 0">
                        AND readDoc.total > readDoc.num
                    </when>
                    <otherwise>
                        <!-- 这里可以添加其他条件的处理，或者什么都不做 -->
                    </otherwise>
                </choose>
            </if>
            <if test="bo.queryName != '' and bo.queryName != null">
                and (readDoc.doc_name like concat('%', #{bo.queryName}, '%') or readDoc.doc_id like concat('%', #{bo.queryName}, '%')  or readDoc.version_value like concat('%', #{bo.queryName}, '%') )
            </if>
        </where>
        ORDER BY start_date desc
    </select>

    <select id="trainDetailStatusList" resultType="com.rzdata.process.domain.vo.DocDistributeVo" parameterType="com.rzdata.process.domain.bo.DocDistributeBo">
        select tdl.status,count(*) total from (
        <include refid="getTrainDetailList"/>
        ) tdl
        <where>
            <if test="bo.deptId != '' and bo.deptId != null">
                and (tdl.receive_user_dept_id = #{bo.deptId} or find_in_set(#{bo.deptId},tdl.ancestors))
            </if>
            <if test="bo.status != '' and bo.status != null">
                and tdl.`status` = #{bo.status}
            </if>
        </where>
        group by tdl.status
    </select>

    <select id="trainDetailList" resultType="com.rzdata.process.domain.vo.DocDistributeVo" parameterType="com.rzdata.process.domain.bo.DocDistributeBo">
        select * from (
        <include refid="getTrainDetailList"/>
        ) tdl
        <where>
            <if test="bo.deptId != '' and bo.deptId != null">
                and (tdl.receive_user_dept_id = #{bo.deptId} or find_in_set(#{bo.deptId},tdl.ancestors))
            </if>
            <if test="bo.status != '' and bo.status != null">
                and tdl.`status` = #{bo.status}
            </if>
        </where>
    </select>
    <sql id="getTrainDetailList">
        select t.*,IFNULL(ddrr.`status`,'N') status,times,sd.ancestors,sd.dept_name receive_user_dept from
            doc_distribute_read_records ddrr
                RIGHT join
            (select dd.version_id,su.dept_id receive_user_dept_id,su.user_name receive_user_name,su.nick_name receive_nick_name
             from doc_distribute dd
                      left join sys_dept sd on (dd.receive_user_dept_id = sd.dept_id or find_in_set(dd.receive_user_dept_id,sd.ancestors))
                      left join sys_user su on su.dept_id = sd.dept_id
             where
                 sd.`STATUS` = '0' AND sd.del_flag = '0'
               and su.`status`='0' and su.`del_flag` = '0' AND su.user_type = '00'
               and type = 'dept'
               and dd.version_id = #{bo.versionId}
             UNION all
             select dd1.version_id,dd1.receive_user_dept_id,dd1.receive_user_name,dd1.receive_nick_name
             from doc_distribute dd1
             where dd1.type = 'person'
               and dd1.version_id = #{bo.versionId}
               and dd1.receive_user_dept_id not in (
                 select sd.dept_id
                 from doc_distribute dd
                          left join
                      sys_dept sd on (dd.receive_user_dept_id = sd.dept_id or find_in_set(dd.receive_user_dept_id,sd.ancestors))
                 where
                     sd.`STATUS` = '0' AND sd.del_flag = '0'
                   and dd.version_id = dd1.version_id
                   and dd.type = 'dept')) t
            on (t.receive_user_name = ddrr.user_name and t.version_id = ddrr.version_id)
                LEFT JOIN sys_dept sd on t.receive_user_dept_id = sd.dept_id
    </sql>
</mapper>

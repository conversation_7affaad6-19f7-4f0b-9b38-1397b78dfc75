<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rzdata.process.mapper.DocDistributeStatMapper">

    <resultMap type="com.rzdata.process.domain.DocDistributeStat" id="DocDistributeStatResult">
        <result property="id" column="id"/>
        <result property="versionId" column="version_id"/>
        <result property="docClass" column="doc_class"/>
        <result property="readNum" column="read_num"/>
        <result property="total" column="total"/>
        <result property="deptTotal" column="dept_total"/>
        <result property="personTotal" column="person_total"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <select id="selectPageDistributeStatList" resultType="com.rzdata.process.domain.vo.DocDistributeStatVo" parameterType="com.rzdata.process.domain.bo.DocDistributeStatBo" >
        SELECT
            t2.doc_name,
            t2.doc_id,
            t1.doc_class,
            t2.version_value,
            t2.dept_id,
            t3.dept_name,
            t2.start_date,
            t2.status,
            t1.version_id,
            t1.read_num,
            t1.total,
            t1.dept_total,
            t1.person_total
        FROM
            doc_distribute_stat t1
        LEFT JOIN doc_version t2 ON t1.version_id = t2.id
        LEFT JOIN sys_dept t3 ON t2.dept_id = t3.dept_id
        where 1=1
        <if test="bo.queryName != null and bo.queryName != ''">
            AND (t2.doc_name like concat('%', #{bo.queryName}, '%') or t2.doc_id like concat('%', #{bo.queryName}, '%') or t2.version_value like concat('%', #{bo.queryName}, '%'))
        </if>
        <if test="bo.docClass != null and bo.docClass != ''">
            AND t1.doc_class like concat('%', #{bo.docClass}, '%')
        </if>
        <if test="bo.tranStatus != null and bo.tranStatus != ''">
            <choose>
                <when test="bo.tranStatus == 1">
                    AND t1.total = t1.read_num
                </when>
                <when test="bo.tranStatus == 0">
                    AND t1.total > t1.read_num
                </when>
                <otherwise>
                    <!-- 这里可以添加其他条件的处理，或者什么都不做 -->
                </otherwise>
            </choose>
        </if>
    </select>

    <select id="selectDeptUserCount" resultType="java.lang.Integer">
        WITH RECURSIVE DeptTree AS (
            SELECT dept_id FROM sys_dept WHERE dept_id = #{deptId} and status='0' and del_flag='0'
            UNION ALL
            SELECT sd.dept_id FROM sys_dept sd
                                       INNER JOIN DeptTree dt ON sd.parent_id = dt.dept_id where sd.status='0' and sd.del_flag='0'
        )

        SELECT count(su.user_id) as total FROM sys_user su
                                                   INNER JOIN DeptTree dt ON su.dept_id = dt.dept_id
        where su.status='0' and su.`del_flag` = '0' AND su.user_type = '00'
    </select>

    <select id="selectDeptUserReadCount" resultType="java.lang.Integer">
        WITH RECURSIVE DeptTree AS (
            SELECT dept_id FROM sys_dept WHERE dept_id = #{deptId} and status='0' and del_flag='0'
            UNION ALL
            SELECT sd.dept_id FROM sys_dept sd
                                       INNER JOIN DeptTree dt ON sd.parent_id = dt.dept_id where sd.status='0' and sd.del_flag='0'
        )
        SELECT
            count(t1.id) as read_num
        FROM
            doc_distribute_read_records t1
                LEFT JOIN sys_user t2 ON t1.user_id = t2.user_id
                INNER JOIN DeptTree dt ON t1.dept_id = dt.dept_id
        WHERE
            t2.STATUS = '0'
          AND t2.del_flag = '0'
          AND t2.user_type = '00'
          AND t1.version_id = #{versionId}
    </select>

    <select id="selectDeptReadRecords" resultType="com.rzdata.process.domain.vo.DocDistributeReadRecordsVo">
        SELECT
            t1.*
        FROM
            doc_distribute_read_records t1
                LEFT JOIN sys_user t2 ON t1.user_id = t2.user_id
                LEFT JOIN sys_dept t3 ON t1.dept_id = t3.dept_id
        WHERE
            t2.STATUS = '0'
          AND t2.del_flag = '0'
          AND t2.user_type = '00'
          AND t3.STATUS = '0'
          AND t3.del_flag = '0'
          AND t1.version_id = #{versionId}
    </select>

    <select id="selectDistributePersonAncestors" resultType="com.rzdata.process.domain.vo.DocDistributeStatReadVo">
        SELECT
            t1.receive_user_name,t1.receive_user_dept_id,t1.doc_class,t2.ancestors
        FROM
            doc_distribute t1 LEFT JOIN sys_dept t2 ON t1.receive_user_dept_id = t2.dept_id
        WHERE
            t1.type = 'person'
          AND t2.del_flag = '0'
          AND t2.STATUS = '0'
          AND t1.version_id = #{versionId}
    </select>

    <select id="selectDistributeByVersion" resultType="com.rzdata.process.domain.vo.DocDistributeStatReadVo">
        SELECT
            version_id
        FROM
            doc_distribute
        WHERE
            type IN ( 'dept', 'person' )
        GROUP BY
            version_id
    </select>


</mapper>

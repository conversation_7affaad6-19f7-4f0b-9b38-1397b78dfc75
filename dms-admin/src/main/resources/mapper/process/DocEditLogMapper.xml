<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rzdata.process.mapper.DocEditLogMapper">

    <resultMap type="com.rzdata.process.domain.DocEditLog" id="DocEditLogResult">
        <result property="id" column="id"/>
        <result property="applyId" column="apply_id"/>
        <result property="versionId" column="version_id"/>
        <result property="fileId" column="file_id"/>
        <result property="protoFileId" column="proto_file_id"/>
        <result property="type" column="type"/>
        <result property="actDefName" column="act_def_name"/>
        <result property="createBy" column="create_by"/>
        <result property="createName" column="create_name"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>


</mapper>

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rzdata.process.mapper.DocLinkLogMapper">

    <resultMap type="com.rzdata.process.domain.vo.DocLinkLogVo" id="DocLinkLogResult">
        <result property="id" column="id"/>
        <result property="docId" column="doc_id"/>
        <result property="linkCode" column="link_code"/>
        <result property="fileName" column="file_name"/>
        <result property="fileId" column="file_id"/>
        <result property="versionValue" column="version_value"/>
        <result property="versionId" column="version_id"/>
        <result property="linkType" column="link_type"/>
    </resultMap>

    <select id="queryLink" resultType="com.rzdata.process.domain.DocLinkLog" parameterType="com.rzdata.process.domain.bo.DocLinkLogBo">
        SELECT
        dll.*
        FROM
        doc_version_link dvl
        LEFT JOIN doc_link_log dll ON dvl.link_id = dll.id
        <where>
            <if test="bo.pVersionId != '' and bo.pVersionId != null">
                and dvl.version_id = #{bo.pVersionId}
            </if>
            <if test="bo.linkType != '' and bo.linkType != null">
                and dll.link_type = #{bo.linkType}
            </if>
            <if test="bo.neLinkType != '' and bo.neLinkType != null">
                and dll.link_type != #{bo.neLinkType}
            </if>
            <if test="bo.linkTypeList != null and bo.linkTypeList.size > 0">
                and dll.link_type in
                <foreach collection="bo.linkTypeList" item="linkType" index="index" open="(" separator="," close=")">
                    #{linkType}
                </foreach>
            </if>
            <if test="bo.status != '' and bo.status != null">
                and dll.status = #{bo.status}
            </if>
            <if test="bo.versionId != '' and bo.versionId != null">
                and dll.version_id = #{bo.versionId}
            </if>
        </where>
        ORDER BY dll.update_time
    </select>

    <select id="docLinkSearchPage" resultType="com.rzdata.process.domain.vo.DocLinkSearchResultVo">
        SELECT
        t.id as version_id,
        t.doc_name,
        t.doc_id,
        t.file_id,
        t.doc_class,
        t.class_name,
        t.release_time,
        t.dept_id,
        t.part_number,
        t.part_remark,
        t.product_version,
        t.start_date,
        t.create_by,
        t.user_name
        FROM
        (
        SELECT
        t2.id,
        t2.doc_name,
        t2.doc_id,
        t2.file_id,
        t3.doc_class,
        t4.class_name,
        t2.release_time,
        t2.dept_id,
        t2.part_number,
        t2.part_remark,
        t2.product_version,
        t2.start_date,
        t2.create_by,
        t2.user_name
        FROM
        doc_link_log t1
        LEFT JOIN doc_version t2 ON t1.version_id = t2.id
        LEFT JOIN doc_standard t3 ON t2.standard_id = t3.id
        LEFT JOIN basic_doc_class t4 ON t3.doc_class = t4.id
        WHERE
        t2.STATUS = '1'
        AND t1.link_type = 'DOC'
        <if test="bo.keyword !=null and bo.keyword != ''">
            AND (
            t2.doc_name LIKE concat('%', #{bo.keyword}, '%')
            OR t2.doc_id LIKE concat('%', #{bo.keyword}, '%')
            OR t2.part_number LIKE concat('%', #{bo.keyword}, '%')
            OR t2.part_remark LIKE concat('%', #{bo.keyword}, '%')
            OR t2.product_version LIKE concat('%', #{bo.keyword}, '%')
            )
        </if>
        UNION
        SELECT
        t2.id,
        t2.doc_name,
        t2.doc_id,
        t2.file_id,
        t4.doc_class,
        t5.class_name,
        t2.release_time,
        t2.dept_id,
        t2.part_number,
        t2.part_remark,
        t2.product_version,
        t2.start_date,
        t2.create_by,
        t2.user_name
        FROM
        doc_version_link t1
        LEFT JOIN doc_version t2 ON t1.version_id = t2.id
        LEFT JOIN doc_link_log t3 ON t1.link_id = t3.id
        LEFT JOIN doc_standard t4 ON t2.standard_id = t4.id
        LEFT JOIN basic_doc_class t5 ON t4.doc_class = t5.id
        WHERE
        t2.STATUS = '1'
        AND t1.link_id IN (SELECT dll.id FROM doc_link_log dll LEFT JOIN doc_version dv ON dll.version_id = dv.id
        WHERE dv.STATUS = '1' AND dll.link_type IN ( 'RECORD', 'REF_DOC' )
        <if test="bo.keyword !=null and bo.keyword != ''">
            AND (
            dv.doc_name LIKE concat('%', #{bo.keyword}, '%')
            OR dv.doc_id LIKE concat('%', #{bo.keyword}, '%')
            OR dv.part_number LIKE concat('%', #{bo.keyword}, '%')
            OR dv.part_remark LIKE concat('%', #{bo.keyword}, '%')
            OR dv.product_version LIKE concat('%', #{bo.keyword}, '%')
            )
        </if>
        )
        ) t ORDER BY t.start_date DESC
    </select>

    <select id="docLinkSearchItems" resultType="com.rzdata.process.domain.vo.DocLinkSearchResultVo">
        SELECT
        t3.id,
        t3.doc_name,
        t3.doc_id,
        t2.file_id,
        t2.doc_class,
        t3.release_time,
        t3.dept_id,
        t3.part_number,
        t3.part_remark,
        t3.product_version,
        t3.start_date,
        t2.link_type
        FROM
        doc_version_link t1
        LEFT JOIN doc_link_log t2 ON t1.link_id = t2.id
        LEFT JOIN doc_version t3 ON t2.version_id = t3.id
        WHERE
        t3.STATUS = '1'
        <if test="linkTypeList != null and linkTypeList.size > 0">
            AND t2.link_type IN
            <foreach collection="linkTypeList" item="linkType" index="index" open="(" separator="," close=")">
                #{linkType}
            </foreach>
        </if>
        <if test="versionId !=null and versionId != ''">
            AND t1.version_id = #{versionId}
        </if>
        ORDER BY
        t3.start_date DESC
    </select>

    <select id="getLinkCount" resultType="int" parameterType="com.rzdata.process.domain.bo.DocLinkLogBo">
        SELECT
            COUNT(*)
        FROM
            `doc_version_link` dvl
        LEFT JOIN `doc_link_log` dll ON dvl.link_id = dll.id
        <where>
        <if test="bo.versionId!=null and bo.versionId!=''">
            AND dvl.version_id = #{bo.versionId}
        </if>
        <if test="bo.fileName!=null and bo.fileName!=''">
            AND dll.file_name = #{bo.fileName}
        </if>
        <if test="bo.linkType!=null and bo.linkType!=''">
            AND dll.link_type = #{bo.linkType}
        </if>
        </where>
    </select>

    <select id="getPageList" resultType="com.rzdata.process.domain.vo.DocLinkLogVo">
        SELECT
        l.id,
        l.link_code,
        l.file_name,
        l.file_id,
        l.link_type,
        l.doc_class,
        l.create_time,
        l.start_date,
        l.release_time,
        l.end_date,
        v.standard_id,
        IF( l.link_type = 'RECORD', l.link_code, v.doc_id ) AS doc_id,
        l.version_value,
        v.id AS version_id
        FROM
        doc_link_log l
        LEFT JOIN doc_version_link dv ON l.id = dv.link_id
        LEFT JOIN doc_version v ON dv.version_id = v.id
        <where>
            <if test="bo.docId != null and  bo.docId !='' ">
                and v.doc_id = #{bo.docId}
            </if>
            <if test="bo.standardId != null and bo.standardId !='' ">
                and v.standard_id = #{bo.standardId}
            </if>
            <if test="bo.linkType != null and bo.linkType !='' ">
                and l.link_type = #{bo.linkType}
            </if>
            <if test="bo.versionId != null and bo.versionId !='' ">
                and v.id = #{bo.versionId}
            </if>
        </where>
    </select>
    <select id="queryDocLinkVo" resultType="com.rzdata.process.domain.vo.DocLinkLogVo">
        SELECT
        l.id,
        l.link_code,
        l.status,
        b.file_name,
        l.file_id,
        l.link_type,
        l.doc_class,
        l.create_time,
        l.version_value,
        l.version_id
        FROM
        doc_link_log l
        LEFT JOIN doc_version_link dvl ON l.id = dvl.link_id
        LEFT JOIN basic_file b ON l.file_id = b.id
        <where>
            <!-- status=1 查出有效的关联文件 2=失效
            l.status = '1' -->
            <if test="versionId!=null and versionId!=''">
                and dvl.version_id = #{versionId}
            </if>
            <if test="linkType!=null and linkType!=''">
                and l.link_type = #{linkType}
            </if>
        </where>
        order by l.create_time desc
    </select>

    <select id="queryByfileIdsDocLinkVo" resultType="com.rzdata.process.domain.vo.DocLinkLogVo">
        SELECT
        l.id,
        l.file_id,
        l.link_type
        FROM
        doc_link_log l
        <where>
            l.file_id in
            <foreach collection="fileIds" item="id" open="(" index="index" separator="," close=")">
                #{id}
            </foreach>
        </where>
        order by l.create_time desc
    </select>

    <update id="disuseValidLinkLog">
        update doc_version_link dvl LEFT JOIN doc_link_log dll on dvl.link_id = dll.id
        set dll.`status` =  '2',dll.`end_date` = NOW()
        <where>
            <if test="versionId!=null and versionId!=''">
                and dvl.version_id = #{versionId}
            </if>
            <if test="linkCode!=null and linkCode!=''">
                and dll.link_code = #{linkCode}
            </if>
            and dll.`status` =  '1'
        </where>
    </update>

    <!-- 查询待转记录到台账数据明细 -->
    <select id="queryInit2Account" resultType="com.rzdata.process.domain.vo.DocLinkLogVo">
        select mt.*, dvl.version_id, dv.standard_id from doc_link_log mt
        join doc_version_link dvl on dvl.link_id = mt.id
        join doc_version dv on dv.id = dvl.version_id
        join doc_standard ds on ds.id = dv.standard_id
        where mt.tenant_id is null
        and mt.id in (
        select max(t.id) id from doc_link_log t
        where t.link_code is not null and t.link_type = 'RECORD'
        group by t.file_id
        )
        <if test="linkCode != null and linkCode!= ''">
            and mt.link_code = #{linkCode}
        </if>
        <if test="versionId !=null and versionId != ''">
            and dvl.version_id = #{versionId}
        </if>
        <if test="standardId !=null and standardId != ''">
            and dv.standard_id = #{standardId}
        </if>
        <if test="dataType !=null and dataType != ''">
            and ds.data_type = #{dataType}
        </if>
        <if test="docClass !=null and docClass != ''">
            and ds.doc_class = #{docClass}
        </if>
        order by mt.create_time asc
    </select>

</mapper>

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rzdata.process.mapper.DocPrintDataAuthMapper">

    <resultMap type="com.rzdata.process.domain.DocPrintDataAuth" id="DocPrintDataAuthResult">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="userName" column="user_name"/>
        <result property="docDistributeId" column="doc_distribute_id"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <delete id="deleteBatchByCondition">
        DELETE FROM doc_print_data_auth
        WHERE (user_id, doc_distribute_id) IN
        <foreach item="item" collection="list" open="(" separator="," close=")">
            (#{item.userId}, #{item.docDistributeId})
        </foreach>
    </delete>


</mapper>

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rzdata.process.mapper.DocStatisticsMapper">
    <resultMap type="com.rzdata.process.domain.vo.DocStatisticsChangeTypeVo" id="DocStatisticsChangeTypeResult">
        <result property="deptId" column="dept_id"/>
        <result property="docClass" column="doc_class"/>
        <result property="addNum" column="add_num"/>
        <result property="updateNum" column="update_num"/>
        <result property="disuseNum" column="disuse_num"/>
        <result property="total" column="total"/>
    </resultMap>

    <resultMap type="com.rzdata.process.domain.vo.DocStatisticsChangeFactorVo" id="DocStatisticsChangeFactorResult">
        <result property="deptId" column="dept_id"/>
        <result property="docClass" column="doc_class"/>
        <result property="personNum" column="person_num"/>
        <result property="machineNum" column="machine_num"/>
        <result property="lawNum" column="law_num"/>
        <result property="materialNum" column="material_num"/>
        <result property="linkNum" column="link_num"/>
        <result property="testNum" column="test_num"/>
        <result property="total" column="total"/>
    </resultMap>

    <resultMap type="com.rzdata.process.domain.vo.DocStatisticsDistributeVo" id="DocStatisticsDisResult">
        <result property="deptId" column="dept_id"/>
        <result property="docId" column="doc_id"/>
        <result property="applyId" column="apply_id"/>
        <result property="distributeNum" column="distribute_nums"/>
    </resultMap>

    <select id="selectChangeType" resultMap="DocStatisticsChangeTypeResult"
            parameterType="com.rzdata.process.domain.bo.DocStatisticsBo">
        SELECT dma.doc_class,dma.dept_id,sum(( CASE WHEN (dma.`change_type` = 'ADD') THEN 1 ELSE 0 END )) AS `add_num`,
        sum(( CASE WHEN (dma.`change_type` = 'UPDATE') THEN 1 ELSE 0 END )) AS `update_num` ,
        sum(( CASE WHEN (dma.`change_type` = 'DISUSE') THEN 1 ELSE 0 END )) AS `disuse_num`,
        sum(1) AS total
        FROM
        `doc_modify_apply` dma
        LEFT JOIN sys_dept sd on sd.dept_id = dma.dept_id
        <where>
            dma.record_status = 'done'
            <if test="bo.deptId != '' and bo.deptId != null">
                AND dma.dept_id = #{bo.deptId}
            </if>
            <if test="bo.docClass != '' and bo.docClass != null">
                AND dma.doc_class = #{bo.docClass}
            </if>
            <if test="bo.dataType != '' and bo.dataType != null">
                AND dma.data_type = #{bo.dataType}
            </if>
            <if test="bo.docClassList != null and bo.docClassList.size > 0">
                and dma.doc_class in
                <foreach collection="bo.docClassList" item="docClass" index="index" open="(" separator="," close=")">
                    #{docClass}
                </foreach>
            </if>
            <if test="bo.deptIds != '' and bo.deptIds != null">
                and (dma.dept_id = #{bo.deptIds} or find_in_set(#{bo.deptIds},sd.ancestors))
            </if>
            <if test="bo.startDateTime != null">
                AND dma.update_time &gt; #{bo.startDateTime}
            </if>
            <if test="bo.endDateTime != null">
                AND dma.update_time &lt;= #{bo.endDateTime}
            </if>
            <if test="bo.searchValue != null">
                AND ( dma.doc_name LIKE CONCAT('%', #{bo.searchValue} ,'%') or  dma.doc_id LIKE CONCAT('%', #{bo.searchValue} ,'%')  or  dma.version_value LIKE CONCAT('%', #{bo.searchValue} ,'%')  )
            </if>
        </where>
        GROUP BY dma.doc_class,dma.dept_id
    </select>
    <select id="selectChangeTypeSum" resultMap="DocStatisticsChangeTypeResult"
            parameterType="com.rzdata.process.domain.bo.DocStatisticsBo">
        SELECT sum(( CASE WHEN (dma.`change_type` = 'ADD') THEN 1 ELSE 0 END )) AS `add_num`,
        sum(( CASE WHEN (dma.`change_type` = 'UPDATE') THEN 1 ELSE 0 END )) AS `update_num` ,
        sum(( CASE WHEN (dma.`change_type` = 'DISUSE') THEN 1 ELSE 0 END )) AS `disuse_num`,
        sum(1) AS total
        FROM
        `doc_modify_apply` dma
        LEFT JOIN sys_dept sd on sd.dept_id = dma.dept_id
        <where>
            dma.record_status = 'done'
            <if test="bo.deptId != '' and bo.deptId != null">
                AND dma.dept_id = #{bo.deptId}
            </if>
            <if test="bo.dataType != '' and bo.dataType != null">
                AND dma.data_type = #{bo.dataType}
            </if>
            <if test="bo.docClassList != null and bo.docClassList.size > 0">
                and dma.doc_class in
                <foreach collection="bo.docClassList" item="docClass" index="index" open="(" separator="," close=")">
                    #{docClass}
                </foreach>
            </if>
            <if test="bo.deptIds != '' and bo.deptIds != null">
                and (dma.dept_id = #{bo.deptIds} or find_in_set(#{bo.deptIds},sd.ancestors))
            </if>
            <if test="bo.docClass != '' and bo.docClass != null">
                AND dma.doc_class = #{bo.docClass}
            </if>
            <if test="bo.startDateTime != null">
                AND dma.update_time &gt; #{bo.startDateTime}
            </if>
            <if test="bo.endDateTime != null">
                AND dma.update_time &lt;= #{bo.endDateTime}
            </if>
        </where>
    </select>


    <select id="selectChangeFactor" resultMap="DocStatisticsChangeFactorResult"
            parameterType="com.rzdata.process.domain.bo.DocStatisticsBo">
        SELECT a.doc_class,a.dept_id,a.person_num,a.machine_num,a.law_num,a.material_num,a.link_num,a.test_num,
        SUM(a.person_num + a.machine_num + a.law_num + a.material_num + a.link_num + a.test_num) AS total
        FROM (
        SELECT
        sum(( CASE WHEN (FIND_IN_SET('人',change_factor)) THEN 1 ELSE 0 END )) AS `person_num`,
        sum(( CASE WHEN (FIND_IN_SET('机',change_factor)) THEN 1 ELSE 0 END )) AS `machine_num`,
        sum(( CASE WHEN (FIND_IN_SET('法',change_factor)) THEN 1 ELSE 0 END )) AS `law_num`,
        sum(( CASE WHEN (FIND_IN_SET('料',change_factor)) THEN 1 ELSE 0 END )) AS `material_num`,
        sum(( CASE WHEN (FIND_IN_SET('环',change_factor)) THEN 1 ELSE 0 END )) AS `link_num`,
        sum(( CASE WHEN (FIND_IN_SET('测',change_factor)) THEN 1 ELSE 0 END )) AS `test_num`,
        doc_class,
        dept_id
        FROM
        `doc_change_apply`
        <where>
            <if test="bo.deptId != '' and bo.deptId != null">
                AND dept_id = #{bo.deptId}
            </if>
            <if test="bo.docClass != '' and bo.docClass != null">
                AND doc_class = #{bo.docClass}
            </if>
            <if test="bo.startDateTime != null">
                AND update_time &gt; #{bo.startDateTime}
            </if>
            <if test="bo.endDateTime != null">
                AND update_time &lt;= #{bo.endDateTime}
            </if>
        </where>
        GROUP BY doc_class,dept_id
        ) as a GROUP BY doc_class,dept_id;
    </select>

    <select id="selectTraining" resultType="com.rzdata.process.domain.vo.DocStatisticsTrainingVo">
        SELECT *
        FROM (
            SELECT dv.release_time,
                ds.doc_name,
                ifnull(su2.nick_name, su.nick_name) AS nick_name,
                dv.doc_id,
                dv.version_value,
                NOT isnull(dmat.version_id) AS trained,
                dv.id as version_id,
                dv.data_type,
                dv.apply_id
            FROM doc_version dv
            INNER JOIN doc_standard ds
            ON dv.standard_id = ds.id
            LEFT JOIN (
                SELECT DISTINCT version_id
                FROM doc_modify_apply_train
            ) dmat
            ON dv.id = dmat.version_id
            LEFT JOIN sys_user su
            ON dv.user_name = su.user_name
            LEFT JOIN doc_modify_apply dma
            ON dv.apply_id = dma.id
            LEFT JOIN sys_user su2
            ON dma.user_name = su2.user_name
            <where>
                AND dv.status = '1'
                AND dv.release_time IS NOT NULL
                <if test="bo.docName != null and bo.docName != ''">
                    AND ds.doc_name LIKE concat ('%', #{bo.docName}, '%')
                </if>
                <if test="bo.releaseTimeStart != null">
                    AND dv.release_time > #{bo.releaseTimeStart}
                </if>
                <if test="bo.releaseTimeEnd != null">
                    AND dv.release_time &lt;= #{bo.releaseTimeEnd}
                </if>
            </where>
        ) t
        <where>
            <if test="bo.nickName != null and bo.nickName != ''">
                AND nick_name LIKE concat('%', #{bo.nickName}, '%')
            </if>
            <if test="bo.trained != null">
                AND trained = #{bo.trained}
            </if>
        </where>
    </select>
</mapper>

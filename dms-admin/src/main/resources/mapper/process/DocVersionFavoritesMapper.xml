<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rzdata.process.mapper.DocVersionFavoritesMapper">

    <resultMap type="com.rzdata.process.domain.DocVersionFavorites" id="DocVersionFavoritesResult">
        <result property="userId" column="user_id"/>
        <result property="versionId" column="version_id"/>
        <result property="tenantId" column="tenant_id"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <select id="queryPageFavorites" resultType="com.rzdata.process.domain.vo.DocDistributeVo">
        SELECT l.*,
        v.start_date,
        v.end_date,
        v.file_id,
        v.merge_file_id,
        v.encrypt_file_id,
        v.review_time,
        v.forever,
        w.proc_inst_id
        FROM doc_distribute l
        LEFT JOIN doc_version v ON l.version_id = v.id
        LEFT JOIN doc_workflow_apply_log w ON l.apply_id = w.id
        LEFT JOIN doc_version_favorites dvf ON dvf.version_id = v.id
        <where>
            l.dept_id= #{loginDeptId} AND v.status = 1
            and dvf.user_id = #{userId}
            <if test="bo.docClass!=null and bo.docClass!=''">
                AND l.doc_class = #{bo.docClass}
            </if>
            <if test="bo.docName!=null and bo.docName!=''">
                AND l.doc_name LIKE CONCAT('%', #{bo.docName} ,'%')
            </if>
            <if test="bo.docId!=null and bo.docId!=''">
                AND l.doc_id LIKE CONCAT('%', #{bo.docId} ,'%')
            </if>
            <!--分发时间查询条件-->
            <if test="bo.startTime!=null and bo.startTime!='' and bo.endTime!=null and bo.endTime!=''">
                AND v.update_time BETWEEN #{bo.startTime} AND #{bo.endTime}
            </if>
            group by l.doc_id,l.version_id
            order by v.start_date desc
        </where>
    </select>

</mapper>

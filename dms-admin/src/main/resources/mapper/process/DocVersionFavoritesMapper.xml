<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rzdata.process.mapper.DocVersionFavoritesMapper">

    <resultMap type="com.rzdata.process.domain.DocVersionFavorites" id="DocVersionFavoritesResult">
        <result property="userId" column="user_id"/>
        <result property="versionId" column="version_id"/>
        <result property="tenantId" column="tenant_id"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <select id="queryPageFavorites" resultType="com.rzdata.process.domain.vo.DocDistributeLogVo">
        SELECT l.*,
        v.start_date,
        v.end_date,
        v.file_id,
        v.merge_file_id,
        v.encrypt_file_id,
        v.review_time,
        v.forever,
        w.proc_inst_id
        FROM doc_distribute_log l
        LEFT JOIN doc_version v ON l.version_id = v.id
        LEFT JOIN doc_workflow_apply_log w ON l.apply_id = w.id
        LEFT JOIN doc_version_favorites dvf ON dvf.version_id = v.id
        <where>
            l.dept_id= #{loginDeptId} AND v.status = 1
            and dvf.user_id = #{userId}
            <if test="bo.docClass!=null and bo.docClass!=''">
                AND l.doc_class = #{bo.docClass}
            </if>
            <if test="bo.docName!=null and bo.docName!=''">
                AND l.doc_name LIKE CONCAT('%', #{bo.docName} ,'%')
            </if>
            <if test="bo.docId!=null and bo.docId!=''">
                AND l.doc_id LIKE CONCAT('%', #{bo.docId} ,'%')
            </if>
            <!--分发时间查询条件-->
            <if test="bo.startTime!=null and bo.startTime!='' and bo.endTime!=null and bo.endTime!=''">
                AND v.update_time BETWEEN #{bo.startTime} AND #{bo.endTime}
            </if>
            group by l.doc_id,l.version_id
            order by v.start_date desc
        </where>
    </select>

    <select id="queryPageFavoritesRecordFile" resultType="com.rzdata.process.domain.vo.VersionVo">
        SELECT
        IF( dll.link_type = 'DOC', dv.doc_name, dll.file_name ) AS doc_name,
        IF( dll.link_type = 'DOC', dv.version_value, dll.version_value ) AS version_value,
        IF( dll.link_type = 'DOC', ds.doc_class, dll.doc_class ) AS doc_class,
        IF( dll.link_type = 'DOC', dv.merge_file_id, dll.file_id ) AS merge_file_id,
        IF( dll.link_type = 'DOC', dv.encrypt_file_id, dll.file_id ) AS encrypt_file_id,
        dll.file_id,
        dll.link_code as record_doc_id,
        dv.review_time,
        dv.start_date,
        dv.end_date,
        dv.status,
        dv.train_dept,
        dv.dept_id,
        dv.doc_id,
        dv.id,
        dv.id as version_id,
        ddl.receive_status,
        w.proc_inst_id,
        ddl.apply_id
        FROM
        doc_link_log dll
        LEFT JOIN doc_version_link dvl ON dll.id = dvl.link_id
        LEFT JOIN doc_version dv ON dvl.version_id = dv.id
        LEFT JOIN `doc_standard` ds ON dv.standard_id = ds.id
        LEFT JOIN `doc_distribute_log` ddl ON ddl.version_id = dv.id
        LEFT JOIN doc_workflow_apply_log w ON ddl.apply_id = w.id
        LEFT JOIN doc_version_favorites dvf ON dvf.version_id = dv.id
        <where>
            ds.`status` = '1'
            AND dll.link_type IN ( 'RECORD', 'DOC' )
            AND dll.`status` = '1'
            and dvf.user_id = #{userId}
            and ddl.dept_id = #{deptId}
            <if test="bo.docClass != null and bo.docClass != ''">
                and (ds.doc_class = #{bo.docClass} or dll.doc_class = #{bo.docClass})
            </if>
            <if test="bo.docId != null and bo.docId != ''">
                and (dv.doc_id = #{bo.docId} or dll.link_code = #{bo.docId})
            </if>
            <if test="bo.docName != null and bo.docName != ''">
                and (dv.doc_name like concat('%', #{bo.docName}, '%') or dll.file_name like concat('%', #{bo.docName},
                '%'))
            </if>
            <if test="bo.params.startTime != null and bo.params.startTime != ''">
                and dv.start_date >= #{bo.params.startTime}
            </if>
            <if test="bo.params.endTime != null and bo.params.endTime != ''">
                and dv.start_date &lt;= #{bo.params.endTime}
            </if>
            <if test="bo.deptId != null and bo.deptId != ''">
                and dv.dept_id = #{bo.deptId}
            </if>
            <if test="bo.inside != null and bo.inside != ''">
                and ddl.dept_id = #{bo.inside}
                and ds.dept_id = #{bo.inside}
            </if>
            <if test="bo.outside != null and bo.outside != ''">
                and ddl.dept_id = #{bo.outside}
                and dv.dept_id != #{bo.outside}
                AND dv.id IN (SELECT version_id FROM doc_distribute_item where dept_id = #{bo.outside})
            </if>
        </where>
        ORDER BY dv.start_date DESC
    </select>
</mapper>

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rzdata.process.mapper.ErrataVersionMapper">

    <resultMap type="com.rzdata.process.domain.ErrataVersion" id="ErrataVersionResult">
        <result property="id" column="id"/>
        <result property="versionId" column="version_id"/>
        <result property="sort" column="sort"/>
        <result property="docClass" column="doc_class"/>
        <result property="standardId" column="standard_id"/>
        <result property="docId" column="doc_id"/>
        <result property="docName" column="doc_name"/>
        <result property="applyId" column="apply_id"/>
        <result property="versionValue" column="version_value"/>
        <result property="startDate" column="start_date"/>
        <result property="endDate" column="end_date"/>
        <result property="status" column="status"/>
        <result property="reason" column="reason"/>
        <result property="reviewTime" column="review_time"/>
        <result property="content" column="content"/>
        <result property="remark" column="remark"/>
        <result property="changeFactor" column="change_factor"/>
        <result property="changeReason" column="change_reason"/>
        <result property="trainDept" column="train_dept"/>
        <result property="fileId" column="file_id"/>
        <result property="mergeFileId" column="merge_file_id"/>
        <result property="encryptFileId" column="encrypt_file_id"/>
        <result property="tenantId" column="tenant_id"/>
        <result property="forever" column="forever"/>
        <result property="dataType" column="data_type"/>
        <result property="pdfFileId" column="pdf_file_id"/>
        <result property="releaseTime" column="release_time"/>
        <result property="isBom" column="is_bom"/>
        <result property="upVersionId" column="up_version_id"/>
        <result property="parentDocId" column="parent_doc_id"/>
        <result property="classType" column="class_type"/>
        <result property="deptId" column="dept_id"/>
        <result property="userName" column="user_name"/>
        <result property="changeType" column="change_type"/>
        <result property="invokeType" column="invoke_type"/>
        <result property="invokeId" column="invoke_id"/>
        <result property="distributeType" column="distribute_type"/>
        <result property="whetherCustomer" column="whether_customer"/>
        <result property="applyTime" column="apply_time"/>
        <result property="productLine" column="product_line"/>
        <result property="process" column="process"/>
        <result property="productType" column="product_type"/>
        <result property="haveLinkFile" column="have_link_file"/>
        <result property="programVersionId" column="program_version_id"/>
        <result property="programDocId" column="program_doc_Id"/>
        <result property="partNumber" column="part_number"/>
        <result property="partRemark" column="part_remark"/>
        <result property="custodyDeptId" column="custody_dept_id"/>
        <result property="shelfLife" column="shelf_life"/>
        <result property="fileEffectiveDate" column="file_effective_date"/>
        <result property="revisionDate" column="revision_date"/>
        <result property="compliance" column="compliance"/>
        <result property="factorys" column="factorys"/>
        <result property="customerCode" column="customer_code"/>
        <result property="deviceCode" column="device_code"/>
        <result property="deviceName" column="device_name"/>
        <result property="productVersion" column="product_version"/>
        <result property="internalDocId" column="internal_doc_id"/>
        <result property="ext1" column="ext1"/>
        <result property="ext2" column="ext2"/>
        <result property="ext3" column="ext3"/>
        <result property="ext4" column="ext4"/>
        <result property="ext5" column="ext5"/>
        <result property="ext6" column="ext6"/>
        <result property="ext7" column="ext7"/>
        <result property="ext8" column="ext8"/>
        <result property="ext9" column="ext9"/>
        <result property="ext10" column="ext10"/>
        <result property="ext11" column="ext11"/>
        <result property="ext12" column="ext12"/>
        <result property="ext13" column="ext13"/>
        <result property="ext14" column="ext14"/>
        <result property="ext15" column="ext15"/>
        <result property="ext16" column="ext16"/>
        <result property="ext17" column="ext17"/>
        <result property="ext18" column="ext18"/>
        <result property="ext19" column="ext19"/>
        <result property="ext20" column="ext20"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <select id="queryPageList" resultType="com.rzdata.process.domain.vo.ErrataVersionVo">
        select * from (SELECT bdc.class_name,dev.* FROM `doc_errata_version` dev
        left join basic_doc_class bdc on dev.doc_class = bdc.id) t
        ${ew.customSqlSegment}
    </select>
</mapper>

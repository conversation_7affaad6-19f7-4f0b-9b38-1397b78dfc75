<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rzdata.process.mapper.FileAdviseMapper">

    <resultMap type="com.rzdata.process.domain.FileAdvise" id="FileAdviseResult">
        <result property="id" column="id"/>
        <result property="docId" column="doc_id"/>
        <result property="docName" column="doc_name"/>
        <result property="docTypeCode" column="doc_type_code"/>
        <result property="docTypeName" column="doc_type_name"/>
        <result property="versionId" column="version_id"/>
        <result property="versionValue" column="version_value"/>
        <result property="docDeptId" column="doc_dept_id"/>
        <result property="docDeptName" column="doc_dept_name"/>
        <result property="summary" column="summary"/>
        <result property="deptId" column="dept_id"/>
        <result property="createName" column="create_name"/>
        <result property="createTime" column="create_time"/>
    </resultMap>

    <select id="selectUserName" resultType="string">
        SELECT nick_name from sys_user where user_name = #{name}
    </select>

    <select id="selectFileAdviseByDept" resultType="com.rzdata.process.domain.vo.IndexVo">
        SELECT
            dept_name as name,
            count( 1 ) as num
        FROM
            doc_file_advise
        <where>
            <if test="bo.deptId != null">
                and dept_id = #{bo.deptId}
            </if>
            <if test="bo.startTime != null and bo.endTime != null">
                and create_time between #{bo.startTime } and #{bo.endTime}
            </if>
        </where>
        GROUP BY
            dept_id
    </select>

    <select id="selectFileAdviseByUser" resultType="com.rzdata.process.domain.vo.IndexVo">
        SELECT
            create_name as name,
            count( 1 ) as num
        FROM
            doc_file_advise
        <where>
            <if test="bo.deptId != null">
                and dept_id = #{bo.deptId}
            </if>
            <if test="bo.startTime != null and bo.endTime != null">
                and create_time between #{bo.startTime } and #{bo.endTime}
            </if>
        </where>
        GROUP BY
            create_name
    </select>
</mapper>

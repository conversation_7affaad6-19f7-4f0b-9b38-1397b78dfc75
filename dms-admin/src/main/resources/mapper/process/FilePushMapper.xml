<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rzdata.process.mapper.FilePushMapper">

    <resultMap type="com.rzdata.process.domain.FilePush" id="FilePushResult">
        <result property="id" column="id"/>
        <result property="applyId" column="apply_id"/>
        <result property="pushCompanyId" column="push_company_id"/>
        <result property="pushCompanyName" column="push_company_name"/>
        <result property="pashClassId" column="pash_class_id"/>
        <result property="pashClassName" column="pash_class_name"/>
        <result property="receiveUserName" column="receive_user_name"/>
        <result property="receiveNickName" column="receive_nick_name"/>
        <result property="receiveDeptId" column="receive_dept_id"/>
        <result property="receiveDeptName" column="receive_dept_name"/>
        <result property="fileId" column="file_id"/>
        <result property="pashVersionId" column="pash_version_id"/>
        <result property="pashDocId" column="pash_doc_id"/>
        <result property="pashDocName" column="pash_doc_name"/>
        <result property="pashVersionValue" column="pash_version_value"/>
        <result property="versionId" column="version_id"/>
        <result property="status" column="status"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>


</mapper>

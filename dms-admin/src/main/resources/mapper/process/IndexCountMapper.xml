<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rzdata.process.mapper.IndexCountMapper">

    <!-- 台账总数，按照状态进行统计 -->
    <select id="queryDocAccount" resultType="java.util.HashMap">
        select t.status ,count(1) num from doc_standard t group by t.status
    </select>

    <!-- 阅知人次 -->
    <select id="queryViewCount" resultType="java.util.HashMap">
        select count(1) num from doc_preview_statistics t
    </select>

    <!-- 流程总数 -->
    <select id="queryFlowCount" resultType="java.util.HashMap">
        select count(1) num from doc_modify_apply t
    </select>

    <!-- 文件类型占比个数 -->
    <select id="queryClassTypeFileCount" resultType="java.util.HashMap">
        select t.id class_id, t.class_name name, t.sort, ifnull(dt.num,0) value from basic_doc_class t
            left join (
                select ds.doc_class_type ,count(1) num from doc_standard ds where ds.status = '1' group by ds.doc_class_type
            ) dt on dt.doc_class_type = t.id
        where t.data_type = 'stdd' and t.parent_class_id = '0' and t.class_status = '1' and t.class_type in ('DOC','RECCORD')
        order by t.sort asc
    </select>

    <!-- 文件类型下的子分类占比个数 -->
    <select id="queryFileCountByClassType" resultType="java.util.HashMap">
        select t.id class_id, t.class_name name, t.sort, ifnull(dt.num,0) value from basic_doc_class t
            left join (
                select ds.doc_class ,count(1) num from doc_standard ds where ds.status = '1' group by ds.doc_class
            ) dt on dt.doc_class = t.id
        where t.data_type = 'stdd' and t.class_status = '1' and t.class_type in ('DOC','RECCORD')
        <if test="classType != null">
            and t.parent_class_id = #{classType}
        </if>
        order by t.parent_class_id ,t.sort asc
    </select>

    <!-- 二级部门归口文件个数 -->
    <select id="querySecDeptGkFileCount" resultType="java.util.HashMap">
        select sd.dept_id , sd.dept_name name, sd.order_num , ifnull(dt.num,0) value from sys_dept sd
            left join (
                select ds.edit_sec_dept_id ,count(1) num from doc_standard ds where ds.status = '1' group by ds.edit_sec_dept_id
            ) dt on dt.edit_sec_dept_id = sd.dept_id
        where sd.dept_level = 2 and sd.status = '0' and sd.del_flag = '0'
        order by sd.order_num asc
    </select>

    <!-- 二级部门阅知文件次数 -->
    <select id="querySecDeptViewCount" resultType="java.util.HashMap">
        select sd.dept_id , sd.dept_name name, sd.order_num , ifnull(dt.num,0) value from sys_dept sd
            left join (
                select sd.sec_dept_id ,count(1) num from doc_preview_statistics t
                join sys_user su on su.user_name = t.preview_user_name
                join sys_dept sd on sd.dept_id = su.dept_id
                group by sd.sec_dept_id
            ) dt on dt.sec_dept_id = sd.dept_id
        where sd.dept_level = 2 and sd.status = '0' and sd.del_flag = '0'
        order by sd.order_num asc
    </select>

</mapper>

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rzdata.process.mapper.LostApplyMapper">

    <resultMap type="com.rzdata.process.domain.LostApply" id="LostApplyResult">
        <result property="id" column="id"/>
        <result property="applyTitle" column="apply_title"/>
        <result property="applyType" column="apply_type"/>
        <result property="deptId" column="dept_id"/>
        <result property="deptName" column="dept_name"/>
        <result property="userName" column="user_name"/>
        <result property="nickName" column="nick_name"/>
        <result property="versionId" column="version_id"/>
        <result property="docId" column="doc_id"/>
        <result property="docName" column="doc_name"/>
        <result property="applyTime" column="apply_time"/>
        <result property="versionValue" column="version_value"/>
        <result property="docClass" column="doc_class"/>
        <result property="isPuttingOut" column="is_putting_out"/>
        <result property="reason" column="reason"/>
        <result property="effect" column="effect"/>
        <result property="precaution" column="precaution"/>
        <result property="status" column="status"/>
        <result property="tenantId" column="tenant_id"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>


</mapper>

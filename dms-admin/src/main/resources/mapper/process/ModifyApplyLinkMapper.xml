<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rzdata.process.mapper.ModifyApplyLinkMapper">

    <resultMap type="com.rzdata.process.domain.ModifyApplyLink" id="ModifyApplyLinkResult">
        <result property="id" column="id"/>
        <result property="applyId" column="apply_id"/>
        <result property="linkId" column="link_id"/>
        <result property="linkClass" column="link_class"/>
        <result property="linkType" column="link_type"/>
        <result property="docName" column="doc_name"/>
        <result property="versionValue" column="version_value"/>
        <result property="versionId" column="version_id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="isDeleted" column="is_deleted"/>
    </resultMap>

    <select id="selectListByVersionLink" resultType="com.rzdata.process.domain.vo.ModifyApplyLinkVo">
        SELECT
        dll.id AS link_id,
        dll.file_id,
        dll.link_code AS doc_id,
        dv2.doc_name,
        bf.file_name AS file_name,
        dll.STATUS,
        dll.STATUS AS is_deleted,
        dll.version_id,
        dll.version_value,
        dll.link_type,
        dll.doc_class,
        dll.start_date,
        dll.release_time,
        dll.end_date,
        dll.proto_file_id
        FROM
        doc_version_link dvl
        LEFT JOIN doc_link_log dll ON dvl.link_id = dll.id
        LEFT JOIN basic_file bf ON bf.id = dll.file_id
        left join doc_version dv on dll.version_id = dv.id
        left join doc_version dv2 on dv2.standard_id=dv.standard_id
        <where>
            dv2.status='1'
            <if test="bo.versionId != '' and bo.versionId != null">
                and dvl.version_id = #{bo.versionId}
            </if>
            <if test="bo.linkType != '' and bo.linkType != null">
                and dll.link_type = #{bo.linkType}
            </if>
            <if test="bo.status != '' and bo.status != null">
                and dll.status = #{bo.status}
            </if>
        </where>
        ORDER BY dll.update_time
    </select>

    <select id="queryDocByApplyIdAndType" resultType="com.rzdata.process.domain.vo.ModifyApplyLinkVo">
        select
        dmal.*,
        bf.file_name as file_name,
        bf.file_size
        from doc_modify_apply_link dmal
        left join basic_file bf on dmal.file_id = bf.id
        <where>
            <if test="applyId!=null and applyId!=''">
                and dmal.apply_id = #{applyId}
            </if>
            <if test="linkType!=null and linkType!=''">
                and dmal.link_type = #{linkType}
            </if>
        </where>
        order by dmal.create_time desc
    </select>
</mapper>

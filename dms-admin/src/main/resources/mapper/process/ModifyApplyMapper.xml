<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rzdata.process.mapper.ModifyApplyMapper">

    <resultMap type="com.rzdata.process.domain.ModifyApply" id="ModifyApplyResult">
        <result property="id" column="id"/>
        <result property="indexNum" column="index_num"/>
        <result property="changeType" column="change_type"/>
        <result property="docClass" column="doc_class"/>
        <result property="docName" column="doc_name"/>
        <result property="docId" column="doc_id"/>
        <result property="deptId" column="dept_id"/>
        <result property="userName" column="user_name"/>
        <result property="applyTime" column="apply_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="expiration" column="expiration"/>
        <result property="versionValue" column="version_value"/>
        <result property="versionId" column="version_id"/>
        <result property="yNTrain" column="y_n_train"/>
        <result property="trainDept" column="train_dept"/>
        <result property="yNMergeDraft" column="y_n_merge_draft"/>
        <result property="yNEncrypt" column="y_n_encrypt"/>
        <result property="yNDistribute" column="y_n_distribute"/>
        <result property="fileId" column="file_id"/>
        <result property="recordStatus" column="record_status"/>
        <result property="changeReason" column="change_reason"/>
        <result property="content" column="content"/>
        <result property="remark" column="remark"/>
        <result property="nickName" column="nick_name"/>

        <result property="invokeType" column="invoke_type"/>
        <result property="invokeId" column="invoke_id"/>
        <result property="projectId" column="project_id"/>
        <result property="preChangeCode" column="pre_change_code"/>
        <result property="partNumber" column="part_number"/>
        <result property="partRemark" column="part_remark"/>
        <result property="factorys" column="factorys"/>
        <result property="customerCode" column="customer_code"/>
        <result property="deviceCode" column="device_code"/>
        <result property="deviceName" column="device_name"/>
        <result property="productVersion" column="product_version"/>
        <result property="systemClause" column="system_clause"/>
        <result property="securityClass" column="security_class"/>
        <result property="keyword" column="keyword"/>
        <result property="docBytes" column="doc_bytes"/>
        <result property="filePurpose" column="file_purpose"/>
        <result property="regulationStandardStatus" column="regulation_standard_status"/>
        <result property="regulationPublishDate" column="regulation_publish_date"/>
        <result property="regulationImplementDate" column="regulation_implement_date"/>
    </resultMap>

    <select id="selectModifyApplyList" resultType="map">
        SELECT
        m.id,
        m.invoke_type as invokeType,
        m.invoke_id as invokeId,
        m.project_id as projectId,
        m.pre_change_code as preChangeCode,
        m.change_type as changeType,
        m.doc_class as docClass,
        m.doc_name as docName,
        m.doc_id as docId,
        m.dept_id as deptId,
        m.user_name as userName,
        m.apply_time as applyTime,
        w.update_time as updateTime,
        m.expiration,
        m.version_id as versionId,
        m.version_value as versionValue,
        m.y_n_train as yNTrain,
        m.train_dept as trainDept,
        m.y_n_merge_draft as yNMergeDraft,
        m.y_n_encrypt as yNEncrypt,
        m.y_n_distribute as yNDistribute,
        m.file_id as fileId,
        m.record_status as recordStatus,
        m.change_reason as changeReason,
        m.content,
        m.remark,
        w.apply_class AS applyClass,
        w.apply_status AS applyStatus,
        w.doc_name AS docName,
        w.doc_id AS docId,
        w.doc_class AS docClass,
        w.version_id AS versionId,
        w.version_value AS versionValue,
        w.sender AS sender,
        d.dept_name AS deptName,
        m.change_id AS changeId,
        m.system_clause AS systemClause,
        m.security_class AS securityClass,
        m.keyword AS keyword,
        m.doc_bytes AS docBytes
        FROM
        doc_modify_apply m
        LEFT JOIN doc_workflow_apply_log w ON m.id = w.id
        LEFT JOIN sys_dept d ON m.dept_id = d.dept_id
        <where>
            <if test="bo.procDefKey != null and bo.procDefKey != ''">
                and w.proc_def_key like  concat(#{bo.procDefKey}, '%')
            </if>

            <!--发起人-->
            <if test="bo.startUserId != null and bo.startUserId != ''">
                and w.sender = #{bo.startUserId}
            </if>

            <if test="bo.changeType != null and bo.changeType != ''">
                and m.change_type = #{bo.changeType}
            </if>
            <if test="bo.docClass != null and bo.docClass != ''">
                and m.doc_class = #{bo.docClass}
            </if>
            <if test="bo.docName != null and bo.docName != ''">
                and m.doc_name like concat('%', #{bo.docName}, '%')
            </if>
            <if test="bo.docId != null and bo.docId != ''">
                and m.doc_id like concat('%', #{bo.docId}, '%')
            </if>
            <if test="bo.deptId != null">
                and m.dept_id = #{bo.deptId}
            </if>
            <if test="bo.userName != null and bo.userName != ''">
                and m.user_name like concat('%', #{bo.userName}, '%')
            </if>
            <if test="bo.versionValue != null and bo.versionValue != ''">
                and m.version_value = #{bo.versionValue}
            </if>
            <if test="bo.versionId != null and bo.versionId != ''">
                and m.version_id = #{bo.versionId}
            </if>
            <if test="bo.applyStartTime != null">
                and m.apply_time &gt;= #{bo.applyStartTime}
            </if>
            <if test="bo.applyEndTime != null">
                and m.apply_time &lt;= #{bo.applyEndTime}
            </if>


            <if test="bo.searchValue != null and bo.searchValue != ''">
                and (
                m.doc_id like concat('%', #{bo.searchValue}, '%')
                OR m.doc_name like concat('%', #{bo.searchValue}, '%')
                OR m.user_name like concat('%', #{bo.searchValue}, '%')
                OR w.doc_name like concat('%', #{bo.searchValue}, '%')
                )
            </if>
            <if test="ids != null and ids.size > 0">
                and m.id in
                <foreach collection="ids" item="id" index="index" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
        ORDER BY m.apply_time desc
    </select>
    <select id="selectModifyApplyList4New" resultType="map">
        SELECT
        m.id,
        m.invoke_type as invokeType,
        m.invoke_id as invokeId,
        m.project_id as projectId,
        m.pre_change_code as preChangeCode,
        m.change_type as changeType,
        m.doc_class as docClass,
        m.doc_name as docName,
        m.doc_id as docId,
        m.dept_id as deptId,
        m.user_name as userName,
        m.apply_time as applyTime,
        m.update_time as updateTime,
        m.expiration,
        m.version_id as versionId,
        m.version_value as versionValue,
        m.y_n_train as yNTrain,
        m.train_dept as trainDept,
        m.y_n_merge_draft as yNMergeDraft,
        m.y_n_encrypt as yNEncrypt,
        m.y_n_distribute as yNDistribute,
        m.file_id as fileId,
        m.record_status as recordStatus,
        m.change_reason as changeReason,
        m.content,
        m.remark,
        m.system_clause as systemClause,
        w.apply_class AS applyClass,
        w.apply_status AS applyStatus,
        w.doc_name AS docName,
        w.doc_id AS docId,
        w.doc_class AS docClass,
        w.version_id AS versionId,
        w.version_value AS versionValue,
        w.sender AS sender,
        d.dept_name AS deptName
        FROM
        doc_modify_apply m
        LEFT JOIN doc_workflow_apply_log w ON m.id = w.id
        LEFT JOIN sys_dept d ON m.dept_id = d.dept_id
        <where>
            <if test="bo.procDefKey != null and bo.procDefKey != ''">
                and w.proc_def_key like  concat(#{bo.procDefKey}, '%')
            </if>

            <!--发起人-->
            <if test="bo.startUserId != null and bo.startUserId != ''">
                and w.sender = #{bo.startUserId}
            </if>

            <if test="bo.changeType != null and bo.changeType != ''">
                and m.change_type = #{bo.changeType}
            </if>
            <if test="bo.docClass != null and bo.docClass != ''">
                and m.doc_class = #{bo.docClass}
            </if>
            <if test="bo.docName != null and bo.docName != ''">
                and m.doc_name like concat('%', #{bo.docName}, '%')
            </if>
            <if test="bo.docId != null and bo.docId != ''">
                and m.doc_id like concat('%', #{bo.docId}, '%')
            </if>
            <if test="bo.deptId != null">
                and m.dept_id = #{bo.deptId}
            </if>
            <if test="bo.userName != null and bo.userName != ''">
                and m.user_name like concat('%', #{bo.userName}, '%')
            </if>
            <if test="bo.versionValue != null and bo.versionValue != ''">
                and m.version_value = #{bo.versionValue}
            </if>
            <if test="bo.versionId != null and bo.versionId != ''">
                and m.version_id = #{bo.versionId}
            </if>
            <if test="bo.applyStartTime != null">
                and m.apply_time &gt;= #{bo.applyStartTime}
            </if>
            <if test="bo.applyEndTime != null">
                and m.apply_time &lt;= #{bo.applyEndTime}
            </if>


            <if test="bo.searchValue != null and bo.searchValue != ''">
                and (
                m.doc_id like concat('%', #{bo.searchValue}, '%')
                OR m.doc_name like concat('%', #{bo.searchValue}, '%')
                OR m.user_name like concat('%', #{bo.searchValue}, '%')
                OR w.doc_name like concat('%', #{bo.searchValue}, '%')
                )
            </if>
            <if test="ids != null and ids.size > 0">
                and m.id in
                <foreach collection="ids" item="id" index="index" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
        ORDER BY m.apply_time desc
    </select>

    <select id="getDocTypeByDocId" resultType="string">
        select doc_class from doc_standard where id = #{docId}
    </select>

    <select id="getDocVersionByDocId" resultType="string">
        select version_value from doc_version where doc_id = #{docId} and `status` = 1 LIMIT 1
    </select>

    <select id="getApplyIdByDocId" resultType="string">
        select id from doc_modify_apply where  doc_id = #{docId} and change_type != 'DISUSE' order by apply_time desc limit 1
    </select>


    <select id="getApplyModifyList" resultType="com.rzdata.process.domain.vo.ModifyApplyVo">
        SELECT
        (@row_number := @row_number + 1) as index_num,
        m.*,
        w.proc_inst_id,
        art.NAME_ as act_task_name,
        art.ASSIGNEE_USER_NAME as assignee_user_name,
        su.nick_name,
        w.change_type type,
        sd.dept_name as deptName
        FROM
        doc_modify_apply m
        LEFT JOIN doc_workflow_apply_log w ON if (m.batch_id is null,m.id,m.batch_id) = w.id
        LEFT JOIN sys_user su ON m.user_name = su.user_name
        LEFT JOIN sys_dept sd ON m.dept_id = sd.dept_id
        LEFT JOIN (SELECT PROC_INST_ID_,NAME_,GROUP_CONCAT(ASSIGNEE_USER_NAME ORDER BY ASSIGNEE_USER_NAME SEPARATOR ', ') AS ASSIGNEE_USER_NAME FROM ${bpmDataSource}.ACT_RU_TASK GROUP BY PROC_INST_ID_,NAME_) art ON art.PROC_INST_ID_ = w.proc_inst_id
        <where>
            <if test="bo.changeType != null and bo.changeType != ''">
                and m.change_type = #{bo.changeType}
            </if>
            <if test="bo.docClass != null and bo.docClass != ''">
                and m.doc_class = #{bo.docClass}
            </if>
            <if test="bo.docName != null and bo.docName != ''">
                and m.doc_name like concat('%', #{bo.docName}, '%')
            </if>
            <if test="bo.docId != null and bo.docId != ''">
                and m.doc_id like concat('%', #{bo.docId}, '%')
            </if>
            <if test="bo.nickName != null and bo.nickName != ''">
                and su.nick_name like concat('%', #{bo.nickName}, '%')
            </if>
            <if test="bo.userName != null and bo.userName != ''">
                and m.user_name like concat('%', #{bo.userName}, '%')
            </if>
            <if test="bo.recordStatus != null and bo.recordStatus != ''">
                and m.record_status = #{bo.recordStatus}
            </if>
            <if test="bo.params.startTime != null and bo.params.startTime != ''">
                and m.apply_time >= #{bo.params.startTime}
            </if>
            <if test="bo.params.endTime != null and bo.params.endTime != ''">
                and m.apply_time &lt;= #{bo.params.endTime}
            </if>
            <if test="bo.dataType != null and bo.dataType != ''">
                and m.data_type = #{bo.dataType}
            </if>
            <if test="bo.partNumber != null and bo.partNumber != ''">
                and m.part_number = #{bo.partNumber}
            </if>
            <if test="bo.partRemark != null and bo.partRemark != ''">
                and m.part_remark = #{bo.partRemark}
            </if>
            <if test="bo.secDeptId != null and bo.secDeptId != ''">
                and (m.dept_id = #{bo.secDeptId} or
                find_in_set(m.dept_id,(select ancestors from sys_dept where dept_id = #{bo.secDeptId})))
            </if>
        </where>
        ORDER BY m.apply_time desc
    </select>
</mapper>

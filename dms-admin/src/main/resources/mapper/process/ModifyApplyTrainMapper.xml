<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rzdata.process.mapper.ModifyApplyTrainMapper">

    <resultMap type="com.rzdata.process.domain.ModifyApplyTrain" id="ModifyApplyTrainResult">
        <result property="id" column="id"/>
        <result property="applyId" column="apply_id"/>
        <result property="fileIds" column="file_ids"/>
        <result property="userName" column="user_name"/>
        <result property="deptId" column="dept_id"/>
        <result property="trainTime" column="train_time"/>
        <result property="isDeleted" column="is_deleted"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="docId" column="doc_id"/>
        <result property="versionId" column="version_id"/>
    </resultMap>

    <select id="validateRequired" resultType="com.rzdata.process.domain.vo.ModifyApplyTrainVo" parameterType="com.rzdata.process.domain.bo.ModifyApplyTrainBo">
        select dma.id apply_id,dma.doc_name,count(dmat.apply_id) num from doc_modify_apply dma
        LEFT JOIN (select * from doc_modify_apply_train where type = #{bo.type}) dmat
        on dma.id = dmat.apply_id
       <where>
           (dmat.type = #{bo.type} or dmat.type is null)
           <if test="bo.batchId != null and bo.batchId != ''">
               and dma.batch_id = #{bo.batchId}
           </if>
           <if test="bo.applyId != null and bo.applyId != ''">
               and dma.id = #{bo.applyId}
           </if>
       </where>
        group by dma.id
    </select>
</mapper>

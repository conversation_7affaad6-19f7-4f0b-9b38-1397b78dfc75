<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rzdata.process.mapper.OssFileLogMapper">

    <resultMap type="com.rzdata.process.domain.OssFileLog" id="OssFileLogResult">
        <result property="id" column="id"/>
        <result property="tenantId" column="tenant_id"/>
        <result property="docStandardId" column="doc_standard_id"/>
        <result property="docVersionId" column="doc_version_id"/>
        <result property="docName" column="doc_name"/>
        <result property="version" column="version"/>
        <result property="invokeType" column="invoke_type"/>
        <result property="beginTime" column="begin_time"/>
        <result property="finishTime" column="finish_time"/>
        <result property="costTime" column="cost_time"/>
        <result property="message" column="message"/>
        <result property="status" column="status"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>


</mapper>

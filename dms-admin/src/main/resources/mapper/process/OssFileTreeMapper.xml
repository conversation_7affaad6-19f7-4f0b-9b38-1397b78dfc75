<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rzdata.process.mapper.OssFileTreeMapper">

    <resultMap type="com.rzdata.process.domain.OssFileTree" id="OssFileTreeResult">
        <result property="id" column="id"/>
        <result property="tenantId" column="tenant_id"/>
        <result property="library" column="library"/>
        <result property="name" column="name"/>
        <result property="parentId" column="parent_id"/>
        <result property="nodeType" column="node_type"/>
        <result property="fileType" column="file_type"/>
        <result property="level" column="level"/>
        <result property="sort" column="sort"/>
        <result property="docStandardId" column="doc_standard_id"/>
        <result property="docVersionId" column="doc_version_id"/>
        <result property="basicFileId" column="basic_file_id"/>
        <result property="status" column="status"/>
        <result property="message" column="message"/>
        <result property="externalFileId" column="external_file_id"/>
        <result property="externalFilePath" column="external_file_path"/>
        <result property="externalRev" column="external_rev"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>


</mapper>

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rzdata.process.mapper.PrintFileDetailMapper">

    <resultMap type="com.rzdata.process.domain.PrintFileDetail" id="PrintFileDetailResult">
        <result property="id" column="id"/>
        <result property="bizId" column="biz_id"/>
        <result property="fileName" column="file_name"/>
        <result property="printUserName" column="print_user_name"/>
        <result property="printUserNickName" column="print_user_nick_name"/>
        <result property="createTime" column="create_time"/>
    </resultMap>

    <select id="getListBydistributeId" resultType="com.rzdata.process.domain.vo.PrintFileDetailVo">
        SELECT t1.*,t3.doc_name as docName FROM doc_print_file_detail t1 LEFT JOIN basic_file_pdf t2 ON t1.biz_id = t2.id
                                                  LEFT JOIN doc_distribute t3 ON t3.id = t2.biz_id WHERE t3.id = #{distributeId} ORDER BY t1.create_time DESC
    </select>


</mapper>

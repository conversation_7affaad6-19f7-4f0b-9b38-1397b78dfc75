<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rzdata.process.mapper.PrintLogMapper">

    <resultMap type="com.rzdata.process.domain.vo.PrintLogVo" id="PrintLogResult">
        <result property="id" column="id"/>
<!--        <result property="distributeId" column="distribute_id"/>-->
        <result property="docId" column="doc_id"/>
<!--        <result property="printUserName" column="print_user_name"/>-->
<!--        <result property="printTime" column="print_time"/>-->
<!--        <result property="status" column="status"/>-->
        <result property="deptId" column="dept_id"/>
<!--        <result property="nums" column="nums"/>-->
<!--        <result property="receiveUserName" column="receive_user_name"/>-->
<!--        <result property="receiveTime" column="receive_time"/>-->
        <result property="versionValue" column="version_value"/>
        <result property="versionId" column="version_id"/>
        <result property="applyId" column="apply_id"/>
        <result property="deptName" column="dept_name"/>
        <result property="docName" column="doc_name"/>
        <result property="docClass" column="doc_class"/>
        <result property="startDate" column="start_date"/>
        <result property="printNums" column="print_nums"/>
        <result property="distributeNums" column="distribute_nums"/>
        <result property="distributeTime" column="distribute_time"/>
        <result property="compileDeptId" column="compile_dept_id"/>
        <result property="compileDeptName" column="compile_dept_name"/>



        <!--        <result property="efficientStartDate" column="start_date"/>-->
<!--        <result property="efficientEndDate" column="efficientEndDate"/>-->
<!--        <result property="typeName" column="class_name"/>-->
    </resultMap>

    <select id="selectDetailById" resultMap="PrintLogResult">
        SELECT l.*,
               d.apply_id,
               d.nums,
               d.receive_user_name,
               d.receive_time,
               d.version_value,
               d.version_id,
               s.dept_name,
               ds.doc_name,
               ds.doc_class,
               v.start_date as efficientStartDate,
               v.end_date as efficientEndDate,
               c.class_name
        FROM doc_print_log l
                 LEFT JOIN doc_distribute_log d ON l.distribute_id = d.id
                 LEFT JOIN doc_standard ds ON l.doc_id = ds.id
                 LEFT JOIN sys_dept s ON v.dept_id = s.dept_id
                 LEFT JOIN doc_version v ON d.version_id = v.id
                 LEFT JOIN basic_doc_class c ON ds.doc_class = c.id
        WHERE l.id = #{id}
    </select>

    <select id="selectPrintLogPage" resultMap="PrintLogResult">
        SELECT l.*,
        d.apply_id,
        d.nums,
        d.receive_user_name,
        d.receive_time,
        d.version_value,
        d.version_id,
        s.dept_name,
        ds.doc_name,
        ds.doc_class,
        v.start_date as efficientStartDate,
        v.end_date as efficientEndDate
        FROM doc_print_log l
        LEFT JOIN doc_distribute_log d ON l.distribute_id = d.id
        LEFT JOIN doc_standard ds ON l.doc_id = ds.id
        LEFT JOIN sys_dept s ON v.dept_id = s.dept_id
        LEFT JOIN doc_version v ON d.version_id = v.id
        <where>
            <if test="bo.status != '' and bo.status != null">
                and l.status = #{bo.status}
            </if>
            <if test="bo.docId != '' and bo.docId != null">
                and l.doc_id = #{bo.docId}
            </if>
            <if test="bo.printUserName != null and bo.printUserName != ''">
                and bo.printUserName like concat('%', #{bo.printUserName}, '%')
            </if>
            <if test="bo.searchValue != null and bo.searchValue != ''">
                and (ds.doc_name like concat('%', #{bo.searchValue} ,'%')
                    or l.doc_id = #{bo.searchValue} )
            </if>
        </where>
        ORDER BY l.print_time DESC
    </select>


    <select id="queryPageList" resultMap="PrintLogResult">
        SELECT l.*,
        v.start_date
        FROM doc_print_log l
        LEFT JOIN doc_version v ON l.version_id = v.id
        <where>
        <!-- 前端传入status=0 表示查询未打印的 -->
            <if test="bo.status != null and bo.status == 0">
                <!-- 已打印数量不等于打印数量(也就是没有全部都打印完) 则为未打印 -->
                and l.print_nums != l.printed_nums
            </if>
            <!-- 前端传入status=1 表示查询已打印的 -->
            <if test="bo.status != null and bo.status == 1">
                <!-- 已打印数量等于打印数量(也就是全部都打印完) 则为已打印 -->
                and l.print_nums = l.printed_nums
            </if>
            <if test="bo.docName != '' and bo.docName != null">
                AND l.doc_name like CONCAT('%', #{bo.docName} ,'%')
            </if>
            <!--文件类型查询条件-->
            <if test="bo.docClass!='' and bo.docClass!=null">
                AND l.doc_class = #{bo.docClass}
            </if>
            <if test="bo.compileDeptId!='' and bo.compileDeptId!=null">
                AND l.compile_dept_id = #{bo.compileDeptId}
            </if>
            <if test="bo.params.startTime != null and bo.params.startTime != ''">
                and l.distribute_time >= #{bo.params.startTime}
            </if>
            <if test="bo.params.endTime != null and bo.params.endTime != ''">
                and l.distribute_time &lt;= #{bo.params.endTime}
            </if>
            <!--分发时间查询条件-->
            <if test="bo.disStartTime!=null and bo.disStartTime!='' and bo.disEndTime!=null and bo.disEndTime!=''">
                AND l.distribute_time BETWEEN #{bo.disStartTime} AND #{bo.disEndTime}
            </if>
            <!--生效日期查询条件-->
            <if test="bo.startTime!=null and bo.startTime!='' and bo.endTime!=null and bo.endTime!=''">
                AND v.start_date BETWEEN #{bo.startTime} AND #{bo.endTime}
            </if>
            <if test="bo.params.dataScope != null and bo.params.dataScope != ''">
                AND ( ${bo.params.dataScope} )
            </if>
        </where>
        ORDER BY l.distribute_time DESC
    </select>
</mapper>

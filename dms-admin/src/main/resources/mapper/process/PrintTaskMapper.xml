<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rzdata.process.mapper.PrintTaskMapper">

    <resultMap type="com.rzdata.process.domain.PrintTask" id="PrintTaskResult">
        <result property="id" column="id"/>
        <result property="docId" column="doc_id"/>
        <result property="docName" column="doc_name"/>
        <result property="versionId" column="version_id"/>
        <result property="printCount" column="print_count"/>
        <result property="status" column="status"/>
        <result property="printerName" column="printer_name"/>
        <result property="printDesc" column="print_desc"/>
        <result property="createBy" column="create_by"/>
        <result property="createName" column="create_name"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateName" column="update_name"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="docType" column="doc_type"/>
        <result property="electronicFileName" column="electronic_file_name"/>
        <result property="classificationNo" column="classification_no"/>
    </resultMap>

    <sql id="selectPrintTaskVo">
        select id, doc_id, doc_name, version_id, print_count, status, printer_name, print_desc,
        doc_type, electronic_file_name, classification_no,
        create_by, create_name, create_time, update_by, update_name, update_time, remark
        from doc_print_task
    </sql>

    <select id="selectPrintTaskPage" resultType="com.rzdata.process.domain.vo.PrintTaskVo">
        <include refid="selectPrintTaskVo"/>
        <where>
            <if test="bo.docId != null and bo.docId != ''">
                AND doc_id = #{bo.docId}
            </if>
            <if test="bo.docName != null and bo.docName != ''">
                AND doc_name like concat('%', #{bo.docName}, '%')
            </if>
            <if test="bo.versionId != null and bo.versionId != ''">
                AND version_id = #{bo.versionId}
            </if>
            <if test="bo.status != null and bo.status != ''">
                AND status = #{bo.status}
            </if>
            <if test="bo.printerName != null and bo.printerName != ''">
                AND printer_name like concat('%', #{bo.printerName}, '%')
            </if>
            <if test="bo.printDesc != null and bo.printDesc != ''">
                AND print_desc like concat('%', #{bo.printDesc}, '%')
            </if>
            <if test="bo.createBy != null and bo.createBy != ''">
                AND create_by = #{bo.createBy}
            </if>
        </where>
        order by create_time desc
    </select>

</mapper> 
<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rzdata.process.mapper.ProjectBaseApplyMapper">

    <resultMap type="com.rzdata.process.domain.ProjectBaseApply" id="ProjectBaseApplyResult">
        <result property="id" column="id"/>
        <result property="tenantId" column="tenant_id"/>
        <result property="procInstId" column="proc_inst_id"/>
        <result property="bpmnStatus" column="bpmn_status"/>
        <result property="createBy" column="create_by"/>
        <result property="createName" column="create_name"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
    </resultMap>


</mapper>

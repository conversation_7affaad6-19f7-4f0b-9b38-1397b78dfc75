<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rzdata.process.mapper.ReadLogMapper">

    <resultMap type="com.rzdata.process.domain.vo.ReadLogVo" id="ReadLogResult">
        <result property="id" column="id"/>
        <result property="docId" column="doc_id"/>
        <result property="versionValue" column="version_value"/>
        <result property="userName" column="user_name"/>
        <result property="deptId" column="dept_id"/>
        <result property="createTime" column="create_time"/>
        <result property="downLoad" column="down_load"/>
        <result property="preview" column="preview"/>
        <result property="externalFileId" column="external_file_id"/>
        <result property="deptName" column="dept_name"/>
        <result property="docName" column="doc_name"/>
        <result property="nickName" column="nick_name"/>
        <result property="fileName" column="file_name"/>
    </resultMap>


</mapper>

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rzdata.process.mapper.RecoveryLogItemMapper">

    <resultMap type="com.rzdata.process.domain.vo.RecoveryLogItemVo" id="RecoveryLogItemResult">
        <result property="id" column="id"/>
        <result property="docId" column="doc_id"/>
        <result property="createTime" column="create_time"/>
        <result property="versionId" column="version_id"/>
        <result property="updateTime" column="update_time"/>
        <result property="applyId" column="apply_id"/>
        <result property="deptId" column="dept_id"/>
        <result property="deptName" column="dept_name"/>
    </resultMap>

    <select id="queryPageList" resultType="com.rzdata.process.domain.vo.RecoveryLogItemVo">
        select l.*,
               d.receive_user_name,
               d.receive_nick_name,
               d.receive_time,
               p.status as print_status
        from  doc_recovery_log_item l
        left join doc_distribute_item d on l.distribute_item_id = d.id
        left join doc_print_log_item p on l.distribute_item_id = p.distribute_item_id
        <where>
            <if test="bo.recoveryId!=null and bo.recoveryId!=''">
                l.recovery_id = #{bo.recoveryId}
            </if>
        </where>
        order by l.create_time desc
    </select>

</mapper>

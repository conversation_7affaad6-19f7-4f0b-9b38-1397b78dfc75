<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rzdata.process.mapper.ReissueApplyMapper">

    <resultMap type="com.rzdata.process.domain.ReissueApply" id="ReissueApplyResult">
        <result property="id" column="id"/>
        <result property="applyTitle" column="apply_title"/>
        <result property="docId" column="doc_id"/>
        <result property="deptId" column="dept_id"/>
        <result property="distributeItemIds" column="distribute_item_ids"/>
        <result property="userName" column="user_name"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="applyReason" column="apply_reason"/>
        <result property="status" column="status"/>
        <result property="versionValue" column="version_value"/>
        <result property="versionId" column="version_id"/>
    </resultMap>

    <select id="selectReissueApplyList" resultType="map">
        SELECT
        m.id,
        m.apply_title as applyTitle,
        m.doc_id as docId,
        m.dept_id as deptId,
        m.distribute_item_ids,
        m.user_name as userName,
        m.create_time as createTime,
        w.update_time as updateTime,
        m.apply_reason as applyReason,
        m.status,
        w.apply_class AS applyClass,
        w.apply_status AS applyStatus,
        w.doc_name AS docName,
        w.doc_id AS docId,
        w.doc_class AS docClass,
        w.version_id AS versionId,
        w.version_value AS versionValue,
        w.sender AS sender,
        d.dept_name AS deptName
        FROM
        doc_reissue_apply m
        LEFT JOIN doc_workflow_apply_log w ON m.id = w.id
        LEFT JOIN sys_dept d ON m.dept_id = d.dept_id
        <where>
            <!--发起人-->
            <if test="bo.startUserId != null and bo.startUserId != ''">
                and w.sender = #{bo.startUserId}
            </if>
            <if test="bo.procDefKey != null and bo.procDefKey != ''">
                and w.proc_def_key like  concat(#{bo.procDefKey}, '%')
            </if>
            <if test="bo.applyTitle != null and bo.applyTitle != ''">
                and m.apply_title like concat('%', #{bo.applyTitle}, '%')
            </if>
            <if test="bo.docId != null and bo.docId != ''">
                and m.doc_id like concat('%', #{bo.docId}, '%')
            </if>
            <if test="bo.deptId != null">
                and m.dept_id = #{bo.deptId}
            </if>
            <if test="bo.userName != null and bo.userName != ''">
                and m.user_name like concat('%', #{bo.searchValue}, '%')
            </if>
            <if test="bo.applyStartTime != null">
                and m.create_time &gt;= #{bo.applyStartTime}
            </if>
            <if test="bo.applyEndTime != null">
                and m.create_time &lt;= #{bo.applyStartTime}
            </if>
            <if test="bo.searchValue != null and bo.searchValue != ''">
                and (
                m.doc_id like concat('%', #{bo.searchValue}, '%')
                OR m.apply_title like concat('%', #{bo.searchValue}, '%')
                OR m.user_name like concat('%', #{bo.searchValue}, '%')
                OR w.doc_name like concat('%', #{bo.searchValue}, '%')
                )
            </if>
            <if test="bo.docName != null and bo.docName != ''">
                AND w.doc_name like concat('%', #{bo.docName}, '%')
            </if>
            <if test="ids != null and ids.size > 0">
                and m.id in
                <foreach collection="ids" item="id" index="index" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
        ORDER BY m.create_time desc
    </select>

    <select id="selectReissueApplyList4New" resultType="map">
        SELECT
        m.id,
        m.apply_title as applyTitle,
        m.doc_id as docId,
        m.dept_id as deptId,
        m.distribute_item_ids,
        m.user_name as userName,
        m.create_time as createTime,
        m.update_time as updateTime,
        m.apply_reason as applyReason,
        m.status,
        w.apply_class AS applyClass,
        w.apply_status AS applyStatus,
        w.doc_name AS docName,
        w.doc_id AS docId,
        w.doc_class AS docClass,
        w.version_id AS versionId,
        w.version_value AS versionValue,
        w.sender AS sender,
        d.dept_name AS deptName
        FROM
        doc_reissue_apply m
        LEFT JOIN doc_workflow_apply_log w ON m.id = w.id
        LEFT JOIN sys_dept d ON m.dept_id = d.dept_id
        <where>
            <!--发起人-->
            <if test="bo.startUserId != null and bo.startUserId != ''">
                and w.sender = #{bo.startUserId}
            </if>
            <if test="bo.procDefKey != null and bo.procDefKey != ''">
                and w.proc_def_key like  concat(#{bo.procDefKey}, '%')
            </if>
            <if test="bo.applyTitle != null and bo.applyTitle != ''">
                and m.apply_title like concat('%', #{bo.applyTitle}, '%')
            </if>
            <if test="bo.docId != null and bo.docId != ''">
                and m.doc_id like concat('%', #{bo.docId}, '%')
            </if>
            <if test="bo.deptId != null">
                and m.dept_id = #{bo.deptId}
            </if>
            <if test="bo.userName != null and bo.userName != ''">
                and m.user_name like concat('%', #{bo.searchValue}, '%')
            </if>
            <if test="bo.applyStartTime != null">
                and m.create_time &gt;= #{bo.applyStartTime}
            </if>
            <if test="bo.applyEndTime != null">
                and m.create_time &lt;= #{bo.applyStartTime}
            </if>
            <if test="bo.searchValue != null and bo.searchValue != ''">
                and (
                m.doc_id like concat('%', #{bo.searchValue}, '%')
                OR m.apply_title like concat('%', #{bo.searchValue}, '%')
                OR m.user_name like concat('%', #{bo.searchValue}, '%')
                OR w.doc_name like concat('%', #{bo.searchValue}, '%')
                )
            </if>
            <if test="bo.docName != null and bo.docName != ''">
                AND w.doc_name like concat('%', #{bo.docName}, '%')
            </if>
            <if test="ids != null and ids.size > 0">
                and m.id in
                <foreach collection="ids" item="id" index="index" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
        ORDER BY m.create_time desc
    </select>
    <select id="listByDisItemId" resultType="com.rzdata.process.domain.ReissueApply">
        select * from doc_reissue_apply r where find_in_set(#{disItemId},r.distribute_item_ids)
    </select>


</mapper>

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rzdata.process.mapper.ReviewApplyItemMapper">

    <resultMap type="com.rzdata.process.domain.vo.ReviewApplyItemVo" id="ReviewApplyItemResult">
        <result property="id" column="id"/>
        <result property="applyId" column="apply_id"/>
        <result property="docId" column="doc_id"/>
        <result property="docName" column="doc_name"/>
        <result property="docClass" column="doc_class"/>
        <result property="versionValue" column="version_value"/>
        <result property="versionId" column="version_id"/>
        <!--<association property="workflowLogVo" column="flowId" javaType="com.rzdata.process.domain.vo.WorkflowLogVo"
                     resultMap="WorkflowLogResult"/>-->
    </resultMap>

    <select id="queryItemList" resultType="com.rzdata.process.domain.vo.ReviewApplyItemVo">
        SELECT
            i.id,
            i.apply_id,
            v.version_value,
            v.id versionId,
            v.doc_id docId,
            s.doc_class,
            v.doc_name,
            case
            when s.encrypt_file_id is null and s.merge_file_id is null then s.file_id
            when s.encrypt_file_id is null or s.encrypt_file_id = '' then s.merge_file_id
            else s.encrypt_file_id end as file_id
        FROM
            doc_review_apply_item i
                LEFT JOIN doc_version v ON i.version_id = v.id
                LEFT JOIN doc_standard s ON i.doc_id = s.id
        <where>
            <if test="bo.applyId != null and bo.applyId != ''">
                and i.apply_id  = #{bo.applyId}
            </if>
            <if test="bo.docName != null and bo.docName != ''">
                and i.doc_name like concat('%', #{bo.docName}, '%')
            </if>

            <if test="bo.docClass != null and bo.docClass != ''">
                and i.doc_class = #{bo.docClass}
            </if>
        </where>
    </select>

</mapper>

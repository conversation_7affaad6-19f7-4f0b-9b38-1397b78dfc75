<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rzdata.process.mapper.SearchMapper">

    <resultMap type="com.rzdata.process.domain.vo.SearchVo" id="VersionResult">
        <result property="id" column="id"/>
        <result property="standardId" column="standard_id"/>
        <result property="versionValue" column="version_value"/>
        <result property="docId" column="doc_id"/>
        <result property="applyId" column="apply_id"/>
        <result property="status" column="status"/>
        <result property="dataType" column="data_type"/>
        <result property="docClass" column="doc_class"/>
        <result property="docName" column="doc_name"/>
        <result property="deptId" column="dept_id"/>
        <result property="releaseTime" column="release_time"/>
    </resultMap>

    <!--搜索查询-->
    <select id="selectSearchEntityPage" resultType="com.rzdata.process.domain.vo.SearchVo">
        select * from (
            SELECT id, standard_id, version_value, doc_id, apply_id, status, data_type, doc_class, doc_name, dept_id, release_time
            FROM (
                     SELECT
                         v.id,
                         v.standard_id,
                         v.version_value,
                         v.doc_id,
                         v.apply_id,
                         v.status,
                         v.data_type,
                         v.release_time,
                         s.doc_class,
                         v.doc_name,
                         v.dept_id
                     FROM doc_version v
                     LEFT JOIN doc_standard s ON v.standard_id = s.id
                     <where>
                         <choose>
                             <when test="bo.isSpecialCharacters != null and bo.isSpecialCharacters">
                                 <if test="bo.nameOrCode != null and bo.nameOrCode != ''">
                                     and v.doc_id LIKE CONCAT('%', #{bo.nameOrCode}, '%')
                                     or v.doc_name LIKE CONCAT('%', #{bo.nameOrCode}, '%')
                                 </if>
                             </when>
                             <otherwise>
                                 <if test="bo.nameOrCode != null and bo.nameOrCode != ''">
                                     and MATCH (v.doc_id) AGAINST ( #{bo.nameOrCode} in boolean mode)
                                     or MATCH (v.doc_name) AGAINST ( #{bo.nameOrCode} in boolean mode)
                                 </if>
                             </otherwise>
                         </choose>
                     </where>
                 ) version_standard
            UNION ALL
            SELECT id, null AS standard_id, version AS version_value, doc_code AS doc_id, null AS apply_id, status, 'external' AS data_type, doc_class, doc_name, dept_id, start_time as release_time
            FROM doc_external
            <where>
                <choose>
                    <when test="bo.isSpecialCharacters != null and bo.isSpecialCharacters">
                        <if test="bo.nameOrCode != null and bo.nameOrCode != ''">
                            and doc_name LIKE CONCAT('%', #{bo.nameOrCode}, '%')
                            or doc_code LIKE CONCAT('%', #{bo.nameOrCode}, '%')
                        </if>
                    </when>
                    <otherwise>
                        <if test="bo.nameOrCode != null and bo.nameOrCode != ''">
                            and MATCH (doc_name, doc_code) AGAINST ( #{bo.nameOrCode} in boolean mode)
                        </if>
                    </otherwise>
                </choose>
            </where>
        ) doc_search
        <where>
            <!-- 使用动态 SQL 构建查询条件 -->
            <if test="bo.statusSet != null and bo.statusSet.size() > 0">
                and status in
                <foreach collection="bo.statusSet" item="status" index="index" open="(" separator="," close=")">
                    #{status}
                </foreach>
            </if>
            <if test="bo.typeSet != null and bo.typeSet.size() > 0">
                and data_type in
                <foreach collection="bo.typeSet" item="type" index="index" open="(" separator="," close=")">
                    #{type}
                </foreach>
            </if>
        </where>
        order by release_time desc

    </select>
</mapper>

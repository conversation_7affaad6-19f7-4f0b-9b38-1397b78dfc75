<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rzdata.process.mapper.TenantMapper">
    <resultMap type="com.rzdata.process.domain.Tenant" id="TenantResult">
        <result property="id" column="id"/>
        <result property="tenantName" column="tenant_name"/>
        <result property="address" column="address"/>
        <result property="contact" column="contact"/>
        <result property="contactTel" column="contact_tel"/>
    </resultMap>
    <select id="selectTenantByUser" parameterType="String" resultMap="TenantResult">
         select a.* from basic_tenant a inner join sys_user_tenant b on a.id=b.tenant_id where b.user_id=#{userId}
    </select>

</mapper>

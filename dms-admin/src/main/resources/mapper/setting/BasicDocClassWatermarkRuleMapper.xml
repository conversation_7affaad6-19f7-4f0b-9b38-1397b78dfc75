<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rzdata.setting.mapper.BasicDocClassWatermarkRuleMapper">

    <resultMap type="com.rzdata.setting.domain.BasicDocClassWatermarkRule" id="BasicDocClassWatermarkRuleResult">
        <result property="id" column="id"/>
        <result property="ruleName" column="rule_name"/>
        <result property="ruleDetails" column="rule_details"/>
        <result property="sort" column="sort"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectBasicDocClassWatermarkRuleVo">
        select id, rule_name, rule_details, sort,
        create_by, create_time, update_by, update_time
        from basic_doc_class_watermark_rule
    </sql>

</mapper> 
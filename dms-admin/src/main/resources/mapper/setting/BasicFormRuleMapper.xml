<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rzdata.setting.mapper.BasicFormRuleMapper">

    <resultMap type="com.rzdata.setting.domain.BasicFormRule" id="BasicFormRuleResult">
        <result property="id" column="id"/>
        <result property="ruleName" column="rule_name"/>
        <result property="ruleDetails" column="rule_details"/>
        <result property="tenantId" column="tenant_id"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>


</mapper>

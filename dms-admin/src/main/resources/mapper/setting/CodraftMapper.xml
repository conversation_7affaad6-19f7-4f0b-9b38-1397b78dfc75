<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rzdata.setting.mapper.CodraftMapper">

    <resultMap type="com.rzdata.setting.domain.Codraft" id="CodraftResult">
        <result property="id" column="id"/>
        <result property="codraftName" column="codraft_name"/>
        <result property="fileId" column="file_id"/>
        <result property="codraftStatus" column="codraft_status"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="position" column="position"/>
        <result property="sort" column="sort"/>
        <result property="classId" column="class_id"/>
    </resultMap>

    <delete id="delByBizId">
        delete  from basic_codraft where biz_id = #{bizId}
    </delete>

    <select id="getVoById" resultType="com.rzdata.setting.domain.Codraft">
        select * from biaoming where id = #{id}
    </select>

    <select id="selectByClassId" resultType="com.rzdata.setting.domain.Codraft">
        select t1.* from basic_codraft t1
        left join combined_file_class t2 on t1.biz_id = t2.combined_mgr_id
        where t2.file_class_id = #{classId} and t1.delete_flag = '0';
    </select>


</mapper>
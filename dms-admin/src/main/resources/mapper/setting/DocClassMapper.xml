<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rzdata.setting.mapper.DocClassMapper">

    <resultMap type="com.rzdata.setting.domain.DocClass" id="DocClassResult">
        <result property="id" column="id"/>
        <result property="className" column="class_name"/>
        <result property="parentClassId" column="parent_class_id"/>
        <result property="classStatus" column="class_status"/>
        <result property="classLevel" column="class_level"/>
        <result property="expiration" column="expiration"/>
        <result property="reviewCycle" column="review_cycle"/>
        <result property="checkType" column="check_type"/>
        <result property="encryptType" column="encrypt_type"/>
        <result property="yNPrint" column="y_n_print"/>
        <result property="yNRecyle" column="y_n_recyle"/>
        <result property="mergeDocConfig" column="merge_doc_config"/>
        <result property="yNOpenCode" column="y_n_open_code"/>
        <result property="yNApplyCode" column="y_n_apply_code"/>
        <result property="distributeId" column="distribute_id"/>
        <result property="openDistribute" column="open_distribute"/>
        <result property="applyDistribute" column="apply_distribute"/>
        <result property="applyPrescription" column="apply_prescription"/>
        <result property="openPrescription" column="open_prescription"/>
        <result property="sort" column="sort"/>
        <result property="openReview" column="open_review"/>
        <result property="applyReview" column="apply_review"/>

        <result property="openMerge" column="open_merge"/>
        <result property="applyMerge" column="apply_merge"/>

        <result property="signatureType" column="signature_type"/>
        <result property="openSignature" column="open_signature"/>
        <result property="applySignature" column="apply_signature"/>

        <result property="codeId" column="code_id"/>
        <result property="fileId" column="file_id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>
    <select id="getClassByPerm" resultType="com.rzdata.setting.domain.vo.DocClassVo">
        select * from (
                          with t as(select a.permission,a.is_file_type,b.id ancestor_id,a.class_code,a.data_type,a.class_type,a.sort, a.class_level, a.id,a.ancestors,a.class_name,a.parent_class_id,a.class_status,c.class_level ancestor_level,b.apply_flag,b.open_flag from basic_doc_class a
                           left join basic_doc_class_purview b on FIND_IN_SET(b.id,a.ancestors) and( (b.open_flag = 'Y' and b.apply_flag='Y')or (b.id = a.id and b.open_flag='Y'))
                           left join basic_doc_class c on b.id = c.id)
                          select if(tt.ancestor_id is null,true,EXISTS(select id from basic_doc_class_purview_detail c LEFT JOIN sys_dept d ON c.type = 'dept'
        AND d.dept_id in <foreach collection="deptIds" separator="," item="item" open="(" close=")">
        #{item}
    </foreach> where
        ((c.type='dept' AND (FIND_IN_SET( c.VALUE, d.ancestors ) or c.value=d.dept_id) ) or
        (c.type='role' and c.value in <foreach collection="roleIds" separator="," item="item" open="(" close=")">
        #{item}
    </foreach>) or
        (c.type='person' and c.value in <foreach collection="persons" separator="," item="item" open="(" close=")">
        #{item}
    </foreach>))
                                                                    and tt.ancestor_id = c.purview_id)) purview,tt.* from
( SELECT ab.*  FROM (select * from t having 1 order by ancestor_level desc) ab group by ab.id ) tt ) s
        ${ew.customSqlSegment}
        </select>

    <!-- 查询dms_zonci_sh库中有效的文件分类 -->
    <select id="selectZonciActiveDocClass" resultType="com.rzdata.setting.domain.vo.DocClassVo">
        SELECT
            id,
            class_name as className,
            parent_class_id as parentClassId,
            class_status as classStatus,
            class_level as classLevel,
            sort,
            create_time as createTime,
            update_time as updateTime
        FROM #{dbName}.basic_doc_class
        WHERE class_status = '1'
        ORDER BY sort ASC, create_time DESC
    </select>
</mapper>

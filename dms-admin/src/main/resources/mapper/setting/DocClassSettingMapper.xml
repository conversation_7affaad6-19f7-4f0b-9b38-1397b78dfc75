<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rzdata.setting.mapper.DocClassSettingMapper">

    <resultMap type="com.rzdata.setting.domain.DocClassSetting" id="DocClassSettingResult">
        <result property="id" column="id"/>
        <result property="docClass" column="doc_class"/>
        <result property="type" column="type"/>
        <result property="applyFlag" column="apply_flag"/>
        <result property="openFlag" column="open_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <select id="getSettingStatus" resultType="com.rzdata.setting.domain.DocClassSetting">
        SELECT
            bdc.id doc_class,
            t.type,
            t.class_level,
            t.rule_id,
            t.setting_id
        FROM
            basic_doc_class bdc left join
            <!-- 根据类型查询所有开启的配置 -->
            (
                SELECT
                    bdc.id doc_class_id,
                    bdc.class_level,
                    bdct.*
                FROM
                    basic_doc_class bdc
                    LEFT JOIN basic_doc_class_setting bdct ON bdc.id = bdct.doc_class
                WHERE
                    bdct.type = #{type}
                  AND bdc.class_status = '1'
                  AND bdct.open_flag = 'Y'
            ) t
            <!-- 关联祖级开启继承的或者自己开启的 -->
            on (FIND_IN_SET(t.doc_class_id,bdc.ancestors) and t.apply_flag = 'Y') or t.doc_class_id=bdc.id
        <where>
            <if test="docClass!=null and docClass!=''">
                bdc.id = #{docClass}
            </if>
        </where>
        <!-- 优先应用层级大的 -->
        ORDER BY t.class_level desc
    </select>
</mapper>

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rzdata.system.mapper.ActHiTaskinstMapper">

    <resultMap type="com.rzdata.system.domain.ActHiTaskinst" id="ActHiTaskinstResult">
        <result property="id" column="ID_"/>
        <result property="procDefId" column="PROC_DEF_ID_"/>
        <result property="taskDefKey" column="TASK_DEF_KEY_"/>
        <result property="procInstId" column="PROC_INST_ID_"/>
        <result property="executionId" column="EXECUTION_ID_"/>
        <result property="name" column="NAME_"/>
        <result property="parentTaskId" column="PARENT_TASK_ID_"/>
        <result property="description" column="DESCRIPTION_"/>
        <result property="owner" column="OWNER_"/>
        <result property="assignee" column="ASSIGNEE_"/>
        <result property="startTime" column="START_TIME_"/>
        <result property="claimTime" column="CLAIM_TIME_"/>
        <result property="endTime" column="END_TIME_"/>
        <result property="duration" column="DURATION_"/>
        <result property="deleteReason" column="DELETE_REASON_"/>
        <result property="priority" column="PRIORITY_"/>
        <result property="dueDate" column="DUE_DATE_"/>
        <result property="formKey" column="FORM_KEY_"/>
        <result property="category" column="CATEGORY_"/>
        <result property="tenantId" column="TENANT_ID_"/>
        <result property="procTitle" column="PROC_TITLE"/>
        <result property="SENDER" column="SENDER"/>
        <result property="preTaskDefKey" column="PRE_TASK_DEF_KEY"/>
        <result property="preTaskId" column="PRE_TASK_ID"/>
        <result property="preTaskDefName" column="PRE_TASK_DEF_NAME"/>
        <result property="actionType" column="ACTION_TYPE"/>
        <result property="topExecutionId" column="TOP_EXECUTION_ID_"/>
        <result property="sendUserName" column="SEND_USER_NAME"/>
        <result property="senderOrgId" column="SENDER_ORG_ID"/>
        <result property="senderOrgName" column="SENDER_ORG_NAME"/>
        <result property="assigneeUserName" column="ASSIGNEE_USER_NAME"/>
        <result property="assigneeOrgId" column="ASSIGNEE_ORG_ID"/>
        <result property="assigneeOrgName" column="ASSIGNEE_ORG_NAME"/>
        <result property="assigneeUserId" column="ASSIGNEE_USER_ID"/>
        <result property="sendUserId" column="SEND_USER_ID"/>
        <result property="procDefName" column="PROC_DEF_NAME"/>
    </resultMap>

    <select id="getList" resultMap="ActHiTaskinstResult">
        SELECT t1.ID_,t1.PROC_DEF_ID_,t1.TASK_DEF_KEY_,t1.EXECUTION_ID_,t1.NAME_,t1.PARENT_TASK_ID_,t1.DESCRIPTION_,t1.OWNER_,t1.ASSIGNEE_,t1.START_TIME_
        FROM act_hi_taskinst t1
        <where>
            1=1
            <if test="bo.id != null and bo.id != ''">
                and t1.ID_ = #{bo.id}
            </if>
            <if test="bo.sendUserId != null and bo.sendUserId != ''">
                and t1.SEND_USER_ID = #{bo.sendUserId}
            </if>
            <if test="bo.minute != null and bo.minute != ''">
                and t1.END_TIME_ > DATE_SUB(NOW(), INTERVAL #{bo.minute} MINUTE)
            </if>
            and t1.DELETE_REASON_ in ('completed','deleted')
        </where>
        order by t1.START_TIME_ desc
    </select>

    <select id="getUniteworkTaskList" resultType="com.rzdata.process.domain.vo.UniteworkTaskVo">
        SELECT
            t1.UNITE_PROCDEFNAME as proc_def_name,
            t1.UNITE_TITLE as title,
            t1.UNITE_CUR_ACTDEFNAME as cur_act_name,
            t1.UNITE_START_USERNAME as start_user_name,
            t1.UNITE_SEND_TIME as send_time,
            t1.UNITE_URL as url,
            t2.doc_id,
            t2.version_value,
            t3.NAME_ as act_task_name,
            t3.ASSIGNEE_USER_NAME as assignee_user_name,
            t2.proc_status,
            t1.UNITE_STATUS as status
        FROM
            ${bpmDataSource}.T_UNITEWORK_HISTORY t1
        LEFT JOIN doc_workflow_apply_log t2 ON t1.UNITE_PROCINSTID = t2.proc_inst_id
        LEFT JOIN (SELECT PROC_INST_ID_,NAME_,GROUP_CONCAT(ASSIGNEE_USER_NAME ORDER BY ASSIGNEE_USER_NAME SEPARATOR ', ') AS ASSIGNEE_USER_NAME FROM ${bpmDataSource}.ACT_RU_TASK GROUP BY PROC_INST_ID_,NAME_) t3 ON t3.PROC_INST_ID_ = t1.UNITE_PROCINSTID
        <where>
            <if test="bo.procStatus != null and bo.procStatus != ''">
                and t2.proc_status = #{bo.procStatus}
            </if>
            <if test="bo.status != null and bo.status != ''">
                and t1.UNITE_STATUS = #{bo.status}
            </if>
            <if test="bo.recUserId != null and bo.recUserId != ''">
                and t1.UNITE_REC_USERID = #{bo.recUserId}
            </if>
            <if test="bo.startUserName != null and bo.startUserName != ''">
                and t1.UNITE_START_USERNAME like concat('%', #{bo.startUserName}, '%')
            </if>
            <if test="bo.docId != null and bo.docId != ''">
                and t2.doc_id like concat('%', #{bo.docId}, '%')
            </if>
            <if test="bo.procDefName != null and bo.procDefName != ''">
                and t1.UNITE_PROCDEFNAME = #{bo.procDefName}
            </if>
            <if test="bo.procDefKey != null and bo.procDefKey != ''">
                and t1.UNITE_PROCDEFKEY = #{bo.procDefKey}
            </if>
            <if test="bo.title != null and bo.title != ''">
                and t1.UNITE_TITLE like concat('%', #{bo.title}, '%')
            </if>
            <if test="bo.params.startTime != null and bo.params.startTime != ''">
                and t1.UNITE_SEND_TIME >= #{bo.params.startTime}
            </if>
            <if test="bo.params.endTime != null and bo.params.endTime != ''">
                and t1.UNITE_SEND_TIME &lt;= #{bo.params.endTime}
            </if>
        </where>
        ORDER BY t1.UNITE_SEND_TIME desc
    </select>

</mapper>

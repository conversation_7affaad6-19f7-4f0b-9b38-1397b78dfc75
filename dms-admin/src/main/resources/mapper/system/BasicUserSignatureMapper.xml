<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rzdata.system.mapper.BasicUserSignatureMapper">

    <resultMap type="com.rzdata.system.domain.BasicUserSignature" id="BasicUserSignatureResult">
        <result property="id" column="id"/>
        <result property="userCode" column="user_code"/>
        <result property="userName" column="user_name"/>
        <result property="fileId" column="file_id"/>
        <result property="status" column="status"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="templateName" column="template_name"/>
        <result property="deleteFlag" column="delete_flag"/>
    </resultMap>

    <select id="getPageList" resultType="com.rzdata.system.domain.vo.BasicUserSignatureVo">
        SELECT t1.id,t1.user_code,t1.user_name,t1.file_id,t1.status,t1.template_name,t1.delete_flag,t1.create_time,t1.update_time,t3.dept_name
        FROM basic_user_signature t1 LEFT JOIN sys_user t2 ON t1.user_code = t2.user_name LEFT JOIN sys_dept t3 ON t2.dept_id = t3.dept_id
        <where>
            <if test="bo.userCode != null and bo.userCode != ''">
                and t1.user_code like CONCAT('%', #{bo.userCode}, '%')
            </if>
            <if test="bo.acquiesceUserCode != null and bo.acquiesceUserCode != ''">
                and t1.user_code = #{bo.acquiesceUserCode}
            </if>
            <if test="bo.userName != null and bo.userName != ''">
                and t1.user_name like CONCAT('%', #{bo.userName}, '%')
            </if>
            <if test="bo.status != null and bo.status != ''">
                and t1.status = #{bo.status}
            </if>

        </where>
    </select>


</mapper>

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rzdata.system.mapper.ProjectInfoMapper">

    <resultMap type="com.rzdata.system.domain.ProjectInfo" id="ProjectInfoResult">
        <result property="id" column="id"/>
        <result property="tenantId" column="tenant_id"/>
        <result property="code" column="code"/>
        <result property="name" column="name"/>
        <result property="status" column="status"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <!-- 查询人员所属项目清单-->
    <select id="queryUserProjectList" parameterType="String" resultMap="ProjectInfoResult">
        select p.* from project_info p where p.status = 'Y'
        and exists (
            select * from project_info_group pig, sys_group sg , sys_user_group sug ,sys_user su
            where sg.status = 'Y' and su.del_flag = '0' and su.status = '0'
            and pig.group_id = sg.id
            and sg.id = sug.group_id
            and sug.user_id = su.user_id
            and p.id = pig.project_id
            and su.user_id = #{userId}
        )
    </select>


</mapper>

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rzdata.system.mapper.SysApiLogMapper">

    <resultMap type="com.rzdata.system.domain.SysApiLog" id="SysApiLogResult">
        <result property="id" column="id"/>
        <result property="tenantId" column="tenant_id"/>
        <result property="sourceId" column="source_id"/>
        <result property="sourceType" column="source_type"/>
        <result property="appId" column="app_id"/>
        <result property="appName" column="app_name"/>
        <result property="apiType" column="api_type"/>
        <result property="apiUrl" column="api_url"/>
        <result property="requestParam" column="request_param"/>
        <result property="requestTime" column="request_time"/>
        <result property="status" column="status"/>
        <result property="responseStatus" column="response_status"/>
        <result property="responseInfo" column="response_info"/>
        <result property="responseTime" column="response_time"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>


</mapper>

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rzdata.system.mapper.SysDeptMapper">

    <resultMap type="SysDept" id="SysDeptResult">
        <id property="deptId" column="dept_id"/>
        <result property="parentId" column="parent_id"/>
        <result property="ancestors" column="ancestors"/>
        <result property="deptName" column="dept_name"/>
        <result property="orderNum" column="order_num"/>
        <result property="leader" column="leader"/>
        <result property="phone" column="phone"/>
        <result property="email" column="email"/>
        <result property="status" column="status"/>
        <result property="delFlag" column="del_flag"/>
        <result property="parentName" column="parent_name"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="deptCode" column="dept_code"/>
        <result property="deptLevel" column="dept_level"/>
        <result property="secDeptId" column="sec_dept_id"/>
        <result property="deptFullPathName" column="dept_full_path_name"/>
        <result property="deptFullPathId" column="dept_full_path_id"/>
    </resultMap>

    <sql id="selectDeptVo">
        select d.dept_id, d.parent_id, d.ancestors, d.dept_name, d.order_num, d.leader, d.phone, d.email, d.status, d.del_flag, d.create_by, d.create_time,d.dept_code,d.dept_level,d.sec_dept_id,d.dept_full_path_name,d.dept_full_path_id
        from sys_dept d
    </sql>

    <select id="selectDeptList" parameterType="SysDept" resultMap="SysDeptResult">
        <include refid="selectDeptVo"/>
        where d.del_flag = '0'
		<if test="deptId != null and deptId != 0">
			AND dept_id = #{deptId}
		</if>
        <if test="parentId != null and parentId != 0">
            AND parent_id = #{parentId}
        </if>
        <if test="deptName != null and deptName != ''">
            AND dept_name like concat('%', #{deptName}, '%')
        </if>
        <if test="tenantId != null and tenantId != ''">
            AND d.tenant_id like concat('%', #{tenantId})
        </if>
        <if test="deptFullPathId != null and deptFullPathId != ''">
            AND d.dept_full_path_id like concat('%', #{deptFullPathId},'%')
        </if>
        <if test="status != null and status != ''">
            AND status = #{status}
        </if>
        <!-- 数据范围过滤 -->
        <if test="params.dataScope != null and params.dataScope != ''">
            AND ( ${params.dataScope} )
        </if>
        <if test="deptLevel != null and deptLevel != 0">
            AND dept_level &lt;= ${deptLevel}
        </if>
        order by d.parent_id, d.order_num
    </select>

    <select id="selectDeptListByRoleId" resultType="Integer">
        select d.dept_id
        from sys_dept d
        left join sys_role_dept rd on d.dept_id = rd.dept_id
        where rd.role_id = #{roleId}
            <if test="deptCheckStrictly">
                and d.dept_id not in (select d.parent_id from sys_dept d inner join sys_role_dept rd on d.dept_id = rd.dept_id and rd.role_id = #{roleId})
            </if>
        order by d.parent_id, d.order_num
    </select>

    <update id="updateDeptChildren" parameterType="java.util.List">
        update sys_dept set ancestors =
        <foreach collection="depts" item="item" index="index"
                 separator=" " open="case dept_id" close="end">
            when #{item.deptId} then #{item.ancestors}
        </foreach>
        where dept_id in
        <foreach collection="depts" item="item" index="index"
                 separator="," open="(" close=")">
            #{item.deptId}
        </foreach>
    </update>

    <select id="selectDeptListToAiShu" resultType="com.rzdata.system.domain.vo.SysDeptVo">
        SELECT
            dept_id as deptId,
            dept_name as deptName,
            parent_id as parentId,
            order_num as orderNum
        FROM
            sys_dept
        WHERE
            STATUS = 0
          AND del_flag = 0
    </select>

    <!-- 根据当前deptId找出父节点 -->
    <select id="findParentOrgTree" resultType="com.rzdata.framework.core.domain.entity.SysDept">
        <include refid="selectDeptVo" />
        where STATUS = 0 and d.del_flag = '0'
        and FIND_IN_SET(dept_id, getOrgParentLst(#{deptId}));
    </select>

    <select id="getCompanyByDeptId" resultType="com.rzdata.framework.core.domain.entity.SysDept" parameterType="string">
        SELECT
            sd.*
        FROM
            sys_dept sd
        WHERE
            dept_level = '1'
          AND FIND_IN_SET(
            sd.dept_id,(
                SELECT
                    sd1.ancestors
                FROM
                    sys_dept sd1
                WHERE
                    sd1.dept_id = #{deptId}
            )
          )
    </select>
</mapper>

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rzdata.system.mapper.SysRoleMenuMapper">

    <resultMap type="SysRoleMenu" id="SysRoleMenuResult">
        <result property="roleId" column="role_id"/>
        <result property="menuId" column="menu_id"/>
    </resultMap>

    <update id="updateScopeById" parameterType="java.util.Map">
        update sys_role_menu set perms=#{perms}

            where role_id=#{roleId} and menu_id=#{menuId}
    </update>

    <select id="queryDataScopeList" resultType="com.rzdata.framework.core.domain.entity.SysRoleMenuVo">
        select srm.role_id,srm.menu_id,srm.perms,m.*
        from sys_role_menu srm join  sys_menu m on srm.menu_id=m.menu_id
        where srm.role_id=#{roleId}
    </select>

</mapper>

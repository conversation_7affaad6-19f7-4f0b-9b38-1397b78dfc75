<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rzdata.system.mapper.SysUserTenantMapper">
    <select id="selectTenantListByUserId" parameterType="String" resultType="String">
        select r.id
        from basic_tenant r
                 left join sys_user_tenant ur on ur.tenant_id = r.id
                 left join sys_user u on u.user_id = ur.user_id
        where u.user_id = #{userId}
    </select>

    <select id="getTenantListByTenantId" resultType="SysUserTenantVo" parameterType="string" >
        SELECT sut.* from sys_user su
        LEFT JOIN sys_user_tenant sut on su.user_id = sut.user_id
        where su.`status`='0' and su.`del_flag` = '0'
          and  sut.tenant_id = #{tenantId}
    </select>

    <insert id="insertUserTenantView" parameterType="com.rzdata.system.domain.SysUserTenant">
        INSERT INTO ${ubaseDataSource}.sys_user_tenant (`id`, `user_id`, `user_name`, `tenant_id`, `tenant_name`)
        VALUES (#{userTenant.id},#{userTenant.userId},#{userTenant.userName},#{userTenant.tenantId},#{userTenant.tenantName});
    </insert>

    <delete id="deleteUserTenantByUserId" parameterType="string">
        delete from ${ubaseDataSource}.sys_user_tenant where user_id=#{userId}
    </delete>

</mapper>

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rzdata.system.mapper.SysUserMapper">

    <resultMap type="com.rzdata.framework.core.domain.entity.SysUser" id="SysUserResult">
        <id property="userId" column="user_id"/>
        <result property="deptId" column="dept_id"/>
        <result property="userName" column="user_name"/>
        <result property="nickName" column="nick_name"/>
        <result property="email" column="email"/>
        <result property="phonenumber" column="phonenumber"/>
        <result property="sex" column="sex"/>
        <result property="avatar" column="avatar"/>
        <result property="password" column="password"/>
        <result property="status" column="status"/>
        <result property="delFlag" column="del_flag"/>
        <result property="loginIp" column="login_ip"/>
        <result property="loginDate" column="login_date"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="thirdId" column="third_id"/>
        <result property="tenantId" column="tenant_id"/>
        <association property="dept" column="dept_id" javaType="com.rzdata.framework.core.domain.entity.SysDept" resultMap="deptResult"/>
        <collection property="roles" javaType="java.util.List" resultMap="RoleResult"/>
    </resultMap>

    <resultMap id="deptResult" type="com.rzdata.framework.core.domain.entity.SysDept">
        <id property="deptId" column="dept_id"/>
        <result property="parentId" column="parent_id"/>
        <result property="deptName" column="dept_name"/>
        <result property="secDeptId" column="sec_dept_id"/>
        <result property="deptLevel" column="dept_level"/>
        <result property="orderNum" column="order_num"/>
        <result property="leader" column="leader"/>
        <result property="status" column="dept_status"/>
        <result property="deptCode" column="dept_code"/>
    </resultMap>

    <resultMap id="RoleResult" type="com.rzdata.framework.core.domain.entity.SysRole">
        <id property="roleId" column="role_id"/>
        <result property="roleName" column="role_name"/>
        <result property="roleKey" column="role_key"/>
        <result property="roleSort" column="role_sort"/>
        <result property="dataScope" column="data_scope"/>
        <result property="status" column="role_status"/>
    </resultMap>

    <sql id="selectUserVo">
        select
               u.third_id,
               u.user_id,
               u.dept_id,
               u.user_name,
               u.nick_name,
               u.email,
               u.avatar,
               u.phonenumber,
               u.password,
               u.sex,
               u.status,
               u.del_flag,
               u.login_ip,
               u.login_date,
               u.create_by,
               u.create_time,
               u.remark,
               d.dept_id,
               d.sec_dept_id,
               d.dept_level,
               d.parent_id,
               d.dept_name,
               d.order_num,
               d.leader,
               d.status as dept_status,
               d.dept_code,
               d.ancestors,
               r.role_id,
               r.role_name,
               r.role_key,
               r.role_sort,
               r.data_scope,
               r.status as role_status,
               t.tenant_id
        from sys_user u
                 join sys_dept d on u.dept_id = d.dept_id and d.status = '0' and d.del_flag = '0'
                 left join sys_user_role ur on u.user_id = ur.user_id
                 left join sys_role r on r.role_id = ur.role_id
                 LEFT JOIN sys_user_tenant t on u.user_id = t.user_id
    </sql>

    <select id="selectPageUserList" parameterType="com.rzdata.framework.core.domain.entity.SysUser" resultMap="SysUserResult">
        select u.third_id,u.user_id, u.dept_id, u.nick_name, u.user_name, u.email, u.avatar, u.phonenumber, u.password, u.sex,
        u.status, u.del_flag, u.login_ip, u.login_date, u.create_by, u.create_time, u.remark, d.dept_name,d.dept_code,
        d.leader from
        sys_user u
            join sys_dept d on u.dept_id = d.dept_id and d.status = '0' and d.del_flag = '0'
            join sys_user_tenant t on t.user_id = u.user_id
        where u.del_flag = '0' and u.user_type = '00'
        <if test="user.userId != null and user.userId != '0'.toString() ">
            AND u.user_id = #{user.userId}
        </if>
        <if test="user.userName != null and user.userName != ''">
            AND u.user_name like concat('%', #{user.userName}, '%')
        </if>
        <if test="user.roleId != null and user.roleId != ''">
            AND u.user_id in (select user_id from sys_user_role where role_id = #{user.roleId})
        </if>
        <if test="user.acquiesceUserCode != null and user.acquiesceUserCode != ''">
            and u.user_name = #{user.acquiesceUserCode}
        </if>
        <if test="user.nickName != null and user.nickName != ''">
            AND u.nick_name like concat('%', #{user.nickName}, '%')
        </if>
        <if test="user.status != null and user.status != ''">
            AND u.status = #{user.status}
        </if>
        <if test="user.phonenumber != null and user.phonenumber != ''">
            AND u.phonenumber like concat('%', #{user.phonenumber}, '%')
        </if>
        <if test="user.params.beginTime != null and user.params.beginTime != ''"><!-- 开始时间检索 -->
            AND date_format(u.create_time,'%y%m%d') &gt;= date_format(#{user.params.beginTime},'%y%m%d')
        </if>
        <if test="user.params.endTime != null and user.params.endTime != ''"><!-- 结束时间检索 -->
            AND date_format(u.create_time,'%y%m%d') &lt;= date_format(#{user.params.endTime},'%y%m%d')
        </if>
        <if test="user.deptId != null and user.deptId != '' and user.deptId != '0'.toString() ">
            AND (u.dept_id = #{user.deptId} OR u.dept_id IN ( SELECT t.dept_id FROM sys_dept t WHERE
            find_in_set(#{user.deptId},
            ancestors) ))
        </if>
        <!-- 数据范围过滤 -->
        <if test="user.params.dataScope != null and user.params.dataScope != ''">
            AND ( ${user.params.dataScope} )
        </if>
    </select>

    <select id="selectPageUserListForGroup" parameterType="com.rzdata.framework.core.domain.entity.SysUser" resultMap="SysUserResult">
        select u.third_id,u.user_id, u.dept_id, u.nick_name, u.user_name, u.email, u.avatar, u.phonenumber, u.password, u.sex,
        u.status, u.del_flag, u.login_ip, u.login_date, u.create_by, u.create_time, u.remark, d.dept_name,d.dept_code,
        d.leader from
        sys_user u
        join sys_dept d on u.dept_id = d.dept_id and d.status = '0' and d.del_flag = '0'
        where u.del_flag = '0'
        <if test="user.userId != null and user.userId != '0'.toString() ">
            AND u.user_id = #{user.userId}
        </if>
        <if test="user.userName != null and user.userName != ''">
            AND u.user_name like concat('%', #{user.userName}, '%')
        </if>
        <if test="user.nickName != null and user.nickName != ''">
            AND u.nick_name like concat('%', #{user.nickName}, '%')
        </if>
        <if test="user.status != null and user.status != ''">
            AND u.status = #{user.status}
        </if>
        <if test="user.phonenumber != null and user.phonenumber != ''">
            AND u.phonenumber like concat('%', #{user.phonenumber}, '%')
        </if>
        <if test="user.params.beginTime != null and user.params.beginTime != ''"><!-- 开始时间检索 -->
            AND date_format(u.create_time,'%y%m%d') &gt;= date_format(#{user.params.beginTime},'%y%m%d')
        </if>
        <if test="user.params.endTime != null and user.params.endTime != ''"><!-- 结束时间检索 -->
            AND date_format(u.create_time,'%y%m%d') &lt;= date_format(#{user.params.endTime},'%y%m%d')
        </if>
        <if test="user.deptId != null and user.deptId != '0'.toString() ">
            AND (u.dept_id = #{user.deptId} OR u.dept_id IN ( SELECT t.dept_id FROM sys_dept t WHERE
            find_in_set(#{user.deptId},
            ancestors) ))
        </if>
        <if test="user.groupType != null and user.groupType != '' and user.groupId != null and user.groupId != ''">
            <if test='user.groupType == "group"'>
                AND u.user_id NOT IN (select user_id from basic_group_user where group_id = #{user.groupId})
            </if>
            <if test='user.groupType == "group_term"'>
                AND u.user_id NOT IN (select user_id from basic_group_user where term_id = #{user.groupId})
            </if>
            <if test='user.groupType == "group_shift"'>
                AND u.user_id NOT IN (select user_id from basic_group_user where shift_id = #{user.groupId})
            </if>
        </if>
        <!-- 数据范围过滤 -->
        <if test="user.params.dataScope != null and user.params.dataScope != ''">
            AND ( ${user.params.dataScope} )
        </if>
    </select>

    <select id="selectUserList" parameterType="com.rzdata.framework.core.domain.entity.SysUser" resultMap="SysUserResult">
        select u.third_id,u.user_id, u.dept_id, u.nick_name, u.user_name, u.email, u.avatar, u.phonenumber, u.password, u.sex,
        u.status, u.del_flag, u.login_ip, u.login_date, u.create_by, u.create_time, u.remark, d.dept_name,d.dept_code,
        d.leader from
        sys_user u
            join sys_dept d on u.dept_id = d.dept_id and d.status = '0' and d.del_flag = '0'
        where u.del_flag = '0'
        <if test="userId != null and userId != '0'.toString() ">
            AND u.user_id = #{userId}
        </if>
        <if test="userName != null and userName != ''">
            AND u.user_name like concat('%', #{userName}, '%')
        </if>
        <if test="status != null and status != ''">
            AND u.status = #{status}
        </if>
        <if test="phonenumber != null and phonenumber != ''">
            AND u.phonenumber like concat('%', #{phonenumber}, '%')
        </if>
        <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
            AND date_format(u.create_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
        </if>
        <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
            AND date_format(u.create_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
        </if>
        <if test="deptId != null and deptId != '0'.toString() ">
            AND (u.dept_id = #{deptId} OR u.dept_id IN ( SELECT t.dept_id FROM sys_dept t WHERE find_in_set(#{deptId},
            ancestors) ))
        </if>
        <!-- 数据范围过滤 -->
        <if test="params.dataScope != null and params.dataScope != ''">
            AND ( ${params.dataScope} )
        </if>
    </select>

    <select id="selectAllocatedList" parameterType="com.rzdata.framework.core.domain.entity.SysUser" resultMap="SysUserResult">
        select distinct u.third_id,u.user_id, u.dept_id, u.user_name, u.nick_name, u.email, u.phonenumber, u.status, u.create_time
        from sys_user u
            join sys_dept d on u.dept_id = d.dept_id and d.status = '0' and d.del_flag = '0'
            left join sys_user_role ur on u.user_id = ur.user_id
            left join sys_role r on r.role_id = ur.role_id
        where u.del_flag = '0' and r.role_id = #{user.roleId}
        <if test="user.userName != null and user.userName != ''">
            AND u.user_name like concat('%', #{user.userName}, '%')
        </if>
        <if test="user.phonenumber != null and user.phonenumber != ''">
            AND u.phonenumber like concat('%', #{user.phonenumber}, '%')
        </if>
        <!-- 数据范围过滤 -->
        <if test="user.params.dataScope != null and user.params.dataScope != ''">
            AND ( ${user.params.dataScope} )
        </if>
    </select>

    <select id="selectUnallocatedList" parameterType="com.rzdata.framework.core.domain.entity.SysUser" resultMap="SysUserResult">
        select distinct u.third_id,u.user_id, u.dept_id, u.user_name, u.nick_name, u.email, u.phonenumber, u.status, u.create_time
        from sys_user u
            join sys_dept d on u.dept_id = d.dept_id and d.status = '0' and d.del_flag = '0'
            left join sys_user_role ur on u.user_id = ur.user_id
            left join sys_role r on r.role_id = ur.role_id
        where u.del_flag = '0' and (r.role_id != #{user.roleId} or r.role_id IS NULL)
        and u.user_id not in (select u.user_id from sys_user u inner join sys_user_role ur on u.user_id = ur.user_id and
        ur.role_id = #{user.roleId})
        <if test="user.userName != null and user.userName != ''">
            AND u.user_name like concat('%', #{user.userName}, '%')
        </if>
        <if test="user.nickName != null and user.nickName != ''">
            AND u.nick_name like concat('%', #{user.nickName}, '%')
        </if>
        <if test="user.phonenumber != null and user.phonenumber != ''">
            AND u.phonenumber like concat('%', #{user.phonenumber}, '%')
        </if>
        <!-- 数据范围过滤 -->
        <if test="user.params.dataScope != null and user.params.dataScope != ''">
            AND ( ${user.params.dataScope} )
        </if>
    </select>

    <select id="selectUserByUserName" parameterType="String" resultMap="SysUserResult">
        <include refid="selectUserVo"/>
        where u.del_flag = '0' and u.user_name = #{userName}
    </select>


    <select id="selectUserByThirdId" parameterType="String" resultMap="SysUserResult">
        <include refid="selectUserVo"/>
        left join sys_user_exp_field e1 on u.user_name = e1.user_code
        where u.del_flag = '0' and e1.open_user_id = #{thirdId}
    </select>


    <select id="selectUserById" parameterType="String" resultMap="SysUserResult">
        <include refid="selectUserVo"/>
        where u.del_flag = '0' and u.user_id = #{userId}
    </select>

    <select id="selectListAllByRoleKey" parameterType="String" resultMap="SysUserResult">
        <include refid="selectUserVo"/>
        where u.del_flag = '0' and r.role_key = #{roleKey}
    </select>

    <select id="selectDeptFileManagerByDeptId" parameterType="String"
            resultType="com.rzdata.framework.core.domain.entity.SysUser">
        <!--SELECT *
        FROM `sys_user`
        WHERE dept_id = #{deptId}
          and user_id IN (SELECT user_id
                          FROM sys_user_role
                          WHERE role_id IN
                                (SELECT role_id FROM sys_role WHERE role_key = '7' OR role_key = 'dept_file_manager'));-->
        SELECT * FROM sys_user u
        LEFT JOIN sys_user_role ur ON u.user_id = ur.user_id
        LEFT JOIN sys_role r ON r.role_id = ur.role_id
        WHERE u.dept_id = #{deptId}  AND r.role_key = '7' OR r.role_key = 'dept_file_manager'
    </select>
    <select id="selectQaAndSc" resultType="com.rzdata.framework.core.domain.entity.SysUser">
        <!--SELECT *
        FROM `sys_user`
        WHERE user_id IN (SELECT user_id
                          FROM sys_user_role
                          WHERE role_id IN (SELECT role_id FROM sys_role WHERE role_key = '1' OR role_key = '2'));-->
        SELECT * FROM sys_user u
        LEFT JOIN sys_user_role ur ON u.user_id = ur.user_id
        LEFT JOIN sys_role r ON r.role_id = ur.role_id
        WHERE r.role_key = '1' OR r.role_key = '2'
    </select>

    <select id="isQa" resultType="com.rzdata.framework.core.domain.entity.SysUser">
        <!--SELECT *
        FROM `sys_user`
        WHERE user_id IN (SELECT user_id
                          FROM sys_user_role
                          WHERE role_id IN
                                (SELECT role_id FROM sys_role WHERE role_key = 'WJGLY' OR role_key = 'company_file_manager')) and user_name = #{username};-->
        SELECT * FROM sys_user u
        LEFT JOIN sys_user_role ur ON u.user_id = ur.user_id
        LEFT JOIN sys_role r ON r.role_id = ur.role_id
        WHERE (r.role_key = 'WJGLY' OR r.role_key = 'company_file_manager') AND u.user_name = #{username}
    </select>

    <select id="selectUserToAiShu" resultType="com.rzdata.system.domain.vo.SysUserVo">
        SELECT
            user_id as userId,
            dept_id as deptId,
            user_name as userName,
            nick_name as nickName,
            email,
            1 as status,
            '' as password,
            0 as sort
        FROM
            sys_user
        WHERE
            `status` = 0
          AND del_flag = 0
    </select>

    <select id="selectUserByDeptPost" resultType="SysUserVo">
        select su.third_id,su.user_id,su.user_name,sd.dept_name from sys_user su
        LEFT JOIN sys_user_post sup on su.user_id = sup.user_id
        JOIN sys_dept sd on su.dept_id = sd.dept_id and d.status = '0' and d.del_flag = '0'
        <where>
            <if test="deptIds != null and deptIds.size()>0" >
               su.dept_id IN
                <foreach collection="deptIds" index="index" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="postIds != null and postIds.size()>0" >
                AND sup.post_id IN
                <foreach collection="postIds" index="index" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            and sup.tenant_id = #{tenantId}
        </where>
    </select>
    <select id="selectUserByDeptIdAndPerms" resultType="com.rzdata.framework.core.domain.entity.SysUser">
        select u.third_id,u.user_id, u.dept_id, u.nick_name, u.user_name, u.email, u.avatar, u.phonenumber, u.password, u.sex,
        u.status, u.del_flag, u.login_ip, u.login_date, u.create_by, u.create_time, u.remark, d.dept_name,d.dept_code,
        d.leader from
        sys_user u
            JOIN sys_dept d ON u.dept_id = d.dept_id and d.status = '0' and d.del_flag = '0'
            LEFT JOIN sys_user_role ur ON ur.user_id = u.user_id
            LEFT JOIN sys_role_menu rm ON rm.role_id = ur.role_id
            LEFT JOIN sys_menu m ON m.menu_id = rm.menu_id
        where u.del_flag = '0'
        <if test="deptId != null and deptId != '0'.toString() ">
            AND (u.dept_id = #{deptId})
        </if>
          <if test="perms!=null and perms!=''">
            AND m.perms = #{perms}
          </if>

    </select>

    <select id="getNumByDeptId" resultType="int" parameterType="string" >
        SELECT count(*) as nums from sys_user u
        LEFT JOIN `sys_dept` d on d.dept_id = u.dept_id
        where u.`status`='0' and u.`del_flag` = '0'
          AND d.`STATUS` = '0' AND d.del_flag = '0'
          AND u.user_type = '00'
          and (find_in_set(#{deptId},d.ancestors)
          or d.dept_id = #{deptId})
    </select>

    <select id="getUserByDeptId" resultType="com.rzdata.framework.core.domain.entity.SysUser" parameterType="string" >
        SELECT u.* from sys_user u
        LEFT JOIN `sys_dept` d on d.dept_id = u.dept_id
        where u.`status`='0' and u.`del_flag` = '0'
          AND d.`STATUS` = '0' AND d.del_flag = '0'
          AND u.user_type = '00'
          and (find_in_set(#{deptId},d.ancestors)
            or d.dept_id = #{deptId})
    </select>

    <select id="getNumByTenantId" resultType="int" parameterType="string" >
        SELECT count(*) from sys_user su
        LEFT JOIN sys_user_tenant sut on su.user_id = sut.user_id
        where su.`status`='0' and su.`del_flag` = '0'
        and  sut.tenant_id = #{tenantId}
    </select>

    <select id="getFullPathRoleUserList" resultType="com.rzdata.framework.core.domain.entity.SysUser" parameterType="string">
        select u.*
        FROM
            sys_user u
                JOIN sys_dept d ON u.dept_id = d.dept_id
                AND d.STATUS = '0'
                AND d.del_flag = '0'
                LEFT JOIN sys_user_role ur ON u.user_id = ur.user_id
                left join sys_role r on r.role_id = ur.role_id
        where
            u.del_flag = '0' and u.STATUS = '0'
            and  r.role_key = #{roleKey}
            and  FIND_IN_SET(u.dept_id,(select dept_full_path_id from sys_dept where dept_id = #{deptId}))
        ORDER BY d.dept_level  desc
    </select>


    <!--根据租户查询用户-->
    <select id="getListByTenantId" parameterType="string" resultMap="SysUserResult" >
        SELECT
            su.user_id, su.dept_id, su.user_name, su.nick_name, su.user_type, su.email,
            su.phonenumber, su.sex, su.avatar, su.status, su.del_flag, su.login_ip,
            su.login_date, su.create_by, su.create_time, su.update_by, su.update_time, su.remark, su.third_id,
            sd.dept_name,sd.dept_code,sd.leader
        FROM
            sys_user su
                LEFT JOIN
            sys_user_tenant sut ON su.user_id = sut.user_id
                LEFT JOIN
            sys_dept sd ON sd.dept_id = su.dept_id
        WHERE
            su.status = '0'
          AND
            su.del_flag = '0'
          and  sut.tenant_id = #{tenantId}
    </select>

    <!-- 查询应用账号分页列表 -->
    <select id="selectPageApplicationUserList" parameterType="com.rzdata.framework.core.domain.entity.SysUser" resultMap="SysUserResult">
        select u.third_id,u.user_id, u.dept_id, u.nick_name, u.user_name, u.email, u.avatar, u.phonenumber, u.password, u.sex,
        u.status, u.del_flag, u.login_ip, u.login_date, u.create_by, u.create_time, u.remark, d.dept_name,d.dept_code,
        d.leader from
        sys_user u
        join sys_dept d on u.dept_id = d.dept_id and d.status = '0' and d.del_flag = '0'
        join sys_user_tenant t on t.user_id = u.user_id
        where u.del_flag = '0' and u.user_type = '01'
        <if test="user.userId != null and user.userId != '0'.toString() ">
            AND u.user_id = #{user.userId}
        </if>
        <if test="user.userName != null and user.userName != ''">
            AND u.user_name like concat('%', #{user.userName}, '%')
        </if>
        <if test="user.nickName != null and user.nickName != ''">
            AND u.nick_name like concat('%', #{user.nickName}, '%')
        </if>
        <if test="user.status != null and user.status != ''">
            AND u.status = #{user.status}
        </if>
        <if test="user.params.beginTime != null and user.params.beginTime != ''"><!-- 开始时间检索 -->
            AND date_format(u.create_time,'%y%m%d') &gt;= date_format(#{user.params.beginTime},'%y%m%d')
        </if>
        <if test="user.params.endTime != null and user.params.endTime != ''"><!-- 结束时间检索 -->
            AND date_format(u.create_time,'%y%m%d') &lt;= date_format(#{user.params.endTime},'%y%m%d')
        </if>

    </select>

    <!-- 新增应用账号 -->
    <insert id="insertApplicationUser" parameterType="com.rzdata.framework.core.domain.entity.SysUser">
        insert into ${ubaseDataSource}.sys_user(user_id, dept_id, user_name, nick_name, user_type, email,
           password, status, del_flag, create_by, create_time, remark)
        values(#{user.userId}, #{user.deptId}, #{user.userName}, #{user.nickName}, #{user.userType}, #{user.email},
           #{user.password}, #{user.status}, #{user.delFlag}, #{user.createBy}, now(), #{user.remark}
        )
    </insert>

    <!-- 修改应用账号 -->
    <update id="updateApplicationUser">
        update ${ubaseDataSource}.sys_user
        <set>
            <if test="user.nickName != null &amp;&amp; user.nickName != ''">nick_name = #{user.nickName},</if>
            <if test="user.email != null &amp;&amp; user.email !=  ''">email = #{user.email},</if>
            <if test="user.status != null  &amp;&amp; user.status !=  ''">status = #{user.status},</if>
            <if test="user.delFlag != null  &amp;&amp; user.delFlag !=  ''">sex = #{user.delFlag},</if>
            <if test="user.remark != null  &amp;&amp; user.remark !=  ''">remark = #{user.remark},</if>
            <if test="user.password != null  &amp;&amp; user.password !=  ''">password = #{user.password},</if>
            update_by = #{user.updateBy},
            update_time = now()
        </set>
        <where>
            user_id = #{user.userId}
        </where>
    </update>

</mapper>

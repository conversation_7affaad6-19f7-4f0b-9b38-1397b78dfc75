<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rzdata.system.mapper.WorkflowMapper">

    <!-- 从流程平台获取 文件新增、文件修订、文件作废的流程KEY -->
    <select id="queryFlowList" resultType="com.rzdata.framework.core.domain.entity.SysDictData">
        SELECT t.KEY_ dictValue,t.NAME_ dictLabel FROM ${bpmDataSource}.ACT_RE_PROCDEF t join ${bpmDataSource}.ACT_RE_DEPLOYMENT ard on t.DEPLOYMENT_ID_ = ard.ID_
        <where>
            t.VERSION_ = (select max(arp.VERSION_) FROM ${bpmDataSource}.ACT_RE_PROCDEF arp where arp.KEY_ = t.KEY_)
            <if test="key!=null and key!=''">
                and t.KEY_ like concat('%',#{key}, '%')
            </if>
            <if test="bpmTenantId!=null and bpmTenantId!=''">
                and t.TENANT_ID_ = #{bpmTenantId}
            </if>
        </where>
    </select>

    <update id="updateTaskTitle">
        update ${bpmDataSource}.T_UNITEWORK_HISTORY set UNITE_TITLE = #{procTitle} where  UNITE_PROCINSTID = #{procInstId};
    </update>

    <update id="updateFlowTitle">
        update ${bpmDataSource}.ACT_HI_PROCINST set PROC_TITLE = #{procTitle} where  PROC_INST_ID_ = #{procInstId};
    </update>

    <update id="updateRuTaskTitle">
        update ${bpmDataSource}.ACT_RU_TASK set PROC_TITLE = #{procTitle} where  PROC_INST_ID_ = #{procInstId};
    </update>
</mapper>

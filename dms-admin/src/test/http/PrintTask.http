### 变量设置
@host = http://localhost:8080/dev-dms-admin
@token = Bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJhZG1pbiIsImxvZ2luX3VzZXJfa2V5IjoiMDZhZjA3NDMtZWM2YS00ZjFjLWIzMzQtZGM2ZGIyMTg5ZjUxIn0.n8_lgGBLOYl5Z8j2pGsz2kFejoXHuJWIzLk3dpkOWNuHjlsiY3LKyy_B3U4Jn6fjx02ZdZc5Gse8_k2RdTs9hg

@contentType = application/json

### 查询打印任务列表
GET {{host}}/process/printTask/list?pageNum=1&pageSize=10
Content-Type: {{contentType}}
Authorization: {{token}}

### 新增打印任务
POST {{host}}/process/printTask
Content-Type: {{contentType}}
Authorization: {{token}}

{
    "docId": "TEST001",
    "docName": "测试文档",
    "versionId": "V1.0",
    "printerName": "默认打印机",
    "printCount": 1,
    "printDesc": "测试打印任务",
    "docType": "1",
    "electronicFileName": "test.pdf",
    "classificationNo": "A001"
}

### 修改打印任务
PUT {{host}}/process/printTask
Content-Type: {{contentType}}
Authorization: {{token}}

{
    "id": "1",
    "docId": "TEST001",
    "docName": "测试文档-修改",
    "versionId": "V1.0",
    "printerName": "默认打印机",
    "printCount": 1,
    "printDesc": "测试打印任务-修改",
    "docType": "1",
    "electronicFileName": "test-updated.pdf",
    "classificationNo": "A001"
}

### 删除打印任务
DELETE {{host}}/process/printTask/1,2
Content-Type: {{contentType}}
Authorization: {{token}}

### 执行打印任务
POST {{host}}/process/printTask/execute/1
Content-Type: {{contentType}}
Authorization: {{token}}

### 打印完成回调
POST   {{host}}/process/printTask/callback
Content-Type: application/json
Authorization: {{token}}

{
    "taskId": "1877669401306734594",          
    "taskStatus": "completed",       
    "taskDesc": "打印完成", 
    "printedCount": 10  
} 
### 变量设置
@host = http://localhost:8080/dev-dms-admin
@contentType = application/json
@token = Bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJhZG1pbiIsImxvZ2luX3VzZXJfa2V5IjoiNGZmN2IzODEtNjdjYi00MGM2LTljZmMtMzVmNTY4MWIxNjU5In0.5o0JrApCUuqY6juFYZ_UhhdUoFS9htT0MPhhklGkAMSw6g20_7xGwzJ7F-QVheVQkQOtpSY89wv-DY1l_Y6TLg


### 登录
# @name login
POST {{host}}/login
Content-Type: {{contentType}}

{
    "username": "admin",
    "password": "12345",
    "code": "",
    "uuid": "",
    "tenantId": "",
    "type": "PC"
}

### 刷新令牌
POST {{host}}/refreshToken
Content-Type: {{contentType}}
Authorization: {{token}}

### 获取用户信息
GET {{host}}/getInfo
Content-Type: {{contentType}}
Authorization: {{token}}

### 获取路由信息
GET {{host}}/getRouters
Content-Type: {{contentType}}
Authorization: {{token}}

### 企业微信SSO登录（网页认证）
GET {{host}}/qywySSOCode
Content-Type: {{contentType}}

### 企业微信授权回调
GET {{host}}/redirectUri
Content-Type: {{contentType}} 
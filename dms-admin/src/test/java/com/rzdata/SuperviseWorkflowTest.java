package com.rzdata;

import com.rzdata.process.domain.vo.UniteworkTaskVo;
import com.rzdata.process.service.SuperviseWorkflowEntranceService;
import com.rzdata.process.service.SuperviseWorkflowHandleService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

/**
 * <AUTHOR>
 * @date 2024年12月25日 14:07
 **/
@SpringBootTest(classes ={AdminApplication.class})
@ActiveProfiles("proddev")
public class SuperviseWorkflowTest {

    @Autowired
    private SuperviseWorkflowEntranceService superviseWorkflowEntranceService;
    @Autowired
    private SuperviseWorkflowHandleService superviseWorkflowHandleService;

    @Test
    public void testLaunchSuperviseWorkflow() {
        superviseWorkflowEntranceService.launchSuperviseWorkflow("1");
    }

    @Test
    public void testSuperviseWorkflowHandleService() {
        UniteworkTaskVo uniteworkTaskVo = new UniteworkTaskVo();
        uniteworkTaskVo.setRecUserId("adminjl");
        uniteworkTaskVo.setTitle("测试");
        superviseWorkflowHandleService.sendSuperviseEmail(uniteworkTaskVo);
    }
}

package com.rzdata.framework.constant;

public class CommonI18nConstant {
    /**
     * 递增因子必须大于0
     */
    public static final String COMMON_DELTA_GE_ZERO = "common.delta.ge.zero";
    /**
     * 递减因子必须小于0
     */
    public static final String COMMON_DELTA_LESS_ZERO = "common.delta.less.zero";
    /**
     * 请检查redis里面存储的数据是否正确
     */
    public static final String COMMON_CHECK_REDIS_DATA = "common.check.redis.data";

    /**
     * 登录用户不存在
     */
    public static final String LOGIN_USERNAME_NOT_PRESENT = "login.username.not.present";
    /**
     * 您的账号已被删除
     */
    public static final String LOGIN_USERNAME_DELETE = "login.username.delete";
    /**
     * 您的账号已停用
     */
    public static final String LOGIN_USERNAME_STOP = "login.username.stop";
    /**
     * 未查询到该文件
     */
    public static final String FILE_UTIL_PATH_NOT_FOUND = "file.util.path.not.found";
    /**
     * 转PDF文件失败!
     */
    public static final String FILE_PDF_ERROR = "file.pdf.error";

    /**
     * 文件类型
     */
    public static final String FILE_LINK_TYPE = "file.link_type.";

    /**
     * 上传文件过大!
     */
    public static final String FILE_UPLOAD_FILE_TOO_LARGE = "file.upload.file.too.large";
    /**
     * 下载路径存在同名文件，下载失败。
     */
    public static final String FILE_DOWNLOAD_SAME_FILE_ERR = "file.download.same.file.err";
    /**
     * 不允许操作超级管理员角色
     */
    public static final String SYS_NOT_OPERATION_ADMIN = "sys.not.operation.admin";
    /**
     * 没有权限访问角色数据
     */
    public static final String SYS_NO_ACCESS_ROLE_DATA = "sys.no.access.role.data";
    /**
     * 已分配,不能删除
     */
    public static final String SYS_NOT_ALLOW_DELETE = "sys.not.allow.delete";
    /**
     * An instance of HttpsURLConnection is expected
     */
    public static final String HTTP_UTIL_URL_INSTANCE_EXPECTED = "http.util.url.instance.expected";
    /**
     * An instance of SSLSocket is expected
     */
    public static final String HTTP_UTIL_SSL_INSTANCE_EXPECTED = "http.util.ssl.instance.expected";
    /**
     * 该文件类型尚未进行流程设置，将无法发起子流程！
     */
    public static final String WORKFLOW_NOT_SET_PROCESS = "workflow.not.set.process";
    /**
     * 生成用章PDF文件失败!
     */
    public static final String FILE_FAIL_GENERATE_CHAPTER_PDF = "file.fail.generate.chapter.pdf";
    /**
     * 生成分发PDF文件失败!
     */
    public static final String FILE_FAIL_GENERATE_DISTRIBUTION_PDF = "file.fail.generate.distribution.pdf";
    /**
     * 比对文件1 非word文件=
     */
    public static final String FILE_COMPARISON1_NON_WORD = "file.comparison1.non.word";
    /**
     * 比对文件2 非word文件=
     */
    public static final String FILE_COMPARISON2_NON_WORD = "file.comparison2.non.word";
    /**
     * 文档比对失败!
     */
    public static final String FILE_DOC_COMPARISON_FAILED = "file.doc.comparison.failed";
    /**
     * 查询类型不能为空
     */
    public static final String INDEX_COUNT_QUERY_NOT_NULL = "index.count.query.not.null";
    /**
     * 导入失败：
     */
    public static final String SERVICE_IMPORT_FAILED = "service.import.failed";
    /**
     * 渲染模板失败，表名：
     */
    public static final String SERVICE_FAIL_RENDER_TEMP_TABLE = "service.fail.render.temp.table";
    /**
     * 同步数据失败，原表结构不存在
     */
    public static final String SERVICE_FAIL_SYCN_DATA_TABLE_NON_EXISTENT = "service.fail.sycn.data.table.non.existent";
    /**
     * 树编码字段不能为空
     */
    public static final String SERVICE_TREE_CODE_NOT_NULL = "service.tree.code.not.null";
    /**
     * 树父编码字段不能为空
     */
    public static final String SERVICE_TREE_PARENT_CODE_NOT_NULL = "service.tree.parent.code.not.null";
    /**
     * 树名称字段不能为空
     */
    public static final String SERVICE_TREE_NAME_NOT_NULL = "service.tree.name.not.null";
    /**
     * 关联子表的表名不能为空
     */
    public static final String SERVICE_RELATED_SUBTABLE_NAME_NOT_NULL = "service.related.subtable.name.not.null";
    /**
     * 子表关联的外键名不能为空
     */
    public static final String SERVICE_RELATED_SUBTABLE_FOREIGN_KEY_NOT_NULL = "service.related.subtable.foreign.key.not.null";
    /**
     * Http Request StatusCode Invalid,statusCode:
     */
    public static final String SERVICE_REQUEST_INVALID = "service.request.invalid";
    /**
     * 此文件类型未配置编号规则
     */
    public static final String FILE_TYPE_NOT_NUM_RULE = "file.type.not.num.rule";
    /**
     * ：找不到对应的文件分发编号
     */
    public static final String FILE_CAN_NOT_FOUND_NUMBER = "file.can.not.found.number";
    /**
     * 没有找到旧的编号
     */
    public static final String FILE_NOT_FOUND_OLD_NUM = "file.not.found.old.num";
    /**
     * 流水号不是数值
     */
    public static final String FILE_STREAM_NUM_NOT_VALUE = "file.stream.num.not.value";
    /**
     * 流水号超过规则约定的位数
     */
    public static final String FILE_STREAM_NUM_EXCEED_RULE = "file.stream.num.exceed.rule";
    /**
     * 编号重复
     */
    public static final String FILE_NUM_DUPLICATE = "file.num.duplicate";
    /**
     * 获取用户ID异常
     */
    public static final String UTIL_SECURITY_GET_USER_ID_ERR = "util.security.get.user.id.err";
    /**
     * 获取部门ID异常
     */
    public static final String UTIL_SECURITY_GET_DEPT_ID_ERR = "util.security.get.dept.id.err";
    /**
     * 获取用户账户异常
     */
    public static final String UTIL_SECURITY_GET_USER_ACCOUNT_ERR = "util.security.get.user.account.err";
    /**
     * 获取用户昵称异常
     */
    public static final String UTIL_SECURITY_GET_USER_NICK_NAME_ERR = "util.security.get.user.nick.name.err";
    /**
     * 获取用户信息异常
     */
    public static final String UTIL_SECURITY_GET_USER_INFO_ERR = "util.security.get.user.info.err";
    /**
     * 文件内容不能为空
     */
    public static final String FILE_DOC_EXTERNAL_NOT_NULL = "file.doc.external.not.null";
    /**
     * 文件名称不能为空
     */
    public static final String FILE_DOC_EXTERNAL_NAME_NOT_NULL = "file.doc.external.name.not.null";
    /**
     * 文件编号不能为空
     */
    public static final String FILE_DOC_EXTERNAL_SERIAL_NUM_NOT_NULL = "file.doc.external.serial.num.not.null";
    /**
     * 内部编号已存在，请修改后重新提交
     */
    public static final String FILE_DOC_EXTERNAL_INTER_NUM_EXIST = "file.doc.external.inter.num.exist";
    /**
     * 发布时间读取错误，请检查单元格格式是否为日期
     */
    public static final String FILE_DOC_EXTERNAL_POST_TIME_ERR = "file.doc.external.post.time.err";
    /**
     * 上传文件保存实体文件异常
     */
    public static final String FILE_BASIC_UPLOAD_SAVE_ERR = "file.basic.upload.save.err";
    /**
     * 文件编号已存在请修改
     */
    public static final String FILE_DOC_LINK_NUM_EXIST = "file.doc.link.num.exist";
    /**
     * 账号没有关联客户,抱歉！
     */
    public static final String LOGIN_USERNAME_WITHOUT_CLIENT = "login.username.without.client";
    /**
     * 未找到流程记录
     */
    public static final String FILE_DOC_MESSAGE_NOT_FOUND_FLOW = "file.doc.message.not.found.flow";
    /**
     * id错误
     */
    public static final String FILE_DOC_DISTRIBUTE_ID_ERR = "file.doc.distribute.id.err";
    /**
     * 流程平台服务异常，请联系管理员！
     */
    public static final String SERVICE_PROCESS_PLATFORM_ERR = "service.process.platform.err";
    /**
     * 已使用的编号规则不能删除
     */
    public static final String SERVICE_CAN_NOT_DELETE_EXIST_CODE_RULE = "service.can.not.delete.exist.code.rule";
    /**
     * 此部门编码尚未维护，请停止操作，联系系统管理员维护！
     */
    public static final String SERVICE_DEPT_CODE_NOT_MAINTAINED = "service.dept.code.not.maintained";
    /**
     * 表单字段没有找到对应的值！
     */
    public static final String SERVICE_FORM_NOT_FOUND_VALUE = "service.form.not.found.value";
    /**
     * 请配置环节处理人
     */
    public static final String SERVICE_WORKFLOW_SET_SESSION_HANDLER = "service.workflow.set.session.handler";
    /**
     * 查询下一流程环节出错
     */
    public static final String SERVICE_WORKFLOW_QUERY_NEXT_ERR = "service.workflow.query.next.err";
    /**
     * 查询流程出错
     */
    public static final String SERVICE_WORKFLOW_QUERY_FLOW_ERR = "service.workflow.query.flow.err";
    /**
     * 返回的数据不完整
     */
    public static final String SERVICE_WORKFLOW_DATA_INCOMPLETE = "service.workflow.data.incomplete";
    /**
     * 没有找到环节实例
     */
    public static final String SERVICE_WORKFLOW_NO_LINK_EXAMPLE_FOUND = "service.workflow.no.link.example.found";
    /**
     * 文件编号已存在！
     */
    public static final String FILE_MODIFY_NUM_EXIST = "file.modify.num.exist";
    /**
     * 参数校验异常
     */
    public static final String UTIL_VALIDATOR_PARAMETER_ERR = "util.validator.parameter.err";
    /**
     * 未设置合稿样式!
     */
    public static final String FILE_DOC_NO_COLLAB_STYLE_SET = "file.doc.no.collab.style.set";
    /**
     * 项目下存在文件，请上传该项目下所有文件再删除项目！
     */
    public static final String SERVICE_PROJECT_INFO_EXIST_FILE = "service.project.info.exist.file";
    /**
     * 是内置参数不能删除
     */
    public static final String SYS_BUILT_IN_PARAM_CAN_NOT_DELETE = "sys.built.in.param.can.not.delete";
    /**
     * 已产生新数据,不可删除
     */
    public static final String SERVICE_STANDARD_NEW_DATA_NOT_DELETE = "service.standard.new.data.not.delete";
    /**
     * 删除失败
     */
    public static final String SERVICE_STANDARD_DELETE_FAIL = "service.standard.delete.fail";
    /**
     * 请先维护文件基础信息再关联记录！
     */
    public static final String SERVICE_STANDARD_MAINTAIN_FILE_FIRST = "service.standard.maintain.file.first";
    /**
     * 文件名称重复
     */
    public static final String SERVICE_STANDARD_FILE_NAME_REPEAT = "service.standard.file.name.repeat";
    /**
     * 自动注入异常
     */
    public static final String HANDLER_AUTO_INJECTION_ERR = "handler.auto.injection.err";
    /**
     * 不允许操作超级管理员用户
     */
    public static final String SERVICE_SYS_NOT_ALLOW_OPERATION_ADMIN = "service.sys.not.allow.operation.admin";
    /**
     * 没有权限访问用户数据！
     */
    public static final String SERVICE_SYS_DO_NOT_ACCESS_USER_DATA = "service.sys.do.not.access.user.data";
    /**
     * 导入用户数据不能为空！
     */
    public static final String SERVICE_SYS_IMPORT_USER_DATA_NOT_NULL = "service.sys.import.user.data.not.null";
    /**
     * 没有权限访问部门数据！
     */
    public static final String SERVICE_SYS_DO_NOT_ACCESS_DEPT_DATA = "service.sys.do.not.access.dept.data";
    /**
     * 部门停用，不允许新增
     */
    public static final String SERVICE_SYS_DEPT_DEACTIVATE = "service.sys.dept.deactivate";
    /**
     * 模板编码已存在
     */
    public static final String SYS_TEMPLATE_CODE_EXIST = "sys.template.code.exist";
    /**
     * 新增角色失败，角色名称已存在
     */
    public static final String SYS_ROLE_ADD_FAIL_NAME_EXIST = "sys.role.add.fail.name.exist";
    /**
     * 新增角色失败，角色权限已存在
     */
    public static final String SYS_ROLE_ADD_FAIL_AUTHORITY_EXIST = "sys.role.add.fail.authority.exist";
    /**
     * 修改角色失败，角色名称已存在
     */
    public static final String SYS_ROLE_EDIT_FAIL_NAME_EXIST = "sys.role.edit.fail.name.exist";
    /**
     * 修改角色失败，角色权限已存在
     */
    public static final String SYS_ROLE_EDIT_FAIL_AUTHORITY_EXIST = "sys.role.edit.fail.authority.exist";
    /**
     * 修改角色失败，请联系管理员
     */
    public static final String SYS_ROLE_EDIT_FAIL_ADMIN = "sys.role.edit.fail.admin";
    /**
     * 新增岗位失败，岗位名称已存在
     */
    public static final String SYS_POST_ADD_FAIL_NAME_EXIST = "sys.post.add.fail.name.exist";
    /**
     * 新增岗位失败，岗位编码已存在
     */
    public static final String SYS_POST_ADD_FAIL_CODE_EXIST = "sys.post.add.fail.code.exist";
    /**
     * 修改岗位失败，岗位名称已存在
     */
    public static final String SYS_POST_EDIT_FAIL_NAME_EXIST = "sys.post.edit.fail.name.exist";
    /**
     * 修改岗位失败，岗位编码已存在
     */
    public static final String SYS_POST_EDIT_FAIL_CODE_EXIST = "sys.post.edit.fail.code.exist";
    /**
     * 接收人不能为空
     */
    public static final String SERVICE_MAIL_ACCEPTER_NOT_NULL = "service.mail.accepter.not.null";
    /**
     * 不能为空
     */
    public static final String SERVICE_MAIL_NOT_NULL = "service.mail.not.null";
    /**
     * 新增菜单失败，菜单名称已存在
     */
    public static final String SYS_MENU_ADD_FAIL_NAME_EXIST = "sys.menu.add.fail.name.exist";
    /**
     * 新增菜单失败，地址必须以http(s)://开头
     */
    public static final String SYS_MENU_ADD_FAIL_URL_HTTP = "sys.menu.add.fail.url.http";
    /**
     * 修改菜单失败，菜单名称已存在
     */
    public static final String SYS_MENU_EDIT_FAIL_NAME_EXIST = "sys.menu.edit.fail.name.exist";
    /**
     * 修改菜单失败，地址必须以http(s)://开头
     */
    public static final String SYS_MENU_EDIT_FAIL_URL_HTTP = "sys.menu.edit.fail.url.http";
    /**
     * 修改菜单失败，上级菜单不能选择自己
     */
    public static final String SYS_MENU_EDIT_FAIL_PARENT_CANNOT_CHOOSE_SELF = "sys.menu.edit.fail.parent.cannot.choose.self";
    /**
     * 存在子菜单,不允许删除
     */
    public static final String SYS_MENU_SUB_EXIST = "sys.menu.sub.exist";
    /**
     * 菜单已分配,不允许删除
     */
    public static final String SYS_MENU_ALLOCATE_EXIST = "sys.menu.allocate.exist";
    /**
     * 操作成功
     */
    public static final String RESULT_AJAX_SUCCESS = "result.ajax.success";
    /**
     * 操作失败
     */
    public static final String RESULT_AJAX_FAIL = "result.ajax.fail";
    /**
     * 系统未启用word转pdf功能
     */
    public static final String SERVICE_DOC_BUILD_NO_WORD_TO_PDF = "service.doc.build.no.word.to.pdf";
    /**
     * 异常
     */
    public static final String SERVICE_DOC_BUILD_ERR = "service.doc.build.err";
    /**
     * 流程标题或者接收人为空
     */
    public static final String FLOW_PLAT_MSG_TITLE_RECEIVER_NULL = "flow.plat.msg.title.receiver.null";
    /**
     * 值不可识别
     */
    public static final String FLOW_PLAT_MSG_UNKNOWN_VALUE = "flow.plat.msg.unknown.value";
    /**
     * 接收人数组不能为空
     */
    public static final String SERVICE_YTL_DOC_RECEIVER_ARRAY_NOT_NULL = "service.ytl.doc.receiver.array.not.null";
    /**
     * 文件版本无效
     */
    public static final String SERVICE_YTL_DOC_INVALID_VERSION = "service.ytl.doc.invalid.version";
    /**
     * 参数不全，请检查target、title、content参数。
     */
    public static final String EMAIL_PARAM_INCOMPLETE = "email.param.incomplete";
    /**
     * 发送邮件成功
     */
    public static final String EMAIL_SEND_SUCCESS = "email.send.success";
    /**
     * 发送邮件异常
     */
    public static final String EMAIL_SEND_ERR = "email.send.err";
    /**
     * 系统未启用水印
     */
    public static final String SERVICE_PDF_WM_NOT_ACTIVATED = "service.pdf.wm.not.activated";
    /**
     * 受控章图片文件未找到，请在【签章管理】验证
     */
    public static final String SERVICE_PDF_WM_NOT_FOUND_CONTROLLED_SEAL = "service.pdf.wm.not.found.controlled.seal";
    /**
     * 作废章图片文件未找到，请在【签章管理】验证
     */
    public static final String SERVICE_PDF_WM_NOT_FOUND_VOID_SEAL = "service.pdf.wm.not.found.void.seal";
    /**
     * 分发号图片文件未找到，请在【签章管理】验证
     */
    public static final String SERVICE_PDF_WM_NOT_FOUND_DISTRIBUTION_NUMBER = "service.pdf.wm.not.found.distribution.number";
    /**
     * 流程编号没有数据，请核对！
     */
    public static final String SERVICE_PDF_WM_PROCESS_NUM_NO_DATA = "service.pdf.wm.process.num.no.data";
    /**
     * 根据流程编号,未识别到文件变更操作申请数据，请核对！
     */
    public static final String SERVICE_PDF_WM_NOT_RECOGNIZED_FILE_CHANGE = "service.pdf.wm.not.recognized.file.change";
    /**
     * PDF文件添加水印失败
     */
    public static final String SERVICE_PDF_WM_PDF_ADD_WM_ERR = "service.pdf.wm.pdf.add.wm.err";
    /**
     * PDF文件添加水印异常
     */
    public static final String SERVICE_PDF_WM_PDF_ADD_WM_EXCEPTION = "service.pdf.wm.pdf.add.wm.exception";
    /**
     * 爱数用户令牌为空
     */
    public static final String AS_SSO_USER_TOKEN_EMPTY = "as.sso.user.token.empty";
    /**
     * 爱数用户令牌解析失败
     */
    public static final String AS_SSO_USER_TOKEN_PARSING_FAIL = "as.sso.user.token.parsing.fail";
    /**
     * 体系文件用户令牌有效
     */
    public static final String AS_SSO_SYS_TOKEN_VALID = "as.sso.sys.token.valid";
    /**
     * 体系文件用户令牌生成失败
     */
    public static final String AS_SSO_SYS_TOKEN_GENERATION_FAIL = "as.sso.sys.generation.valid.fail";
    /**
     * 鉴定爱数用户令牌是否有效异常
     */
    public static final String AS_SSO_USER_CHECK_TOKEN = "as.sso.user.check.token";
    /**
     * ssoCheck exception
     */
    public static final String AS_SSO_USER_EXCEPTION = "as.sso.user.exception";
    /**
     * 获取爱数应用账号令牌失败
     */
    public static final String AS_SYNC_ORG_GET_TOKEN_FAIL = "as.sync.org.get.token.fail";
    /**
     * 查询消息失败
     */
    public static final String INDEX_COUNT_QUERY_FAIL = "index.count.query.fail";
    /**
     * 用户新增失败，手机号码已存在
     */
    public static final String SYS_USER_ADD_FAIL_PHONE_EXIST = "sys.user.add.fail.phone.exist";
    /**
     * 用户新增失败，邮箱账号已存在
     */
    public static final String SYS_USER_ADD_FAIL_EMAIL_EXIST = "sys.user.add.fail.email.exist";
    /**
     * 用户修改失败，手机号码已存在
     */
    public static final String SYS_USER_EDIT_FAIL_PHONE_EXIST = "sys.user.edit.fail.phone.exist";
    /**
     * 用户修改失败，邮箱账号已存在
     */
    public static final String SYS_USER_EDIT_FAIL_EMAIL_EXIST = "sys.user.edit.fail.email.exist";
    /**
     * 部门新增失败，部门名称已存在
     */
    public static final String SYS_DEPT_ADD_FAIL_NAME_EXIST = "sys.dept.add.fail.name.exist";
    /**
     * 部门修改失败，部门名称已存在
     */
    public static final String SYS_DEPT_EDIT_FAIL_NAME_EXIST = "sys.dept.edit.fail.name.exist";
    /**
     * 部门修改失败，上级部门不能是自己
     */
    public static final String SYS_DEPT_EDIT_FAIL_PARENT_NOT_SELF = "sys.dept.edit.fail.parent.not.self";
    /**
     * 该部门包含未停用的子部门！
     */
    public static final String SYS_DEPT_CONTAIN_SUB = "sys.dept.contain.sub";
    /**
     * 存在下级部门,不允许删除
     */
    public static final String SYS_DEPT_SUB_EXIST_NOT_DELETE = "sys.dept.sub.exist.not.delete";
    /**
     * 部门存在用户,不允许删除
     */
    public static final String SYS_DEPT_USER_EXIST_NOT_DELETE = "sys.dept.user.exist.not.delete";
    /**
     * 不符合要求，请重新上传
     */
    public static final String DOC_EXTERNAL_NOT_UP_TO_STANDARD = "doc.external.not.up.to.standard";
    /**
     * 更新消息状态失败
     */
    public static final String DOC_MSG_EDIT_MODE_FAIL = "doc.msg.edit.mode.fail";
    /**
     * 入参缺失
     */
    public static final String DOC_MSG_MISS_INPUT_PARAM = "doc.msg.miss.input.param";
    /**
     * 批量发送简单站内消息异常
     */
    public static final String DOC_MSG_BATCH_SEND_ERR = "doc.msg.batch.send.err";
    /**
     * DMS文件已存在！
     */
    public static final String WORKFLOW_API_DMS_EXIST = "workflow.api.dms.exist";
    /**
     * DMS系统异常，请联系管理员
     */
    public static final String WORKFLOW_API_DMS_SYS_ERR = "workflow.api.dms.sys.err";

    /**
     * 审核人重复，系统自动通过
     */
    public static final String WORKFLOW_API_DMS_SYS_AUTOMATIC_SUBMIT = "workflow.api.dms.sys.automatic.submit";

    /**
     * 审核人重复，系统自动通过
     */
    public static final String WORKFLOW_API_DMS_SYS_AUTOMATIC_PROCESS = "workflow.api.dms.sys.automatic.process";
    /**
     * 未启用文件合稿设置!
     */
    public static final String DOC_MERGE_SETTING_NOT_ENABLED = "doc.merge.setting.not.enabled";
    /**
     * 只支持doc/docx文件合稿
     */
    public static final String DOC_MERGE_SUPPORT_DOC_DOCX = "doc.merge.support.doc.docx";
    /**
     * 参数新增失败，参数键名已存在
     */
    public static final String SYS_CONFIG_PARAM_ADD_FAIL_KEY_EXIST = "sys.config.param.add.fail.key.exist";
    /**
     * 参数修改失败，参数键名已存在
     */
    public static final String SYS_CONFIG_PARAM_EDIT_FAIL_KEY_EXIST = "sys.config.param.edit.fail.key.exist";
    /**
     * 还未签收,请签收后再进行操作
     */
    public static final String DOC_DISTRIBUTE_NOT_SIGN_FOR = "doc.distribute.not.sign.for";
    /**
     * 没有权限，请联系管理员授权。
     */
    public static final String HANDLER_GLOBAL_NO_AUTHORITY = "handler.global.no.authority";
    /**
     * 程序访问异常，请截图反馈系统管理员。
     */
    public static final String HANDLER_GLOBAL_PROGRAM_ACCESS_ERR = "handler.global.program.access.err";
    /**
     * 演示模式，不允许操作。
     */
    public static final String HANDLER_GLOBAL_DEMO_MODE = "handler.global.demo.mode";
    /**
     * 没有传入类型参数type
     */
    public static final String UNITE_TASK_NOT_INPUT_TYPE_PARAM = "unite.task.not.input.type.param";
    /**
     * wordFilePath1文件未找到，file path=
     */
    public static final String SERVICE_WORD_PATH1_NOT_FOUND = "service.word.path1.not.found";
    /**
     * wordFilePath2文件未找到，file path=
     */
    public static final String SERVICE_WORD_PATH2_NOT_FOUND = "service.word.path2.not.found";
    /**
     * 保存比对结果文件不存在
     */
    public static final String SERVICE_WORD_SAVE_COMPARE_FILE_NOT_EXIST = "service.word.save.compare.file.not.exist";
    /**
     * 保存结果文件不存在=
     */
    public static final String SERVICE_WORD_SAVE_FILE_NOT_EXIST = "service.word.save.file.not.exist";
    /**
     * word比对文件异常
     */
    public static final String SERVICE_WORD_FILE_COMPARE_ERR = "service.word.file.compare.err";
    /**
     * 成功
     */
    public static final String FILE_BASE_SUCCESS = "file.base.success";
    /**
     * 文件过大
     */
    public static final String SERVICE_STANDARD_FILE_TOO_LARGE = "service.standard.file.too.large";
    /**
     * 已产生新数据,不可替换
     */
    public static final String SERVICE_STANDARD_NEW_DATA_CANNOT_REPLACE = "service.standard.new.data.cannot.replace";
    /**
     * 文件上传异常
     */
    public static final String SERVICE_STANDARD_FILE_UPLOAD_ERR = "service.standard.file.upload.err";
    /**
     * 可以通过
     */
    public static final String WORKFLOW_APPLY_PERMIT_ACCESS = "workflow.apply.permit.access";
    /**
     * 已被
     */
    public static final String WORKFLOW_APPLY_ALREADY = "workflow.apply.already";
    /**
     * 占用，如有疑问请联系此流程编制人员
     */
    public static final String WORKFLOW_APPLY_OCCUPY_TEXT = "workflow.apply.occupy.text";
    /**
     * 的上级文件
     */
    public static final String WORKFLOW_APPLY_SUPERIOR_FILE = "workflow.apply.superior.file";
    /**
     * 的下属记录文件：
     */
    public static final String WORKFLOW_APPLY_SUB_RECORD_FILE = "workflow.apply.sub.record.file";
    /**
     * 正在进行
     */
    public static final String WORKFLOW_APPLY_IN_PROGRESS = "workflow.apply.in.progress";
    /**
     * 流程，如有疑问请联系此流程编制人员
     */
    public static final String WORKFLOW_APPLY_FLOW_TEXT = "workflow.apply.flow.text";
    /**
     * 未配置流程节点，请联系管理员。
     */
    public static final String DOC_CLASS_NO_PROCESS_NODES = "doc.class.no.process.nodes";
    /**
     * 修改个人信息异常，请联系管理员
     */
    public static final String SYS_PROFILE_EDIT_PERSON_INFO_ERR = "sys.profile.edit.person.info.err";
    /**
     * 修改密码失败，旧密码错误
     */
    public static final String SYS_PROFILE_WRONG_OLD_PWD_EDIT_ERR = "sys.profile.wrong.old.pwd.edit.err";
    /**
     * 新密码不能与旧密码相同
     */
    public static final String SYS_PROFILE_OLD_PWD_NOT_EQUAL_NEW_PWD = "sys.profile.old.pwd.not.equal.new.pwd";
    /**
     * 修改密码异常，请联系管理员
     */
    public static final String SYS_PROFILE_EDIT_PWD_ERR = "sys.profile.edit.pwd.err";
    /**
     * 上传图片异常，请联系管理员
     */
    public static final String SYS_PROFILE_UPLOAD_PIC_ERR = "sys.profile.upload.pic.err";
    /**
     * 此文件版本的文件版本不存在
     */
    public static final String DOC_LINK_FILE_VERSION_NOT_EXIST = "doc.link.file.version.not.exist";
    /**
     * 此文件版本的文件台账不存在
     */
    public static final String DOC_LINK_FILE_LEDGER_NOT_EXIST = "doc.link.file.ledger.not.exist";
    /**
     * 未找到该版本文件
     */
    public static final String DOC_PREVIEW_NOT_FOUND_VERSION_FILE = "doc.preview.not.found.version.file";
    /**
     * 签章生效分发异常
     */
    public static final String FILE_SIGNATURE_VALIDATION_ERR = "file.signature.validation.err";
    /**
     * 新增成功
     */
    public static final String INDEX_VUE_ADD_SUCCESS = "index.vue.add.success";
    /**
     * 请求成功
     */
    public static final String DOC_PREVIEW_REQ_SUCCESS = "doc.preview.req.success";
    /**
     * 插入预览数据失败,未找到文档信息
     */
    public static final String DOC_PREVIEW_INSERT_DATA_ERR = "doc.preview.insert.data.err";
    /**
     * 查询成功
     */
    public static final String VERSION_RULE_QUERY_SUCCESS = "version.rule.query.success";
    /**
     * 参数不符合规范，不能进行查询
     */
    public static final String UTIL_SQL_PARAM_INCONSISTENT_STANDARD = "util.sql.param.inconsistent.standard";
    /**
     * 新增培训文件失败
     */
    public static final String MODIFY_APPLY_ADD_TRAIN_FILE_ERR = "modify.apply.add.train.file.err";
    /**
     * 新增重打记录失败
     */
    public static final String REPRINT_LOG_ADD_RETYPE_RECORD_ERR = "reprint.log.add.retype.record.err";
    /**
     * 文件不能为空
     */
    public static final String FILE_STANDARD_FILE_NOT_NULL = "file.standard.file.not.null";
    /**
     * 文件格式不对
     */
    public static final String FILE_STANDARD_FILE_FORMAT_WRONG = "file.standard.file.format.wrong";
    /**
     * 文件编号重复
     */
    public static final String FILE_STANDARD_FILE_ID_REPEAT = "file.standard.file.id.repeat";
    /**
     * 已产生新数据,不可修改
     */
    public static final String FILE_STANDARD_NEW_DATA_NOT_EDIT = "file.standard.new.data.not.edit";
    /**
     * 生成失败
     */
    public static final String DOC_BUILD_GENERATE_ERR = "doc.build.generate.err";
    /**
     * 分发记录不存在
     */
    public static final String FILE_SIGNATURE_DISTRIBUTE_RECORD_NOT_EXIST = "file.signature.distribute.record.not.exist";
    /**
     * dingding消息通道未启用，请排查配置文件。
     */
    public static final String SERVICE_MSG_DD_CHANNEL_NOT_ENABLED = "service.msg.dd.channel.not.enabled";
    /**
     * 获取钉钉应用访问令牌异常=
     */
    public static final String SERVICE_MSG_DD_GET_TOKEN_ERR = "service.msg.dd.get.token.err";
    /**
     * 发送工作通知消息异常
     */
    public static final String SERVICE_MSG_DD_SEND_MSG_ERR = "service.msg.dd.send.msg.err";
    /**
     * 合稿成功
     */
    public static final String DOC_MERGE_COLLABORATED_SUCCESS = "doc.merge.collaborated.success";
    /**
     * 请访问签章地址完成签章
     */
    public static final String DOC_SIGN_VISIT_ADDRESS_TO_COMPLETE = "doc.sign.visit.address.to.complete";

    /**
     * 未找到下载文件保存地址
     */
    public static final String DOC_SIGN_NOT_FOUND_DOWNLOAD_FILE = "doc.sign.not.found.download.file";

    /**
     * 查询统计文件失败
     */
    public static final String DOC_STATISTICS_QUERY_FILE_ERR = "doc.statistics.query.file.err";

    /**
     * 分发回收统计失败!
     */
    public static final String DOC_STATISTICS_DISTRIBUTE_ERR = "doc.statistics.distribute.err";

    /**
     * 打印成功
     */
    public static final String SERVICE_PRINT_SUCCESS = "service.print.success";

    /**
     * 您当前所在的部门可直接打印，无需补发！补发仅限于文件丢失+文件无打印次数情况下申请！
     */
    public static final String DOC_CLASS_PRINT_TEXT = "doc.class.print.text";

    /**
     * 可以访问
     */
    public static final String DOC_CLASS_ACCESSIBLE = "doc.class.accessible";

    /**
     * 没有打印，不能进行补发申请
     */
    public static final String DOC_CLASS_NO_PRINT_CAN_NOT_REISSUE = "doc.class.no.print.can.not.reissue";

    /**
     * 字典新增失败，字典类型已存在
     */
    public static final String SYS_DICT_ADD_FAIL_TYPE_EXIST = "sys.dict.add.fail.type.exist";

    /**
     * 字典修改失败，字典类型已存在
     */
    public static final String SYS_DICT_EDIT_FAIL_TYPE_EXIST = "sys.dict.edit.fail.type.exist";

    /**
     * 已产生新数据不可修改
     */
    public static final String SERVICE_STANDARD_NEW_DATA_NOT_EDIT = "service.standard.new.data.not.edit";


    /**
     * 文件已删除，请重新导出模板
     */
    public static final String SERVICE_STANDARD_FILE_ALREADY_DELETE = "service.standard.file.already.delete";

    /**
     * 文件类型不存在
     */
    public static final String SERVICE_STANDARD_FILE_TYPE_NOT_EXIST = "service.standard.file.type.not.exist";

    /**
     * 文件类型不能为空
     */
    public static final String SERVICE_STANDARD_FILE_TYPE_NOT_NULL = "service.standard.file.type.not.null";

    /**
     * 文件版本号不能为空
     */
    public static final String SERVICE_STANDARD_FILE_VER_NOT_NULL = "service.standard.file.ver.not.null";

    /**
     * 变更类型不存在
     */
    public static final String SERVICE_STANDARD_CHANGE_TYPE_NOT_EXIST = "service.standard.change.type.not.exist";

    /**
     * 变更类型不能为空
     */
    public static final String SERVICE_STANDARD_CHANGE_TYPE_NOT_NULL = "service.standard.change.type.not.null";

    /**
     * 编制部门不存在
     */
    public static final String SERVICE_STANDARD_ORG_DEPT_NOT_EXIST = "service.standard.org.dept.not.exist";

    /**
     * 编制部门不能为空
     */
    public static final String SERVICE_STANDARD_ORG_DEPT_NOT_NULL = "service.standard.org.dept.not.null";

    /**
     * 编制人员不存在
     */
    public static final String SERVICE_STANDARD_ORG_USER_NOT_EXIST = "service.standard.org.user.not.exist";

    /**
     * 保管部门不存在
     */
    public static final String SERVICE_STANDARD_CUSTODY_DEPT_NOT_EXIST = "service.standard.custody.dept.not.exist";

    /**
     * 保管部门不能为空
     */
    public static final String SERVICE_STANDARD_CUSTODY_DEPT_NOT_NULL = "service.standard.custody.dept.not.null";

    /**
     * 生效日期读取错误
     */
    public static final String SERVICE_STANDARD_EFFECT_DATE_READ_ERR = "service.standard.effect.date.read.err";

    /**
     * 发布日期读取错误
     */
    public static final String SERVICE_STANDARD_RELEASE_DATE_READ_ERR = "service.standard.release.date.read.err";

    /**
     * 修订日期读取错误
     */
    public static final String SERVICE_STANDARD_UPDATE_DATE_READ_ERR = "service.standard.update.date.read.err";


    /**
     * 关联文件编号不能为空
     */
    public static final String SERVICE_STANDARD_CORRELATION_FILE_CODE_NOT_NULL = "service.standard.correlation.file.code.not.null";
    /**
     * 主文件版本不能为空
     */
    public static final String SERVICE_STANDARD_MAIN_FILE_VERSION_NOT_NULL = "service.standard.main.file.version.not.null";
    /**
     * 关联文件版本不能为空
     */
    public static final String SERVICE_STANDARD_CORRELATION_FILE_VERSION_NOT_NULL = "service.standard.correlation.file.version.not.null";
    /**
     * 主-文件类型不存在
     */
    public static final String SERVICE_STANDARD_MAIN_FILE_TYPE_NOT_EXIST = "service.standard.main.file.type.not.exist";
    /**
     * 主-文件类型不能为空
     */
    public static final String SERVICE_STANDARD_MAIN_FILE_TYPE_NOT_NULL = "service.standard.main.file.type.not.null";
    /**
     * 关联-文件类型不存在
     */
    public static final String SERVICE_STANDARD_CORRELATION_FILE_TYPE_NOT_EXIST = "service.standard.correlation.file.type.not.exist";
    /**
     * 关联-文件类型不能为空
     */
    public static final String SERVICE_STANDARD_CORRELATION_FILE_TYPE_NOT_NULL = "service.standard.correlation.file.type.not.null";
    /**
     * 主-文件不存在
     */
    public static final String SERVICE_STANDARD_MAIN_FILE_NOT_EXIST = "service.standard.main.file.not.exist";
    /**
     * 主-文件不是有效文件
     */
    public static final String SERVICE_STANDARD_MAIN_FILE_NOT_A_VALID = "service.standard.main.file.not.a.valid";
    /**
     * 主-文件类型与系统存储不一致
     */
    public static final String SERVICE_STANDARD_MAIN_FILE_TYPE_AND_SYS_INCONSISTENT = "service.standard.main.file.type.and.sys.inconsistent";
    /**
     * 关联-文件不存在
     */
    public static final String service_standard_correlation_file_not_exist = "service.standard.correlation.file.not.exist";
    /**
     * 关联-文件不是有效文件
     */
    public static final String service_standard_correlation_file_not_a_valid = "service.standard.correlation.file.not.a.valid";
    /**
     * 关联-文件类型与系统存储不一致
     */
    public static final String service_standard_correlation_file_type_and_sys_inconsistent = "service.standard.correlation.file.type.and.sys.inconsistent";
    /**
     * 数据已存在不予添加
     */
    public static final String service_standard_data_exist_not_add = "service.standard.data.exist.not.add";


    /**
     * 类别不正确，请核对！
     */
    public static final String SERVICE_STANDARD_TYPE_WRONG = "service.standard.type.wrong";

    /**
     * 类别不能为空
     */
    public static final String SERVICE_STANDARD_TYPE_NOT_NULL = "service.standard.type.not.null";

    /**
     * 产线不正确，请核对！
     */
    public static final String SERVICE_STANDARD_PRODUCT_LINE_WRONG = "service.standard.product.line.wrong";

    /**
     * 产线不能为空
     */
    public static final String SERVICE_STANDARD_PRODUCT_LINE_NOT_NULL = "service.standard.product.line.not.null";

    /**
     * 工序不正确，请核对！
     */
    public static final String SERVICE_STANDARD_PROCEDURE_WRONG = "service.standard.procedure.wrong";

    /**
     * 工序不能为空
     */
    public static final String SERVICE_STANDARD_PROCEDURE_NOT_NULL = "service.standard.procedure.not.null";

    /**
     * 保密级别不正确，请核对！
     */
    public static final String SERVICE_STANDARD_SECURITY_LEVEL_WRONG = "service.standard.security.level.wrong";

    /**
     * 保密级别不能为空
     */
    public static final String SERVICE_STANDARD_SECURITY_LEVEL_NOT_NULL = "service.standard.security.level.not.null";



    /**
     * 文件类型
     */
    public static final String STANDARD_FILE_CLASS_NAME = "standard.file.class.name";
    /**
     * 物料编码不能为空
     */
    public static final String STANDARD_MATERIAL_CODE_NOT_NULL = "standard.material.code.not.null";

    /**
     * 物料编码
     */
    public static final String STANDARD_MATERIAL_CODE = "standard.material.code";

    /**
     * 物料描述不能为空
     */
    public static final String STANDARD_MATERIAL_MSG_NOT_NULL = "standard.material.msg.not.null";
    /**
     * 物料描述
     */
    public static final String STANDARD_MATERIAL_MSG = "standard.material.msg";

    /**
     * 设备编码不能为空
     */
    public static final String STANDARD_DEVICE_CODE_NOT_NULL = "standard.device.code.not.null";
    /**
     * 设备编码
     */
    public static final String STANDARD_DEVICE_CODE = "standard.device.code";
    /**
     * 设备名称不能为空
     */
    public static final String STANDARD_DEVICE_NAME_NOT_NULL = "standard.device.name.not.null";
    /**
     * 设备名称
     */
    public static final String STANDARD_DEVICE_NAME = "standard.device.name";
    /**
     * 客户编码不能为空
     */
    public static final String STANDARD_CUSTOMER_CODE_NOT_NULL = "standard.customer.code.not.null";

    /**
     * 重复，请修改
     */
    public static final String STANDARD_DUPLICATE_PLEASE_MODIFY = "standard.duplicate.please.modify";
    /**
     * 已存在，请修改
     */
    public static final String STANDARD_EXISTS_PLEASE_MODIFY = "standard.exists.please.modify";

    /**
     * 文件转pdf
     * 变更前内容
     */
    public static final String FILE_PDF_CHANGE_BEFORE_CONTENT = "file.pdf.change.before.content";
    /**
     * 文件转pdf
     * 变更后内容
     */
    public static final String FILE_PDF_CHANGE_AFTER_CONTENT = "file.pdf.change.after.content";
    /**
     * 文件转pdf
     * 生效日期
     */
    public static final String FILE_PDF_START_DATE = "file.pdf.start.date";

    /**
     * 文件转pdf
     * 分发日期
     */
    public static final String FILE_PDF_DISPENSE_DATE = "file.pdf.dispense.date";

    /**
     * 文件转pdf
     * 发布日期
     */
    public static final String FILE_PDF_RELEASE_DATE = "file.pdf.release.date";

    /**
     * 文件转pdf
     * 作废日期
     */
    public static final String FILE_PDF_CANCEL_DATE = "file.pdf.cancel.date";

    /**
     * 文件转pdf
     * 留用截止日期
     */
    public static final String FILE_PDF_RETAIN_DATE = "file.pdf.retain.date";
    /**
     * 文件转pdf
     * 文件编号
     */
    public static final String FILE_PDF_DOC_ID = "file.pdf.doc.id";
    /**
     * 文件转pdf
     * 版本
     */
    public static final String FILE_PDF_VERSION = "file.pdf.version";

    /**
     *
     */
    public static final String SYS_USER_ADD_FAIL_USERNAME_EXIST = "sys_user_add_fail_username_exist";

    /**
     * 文件转pdf
     * 编制人
     */
    public static final String FILE_PDF_ORGANIZER = "file.pdf.organizer";

    /**
     * 文件转pdf
     * 审核人
     */
    public static final String FILE_PDF_AUDITOR = "file.pdf.auditor";

    /**
     * 文件转pdf
     * 批准人
     */
    public static final String FILE_PDF_APPROVER = "file.pdf.approver";

    /**
     * 文件转pdf
     * 内部文件编号
     */
    public static final String FILE_PDF_INTERNAL_DOC_ID = "file.pdf.internal.doc.id";

    /**
     * id不存在
     */
    public static final String SERVICE_ID_NOT_EXIST = "service.id.not.exist";

    /**
     * 新增菜单失败，菜单名称已存在
     */
    public static final String ONLINE_TYPE_ENUM_ERRATA = "online.type.enum.errata";
}

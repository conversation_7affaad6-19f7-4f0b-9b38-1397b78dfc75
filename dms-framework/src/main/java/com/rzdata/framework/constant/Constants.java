package com.rzdata.framework.constant;

import io.jsonwebtoken.Claims;

/**
 * 通用常量信息
 *
 * <AUTHOR>
 */
public class Constants {
    /**
     * UTF-8 字符集
     */
    public static final String UTF8 = "UTF-8";

    /**
     * GBK 字符集
     */
    public static final String GBK = "GBK";

    /**
     * http请求
     */
    public static final String HTTP = "http://";

    /**
     * https请求
     */
    public static final String HTTPS = "https://";

    /**
     * 通用成功标识
     */
    public static final String SUCCESS = "0";

    /**
     * 通用失败标识
     */
    public static final String FAIL = "1";

    /**
     * 登录成功
     */
    public static final String LOGIN_SUCCESS = "Success";

    /**
     * 注销
     */
    public static final String LOGOUT = "Logout";

    /**
     * 注册
     */
    public static final String REGISTER = "Register";

    /**
     * 登录失败
     */
    public static final String LOGIN_FAIL = "Error";

    /**
     * 验证码 redis key
     */
    public static final String CAPTCHA_CODE_KEY = "captcha_codes:";

    /**
     * 登录用户 redis key
     */
    public static final String LOGIN_TOKEN_KEY = "login_tokens:";

    /**
     * 防重提交 redis key
     */
    public static final String REPEAT_SUBMIT_KEY = "repeat_submit:";

    /**
     * 限流 redis key
     */
    public static final String RATE_LIMIT_KEY = "rate_limit:";

    /**
     * 验证码有效期（分钟）
     */
    public static final Integer CAPTCHA_EXPIRATION = 2;

    /**
     * 令牌
     */
    public static final String TOKEN = "token";

    /**
     * 令牌前缀
     */
    public static final String TOKEN_PREFIX = "Bearer ";

    /**
     * 令牌前缀
     */
    public static final String LOGIN_USER_KEY = "login_user_key";

    /**
     * 用户ID
     */
    public static final String JWT_USERID = "userid";

    /**
     * 用户名称
     */
    public static final String JWT_USERNAME = Claims.SUBJECT;

    /**
     * 用户头像
     */
    public static final String JWT_AVATAR = "avatar";

    /**
     * 创建时间
     */
    public static final String JWT_CREATED = "created";

    /**
     * 用户权限
     */
    public static final String JWT_AUTHORITIES = "authorities";

    /**
     * 参数管理 cache key
     */
    public static final String SYS_CONFIG_KEY = "sys_config:";

    /**
     * 字典管理 cache key
     */
    public static final String SYS_DICT_KEY = "sys_dict:";

    /**
     * 流程实例
     */
    public static final String PROCESS_KEY = "sys.process.key";

    /**
     *  巡检任务状态 未完成
     */
    public static final String PATROL_TASK_STATUS_INCOMPLETE = "incomplete";

    /**
     *  巡检任务状态 已完成
     */
    public static final String PATROL_TASK_STATUS_COMPLETED = "completed";

    /**
     *  空格
     */
    public static final String SPACE = " ";

    /**
     * 0
     */
    public static final String ZERO = "0";

    /**
     * 1
     */
    public static final String ONE = "1";

    /**
     * 1
     */
    public static final String TWO = "2";
    /**
     *  超级管理员 账号
     */
    public static final String ADMIN_USERNAME = "admin";

    /**
     * 爱数access_token
     */
    public static final String ASAS7_TOKEN = "ASAS7_TOKEN";

    /**
     * 是否开启爱数平台上传文件
     */
    public static final String ASAS7_PLATFORM = "ASAS7_PLATFORM";

    public static final String AS_BASE_URL = "AS_BASE_URL";

    public static final String AS_USER_NAME = "AS_USER_NAME";

    public static final String AS_USER_PWD ="AS_USER_PWD";

    public static final String AS_SECRET = "AS_SECRET";

    public static final String AS_FILE_PATH = "AS_FILE_PATH";

    /**
     * 字符串：系统初始化
     */
    public static final String VALUE_INIT = "系统初始化";

    /**
     * 字符串：新增
     */
    public static final String VALUE_ADD = "新增";

    public static final String SLASH = "/";

    public static final String CHARACTER_DROP = ".";

    public static final String ID_SPLIT_KEY = ",";

    /**
     * 分发状态 签收
     */
    public static final String DISTRIBUTE_STATUS_RECEIVE = "receive";
    /**
     * 分发状态 回收
     */
    public static final String DISTRIBUTE_STATUS_RECOVERY = "recovery";
    /**
     * 分发状态 签收
     */
    public static final String DISTRIBUTE_STATUS_LOST = "lost";

    /**
     * 类型 公司
     */
    public static final String TYPE_COMPANY = "company";

    /**
     * 类型 部门
     */
    public static final String TYPE_DEPT = "dept";

    /**
     * 类型 个人
     */
    public static final String TYPE_PERSON = "person";

    /**
     * 类型 部门和个人
     */
    public static final String TYPE_DEPT_PERSON = "dept_person";

    /**
     * 分发类型 打印
     */
    public static final String DISTRIBUTE_TYPE_PRINT = "print";

    /**
     * 分发类型 培训
     */
    public static final String DISTRIBUTE_TYPE_TRAIN ="train";

    /**
     * 类型 角色
     */
    public static final String TYPE_ROLE = "role";

    /**
     * 流程类型 新增
     */
    public static final String  CHANGE_TYPE_ADD = "add";

    /**
     * 流程类型 修订
     */
    public static final String  CHANGE_TYPE_UPDATE = "update";


    /**
     * 流程类型 作废
     */
    public static final String  CHANGE_TYPE_DISUSE = "disuse";

    /**
     * 文档转pdf文件支持的扩展类型参数KEY
     */
    public static final String  FILE_TO_PDF_EXT = "file.pdf.ext";

    public static final String DICT_TYPE_RESET_CYCLE = "reset_cycle";

    public static final String DMS_SUB_NODE = "DMS_SUB_NODE";

    /** 常见特殊字符的正则表达式模式 **/
    public static final String SPECIAL_CHAR_PATTERN = ".*[!@#$%^&*()\\[\\]{}'\"—-].*";

    /** 文件后缀类型 **/
    public static final String FILE_TYPE_PDF = "pdf";

    /** 所有权限标识 **/
    public static final String PERMISSION_ALL = "*:*:*";

    public static final String DOCUMENT_APPLY_DATA_ALL = "document:apply:data:all";

    /** 全局搜索文件开关 **/
    public static final String SEARCH_FILE_SWITCH = "search_file_switch";

    public static final String DATA_TYPE_STDD="stdd";
    /**
     * 参数设置键 主文件作废记录文件是否联动作废
     */
    public static final String RECORD_LINK_DISUSE="record_link_disuse";

    /**
     * 参数设置键 分发范围默认类型
     */
    public static final String DISTRIBUTE_RANGE_TYPE="distribute_range_type";

    /**
     * 是
     */
    public static final String VALUE_Y="Y";

    /**
     * 否
     */
    public static final String VALUE_N="N";

    /**
     * 文件添加方式 导入import
     */
    public static final String ADD_TYPE_IMPORT="import";
    /**
     * 文件添加方式 流程workflow
     */
    public static final String ADD_TYPE_WORKFLOW="workflow";

    /**
     * 字典水印类型key
     */
    public static final String WATERMARK_TYPE_KEY="watermark_type";

    public static final String IMAGE="image";

    public static final String TEXT="text";

    public static final String FILE_NUMBER="file_number";
    public static final String FILE_VERSION="file_version";
    public static final String RELEASE_DATE="release_date";
    public static final String END_DATE="end_date";
    public static final String EFFECTIVE_DATE="effective_date";
    public static final String DISPENSE_DATE="dispense_date";
    public static final String CANCEL_DATE="cancel_date";
    public static final String DISPENSE_TEXT="dispense_text";

    public static final String CONTROL_IMAGE="control_image";
    public static final String CANCEL_IMAGE="cancel_image";
    public static final String DISPENSE_IMAGE="dispense_image";

    public static final String FOREIGN_FILE_IMAGE="foreign_file_image";
    public static final String TEMPORARY_FILE_IMAGE="temporary_file_image";
    public static final String ENGINEERING_DOCUMENTS_CHAPTER="engineering_documents_chapter";

    public static final String BIZ_TYPE_PRINT = "PRINT";

    public static final String ORGANIZER_TEXT="organizer_text";
    public static final String AUDITOR_TEXT="auditor_text";
    public static final String APPROVER_TEXT="approver_text";
    public static final String ORGANIZER_IMAGE="organizer_image";
    public static final String AUDITOR_IMAGE="auditor_image";
    public static final String APPROVER_IMAGE="approver_image";
    public static final String ORGANIZER_HANDLE_DATE="organizer_handle_date";
    public static final String AUDITOR_HANDLE_DATE="auditor_handle_date";
    public static final String APPROVER_HANDLE_DATE="approver_handle_date";
    public static final String INTERNAL_FILE_NUMBER_TEXT="internal_file_number_text";
    public static final String SHELF_LIFE="shelf_life";


    /**
     * 产品类别
     */
    public static final String DOC_PRODUCT_TYPE = "doc_product_type";

    /**
     * 产线
     */
    public static final String DOC_PRODUCT_LINE = "doc_product_line";

    /**
     * 工序
     */
    public static final String DOC_PROCEDURE = "doc_procedure";

    /**
     * 保密级别
     */
    public static final String DOC_SECURITY_LEVEL = "doc_security_level";

    /**
     * 分类所属类型-文件台账
     */
    public static final String CLASS_TYPE_DOC = "DOC";

    /**
     * 分类所属类型-记录台账
     */
    public static final String CLASS_TYPE_RECORD = "RECORD";

    /**
     * 分类所属类型-外来文件
     */
    public static final String CLASS_TYPE_FOREIGN = "FOREIGN";


    public static final String RU_TYPE_DATE = "DATE";
    public static final String RU_TYPE_STR = "STR";
    public static final String RU_TYPE_FD = "FORM";
    public static final String RU_TYPE_SNUM = "SNUM";
    public static final String RU_TYPE_DICT= "DICT";
    public static final String CYCLE_Y = "Y";
    public static final String CYCLE_M = "M";
    public static final String CYCLE_D = "D";
    public static final String CYCLE_NO = "NO";

    /**
     * 租户ID
     */
    public static final String TENANTID_CAM = "CAM";
    public static final String LUOB = "luob";


    /** 版本-本部门-查询其他部门 **/
    public static final String VERSION_QUERY_OTHER_DEPT = "listOtherDept";
    /** 版本-公司-查询集合 **/
    public static final String VERSION_QUERY_LIST = "list";
    /** 版本-外来文件-查询集合 **/
    public static final String VERSION_QUERY_FOREIGN_LIST = "foreignList";

    public static final String FILE_ENCRYT = "file_encryt";

    /**
     * 文件类型-物料编码+物料描述+工厂是否必填
     */
    public static final String TYPE_FORMSHOW = "formShow";
    public static final String TYPE_CUSTOMERSHOW = "formCustomerShow";
    public static final String TYPE_DEVICESHOW = "formDeviceShow";

    public static final String TYPE_PRODUCTVERSIONSHOW = "formProductVersionShow";
    public static final String IN_EFFECT = "有效";
    public static final String INVALID = "失效";

    public static final String YYYY_MM_DD = "yyyy-MM-dd";
    public static final Long LONG_ONE = 1L;

    /** 打印权限 **/
    public static final String DMS_PDF_PRINT = "dms:pdf:print";
    /** 分配打印权限 **/
    public static final String FILE_PRINT_DISTRIBUTION_AUTH = "file:docPrint:distribution:auth";

    /**
     * 应用范围：（主文件：main，版本附件：attach，都支持：all）
     */
    public static final String APPLIED_RANGE_MAIN = "main";
    public static final String APPLIED_RANGE_ATTACH = "attach";

    /** 签名管理查看所有权限 **/
    public static final String SYSTEM_USER_SIGNATURE_SETTINGS = "system:userSignature:settings";



    /**
     * 部门表-类型
     */
    public static final String DEPT_TYPE = "DEPT";


    /**
     * 参数管理 key 国际化
     */
    public static final String SYS_CONFIG_INIT_I18N_BACK = "INIT_I18N_BACK";

    /**
     * 记录文件全局打印权限
     */
    public static final String RECORD_FILE_PRINT_PERMISSION = "record_file_print_permission";
    public static final String NORMAL = "normal";
    public static final String FAILURE = "failure";
    /**
     * 工程样板
     */
    public static final String FILE_G = "G";
    /**
     * 临时文件
     */
    public static final String FILE_Y = "Y";
}

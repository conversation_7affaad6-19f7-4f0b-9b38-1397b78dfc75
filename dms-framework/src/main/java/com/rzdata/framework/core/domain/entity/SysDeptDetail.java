package com.rzdata.framework.core.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rzdata.framework.core.domain.TreeEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * 部门表 sys_dept
 *
 * <AUTHOR> Li
 */

@Data
@Accessors(chain = true)
@TableName("sys_dept_detail")
@ApiModel("部门详情业务对象")
public class SysDeptDetail  {
	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "部门id")
	@TableId(value = "dept_id" ,type = IdType.ASSIGN_ID)
	private String deptId;
	/**
	 * 部门领导编号
	 */
	@ApiModelProperty("部门领导编号")
	private String leaderCode;
	/**
	 * 部门领导姓名
	 */
	@ApiModelProperty("部门领导姓名")
	private String leaderName;
	/**
	 * 部门负责人编号
	 */
	@ApiModelProperty("部门负责人编号")
	private String principalCode;
	/**
	 * 部门负责人姓名
	 */
	@ApiModelProperty("部门负责人姓名")
	private String principalName;
	/**
	 * 部门文控管理员编号
	 */
	@ApiModelProperty("部门文控管理员编号")
	private String docManagerCode;
	/**
	 * 部门文控员姓名
	 */
	@ApiModelProperty("部门文控员姓名")
	private String docManagerName;

}

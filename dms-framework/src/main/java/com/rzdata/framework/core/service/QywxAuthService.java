package com.rzdata.framework.core.service;

import com.jeecg.qywx.api.core.common.AccessToken;
import com.rzdata.framework.core.domain.model.CorpInfo;
import com.rzdata.framework.core.domain.model.LoginUser;

/**
 * @Description:企业微信认证服务
 * @author: gj
 * @date 2024/9/26
 */
public interface QywxAuthService {


    /**
     * 根据企业代码获取企业信息
     *
     * @return 返回企业的相关信息
     */
    CorpInfo getCorpInfo();


    /**
     * 获取访问令牌
     * 通过调用此方法，向服务器请求一个新的访问令牌 该令牌通常用于后续的接口调用，证明客户端的身份
     *
     * @param corpID 企业唯一标识符
     * @param secret 安全密钥
     * @return 返回获取到的访问令牌
     */
    AccessToken getAccessToken(String corpID, String secret);


    /**
     * 根据企业微信用户ID获取DMS用户信息
     *
     * @param wechatUserId 企业微信用户ID
     * @return 返回与企业微信用户ID关联的登录用户信息
     */
    LoginUser getLoginUser(String wechatUserId);


    /**
     * 通过用户姓名获取token
     *
     * @param userName 用户名，用于生成登录令牌
     * @return 生成的登录令牌字符串
     */
    String loginSSO(String userName);


    /**
     * 后端调用企业微信实现单点登录功能
     * 通过调用相关API，完成用户身份验证和授权过程
     */
    void qywxSSOLogin();



}

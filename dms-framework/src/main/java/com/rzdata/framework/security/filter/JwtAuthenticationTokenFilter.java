package com.rzdata.framework.security.filter;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.jeecg.qywx.api.core.common.AccessToken;
import com.jeecg.qywx.api.user.vo.User;
import com.rzdata.framework.core.domain.model.CorpInfo;
import com.rzdata.framework.core.domain.model.LoginUser;
import com.rzdata.framework.core.service.QywxAuthService;
import com.rzdata.framework.core.service.TokenService;
import com.rzdata.framework.utils.QywxUtil;
import com.rzdata.framework.utils.SecurityUtils;
import com.rzdata.framework.utils.StringUtils;
import com.rzdata.framework.utils.UrlUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * token过滤器 验证token有效性
 *
 * <AUTHOR>
 * @updated by gj,2024/09/26, 增加了适配PC端与移动端的认证逻辑，
 * 1）PC端保持不变（基于登陆页用户输入的账密，并形成token，以及相关的认证体系）。
 * 2）移动端则集成企业微信的OAuth认证，默认情况下移动端不存在用户输入账密的场景，通过集成企业微信OAuth,可获取到当前用户在企业微信的user_id,
 * 再通过企业微信的user_id与DMS平台的userId映射关系，即可确认当前用户在DMS平台中的身份信息
 */
@Component
@Slf4j
public class JwtAuthenticationTokenFilter extends OncePerRequestFilter {

    @Autowired
    private TokenService tokenService;

    @Autowired
    private QywxAuthService qywxAuthService;


    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain chain)
            throws ServletException, IOException {
        LoginUser loginUser = tokenService.getLoginUser(request);
        if (StringUtils.isNotNull(loginUser) && StringUtils.isNull(SecurityUtils.getAuthentication())) {
            tokenService.verifyToken(loginUser);
            UsernamePasswordAuthenticationToken authenticationToken = new UsernamePasswordAuthenticationToken(loginUser, null, loginUser.getAuthorities());
            authenticationToken.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
            SecurityContextHolder.getContext().setAuthentication(authenticationToken);
        }
        if (ObjectUtil.isNull(loginUser)) {
            if (UrlUtils.isMobile(request.getHeader("User-Agent"))) {//移动端需要另外处理，走企业微信SSO完成单点登录
                doSSOLogin();
            }
        }

        chain.doFilter(request, response);
    }

    private void doSSOLogin() {
        qywxAuthService.qywxSSOLogin();
        try {
            Thread.sleep(3000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }

//    private void doFilter(HttpServletRequest request, HttpServletResponse response, FilterChain chain)
//            throws ServletException, IOException {
//        String wxagentid = request.getHeader("wxagentid");
//        log.error("wxagentid:" + wxagentid);
//        if (StringUtils.isNotNull(wxagentid)) { //mobile
//            mobileFilter(request, response, chain);
//        } else { //pc
//            pcFilter(request, response, chain);
//        }
//    }
//
//    //pc端认证鉴权
//    private void pcFilter(HttpServletRequest request, HttpServletResponse response, FilterChain chain)
//            throws ServletException, IOException {
//        LoginUser loginUser = tokenService.getLoginUser(request);
//        if (StringUtils.isNotNull(loginUser) && StringUtils.isNull(SecurityUtils.getAuthentication())) {
//            tokenService.verifyToken(loginUser);
//            UsernamePasswordAuthenticationToken authenticationToken = new UsernamePasswordAuthenticationToken(loginUser, null, loginUser.getAuthorities());
//            authenticationToken.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
//            SecurityContextHolder.getContext().setAuthentication(authenticationToken);
//        }
//        chain.doFilter(request, response);
//    }
//
//
//    //移动端认证鉴权，集成企业微信sso认证
//    private void mobileFilter(HttpServletRequest request, HttpServletResponse response, FilterChain chain)
//            throws ServletException, IOException {
//        LoginUser loginUser = tokenService.getLoginUser(request);
//        if (StringUtils.isNotNull(loginUser) && StringUtils.isNull(SecurityUtils.getAuthentication())) {
//            tokenService.verifyToken(loginUser);
//            UsernamePasswordAuthenticationToken authenticationToken = new UsernamePasswordAuthenticationToken(loginUser, null, loginUser.getAuthorities());
//            authenticationToken.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
//            SecurityContextHolder.getContext().setAuthentication(authenticationToken);
//            chain.doFilter(request, response);
//        } else {
//            //通过QywxService，根据???去redis中查询企业微信用户
//            String code = request.getHeader("code");
//            log.error("code:" + code);
//            CorpInfo corpInfo = qywxAuthService.getCorpInfo();
//            if (StringUtils.isNotEmpty(code)) {
//                AccessToken accessToken = qywxAuthService.getAccessToken(corpInfo.getCorpId(), corpInfo.getSecret()); //通过QywxService 获取
//                if (ObjectUtil.isNotNull(accessToken)) {
//                    log.error(JSON.toJSONString(accessToken));
//                }
//                User wechatUser = QywxUtil.getUserInfoByCode(code, accessToken.getAccesstoken());
//                if (null != wechatUser) {
//                    log.error(JSON.toJSONString(wechatUser));
//                    //根据企业微信user获取DMS中的dmsUser, 并将dmsUser转化为loginUser
//                    loginUser = qywxAuthService.getLoginUser(wechatUser.getUserid());
//                    String token = qywxAuthService.loginSSO(loginUser.getUsername());
//                    if (ObjectUtil.isNotNull(loginUser)) {
//                        log.error(JSON.toJSONString(loginUser));
//                    }
//                    UsernamePasswordAuthenticationToken authenticationToken = new UsernamePasswordAuthenticationToken(loginUser, null, loginUser.getAuthorities());
//                    authenticationToken.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
//                    SecurityContextHolder.getContext().setAuthentication(authenticationToken);
//                    chain.doFilter(request, response);
////                    response.sendRedirect(baseUrl + "?redirect="+ redirectUri + "&token=" + token);
//                }
//                chain.doFilter(request, response);
//            }
//        }
//    }
}

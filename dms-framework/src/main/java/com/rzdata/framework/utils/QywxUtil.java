package com.rzdata.framework.utils;

import com.alibaba.fastjson.JSONObject;
import com.jeecg.qywx.api.base.JwAccessTokenAPI;
import com.jeecg.qywx.api.core.common.AccessToken;
import com.jeecg.qywx.api.user.JwUserAPI;
import com.jeecg.qywx.api.user.vo.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;


@Slf4j
@Component
public class QywxUtil {

    //发起企业微信网页授权请求链接
    private static String url = "https://open.weixin.qq.com/connect/oauth2/authorize?appid=CORPID&redirect_uri=REDIRECT_URI&response_type=code&scope=SCOPE&state=STATE#wechat_redirect";

    private static final String SCOPE = "snsapi_base";//默认静默授权

    /*public static void main(String[] args) {
        authorize("ww17eb31c36ede9f60", "http://www.baidu.com", "DMS");
    }*/

    /**
     * 发起企业微信网页授权
     *
     * @param corpId      企业微信ID
     * @param redirectUrl 授权后重定向的回调链接地址，请使用urlencode对链接进行处理
     * @param state       重定向后会带上state参数，企业可以填写a-zA-Z0-9的参数值，长度不可超过128个字节
     * @return null
     */
    public static void authorize(String corpId, String redirectUrl, String state, HttpServletResponse response) {
        //token过期前重新获取
        try {
            String authUrl = url.replace("CORPID", corpId)
                    .replace("REDIRECT_URI", URLEncoder.encode(redirectUrl, "utf8"))
                    .replace("STATE", state)
                    .replace("SCOPE", SCOPE);
            log.error("authUrl========>" + authUrl);
            response.sendRedirect(authUrl);
            //com.jeecg.qywx.api.core.util.HttpUtil.sendGet(authUrl);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    /**
     * 获取企业微信应用的访问令牌
     * 当令牌即将过期时，该方法将重新获取新的令牌
     *
     * @param corpID 企业ID，标识企业的小程序或企业应用
     * @param secret 应用密钥，企业微信应用的密钥
     * @return AccessToken 包含访问令牌的实体类对象
     */
    public static AccessToken getAccessToken(String corpID, String secret) {
        try {
            AccessToken token = JwAccessTokenAPI.getAccessToken(corpID, secret);
            return token;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }


    /**
     * 通过code获取用户信息
     *
     * @param code        用户代码
     * @param accessToken 访问令牌
     * @return 返回User对象，包含用户信息如果请求失败或用户信息无效，则返回null
     * <p>
     * 该方法调用第三方API来获取用户信息
     */
    public static User getUserInfoByCode(String code, String accessToken) {
        try {
            JSONObject jsonObject = JwUserAPI.getUserInfoByCode(code, accessToken);
            if (null != jsonObject) {
                log.error(JSONObject.toJSONString(jsonObject));
                int errcode = jsonObject.getIntValue("errcode");
                if (0 == errcode) {
                    User user = (User) JSONObject.toJavaObject(jsonObject, User.class);
                    return user;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }


}

package com.rzdata.framework.utils;

import cn.hutool.core.map.MapUtil;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.Pattern;

/**
 * @auther xcy
 * @create 2022-01-05 14:06
 * url 工具类
 */
public class UrlUtils {

    private static final String PHONE_REG = "\\b(ip(hone|od)|android|opera m(ob|in)i" +
            "|windows (phone|ce)|blackberry" +
            "|s(ymbian|eries60|amsung)|p(laybook|alm|rofile/midp" +
            "|laystation portable)|nokia|fennec|htc[-_]" +
            "|mobile|up.browser|[1-4][0-9]{2}x[1-4][0-9]{2})\\b";
    private static final String TABLET_REG = "\\b(ipad|tablet|(Nexus 7)|up.browser" +
            "|[1-4][0-9]{2}x[1-4][0-9]{2})\\b";
    private static final Pattern PHONE_PAT = Pattern.compile(PHONE_REG, Pattern.CASE_INSENSITIVE);
    private static final Pattern TABLET_PAT = Pattern.compile(TABLET_REG, Pattern.CASE_INSENSITIVE);

    /**
     * 获取请求地址中的某个参数
     * @param url
     * @param name
     * @return
     */
    public static String getParam(String url, String name) {
        Map<String, String> map = urlSplit(url);
        return MapUtil.getStr(map , name);
    }

    /**
     * 去掉url中的路径，留下请求参数部分
     * @param url url地址
     * @return url请求参数部分
     */
    private static String truncateUrlPage(String url) {
        String strAllParam = null;
        String[] arrSplit = null;
        url = url.trim().toLowerCase();
        arrSplit = url.split("[?]");
        if (url.length() > 1) {
            if (arrSplit.length > 1) {
                for (int i = 1; i < arrSplit.length; i++) {
                    strAllParam = arrSplit[i];
                }
            }
        }
        return strAllParam;
    }

    /**
     * 将参数存入map集合
     * @param url  url地址
     * @return url请求参数部分存入map集合
     */
    public static Map<String, String> urlSplit(String url) {
        Map<String, String> mapRequest = new HashMap<String, String>();
        String[] arrSplit = null;
        String strUrlParam = truncateUrlPage(url);
        if (strUrlParam == null) {
            return mapRequest;
        }
        arrSplit = strUrlParam.split("[&]");
        for (String strSplit : arrSplit) {
            String[] arrSplitEqual = null;
            arrSplitEqual = strSplit.split("[=]");
            //解析出键值
            if (arrSplitEqual.length > 1) {
                //正确解析
                mapRequest.put(arrSplitEqual[0], arrSplitEqual[1]);
            } else {
                if (arrSplitEqual[0] != "") {
                    //只有参数没有值，不加入
                    mapRequest.put(arrSplitEqual[0], "");
                }
            }
        }
        return mapRequest;
    }

    /**
     * 判断给定的用户代理字符串是否属于移动设备
     *
     * @param userAgent 用户代理字符串，通常来自HTTP请求头
     * @return 如果用户代理表示移动设备，则返回true；否则返回false
     */
    public static boolean isMobile(String userAgent) {
        if (null == userAgent) {
            userAgent = "";
        }
        return PHONE_PAT.matcher(userAgent).find() || TABLET_PAT.matcher(userAgent).find();
    }
}

package com.rzdata.framework.utils.poi;

import cn.hutool.core.util.IdUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.rzdata.framework.constant.CommonI18nConstant;
import com.rzdata.framework.convert.ExcelBigNumberConvert;
import com.rzdata.framework.core.service.ExcelListener;
import com.rzdata.framework.core.service.ExcelResult;
import com.rzdata.framework.handler.SerialNumberCellWriteHandler;
import com.rzdata.framework.utils.I18nUtils;
import com.rzdata.framework.utils.StringUtils;
import com.rzdata.framework.utils.file.FileUtils;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.Map;

/**
 * Excel相关处理
 *
 * <AUTHOR> Li
 */
public class ExcelUtil {

	/**
	 * 对excel表单默认第一个索引名转换成list（EasyExcel）
	 *
	 * @param is 输入流
	 * @return 转换后集合
	 */
	public static <T> List<T> importExcel(InputStream is, Class<T> clazz) {
		return EasyExcel.read(is).head(clazz).autoCloseStream(false).sheet().doReadSync();
	}

	/**
	 * 对导入的文件进行读取，并执行自定义的监听器（EasyExcel）
	 * @param is
	 * @param clazz
	 * @param readListener
	 * @return
	 * @param <T>
	 */
	public static <T> ExcelResult<T> importExcel(InputStream is, Class<T> clazz, ExcelListener<T> readListener) {
		EasyExcel.read(is,clazz,readListener).autoCloseStream(false).sheet().doRead();
		return readListener.getExcelResult();
	}

	/**
	 * 对list数据源将其里面的数据导入到excel表单（EasyExcel）
	 *
	 * @param list      导出数据集合
	 * @param sheetName 工作表的名称
	 * @return 结果
	 */
	public static <T> void exportExcel(List<T> list, String sheetName, Class<T> clazz, HttpServletResponse response) {
		try {
			String filename = encodingFilename(sheetName);
			response.reset();
			FileUtils.setAttachmentResponseHeader(response, filename);
			response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8");
			ServletOutputStream os = response.getOutputStream();
			EasyExcel.write(os, clazz)
					.autoCloseStream(false)
					// 自动适配
					.registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
					.registerWriteHandler(new SerialNumberCellWriteHandler())
					// 大数值自动转换 防止失真
					.registerConverter(new ExcelBigNumberConvert())
					.sheet(sheetName).doWrite(list);
		} catch (IOException e) {
			throw new RuntimeException(I18nUtils.getTitle(CommonI18nConstant.FILE_PDF_ERROR));
		}
	}
	public static <T> void exportExcel(List<T> list, String sheetName, Class<T> clazz, HttpServletResponse response,List<String> excludeColNames) {
		try {
			String filename = encodingFilename(sheetName);
			response.reset();
			FileUtils.setAttachmentResponseHeader(response, filename);
			response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8");
			ServletOutputStream os = response.getOutputStream();
			EasyExcel.write(os, clazz)
					.autoCloseStream(false)
					// 自动适配
					.registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
					.registerWriteHandler(new SerialNumberCellWriteHandler())
					// 大数值自动转换 防止失真
					.registerConverter(new ExcelBigNumberConvert())
					// 忽略字段
					.excludeColumnFiledNames(excludeColNames)
					.sheet(sheetName).doWrite(list);
		} catch (IOException e) {
			throw new RuntimeException(I18nUtils.getTitle(CommonI18nConstant.FILE_PDF_ERROR));
		}
	}

	/**
	 *
	 * EasyExcel 填充报表
	 *
	 * @param response
	 * @param sheetAndDataMap  key:sheet页，value:填充的list集合
	 */
	public static void exportExcel(Map<String, List<?>> sheetAndDataMap, String filePath, HttpServletResponse response){
		try {
			String filename = encodingFilename("历史文件初始化导出模板");
			response.reset();
			FileUtils.setAttachmentResponseHeader(response, filename);
//			response.reset();
//			response.addHeader("Access-Control-Allow-Origin", "*");
//			response.addHeader("Access-Control-Expose-Headers", "Content-Disposition");
			response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8");
			ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream()).withTemplate(filePath).build();
			for(Map.Entry<String, List<?>> entry : sheetAndDataMap.entrySet()){
				List<?> value = entry.getValue();
				WriteSheet writeSheet = EasyExcel.writerSheet(Integer.valueOf(entry.getKey())).build();
				FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.TRUE).build();
				excelWriter.fill(value, fillConfig, writeSheet);
			}
			excelWriter.finish();
		}catch (Exception e){
			e.printStackTrace();
		}
	}



	/**
	 *
	 * EasyExcel 填充报表
	 *
	 * @param response
	 * @param sheetAndDataMap  key:sheet页，value:填充的list集合
	 */
	public static void exportExcel(Map<String, List<?>> sheetAndDataMap, String filePath,
								   String filename,
								   HttpServletResponse response){
		try {
			response.reset();
			FileUtils.setAttachmentResponseHeader(response, filename);
			response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8");
			ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream()).withTemplate(filePath).build();
			for(Map.Entry<String, List<?>> entry : sheetAndDataMap.entrySet()){
				List<?> value = entry.getValue();
				WriteSheet writeSheet = EasyExcel.writerSheet(Integer.valueOf(entry.getKey())).build();
				FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.TRUE).build();
				excelWriter.fill(value, fillConfig, writeSheet);
			}
			excelWriter.finish();
		}catch (Exception e){
			e.printStackTrace();
		}
	}

	/**
	 * 解析导出值 0=男,1=女,2=未知
	 *
	 * @param propertyValue 参数值
	 * @param converterExp  翻译注解
	 * @param separator     分隔符
	 * @return 解析后值
	 */
	public static String convertByExp(String propertyValue, String converterExp, String separator) {
		StringBuilder propertyString = new StringBuilder();
		String[] convertSource = converterExp.split(",");
		for (String item : convertSource) {
			String[] itemArray = item.split("=");
			if (StringUtils.containsAny(separator, propertyValue)) {
				for (String value : propertyValue.split(separator)) {
					if (itemArray[0].equals(value)) {
						propertyString.append(itemArray[1] + separator);
						break;
					}
				}
			} else {
				if (itemArray[0].equals(propertyValue)) {
					return itemArray[1];
				}
			}
		}
		return StringUtils.stripEnd(propertyString.toString(), separator);
	}

	/**
	 * 反向解析值 男=0,女=1,未知=2
	 *
	 * @param propertyValue 参数值
	 * @param converterExp  翻译注解
	 * @param separator     分隔符
	 * @return 解析后值
	 */
	public static String reverseByExp(String propertyValue, String converterExp, String separator) {
		StringBuilder propertyString = new StringBuilder();
		String[] convertSource = converterExp.split(",");
		for (String item : convertSource) {
			String[] itemArray = item.split("=");
			if (StringUtils.containsAny(separator, propertyValue)) {
				for (String value : propertyValue.split(separator)) {
					if (itemArray[1].equals(value)) {
						propertyString.append(itemArray[0] + separator);
						break;
					}
				}
			} else {
				if (itemArray[1].equals(propertyValue)) {
					return itemArray[0];
				}
			}
		}
		return StringUtils.stripEnd(propertyString.toString(), separator);
	}

	/**
	 * 编码文件名
	 */
	public static String encodingFilename(String filename) {
		return IdUtil.fastSimpleUUID() + "_" + filename + ".xlsx";
	}

}

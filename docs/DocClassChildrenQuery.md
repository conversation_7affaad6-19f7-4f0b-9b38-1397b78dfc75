# 文件分类子级查询功能

## 功能概述

新增了根据文件分类ID查找所有子级的功能，基于 `ancestors` 字段进行查询。

## 数据结构说明

根据您提供的SQL数据：
```sql
INSERT INTO dms_mehow.basic_doc_class (ancestors) VALUES ('0,1,1791269862321500162');
INSERT INTO dms_mehow.basic_doc_class (ancestors) VALUES ('0,1791293188540542978');
INSERT INTO dms_mehow.basic_doc_class (ancestors) VALUES ('0,1,1791668356344725505');
INSERT INTO dms_mehow.basic_doc_class (ancestors) VALUES ('0,1,1791668623731605506');
```

`ancestors` 字段存储了从根节点到当前节点的完整路径，用逗号分隔。

## 新增方法

### 1. Service接口方法

```java
/**
 * 根据ID查找所有子级（基于ancestors字段）
 * @param id 父级ID
 * @return 所有子级的DocClass列表
 */
List<DocClassVo> findAllChildrenById(String id);

/**
 * 根据ID查找所有子级ID（基于ancestors字段）
 * @param id 父级ID
 * @return 所有子级的ID列表
 */
List<String> findAllChildrenIdsById(String id);
```

### 2. 实现逻辑

使用 `FIND_IN_SET` 函数查询 `ancestors` 字段包含指定ID的所有记录：

```java
@Override
public List<DocClassVo> findAllChildrenById(String id) {
    if (StringUtils.isBlank(id)) {
        return new ArrayList<>();
    }
    
    // 使用 FIND_IN_SET 查询 ancestors 字段包含指定 id 的所有记录
    LambdaQueryWrapper<DocClass> wrapper = new LambdaQueryWrapper<DocClass>()
            .apply("FIND_IN_SET({0}, ancestors)", id)
            .eq(DocClass::getClassStatus, Constants.ONE) // 只查询有效的分类
            .orderByAsc(DocClass::getSort);
    
    return listVo(wrapper);
}
```

### 3. Controller接口

```java
/**
 * 根据ID查找所有子级分类
 */
@ApiOperation("根据ID查找所有子级分类")
@GetMapping("/children/{id}")
public AjaxResult<List<DocClassVo>> findAllChildren(@PathVariable String id) {
    List<DocClassVo> children = iDocClassService.findAllChildrenById(id);
    return AjaxResult.success(children);
}

/**
 * 根据ID查找所有子级分类ID
 */
@ApiOperation("根据ID查找所有子级分类ID")
@GetMapping("/children/ids/{id}")
public AjaxResult<List<String>> findAllChildrenIds(@PathVariable String id) {
    List<String> childrenIds = iDocClassService.findAllChildrenIdsById(id);
    return AjaxResult.success(childrenIds);
}
```

## 使用示例

### 1. 在Service中使用

```java
@Autowired
private IDocClassService docClassService;

// 查找ID为"1"的所有子级分类
List<DocClassVo> children = docClassService.findAllChildrenById("1");

// 只获取子级ID列表
List<String> childrenIds = docClassService.findAllChildrenIdsById("1");
```

### 2. 通过API调用

```bash
# 获取所有子级分类详情
GET /setting/docClass/children/1

# 只获取子级ID列表
GET /setting/docClass/children/ids/1
```

### 3. 前端调用示例

```javascript
// 获取子级分类详情
this.$http.get('/setting/docClass/children/' + parentId).then(response => {
    const children = response.data.data;
    console.log('子级分类:', children);
});

// 获取子级ID列表
this.$http.get('/setting/docClass/children/ids/' + parentId).then(response => {
    const childrenIds = response.data.data;
    console.log('子级ID列表:', childrenIds);
});
```

## 查询逻辑说明

根据您的数据示例：
- 查询 `id = "1"` 的子级，会返回 ancestors 包含 "1" 的所有记录
- 即：`1791269862321500162`, `1791668356344725505`, `1791668623731605506`
- 查询 `id = "1791269862321500162"` 的子级，会返回 ancestors 包含该ID的记录

## 性能优化

1. **索引建议**：在 `ancestors` 字段上建立索引以提高查询性能
2. **状态过滤**：只查询有效状态的分类（`class_status = 1`）
3. **排序优化**：按 `sort` 字段排序，保证结果有序

## 测试

运行测试类 `DocClassServiceTest` 来验证功能：

```bash
mvn test -Dtest=DocClassServiceTest
```

## 注意事项

1. 传入的ID不能为空
2. 只返回状态为有效的分类
3. 结果按排序字段排序
4. 使用 `FIND_IN_SET` 函数，确保数据库支持该函数

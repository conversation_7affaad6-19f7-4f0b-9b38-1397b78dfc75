CREATE TABLE `basic_doc_class_watermark_rule` (
  `id` varchar(32) NOT NULL COMMENT '主键',
  `class_id` varchar(32) NOT NULL COMMENT '分类ID',
  `class_name` varchar(100) NOT NULL COMMENT '分类名称',
  `class_code` varchar(50) NOT NULL COMMENT '分类编码',
  `watermark_content` varchar(500) NOT NULL COMMENT '水印内容',
  `watermark_font` varchar(50) DEFAULT NULL COMMENT '水印字体',
  `watermark_size` int DEFAULT NULL COMMENT '水印大小',
  `watermark_color` varchar(20) DEFAULT NULL COMMENT '水印颜色',
  `watermark_opacity` int DEFAULT NULL COMMENT '水印透明度',
  `watermark_rotation` int DEFAULT NULL COMMENT '水印角度',
  `watermark_position` varchar(20) DEFAULT NULL COMMENT '水印位置',
  `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_class_id` (`class_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='文档水印规则表'; 
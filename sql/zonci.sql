INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible,
                      status, perms, icon, create_by, create_time, update_by, update_time, remark, tenant_id, open_type,
                      iframe_url)
VALUES ('下推分类映射', 2175, 3, 'type_settings/push_class', 'file_settings/pushClass/index', '{"dataType":"stdd"}',
        1, 0, 'C', '0', '0', null, '#', 'admin', '2021-12-24 09:36:52', 'admin', '2023-07-18 16:15:07', '',
        'dc41618350206272c0b3271ccb9c3c76', 'route', null);

-- 为 basic_doc_class 表添加新字段
ALTER TABLE basic_doc_class
    ADD COLUMN subsidiary_company varchar(50) NULL COMMENT '下推公司',
    ADD COLUMN subsidiary_doc_class_id varchar(50) NULL COMMENT '下推文件分类ID',
    ADD COLUMN subsidiary_doc_class_name varchar(50) NULL COMMENT '下推文件分类名称',
    ADD COLUMN connector_id varchar(50) NULL COMMENT '接收人ID',
    ADD COLUMN connector varchar(100) NULL COMMENT '接收人',
    ADD COLUMN update_by varchar(50) NULL COMMENT '修改人';
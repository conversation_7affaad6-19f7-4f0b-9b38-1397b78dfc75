INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible,
                      status, perms, icon, create_by, create_time, update_by, update_time, remark, tenant_id, open_type,
                      iframe_url)
VALUES ('下推分类映射', 2175, 3, 'type_settings/push_class', 'file_settings/pushClass/index', '{"dataType":"stdd"}',
        1, 0, 'C', '0', '0', null, '#', 'admin', '2021-12-24 09:36:52', 'admin', '2023-07-18 16:15:07', '',
        'dc41618350206272c0b3271ccb9c3c76', 'route', null);



INSERT INTO sys_dict_type (dict_name, dict_type, status, create_by, create_time, update_by, update_time, remark, tenant_id) VALUES ('下推公司', 'downpush_company', '0', 'admin', '2025-08-05 14:35:11', 'admin', '2025-08-05 14:35:11', null, null);
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark, customer_id, tenant_id) VALUES (0, '三河', 'sanhe', 'downpush_company', null, 'default', 'N', '0', 'admin', '2025-08-05 14:40:47', 'admin', '2025-08-05 14:40:47', null, null, 'dc41618350206272c0b3271ccb9c3c76');


-- 为 basic_doc_class 表添加新字段
ALTER TABLE basic_doc_class
    ADD COLUMN subsidiary_company varchar(50) NULL COMMENT '下推公司',
    ADD COLUMN subsidiary_doc_class_id varchar(50) NULL COMMENT '下推文件分类ID',
    ADD COLUMN subsidiary_doc_class_name varchar(50) NULL COMMENT '下推文件分类名称',
    ADD COLUMN connector_code varchar(50) NULL COMMENT '接收人员编码',
    ADD COLUMN connector varchar(100) NULL COMMENT '接收人',
    ADD COLUMN update_by varchar(50) NULL COMMENT '修改人';



-- 为 doc_modify_apply 表添加新字段
ALTER TABLE doc_modify_apply
    ADD COLUMN is_subsidiary_file varchar(1) DEFAULT 'N' NULL COMMENT '是否下推文件 Y是 N否',
    ADD COLUMN subsidiary_company varchar(100) NULL COMMENT '下推公司',
    ADD COLUMN subsidiary_category_id varchar(50) NULL COMMENT '下推分类ID',
    ADD COLUMN subsidiary_category varchar(50) NULL COMMENT '下推分类',
    ADD COLUMN connector_code varchar(50) NULL COMMENT '接收人员编码',
    ADD COLUMN connector varchar(50) NULL COMMENT '接收人员',
    ADD COLUMN reply_status varchar(50) NULL COMMENT '回复状态';

-- 为 doc_version表添加新字段
ALTER TABLE doc_version
    ADD COLUMN is_subsidiary_file varchar(1) DEFAULT 'N' NULL COMMENT '是否下推文件 Y是 N否',
    ADD COLUMN subsidiary_company varchar(100) NULL COMMENT '下推公司',
    ADD COLUMN subsidiary_category_id varchar(50) NULL COMMENT '下推分类ID',
    ADD COLUMN subsidiary_category varchar(50) NULL COMMENT '下推分类',
    ADD COLUMN connector_code varchar(50) NULL COMMENT '接收人员编码',
    ADD COLUMN connector varchar(50) NULL COMMENT '接收人员',
    ADD COLUMN reply_status varchar(50) NULL COMMENT '回复状态';
ALTER TABLE basic_doc_class
    ADD COLUMN subsidiary_company        varchar(100)  NULL COMMENT '下推公司',
    ADD COLUMN subsidiary_doc_class_id   varchar(50)   NULL COMMENT '下推文件分类ID',
    ADD COLUMN subsidiary_doc_class_name varchar(1000) NULL COMMENT '下推文件分类名称',
    ADD COLUMN connector_id              varchar(50)   NULL COMMENT '接收人ID',
    ADD COLUMN connector                 varchar(100)  NULL COMMENT '接收人',
    ADD COLUMN update_by                 varchar(50)   NULL COMMENT '修改人';

INSERT INTO sys_dict_type (dict_name, dict_type, status, create_by, create_time, update_by, update_time, remark,
                           tenant_id)
VALUES ('下推公司', 'downpush_company', '0', 'admin', '2025-08-05 14:35:11', 'admin', '2025-08-05 14:35:11', null,
        null);
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status,
                           create_by, create_time, update_by, update_time, remark, customer_id, tenant_id)
VALUES (0, '三河', 'sanhe', 'downpush_company', null, 'default', 'N', '0', 'admin', '2025-08-05 14:40:47', 'admin',
        '2025-08-05 14:40:47', null, null, 'dc41618350206272c0b3271ccb9c3c76');


-- 新增字段国际化SQL语句
-- 下推公司 (subsidiary_company)
INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by,
                                         update_time)
VALUES (UUID(), 'file_set.subsidiary_company', '下推公司', 'zh', 'front', 'CAM', 'admin', NOW(), 'admin', NOW());

INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by,
                                         update_time)
VALUES (UUID(), 'file_set.subsidiary_company', 'Subsidiary Company', 'en', 'front', 'CAM', 'admin', NOW(), 'admin',
        NOW());

-- 下推文件分类名称 (subsidiary_doc_class_name)
INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by,
                                         update_time)
VALUES (UUID(), 'file_set.subsidiary_doc_class_name', '下推文件分类名称', 'zh', 'front', 'CAM', 'admin', NOW(), 'admin',
        NOW());

INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by,
                                         update_time)
VALUES (UUID(), 'file_set.subsidiary_doc_class_name', 'Subsidiary Document Class Name', 'en', 'front', 'CAM', 'admin',
        NOW(), 'admin', NOW());

-- 接收人 (connector)
INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by,
                                         update_time)
VALUES (UUID(), 'file_set.connector', '接收人', 'zh', 'front', 'CAM', 'admin', NOW(), 'admin', NOW());

INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by,
                                         update_time)
VALUES (UUID(), 'file_set.connector', 'Connector', 'en', 'front', 'CAM', 'admin', NOW(), 'admin', NOW());

-- 操作时间 (operation_time)
INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by,
                                         update_time)
VALUES (UUID(), 'file_set.operation_time', '操作时间', 'zh', 'front', 'CAM', 'admin', NOW(), 'admin', NOW());

INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by,
                                         update_time)
VALUES (UUID(), 'file_set.operation_time', 'Operation Time', 'en', 'front', 'CAM', 'admin', NOW(), 'admin', NOW());

-- 操作人 (operator)
INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by,
                                         update_time)
VALUES (UUID(), 'file_set.operator', '操作人', 'zh', 'front', 'CAM', 'admin', NOW(), 'admin', NOW());

INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by,
                                         update_time)
VALUES (UUID(), 'file_set.operator', 'Operator', 'en', 'front', 'CAM', 'admin', NOW(), 'admin', NOW());


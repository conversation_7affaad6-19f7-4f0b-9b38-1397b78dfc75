import request from '@/utils/request'

// 查询文件版本记录列表
export function listVersion(query) {
  return request({
    url: '/process/version/list',
    method: 'post',
    data: query
  })
}

// 查询项目文件版本记录列表
export function listProjectVersion(query) {
  return request({
    url: '/process/version/listProject',
    method: 'get',
    params: query
  })
}

// 查询体系文件版本记录列表
export function listStddVersion(query) {
  return request({
    url: '/process/version/listStdd',
    method: 'get',
    params: query
  })
}

// 查询文件版本记录列表(原始版本)
export function listVersionOriginal(query) {
  return request({
    url: '/process/version/listVersion',
    method: 'get',
    params: query
  })
}

// 查询文件版本外部门列表
export function listOtherDeptVersion(query) {
  return request({
    url: '/process/version/list/other/dept',
    method: 'post',
    data: query
  })
}

// 查询文件版本记录详细
export function getVersion(id) {
  return request({
    url: '/process/version/' + id,
    method: 'get'
  })
}

// 查询版本详情（包含关联信息）
export function getVersionDetail(docId, versionValue) {
  return request({
    url: '/process/version/detail',
    method: 'get',
    params: {
      docId: docId,
      versionValue: versionValue
    }
  })
}

// 新增文件版本记录
export function addVersion(data) {
  return request({
    url: '/process/version',
    method: 'post',
    data: data
  })
}

// 修改文件版本记录
export function updateVersion(data) {
  return request({
    url: '/process/version',
    method: 'put',
    data: data
  })
}

// 删除文件版本记录
export function delVersion(id) {
  return request({
    url: '/process/version/' + id,
    method: 'delete'
  })
}

// 导出文件版本记录
export function exportVersion(query) {
  return request({
    url: '/process/version/export',
    method: 'post',
    data: query
  })
}

// 根据文档ID查询版本列表
export function getVersionsByDocId(docId) {
  return request({
    url: '/process/version/listByDocId',
    method: 'get',
    params: { docId: docId }
  })
}

// 根据申请ID查询版本列表
export function getVersionsByApplyId(applyId) {
  return request({
    url: '/process/version/listByApplyId',
    method: 'get',
    params: { applyId: applyId }
  })
}

// 检查文档ID是否存在
export function checkDocIdExist(docId) {
  return request({
    url: '/process/version/checkDocId',
    method: 'get',
    params: { docId: docId }
  })
}

// 预览版本文件
export function previewVersion(id, mode) {
  return request({
    url: '/process/doc-preview/view-by-version',
    method: 'get',
    params: {
      id: id,
      mode: mode || 'view'
    }
  })
}

// 下载版本文件
export function downloadVersion(id) {
  return request({
    url: '/process/version/download/' + id,
    method: 'get',
    responseType: 'blob'
  })
}

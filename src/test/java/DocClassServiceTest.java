package com.rzdata.setting.service;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.beans.factory.annotation.Autowired;
import com.rzdata.setting.domain.vo.DocClassVo;
import java.util.List;

/**
 * 文件分类服务测试类
 * 用于测试根据ID查找所有子级的功能
 */
@SpringBootTest
public class DocClassServiceTest {

    @Autowired
    private IDocClassService docClassService;

    /**
     * 测试查找所有子级分类
     */
    @Test
    public void testFindAllChildrenById() {
        // 测试数据基于您提供的SQL
        // ancestors 数据：
        // '0,1,1791269862321500162'
        // '0,1791293188540542978'
        // '0,1,1791668356344725505'
        // '0,1,1791668623731605506'
        
        // 测试查找 ID = "1" 的所有子级
        String parentId = "1";
        List<DocClassVo> children = docClassService.findAllChildrenById(parentId);
        
        System.out.println("父级ID: " + parentId);
        System.out.println("找到的子级数量: " + children.size());
        
        for (DocClassVo child : children) {
            System.out.println("子级ID: " + child.getId() + 
                             ", 名称: " + child.getClassName() + 
                             ", ancestors: " + child.getAncestors());
        }
    }

    /**
     * 测试查找所有子级分类ID
     */
    @Test
    public void testFindAllChildrenIdsById() {
        String parentId = "1";
        List<String> childrenIds = docClassService.findAllChildrenIdsById(parentId);
        
        System.out.println("父级ID: " + parentId);
        System.out.println("子级ID列表: " + childrenIds);
    }

    /**
     * 测试多个不同的父级ID
     */
    @Test
    public void testMultipleParentIds() {
        String[] testIds = {"1", "1791269862321500162", "1791293188540542978"};
        
        for (String parentId : testIds) {
            List<String> childrenIds = docClassService.findAllChildrenIdsById(parentId);
            System.out.println("父级ID: " + parentId + " -> 子级数量: " + childrenIds.size() + " -> 子级IDs: " + childrenIds);
        }
    }
}

# 查看项目版本功能

## 功能概述

这个模块提供了完整的项目版本查看和管理功能，包括：

- 项目版本列表查看
- 版本详情查看
- 版本文件预览
- 版本数据导出
- 高级搜索和筛选

## 文件结构

```
src/views/process/version/
├── index.vue          # 版本管理主页面（包含标签页切换）
├── project.vue        # 项目版本查看页面
└── README.md          # 说明文档

src/api/process/
└── version.js         # 版本相关API接口
```

## 主要功能

### 1. 项目版本列表 (project.vue)

**功能特性：**
- 分页显示项目版本列表
- 支持多条件搜索（项目代码、文件名称、文件编号、版本号、状态）
- 显示版本基本信息（项目代码、文件编号、文件名称、版本号、状态、编制人、编制部门、发布时间）
- 状态标签显示（有效/失效）

**操作功能：**
- 查看详情：显示版本完整信息和历史版本
- 预览文件：在新窗口预览版本文件
- 导出数据：导出Excel格式的版本数据

### 2. 版本详情查看

**详情信息包括：**
- 基本信息：项目代码、文件编号、文件名称、版本号、状态
- 编制信息：编制人、编制部门、发布时间
- 扩展信息：物料编码、物料描述、工厂、产品版本、备注
- 历史版本：显示该文件的所有历史版本

### 3. 文件预览

- 支持在线预览版本文件
- 在新窗口打开，不影响当前操作
- 自动处理预览失败的情况

### 4. 数据导出

- 支持根据当前搜索条件导出数据
- Excel格式导出
- 包含完整的版本信息

## API接口

### 后端接口 (VersionController)

```java
// 查询项目文件版本记录列表
GET /process/version/listProject

// 获取版本详情（包含关联信息）
GET /process/version/detail?docId={docId}&versionValue={versionValue}

// 导出文件版本记录列表
POST /process/version/export

// 预览版本文件
GET /process/doc-preview/view-by-version?id={id}&mode=view
```

### 前端API (version.js)

```javascript
// 查询项目文件版本记录列表
listProjectVersion(query)

// 查询版本详情
getVersionDetail(docId, versionValue)

// 导出版本数据
exportVersion(query)
```

## 使用方法

### 1. 访问页面
通过路由访问 `/process/version` 页面

### 2. 搜索版本
- 在搜索表单中输入搜索条件
- 点击"搜索"按钮查询
- 点击"重置"按钮清空搜索条件

### 3. 查看详情
- 点击表格中的"查看详情"按钮
- 在弹出的对话框中查看完整信息
- 可以查看历史版本列表

### 4. 预览文件
- 点击表格中的"预览"按钮
- 系统会在新窗口打开文件预览

### 5. 导出数据
- 点击工具栏中的"导出"按钮
- 确认导出后下载Excel文件

## 权限控制

- `process:version:query` - 查询版本信息权限
- `process:version:export` - 导出版本数据权限

## 技术特点

1. **响应式设计**：适配不同屏幕尺寸
2. **组件化开发**：模块化的Vue组件结构
3. **错误处理**：完善的错误提示和异常处理
4. **用户体验**：Loading状态、确认对话框、成功提示
5. **数据安全**：权限控制和参数验证

## 扩展功能

可以在此基础上扩展：
- 体系版本查看功能
- 版本比较功能
- 版本审批流程
- 版本关联关系图
- 批量操作功能

## 注意事项

1. 确保后端API接口正常运行
2. 预览功能需要文件服务支持
3. 导出功能需要相应的权限
4. 大数据量时注意分页性能

<template>
  <div class="app-container">
    <el-tabs v-model="activeTab" @tab-click="handleTabClick">
      <el-tab-pane label="项目版本" name="project">
        <project-version ref="projectVersion" />
      </el-tab-pane>
      <el-tab-pane label="体系版本" name="system">
        <system-version ref="systemVersion" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import ProjectVersion from './project.vue'

export default {
  name: "VersionIndex",
  components: {
    ProjectVersion,
    // SystemVersion 组件可以后续添加
    SystemVersion: {
      template: '<div class="text-center p-4">体系版本功能开发中...</div>'
    }
  },
  data() {
    return {
      activeTab: 'project'
    }
  },
  methods: {
    handleTabClick(tab) {
      // 可以在这里添加切换标签页时的逻辑
      console.log('切换到标签页:', tab.name);
    }
  }
}
</script>

<style scoped>
.app-container {
  padding: 20px;
}
</style>

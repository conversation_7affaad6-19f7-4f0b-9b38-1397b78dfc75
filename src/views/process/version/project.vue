<template>
  <div class="app-container">
    <!-- 搜索表单 -->
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="100px">
      <el-form-item label="项目代码" prop="projectCode">
        <el-input
          v-model="queryParams.projectCode"
          placeholder="请输入项目代码"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="文件名称" prop="docName">
        <el-input
          v-model="queryParams.docName"
          placeholder="请输入文件名称"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="文件编号" prop="docId">
        <el-input
          v-model="queryParams.docId"
          placeholder="请输入文件编号"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="版本号" prop="versionValue">
        <el-input
          v-model="queryParams.versionValue"
          placeholder="请输入版本号"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable size="small">
          <el-option label="有效" value="1" />
          <el-option label="失效" value="2" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['process:version:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 表格数据 -->
    <el-table v-loading="loading" :data="versionList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="序号" type="index" width="50" align="center" />
      <el-table-column label="项目代码" align="center" prop="projectCode" width="120" />
      <el-table-column label="文件编号" align="center" prop="docId" width="150" />
      <el-table-column label="文件名称" align="center" prop="docName" min-width="200" show-overflow-tooltip />
      <el-table-column label="版本号" align="center" prop="versionValue" width="100" />
      <el-table-column label="状态" align="center" prop="status" width="80">
        <template slot-scope="scope">
          <el-tag :type="scope.row.status === '1' ? 'success' : 'danger'">
            {{ scope.row.status === '1' ? '有效' : '失效' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="编制人" align="center" prop="nickName" width="100" />
      <el-table-column label="编制部门" align="center" prop="deptName" width="120" />
      <el-table-column label="发布时间" align="center" prop="releaseTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.releaseTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="180">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleDetail(scope.row)"
            v-hasPermi="['process:version:query']"
          >查看详情</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-document"
            @click="handlePreview(scope.row)"
            v-hasPermi="['process:version:query']"
          >预览</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 版本详情对话框 -->
    <el-dialog title="版本详情" :visible.sync="detailOpen" width="80%" append-to-body>
      <el-descriptions :column="2" border v-if="versionDetail">
        <el-descriptions-item label="项目代码">{{ versionDetail.projectCode }}</el-descriptions-item>
        <el-descriptions-item label="文件编号">{{ versionDetail.docId }}</el-descriptions-item>
        <el-descriptions-item label="文件名称">{{ versionDetail.docName }}</el-descriptions-item>
        <el-descriptions-item label="版本号">{{ versionDetail.versionValue }}</el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="versionDetail.status === '1' ? 'success' : 'danger'">
            {{ versionDetail.status === '1' ? '有效' : '失效' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="编制人">{{ versionDetail.nickName }}</el-descriptions-item>
        <el-descriptions-item label="编制部门">{{ versionDetail.deptName }}</el-descriptions-item>
        <el-descriptions-item label="发布时间">{{ parseTime(versionDetail.releaseTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</el-descriptions-item>
        <el-descriptions-item label="物料编码">{{ versionDetail.partNumber }}</el-descriptions-item>
        <el-descriptions-item label="物料描述">{{ versionDetail.partRemark }}</el-descriptions-item>
        <el-descriptions-item label="工厂">{{ versionDetail.factorys }}</el-descriptions-item>
        <el-descriptions-item label="产品版本">{{ versionDetail.productVersion }}</el-descriptions-item>
        <el-descriptions-item label="备注" :span="2">{{ versionDetail.remark }}</el-descriptions-item>
      </el-descriptions>
      
      <!-- 历史版本 -->
      <div style="margin-top: 20px;" v-if="versionDetail.extend && versionDetail.extend.versions">
        <h4>历史版本</h4>
        <el-table :data="versionDetail.extend.versions" size="small">
          <el-table-column label="版本号" prop="versionValue" width="100" />
          <el-table-column label="发布时间" prop="releaseTime" width="180">
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.releaseTime, '{y}-{m}-{d}') }}</span>
            </template>
          </el-table-column>
          <el-table-column label="编制人" prop="nickName" width="100" />
          <el-table-column label="状态" prop="status" width="80">
            <template slot-scope="scope">
              <el-tag :type="scope.row.status === '1' ? 'success' : 'danger'" size="mini">
                {{ scope.row.status === '1' ? '有效' : '失效' }}
              </el-tag>
            </template>
          </el-table-column>
        </el-table>
      </div>
      
      <div slot="footer" class="dialog-footer">
        <el-button @click="detailOpen = false">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listProjectVersion, getVersionDetail, exportVersion } from "@/api/process/version";

export default {
  name: "ProjectVersion",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 版本表格数据
      versionList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示详情对话框
      detailOpen: false,
      // 版本详情
      versionDetail: null,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        projectCode: null,
        docName: null,
        docId: null,
        versionValue: null,
        status: null,
        dataType: 'project'
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询版本列表 */
    getList() {
      this.loading = true;
      listProjectVersion(this.queryParams).then(response => {
        this.versionList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 查看详情按钮操作 */
    handleDetail(row) {
      this.loading = true;
      getVersionDetail(row.docId, row.versionValue).then(response => {
        this.versionDetail = response.data;
        this.detailOpen = true;
        this.loading = false;
      });
    },
    /** 预览按钮操作 */
    handlePreview(row) {
      this.loading = true;
      // 调用预览接口
      this.$http.get('/process/doc-preview/view-by-version', {
        params: {
          id: row.id,
          mode: 'view'
        }
      }).then(response => {
        if (response.data.code === 200 && response.data.data && response.data.data.url) {
          // 在新窗口打开预览
          window.open(response.data.data.url, '_blank');
        } else {
          this.$message.error('预览失败：' + (response.data.msg || '未知错误'));
        }
        this.loading = false;
      }).catch(error => {
        console.error('预览失败:', error);
        this.$message.error('预览失败，请稍后重试');
        this.loading = false;
      });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.$modal.confirm('是否确认导出所有项目版本数据项？').then(() => {
        this.loading = true;
        return exportVersion({
          ...this.queryParams,
          dataType: 'project'
        });
      }).then(response => {
        this.$download.excel(response, `project_version_${new Date().getTime()}.xlsx`);
        this.loading = false;
      }).catch(() => {
        this.loading = false;
      });
    }
  }
};
</script>
